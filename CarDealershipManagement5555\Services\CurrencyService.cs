using CarDealershipManagement.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Globalization;
using System.Threading.Tasks;

namespace CarDealershipManagement.Services
{
    /// <summary>
    /// خدمة إدارة العملة في النظام
    /// المطور: <PERSON><PERSON> - 01285626623 - <EMAIL>
    /// </summary>
    public static class CurrencyService
    {
        private static string _currentCurrency = "EGP"; // الجنيه المصري افتراضياً
        private static string _currencySymbol = "ج.م";
        
        /// <summary>
        /// العملة الحالية
        /// </summary>
        public static string CurrentCurrency => _currentCurrency;
        
        /// <summary>
        /// رمز العملة الحالية
        /// </summary>
        public static string CurrencySymbol => _currencySymbol;
        
        /// <summary>
        /// العملات المدعومة
        /// </summary>
        public static readonly Dictionary<string, string> SupportedCurrencies = new()
        {
            { "EGP", "ج.م" },    // الجنيه المصري
            { "USD", "$" },      // الدولار الأمريكي
            { "EUR", "€" },      // اليورو
            { "SAR", "ر.س" },   // الريال السعودي
            { "AED", "د.إ" },   // الدرهم الإماراتي
            { "KWD", "د.ك" },   // الدينار الكويتي
            { "QAR", "ر.ق" },   // الريال القطري
            { "BHD", "د.ب" },   // الدينار البحريني
            { "JOD", "د.أ" },   // الدينار الأردني
            { "LBP", "ل.ل" },   // الليرة اللبنانية
            { "GBP", "£" },     // الجنيه الإسترليني
            { "JPY", "¥" },     // الين الياباني
            { "CNY", "¥" },     // اليوان الصيني
        };
        
        /// <summary>
        /// تحميل العملة من قاعدة البيانات
        /// </summary>
        public static async Task LoadCurrencyFromDatabase()
        {
            try
            {
                using var context = DbContextFactory.CreateContext();
                var settings = await context.SystemSettings.FirstOrDefaultAsync();
                
                if (settings != null && !string.IsNullOrEmpty(settings.Currency))
                {
                    SetCurrency(settings.Currency);
                }
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، استخدم العملة الافتراضية
                Console.WriteLine($"Error loading currency: {ex.Message}");
                SetCurrency("EGP");
            }
        }
        
        /// <summary>
        /// تعيين العملة الحالية
        /// </summary>
        public static void SetCurrency(string currencyCode)
        {
            if (SupportedCurrencies.ContainsKey(currencyCode))
            {
                _currentCurrency = currencyCode;
                _currencySymbol = SupportedCurrencies[currencyCode];
            }
            else
            {
                // العملة الافتراضية
                _currentCurrency = "EGP";
                _currencySymbol = "ج.م";
            }
        }
        
        /// <summary>
        /// حفظ العملة في قاعدة البيانات
        /// </summary>
        public static async Task SaveCurrencyToDatabase(string currencyCode)
        {
            try
            {
                using var context = DbContextFactory.CreateContext();
                var settings = await context.SystemSettings.FirstOrDefaultAsync();
                
                if (settings != null)
                {
                    settings.Currency = currencyCode;
                    settings.ModifiedDate = DateTime.Now;
                    await context.SaveChangesAsync();
                    
                    // تحديث العملة الحالية
                    SetCurrency(currencyCode);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ العملة: {ex.Message}");
            }
        }
        
        /// <summary>
        /// تنسيق المبلغ مع العملة
        /// </summary>
        public static string FormatCurrency(decimal amount)
        {
            try
            {
                return $"{amount:N2} {_currencySymbol}";
            }
            catch
            {
                return $"{amount} {_currencySymbol}";
            }
        }
        
        /// <summary>
        /// تنسيق المبلغ مع العملة (بدون كسور عشرية)
        /// </summary>
        public static string FormatCurrencyNoDecimals(decimal amount)
        {
            try
            {
                return $"{amount:N0} {_currencySymbol}";
            }
            catch
            {
                return $"{amount} {_currencySymbol}";
            }
        }
        
        /// <summary>
        /// تنسيق المبلغ مع العملة المحددة
        /// </summary>
        public static string FormatCurrency(decimal amount, string currencyCode)
        {
            if (SupportedCurrencies.ContainsKey(currencyCode))
            {
                var symbol = SupportedCurrencies[currencyCode];
                return $"{amount:N2} {symbol}";
            }
            
            return FormatCurrency(amount);
        }
        
        /// <summary>
        /// الحصول على اسم العملة بالعربية
        /// </summary>
        public static string GetCurrencyNameInArabic(string currencyCode)
        {
            return currencyCode switch
            {
                "EGP" => "الجنيه المصري",
                "USD" => "الدولار الأمريكي",
                "EUR" => "اليورو",
                "SAR" => "الريال السعودي",
                "AED" => "الدرهم الإماراتي",
                "KWD" => "الدينار الكويتي",
                "QAR" => "الريال القطري",
                "BHD" => "الدينار البحريني",
                "JOD" => "الدينار الأردني",
                "LBP" => "الليرة اللبنانية",
                "GBP" => "الجنيه الإسترليني",
                "JPY" => "الين الياباني",
                "CNY" => "اليوان الصيني",
                _ => currencyCode
            };
        }
        
        /// <summary>
        /// الحصول على قائمة العملات للعرض
        /// </summary>
        public static List<KeyValuePair<string, string>> GetCurrenciesForDisplay()
        {
            var currencies = new List<KeyValuePair<string, string>>();
            
            foreach (var currency in SupportedCurrencies)
            {
                var displayName = $"{GetCurrencyNameInArabic(currency.Key)} ({currency.Value})";
                currencies.Add(new KeyValuePair<string, string>(currency.Key, displayName));
            }
            
            return currencies.OrderBy(c => c.Value).ToList();
        }
        
        /// <summary>
        /// تحويل النص إلى مبلغ مالي
        /// </summary>
        public static bool TryParseCurrency(string text, out decimal amount)
        {
            amount = 0;
            
            if (string.IsNullOrWhiteSpace(text))
                return false;
            
            // إزالة رموز العملة والمسافات
            var cleanText = text;
            foreach (var symbol in SupportedCurrencies.Values)
            {
                cleanText = cleanText.Replace(symbol, "").Trim();
            }
            
            // إزالة الفواصل
            cleanText = cleanText.Replace(",", "");
            
            return decimal.TryParse(cleanText, out amount);
        }
        
        /// <summary>
        /// التحقق من صحة رمز العملة
        /// </summary>
        public static bool IsValidCurrency(string currencyCode)
        {
            return !string.IsNullOrEmpty(currencyCode) && SupportedCurrencies.ContainsKey(currencyCode);
        }
        
        /// <summary>
        /// تهيئة خدمة العملة
        /// </summary>
        public static async Task InitializeAsync()
        {
            await LoadCurrencyFromDatabase();
        }
    }
}
