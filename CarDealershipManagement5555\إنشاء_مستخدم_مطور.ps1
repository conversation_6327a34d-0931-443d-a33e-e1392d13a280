# إنشاء مستخدم مطور مع صلاحيات كاملة
Add-Type -AssemblyName System.Data.SQLite

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🔧 إنشاء مستخدم مطور مع صلاحيات كاملة" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$currentDir = $PSScriptRoot
$dbPath = Join-Path $currentDir "CarDealership.db"
$binDbPath = Join-Path $currentDir "bin\Debug\net8.0-windows\CarDealership.db"

# البحث عن قاعدة البيانات
$dbToUse = $null
if (Test-Path $dbPath) {
    $dbToUse = $dbPath
    Write-Host "✅ استخدام قاعدة البيانات: $dbPath" -ForegroundColor Green
} elseif (Test-Path $binDbPath) {
    $dbToUse = $binDbPath
    Write-Host "✅ استخدام قاعدة البيانات: $binDbPath" -ForegroundColor Green
} else {
    Write-Host "❌ لم يتم العثور على قاعدة البيانات" -ForegroundColor Red
    Write-Host "🔧 إنشاء قاعدة بيانات جديدة..." -ForegroundColor Yellow
    $dbToUse = $dbPath
}

try {
    # الاتصال بقاعدة البيانات
    $connectionString = "Data Source=$dbToUse;Version=3;"
    $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
    $connection.Open()
    
    Write-Host "🔗 تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    
    # إنشاء جدول المستخدمين إذا لم يكن موجوداً
    $createUsersTableQuery = @"
CREATE TABLE IF NOT EXISTS Users (
    UserId INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT UNIQUE NOT NULL,
    PasswordHash TEXT NOT NULL,
    FullName TEXT NOT NULL,
    Role INTEGER NOT NULL DEFAULT 3,
    IsActive INTEGER NOT NULL DEFAULT 1,
    CreatedDate TEXT NOT NULL,
    LastLoginDate TEXT,
    ModifiedDate TEXT
);
"@
    
    $command = New-Object System.Data.SQLite.SQLiteCommand($createUsersTableQuery, $connection)
    $command.ExecuteNonQuery() | Out-Null
    
    # إنشاء جدول الصلاحيات إذا لم يكن موجوداً
    $createPermissionsTableQuery = @"
CREATE TABLE IF NOT EXISTS UserPermissions (
    UserPermissionsId INTEGER PRIMARY KEY AUTOINCREMENT,
    UserId INTEGER NOT NULL,
    
    -- صلاحيات المخزون
    CanViewInventory INTEGER NOT NULL DEFAULT 0,
    CanAddCar INTEGER NOT NULL DEFAULT 0,
    CanEditCar INTEGER NOT NULL DEFAULT 0,
    CanDeleteCar INTEGER NOT NULL DEFAULT 0,
    CanViewCarDetails INTEGER NOT NULL DEFAULT 0,
    CanManageCarFiles INTEGER NOT NULL DEFAULT 0,
    CanPrintInventory INTEGER NOT NULL DEFAULT 0,
    CanExportInventory INTEGER NOT NULL DEFAULT 0,
    
    -- صلاحيات المبيعات
    CanViewSales INTEGER NOT NULL DEFAULT 0,
    CanAddSale INTEGER NOT NULL DEFAULT 0,
    CanEditSale INTEGER NOT NULL DEFAULT 0,
    CanDeleteSale INTEGER NOT NULL DEFAULT 0,
    CanProcessPayment INTEGER NOT NULL DEFAULT 0,
    CanManageInstallments INTEGER NOT NULL DEFAULT 0,
    CanPrintSalesReports INTEGER NOT NULL DEFAULT 0,
    CanExportSalesData INTEGER NOT NULL DEFAULT 0,
    
    -- صلاحيات العملاء
    CanViewCustomers INTEGER NOT NULL DEFAULT 0,
    CanAddCustomer INTEGER NOT NULL DEFAULT 0,
    CanEditCustomer INTEGER NOT NULL DEFAULT 0,
    CanDeleteCustomer INTEGER NOT NULL DEFAULT 0,
    CanViewCustomerHistory INTEGER NOT NULL DEFAULT 0,
    CanManageCustomerFiles INTEGER NOT NULL DEFAULT 0,
    CanPrintCustomerReports INTEGER NOT NULL DEFAULT 0,
    CanExportCustomerData INTEGER NOT NULL DEFAULT 0,
    
    -- صلاحيات التقارير
    CanViewReports INTEGER NOT NULL DEFAULT 0,
    CanGenerateReports INTEGER NOT NULL DEFAULT 0,
    CanPrintReports INTEGER NOT NULL DEFAULT 0,
    CanExportReports INTEGER NOT NULL DEFAULT 0,
    CanViewFinancials INTEGER NOT NULL DEFAULT 0,
    CanViewStatistics INTEGER NOT NULL DEFAULT 0,
    CanScheduleReports INTEGER NOT NULL DEFAULT 0,
    CanCustomizeReports INTEGER NOT NULL DEFAULT 0,
    
    -- صلاحيات إدارة المستخدمين
    CanManageUsers INTEGER NOT NULL DEFAULT 0,
    CanAddUser INTEGER NOT NULL DEFAULT 0,
    CanEditUser INTEGER NOT NULL DEFAULT 0,
    CanDeleteUser INTEGER NOT NULL DEFAULT 0,
    CanResetPasswords INTEGER NOT NULL DEFAULT 0,
    CanManagePermissions INTEGER NOT NULL DEFAULT 0,
    CanViewUserActivity INTEGER NOT NULL DEFAULT 0,
    CanManageRoles INTEGER NOT NULL DEFAULT 0,
    
    -- صلاحيات النظام والإعدادات
    CanAccessSettings INTEGER NOT NULL DEFAULT 0,
    CanBackupDatabase INTEGER NOT NULL DEFAULT 0,
    CanRestoreDatabase INTEGER NOT NULL DEFAULT 0,
    CanArchiveData INTEGER NOT NULL DEFAULT 0,
    CanExportData INTEGER NOT NULL DEFAULT 0,
    CanImportData INTEGER NOT NULL DEFAULT 0,
    CanManageSubscription INTEGER NOT NULL DEFAULT 0,
    CanViewSystemLogs INTEGER NOT NULL DEFAULT 0,
    
    -- صلاحيات المطور (Developer-only)
    CanAddManager INTEGER NOT NULL DEFAULT 0,
    CanManageManagerPassword INTEGER NOT NULL DEFAULT 0,
    CanActivateSubscription INTEGER NOT NULL DEFAULT 0,
    CanActivateInstallation INTEGER NOT NULL DEFAULT 0,
    CanResetSystem INTEGER NOT NULL DEFAULT 0,
    CanRestoreDefaults INTEGER NOT NULL DEFAULT 0,
    CanAccessDeveloperTools INTEGER NOT NULL DEFAULT 0,
    CanModifySystemCore INTEGER NOT NULL DEFAULT 0,
    
    -- صلاحيات المدير (Manager-only)
    CanFullActivityManagement INTEGER NOT NULL DEFAULT 0,
    CanCopyDatabase INTEGER NOT NULL DEFAULT 0,
    CanArchiveSystem INTEGER NOT NULL DEFAULT 0,
    CanAddSalesRep INTEGER NOT NULL DEFAULT 0,
    CanManageSalesRepPassword INTEGER NOT NULL DEFAULT 0,
    
    FOREIGN KEY (UserId) REFERENCES Users (UserId) ON DELETE CASCADE
);
"@
    
    $command = New-Object System.Data.SQLite.SQLiteCommand($createPermissionsTableQuery, $connection)
    $command.ExecuteNonQuery() | Out-Null
    
    Write-Host "📋 تم إنشاء/التحقق من جداول النظام" -ForegroundColor Green
    
    # حذف المستخدمين الموجودين
    $deleteUsersQuery = "DELETE FROM Users;"
    $command = New-Object System.Data.SQLite.SQLiteCommand($deleteUsersQuery, $connection)
    $command.ExecuteNonQuery() | Out-Null
    
    $deletePermissionsQuery = "DELETE FROM UserPermissions;"
    $command = New-Object System.Data.SQLite.SQLiteCommand($deletePermissionsQuery, $connection)
    $command.ExecuteNonQuery() | Out-Null
    
    Write-Host "🗑️ تم حذف المستخدمين السابقين" -ForegroundColor Yellow
    
    # إنشاء مستخدم مطور مع صلاحيات كاملة
    $developerUser = @{
        Username = "developer"
        Password = "dev123"
        FullName = "مطور النظام"
        Role = 1  # Developer = 1
    }
    
    # إنشاء مستخدمين إضافيين
    $users = @(
        $developerUser,
        @{ Username = "admin"; Password = "123"; FullName = "المدير العام"; Role = 2 },  # Manager = 2
        @{ Username = "amrali"; Password = "braa"; FullName = "عمرو علي"; Role = 2 },      # Manager = 2
        @{ Username = "user"; Password = "pass"; FullName = "مستخدم عام"; Role = 3 },     # SalesRep = 3
        @{ Username = "test"; Password = "test"; FullName = "مستخدم تجريبي"; Role = 3 }   # SalesRep = 3
    )
    
    foreach ($user in $users) {
        # إدراج المستخدم
        $insertUserQuery = @"
INSERT INTO Users (Username, PasswordHash, FullName, Role, IsActive, CreatedDate)
VALUES ('$($user.Username)', '$($user.Password)', '$($user.FullName)', $($user.Role), 1, datetime('now'));
"@
        
        $command = New-Object System.Data.SQLite.SQLiteCommand($insertUserQuery, $connection)
        $result = $command.ExecuteNonQuery()
        
        if ($result -gt 0) {
            # الحصول على UserId
            $getUserIdQuery = "SELECT UserId FROM Users WHERE Username = '$($user.Username)';"
            $command = New-Object System.Data.SQLite.SQLiteCommand($getUserIdQuery, $connection)
            $userId = $command.ExecuteScalar()
            
            # تحديد الصلاحيات حسب الدور
            $permissions = switch ($user.Role) {
                1 { # Developer - جميع الصلاحيات
                    @"
INSERT INTO UserPermissions (
    UserId, CanViewInventory, CanAddCar, CanEditCar, CanDeleteCar, CanViewCarDetails, CanManageCarFiles, CanPrintInventory, CanExportInventory,
    CanViewSales, CanAddSale, CanEditSale, CanDeleteSale, CanProcessPayment, CanManageInstallments, CanPrintSalesReports, CanExportSalesData,
    CanViewCustomers, CanAddCustomer, CanEditCustomer, CanDeleteCustomer, CanViewCustomerHistory, CanManageCustomerFiles, CanPrintCustomerReports, CanExportCustomerData,
    CanViewReports, CanGenerateReports, CanPrintReports, CanExportReports, CanViewFinancials, CanViewStatistics, CanScheduleReports, CanCustomizeReports,
    CanManageUsers, CanAddUser, CanEditUser, CanDeleteUser, CanResetPasswords, CanManagePermissions, CanViewUserActivity, CanManageRoles,
    CanAccessSettings, CanBackupDatabase, CanRestoreDatabase, CanArchiveData, CanExportData, CanImportData, CanManageSubscription, CanViewSystemLogs,
    CanAddManager, CanManageManagerPassword, CanActivateSubscription, CanActivateInstallation, CanResetSystem, CanRestoreDefaults, CanAccessDeveloperTools, CanModifySystemCore,
    CanFullActivityManagement, CanCopyDatabase, CanArchiveSystem, CanAddSalesRep, CanManageSalesRepPassword
) VALUES (
    $userId, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1
);
"@
                }
                2 { # Manager - صلاحيات إدارية
                    @"
INSERT INTO UserPermissions (
    UserId, CanViewInventory, CanAddCar, CanEditCar, CanDeleteCar, CanViewCarDetails, CanManageCarFiles, CanPrintInventory, CanExportInventory,
    CanViewSales, CanAddSale, CanEditSale, CanDeleteSale, CanProcessPayment, CanManageInstallments, CanPrintSalesReports, CanExportSalesData,
    CanViewCustomers, CanAddCustomer, CanEditCustomer, CanDeleteCustomer, CanViewCustomerHistory, CanManageCustomerFiles, CanPrintCustomerReports, CanExportCustomerData,
    CanViewReports, CanGenerateReports, CanPrintReports, CanExportReports, CanViewFinancials, CanViewStatistics, CanScheduleReports, CanCustomizeReports,
    CanManageUsers, CanAddUser, CanEditUser, CanDeleteUser, CanResetPasswords, CanManagePermissions, CanViewUserActivity, CanManageRoles,
    CanAccessSettings, CanBackupDatabase, CanRestoreDatabase, CanArchiveData, CanExportData, CanImportData, CanManageSubscription, CanViewSystemLogs,
    CanFullActivityManagement, CanCopyDatabase, CanArchiveSystem, CanAddSalesRep, CanManageSalesRepPassword
) VALUES (
    $userId, 1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1
);
"@
                }
                3 { # Sales Representative - صلاحيات أساسية
                    @"
INSERT INTO UserPermissions (
    UserId, CanViewInventory, CanAddCar, CanEditCar, CanDeleteCar, CanViewCarDetails, CanManageCarFiles, CanPrintInventory, CanExportInventory,
    CanViewSales, CanAddSale, CanEditSale, CanDeleteSale, CanProcessPayment, CanManageInstallments, CanPrintSalesReports, CanExportSalesData,
    CanViewCustomers, CanAddCustomer, CanEditCustomer, CanDeleteCustomer, CanViewCustomerHistory, CanManageCustomerFiles, CanPrintCustomerReports, CanExportCustomerData,
    CanViewReports, CanGenerateReports, CanPrintReports, CanExportReports, CanViewFinancials, CanViewStatistics, CanScheduleReports, CanCustomizeReports
) VALUES (
    $userId, 1, 0, 0, 0, 1, 0, 1, 0,
    1, 1, 1, 0, 1, 1, 1, 0,
    1, 1, 1, 0, 1, 0, 1, 0,
    1, 1, 1, 0, 0, 1, 0, 0
);
"@
                }
            }
            
            $command = New-Object System.Data.SQLite.SQLiteCommand($permissions, $connection)
            $command.ExecuteNonQuery() | Out-Null
            
            $roleText = switch ($user.Role) {
                1 { "🔧 مطور النظام" }
                2 { "👔 مدير" }
                3 { "🤝 مندوب مبيعات" }
            }
            
            Write-Host "✅ تم إنشاء المستخدم: $($user.Username) ($roleText)" -ForegroundColor Green
        }
    }
    
    $connection.Close()
    
    # نسخ قاعدة البيانات إلى جميع المواقع
    $locations = @($dbPath, $binDbPath)
    foreach ($location in $locations) {
        if ($location -ne $dbToUse) {
            try {
                $dir = Split-Path $location
                if (-not (Test-Path $dir)) {
                    New-Item -ItemType Directory -Path $dir -Force | Out-Null
                }
                Copy-Item $dbToUse $location -Force
                Write-Host "📋 تم نسخ قاعدة البيانات إلى: $location" -ForegroundColor Green
            } catch {
                Write-Host "⚠️ تحذير: لم يتم نسخ قاعدة البيانات إلى: $location" -ForegroundColor Yellow
            }
        }
    }
    
} catch {
    Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
    if ($connection -and $connection.State -eq 'Open') {
        $connection.Close()
    }
}

Write-Host ""
Write-Host "🎉 تم إنشاء المستخدمين مع الصلاحيات بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "🔧 مستخدم المطور (صلاحيات كاملة):" -ForegroundColor Cyan
Write-Host "   اسم المستخدم: developer" -ForegroundColor White
Write-Host "   كلمة المرور: dev123" -ForegroundColor White
Write-Host "   الصلاحيات: جميع الصلاحيات متاحة (مطور النظام)" -ForegroundColor Green
Write-Host ""
Write-Host "👔 مستخدمين إداريين:" -ForegroundColor Cyan
Write-Host "   admin / 123 (مدير عام)" -ForegroundColor White
Write-Host "   amrali / braa (عمرو علي - مدير)" -ForegroundColor White
Write-Host ""
Write-Host "🤝 مستخدمين عاديين:" -ForegroundColor Cyan
Write-Host "   user / pass (مستخدم عام)" -ForegroundColor White
Write-Host "   test / test (مستخدم تجريبي)" -ForegroundColor White
Write-Host ""
Write-Host "💡 نصائح:" -ForegroundColor Yellow
Write-Host "   • استخدم حساب المطور (developer/dev123) للوصول لجميع الميزات" -ForegroundColor White
Write-Host "   • المطور يمكنه إدارة جميع المستخدمين والصلاحيات" -ForegroundColor White
Write-Host "   • المطور له صلاحيات خاصة لإدارة النظام والاشتراكات" -ForegroundColor White
Write-Host ""
Write-Host "🚀 الآن شغل البرنامج وجرب تسجيل الدخول بحساب المطور!" -ForegroundColor Green
Write-Host ""
Write-Host "📞 للمساعدة أو الدعم الفني، اتصل بالمطور" -ForegroundColor Magenta
Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
