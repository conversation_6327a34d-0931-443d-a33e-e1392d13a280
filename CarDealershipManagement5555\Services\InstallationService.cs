using System.Management;
using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;

namespace CarDealershipManagement.Services
{
public class InstallationService
{
    private readonly CarDealershipContext _context;
    private readonly string _installationFile;

    public InstallationService(CarDealershipContext context)
    {
        _context = context;
        _installationFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "installation.auth");
    }

    /// <summary>
    /// Gets the unique device identifier based on hardware characteristics
    /// </summary>
    public string GetDeviceId()
    {
        try
        {
            var deviceInfo = new StringBuilder();

            // Get motherboard serial number
            using(var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
            {
                foreach(ManagementObject obj in searcher.Get())
                {
                    deviceInfo.Append(obj["SerialNumber"]?.ToString() ?? "");
                    break;
                }
            }

            // Get processor ID
            using(var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
            {
                foreach(ManagementObject obj in searcher.Get())
                {
                    deviceInfo.Append(obj["ProcessorId"]?.ToString() ?? "");
                    break;
                }
            }

            // Get hard drive serial number
            using(var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_PhysicalMedia"))
            {
                foreach(ManagementObject obj in searcher.Get())
                {
                    var serial = obj["SerialNumber"]?.ToString()?.Trim();
                    if(!string.IsNullOrEmpty(serial))
                    {
                        deviceInfo.Append(serial);
                        break;
                    }
                }
            }

            // Create hash of device information
            using(var sha256 = SHA256.Create())
            {
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(deviceInfo.ToString()));
                return Convert.ToBase64String(hash);
            }
        }
        catch(Exception)
        {
            // Fallback to environment-based ID
            var fallbackInfo = $"{Environment.MachineName}_{Environment.UserName}_{Environment.OSVersion}";
            using(var sha256 = SHA256.Create())
            {
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fallbackInfo));
                return Convert.ToBase64String(hash);
            }
        }
    }

    /// <summary>
    /// Checks if the current installation is authorized
    /// </summary>
    public async Task<bool> IsInstallationAuthorizedAsync()
    {
        try
        {
            if(!File.Exists(_installationFile))
            {
                return false;
            }

            var authData = await File.ReadAllTextAsync(_installationFile);
            var parts = authData.Split('|');

            if(parts.Length != 3)
            {
                return false;
            }

            var deviceId = parts[0];
            var activationCode = parts[1];
            var activationDate = DateTime.Parse(parts[2]);

            // Check if device ID matches
            if(deviceId != GetDeviceId())
            {
                return false;
            }

            // Check if activation is still valid (e.g., not older than 1 year)
            if(DateTime.Now.Subtract(activationDate).TotalDays > 365)
            {
                return false;
            }

            // Verify activation code in database
            var installation = await _context.AuthorizedInstallations
                               .FirstOrDefaultAsync(i => i.DeviceId == deviceId && i.ActivationCode == activationCode);

            return installation != null && installation.IsActive;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Activates installation for the current device (Developer only)
    /// </summary>
    public async Task<(bool Success, string Message)> ActivateInstallationAsync(User developer, string activationReason = "")
    {
        try
        {
            // Check if user has permission
            if(developer.Permissions?.CanActivateInstallation != true)
            {
                return (false, "You don't have permission to activate installations.");
            }

            var deviceId = GetDeviceId();
            var activationCode = GenerateActivationCode();
            var activationDate = DateTime.Now;

            // Check if device is already activated
            var existingInstallation = await _context.AuthorizedInstallations
                                       .FirstOrDefaultAsync(i => i.DeviceId == deviceId);

            if(existingInstallation != null)
            {
                // Update existing installation
                existingInstallation.ActivationCode = activationCode;
                existingInstallation.ActivationDate = activationDate;
                existingInstallation.ActivatedBy = developer.Username;
                existingInstallation.ActivationReason = activationReason;
                existingInstallation.IsActive = true;
                existingInstallation.ModifiedDate = activationDate;
            }
            else
            {
                // Create new installation record
                var installation = new AuthorizedInstallation
                {
                    DeviceId = deviceId,
                    ActivationCode = activationCode,
                    ActivationDate = activationDate,
                    ActivatedBy = developer.Username,
                    ActivationReason = activationReason,
                    IsActive = true,
                    CreatedDate = activationDate
                };

                _context.AuthorizedInstallations.Add(installation);
            }

            await _context.SaveChangesAsync();

            // Create authorization file
            var authData = $"{deviceId}|{activationCode}|{activationDate:yyyy-MM-dd HH:mm:ss}";
            await File.WriteAllTextAsync(_installationFile, authData);

            return (true, "Installation activated successfully.");
        }
        catch(Exception ex)
        {
            return (false, $"Failed to activate installation: {ex.Message}");
        }
    }

    /// <summary>
    /// Deactivates installation for the current device
    /// </summary>
    public async Task<(bool Success, string Message)> DeactivateInstallationAsync(User developer, string reason = "")
    {
        try
        {
            if(developer.Permissions?.CanActivateInstallation != true)
            {
                return (false, "You don't have permission to deactivate installations.");
            }

            var deviceId = GetDeviceId();
            var installation = await _context.AuthorizedInstallations
                               .FirstOrDefaultAsync(i => i.DeviceId == deviceId);

            if(installation != null)
            {
                installation.IsActive = false;
                installation.DeactivationDate = DateTime.Now;
                installation.DeactivatedBy = developer.Username;
                installation.DeactivationReason = reason;
                installation.ModifiedDate = DateTime.Now;

                await _context.SaveChangesAsync();
            }

            // Remove authorization file
            if(File.Exists(_installationFile))
            {
                File.Delete(_installationFile);
            }

            return (true, "Installation deactivated successfully.");
        }
        catch(Exception ex)
        {
            return (false, $"Failed to deactivate installation: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets all authorized installations
    /// </summary>
    public async Task<List<AuthorizedInstallation>> GetAuthorizedInstallationsAsync()
    {
        return await _context.AuthorizedInstallations
               .OrderByDescending(i => i.CreatedDate)
               .ToListAsync();
    }

    /// <summary>
    /// Generates a unique activation code
    /// </summary>
    private string GenerateActivationCode()
    {
        using(var rng = RandomNumberGenerator.Create())
        {
            var bytes = new byte[16];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes).Replace("+", "").Replace("/", "").Replace("=", "").Substring(0, 12);
        }
    }
}
}
