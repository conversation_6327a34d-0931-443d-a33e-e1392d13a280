# إنشاء مجموعة أيقونات مختلفة للاختيار من بينها
# المطور: <PERSON><PERSON> <PERSON> - 01285626623 - <EMAIL>

Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🎨 إنشاء مجموعة أيقونات للاختيار" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# إنشاء أيقونة كلاسيكية زرقاء
function New-ClassicBlueIcon {
    param([string]$OutputPath, [int]$Size = 256)
    
    $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # خلفية زرقاء كلاسيكية
    $blueBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 70, 130, 180))
    $graphics.FillEllipse($blueBrush, 10, 10, $Size-20, $Size-20)
    
    # حدود بيضاء
    $whitePen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 3)
    $graphics.DrawEllipse($whitePen, 10, 10, $Size-20, $Size-20)
    
    # سيارة بسيطة
    $carBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $carRect = New-Object System.Drawing.Rectangle($Size/3, $Size/2-15, $Size/3, 30)
    $graphics.FillRectangle($carBrush, $carRect)
    
    # نص
    $font = New-Object System.Drawing.Font("Arial", $Size/12, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $graphics.DrawString("CAR", $font, $textBrush, $Size/2-20, $Size/4)
    
    $graphics.Dispose()
    $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    $bitmap.Dispose()
}

# إنشاء أيقونة حديثة خضراء
function New-ModernGreenIcon {
    param([string]$OutputPath, [int]$Size = 256)
    
    $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # خلفية متدرجة خضراء
    $centerPoint = New-Object System.Drawing.PointF($Size/2, $Size/2)
    $gradientBrush = New-Object System.Drawing.Drawing2D.PathGradientBrush(@($centerPoint))
    $gradientBrush.CenterColor = [System.Drawing.Color]::FromArgb(255, 50, 205, 50)
    $gradientBrush.SurroundColors = @([System.Drawing.Color]::FromArgb(255, 34, 139, 34))
    
    $rect = New-Object System.Drawing.Rectangle(10, 10, $Size-20, $Size-20)
    $graphics.FillEllipse($gradientBrush, $rect)
    
    # سيارة مع تفاصيل
    $carColor = [System.Drawing.Color]::White
    $carBrush = New-Object System.Drawing.SolidBrush($carColor)
    
    # جسم السيارة
    $carBody = New-Object System.Drawing.Rectangle($Size/4, $Size/2-20, $Size/2, 25)
    $graphics.FillRectangle($carBrush, $carBody)
    
    # عجلات
    $wheelBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Black)
    $wheel1 = New-Object System.Drawing.Rectangle($Size/4+10, $Size/2+20, 15, 15)
    $wheel2 = New-Object System.Drawing.Rectangle($Size/4+35, $Size/2+20, 15, 15)
    $graphics.FillEllipse($wheelBrush, $wheel1)
    $graphics.FillEllipse($wheelBrush, $wheel2)
    
    $graphics.Dispose()
    $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    $bitmap.Dispose()
}

# إنشاء أيقونة أنيقة رمادية
function New-ElegantGrayIcon {
    param([string]$OutputPath, [int]$Size = 256)
    
    $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # خلفية رمادية متدرجة
    $grayBrush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
        [System.Drawing.Point]::new(0, 0),
        [System.Drawing.Point]::new($Size, $Size),
        [System.Drawing.Color]::FromArgb(255, 105, 105, 105),
        [System.Drawing.Color]::FromArgb(255, 47, 79, 79)
    )
    
    $rect = New-Object System.Drawing.Rectangle(10, 10, $Size-20, $Size-20)
    $graphics.FillEllipse($grayBrush, $rect)
    
    # حدود ذهبية
    $goldPen = New-Object System.Drawing.Pen([System.Drawing.Color]::Gold, 4)
    $graphics.DrawEllipse($goldPen, $rect)
    
    # سيارة فاخرة
    $carBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Silver)
    $carRect = New-Object System.Drawing.Rectangle($Size/3, $Size/2-15, $Size/3, 30)
    $graphics.FillRectangle($carBrush, $carRect)
    
    $graphics.Dispose()
    $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    $bitmap.Dispose()
}

# إنشاء أيقونة نارية حمراء
function New-FireRedIcon {
    param([string]$OutputPath, [int]$Size = 256)
    
    $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # خلفية حمراء نارية
    $centerPoint = New-Object System.Drawing.PointF($Size/2, $Size/2)
    $gradientBrush = New-Object System.Drawing.Drawing2D.PathGradientBrush(@($centerPoint))
    $gradientBrush.CenterColor = [System.Drawing.Color]::FromArgb(255, 255, 69, 0)
    $gradientBrush.SurroundColors = @([System.Drawing.Color]::FromArgb(255, 139, 0, 0))
    
    $rect = New-Object System.Drawing.Rectangle(10, 10, $Size-20, $Size-20)
    $graphics.FillEllipse($gradientBrush, $rect)
    
    # سيارة رياضية
    $carBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $carRect = New-Object System.Drawing.Rectangle($Size/4, $Size/2-15, $Size/2, 25)
    $graphics.FillRectangle($carBrush, $carRect)
    
    # مصابيح LED
    $ledBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Cyan)
    $led1 = New-Object System.Drawing.Rectangle($Size/4-5, $Size/2-10, 5, 5)
    $led2 = New-Object System.Drawing.Rectangle($Size/4-5, $Size/2-5, 5, 5)
    $graphics.FillEllipse($ledBrush, $led1)
    $graphics.FillEllipse($ledBrush, $led2)
    
    $graphics.Dispose()
    $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    $bitmap.Dispose()
}

# إنشاء أيقونة فاخرة بنفسجية
function New-LuxuryPurpleIcon {
    param([string]$OutputPath, [int]$Size = 256)
    
    $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # خلفية بنفسجية فاخرة
    $purpleBrush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
        [System.Drawing.Point]::new(0, 0),
        [System.Drawing.Point]::new($Size, $Size),
        [System.Drawing.Color]::FromArgb(255, 138, 43, 226),
        [System.Drawing.Color]::FromArgb(255, 75, 0, 130)
    )
    
    $rect = New-Object System.Drawing.Rectangle(10, 10, $Size-20, $Size-20)
    $graphics.FillEllipse($purpleBrush, $rect)
    
    # حدود ذهبية مزدوجة
    $goldPen1 = New-Object System.Drawing.Pen([System.Drawing.Color]::Gold, 3)
    $goldPen2 = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(255, 255, 215, 0), 1)
    $graphics.DrawEllipse($goldPen1, $rect)
    $graphics.DrawEllipse($goldPen2, 15, 15, $Size-30, $Size-30)
    
    # سيارة فاخرة
    $carBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Platinum)
    $carRect = New-Object System.Drawing.Rectangle($Size/3, $Size/2-15, $Size/3, 30)
    $graphics.FillRectangle($carBrush, $carRect)
    
    # تاج فاخر
    $crownBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Gold)
    $crownPoints = @(
        [System.Drawing.Point]::new($Size/2-15, 40),
        [System.Drawing.Point]::new($Size/2-10, 25),
        [System.Drawing.Point]::new($Size/2, 20),
        [System.Drawing.Point]::new($Size/2+10, 25),
        [System.Drawing.Point]::new($Size/2+15, 40)
    )
    $graphics.FillPolygon($crownBrush, $crownPoints)
    
    $graphics.Dispose()
    $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    $bitmap.Dispose()
}

# إنشاء أيقونة عصرية برتقالية
function New-ModernOrangeIcon {
    param([string]$OutputPath, [int]$Size = 256)
    
    $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # خلفية برتقالية عصرية
    $orangeBrush = New-Object System.Drawing.Drawing2D.RadialGradientBrush(
        [System.Drawing.Rectangle]::new(0, 0, $Size, $Size),
        [System.Drawing.Color]::FromArgb(255, 255, 165, 0),
        [System.Drawing.Color]::FromArgb(255, 255, 69, 0)
    )
    
    $rect = New-Object System.Drawing.Rectangle(10, 10, $Size-20, $Size-20)
    $graphics.FillEllipse($orangeBrush, $rect)
    
    # تأثير لمعان
    $shineBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(100, 255, 255, 255))
    $shineRect = New-Object System.Drawing.Rectangle(20, 20, $Size/3, $Size/3)
    $graphics.FillEllipse($shineBrush, $shineRect)
    
    # سيارة حديثة
    $carBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $carRect = New-Object System.Drawing.Rectangle($Size/4, $Size/2-15, $Size/2, 25)
    $graphics.FillRectangle($carBrush, $carRect)
    
    $graphics.Dispose()
    $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    $bitmap.Dispose()
}

# إنشاء جميع الأيقونات
Write-Host "🎨 إنشاء الأيقونات..." -ForegroundColor Yellow

New-ClassicBlueIcon -OutputPath "icon_option_1_classic_blue.png"
Write-Host "✅ تم إنشاء الأيقونة الكلاسيكية الزرقاء" -ForegroundColor Green

New-ModernGreenIcon -OutputPath "icon_option_2_modern_green.png"
Write-Host "✅ تم إنشاء الأيقونة الحديثة الخضراء" -ForegroundColor Green

New-ElegantGrayIcon -OutputPath "icon_option_3_elegant_gray.png"
Write-Host "✅ تم إنشاء الأيقونة الأنيقة الرمادية" -ForegroundColor Green

New-FireRedIcon -OutputPath "icon_option_4_fire_red.png"
Write-Host "✅ تم إنشاء الأيقونة النارية الحمراء" -ForegroundColor Green

New-LuxuryPurpleIcon -OutputPath "icon_option_5_luxury_purple.png"
Write-Host "✅ تم إنشاء الأيقونة الفاخرة البنفسجية" -ForegroundColor Green

New-ModernOrangeIcon -OutputPath "icon_option_6_modern_orange.png"
Write-Host "✅ تم إنشاء الأيقونة العصرية البرتقالية" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 تم إنشاء جميع الأيقونات بنجاح!" -ForegroundColor Cyan
Write-Host ""
Write-Host "📁 الملفات المنشأة:" -ForegroundColor White
Write-Host "   1. icon_option_1_classic_blue.png - كلاسيكية زرقاء" -ForegroundColor Gray
Write-Host "   2. icon_option_2_modern_green.png - حديثة خضراء" -ForegroundColor Gray
Write-Host "   3. icon_option_3_elegant_gray.png - أنيقة رمادية" -ForegroundColor Gray
Write-Host "   4. icon_option_4_fire_red.png - نارية حمراء" -ForegroundColor Gray
Write-Host "   5. icon_option_5_luxury_purple.png - فاخرة بنفسجية" -ForegroundColor Gray
Write-Host "   6. icon_option_6_modern_orange.png - عصرية برتقالية" -ForegroundColor Gray
Write-Host "   7. professional-car-icon.png - الأيقونة الاحترافية الحالية" -ForegroundColor Gray

Write-Host ""
Write-Host "👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy" -ForegroundColor Green
Write-Host "📞 للتواصل: 01285626623" -ForegroundColor Green
Write-Host "📧 البريد: <EMAIL>" -ForegroundColor Green

Write-Host ""
Read-Host "اضغط Enter للمتابعة"
