@echo off
chcp 65001 >nul
title برنامج إدارة معرض السيارات - النسخة النهائية مع الأيقونة

echo.
echo ========================================
echo   🚗 برنامج إدارة معرض السيارات
echo ========================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo 🔢 الإصدار: 1.0.0 - ديسمبر 2024
echo.

echo 🎨 الميزات الجديدة:
echo    ✅ أيقونة احترافية مخصصة
echo    ✅ حقوق المطور في جميع الملفات
echo    ✅ نظام تفعيل متقدم
echo    ✅ مجلد مستقل للتوزيع
echo.

set "STANDALONE_DIR=%~dp0CarDealership_Standalone"
set "RELEASE_DIR=%~dp0bin\Release\net8.0-windows"

echo 🔍 البحث عن البرنامج...
echo.

if exist "%STANDALONE_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج في المجلد المستقل
    echo 📁 المكان: %STANDALONE_DIR%
    echo.
    
    REM عرض معلومات الملف
    for %%A in ("%STANDALONE_DIR%\CarDealershipManagement.exe") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📊 معلومات الملف:
        echo    📏 الحجم: !FILE_SIZE_MB! ميجابايت
        echo    📅 تاريخ التعديل: %%~tA
    )
    
    echo.
    echo 🎯 محتويات المجلد المستقل:
    if exist "%STANDALONE_DIR%\app-icon.ico" echo    ✅ app-icon.ico - أيقونة البرنامج
    if exist "%STANDALONE_DIR%\README.txt" echo    ✅ README.txt - دليل المستخدم
    if exist "%STANDALONE_DIR%\تشغيل_البرنامج.bat" echo    ✅ تشغيل_البرنامج.bat - تشغيل سريع
    echo    ✅ CarDealershipManagement.exe - البرنامج الرئيسي
    echo    ✅ جميع المكتبات المطلوبة
    echo.
    
    echo 🚀 تشغيل البرنامج من المجلد المستقل...
    cd /d "%STANDALONE_DIR%"
    set "PROGRAM_PATH=%STANDALONE_DIR%"
    
) else if exist "%RELEASE_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج في مجلد Release
    echo 📁 المكان: %RELEASE_DIR%
    echo.
    
    REM عرض معلومات الملف
    for %%A in ("%RELEASE_DIR%\CarDealershipManagement.exe") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📊 معلومات الملف:
        echo    📏 الحجم: !FILE_SIZE_MB! ميجابايت
        echo    📅 تاريخ التعديل: %%~tA
    )
    
    echo.
    echo 🚀 تشغيل البرنامج من مجلد Release...
    cd /d "%RELEASE_DIR%"
    set "PROGRAM_PATH=%RELEASE_DIR%"
    
) else (
    echo ❌ لم يتم العثور على البرنامج
    echo.
    echo 🔧 يرجى بناء البرنامج أولاً باستخدام:
    echo    1. dotnet build --configuration Release
    echo    2. أو تشغيل "بناء_وإنشاء_ملف_التثبيت.bat"
    echo.
    goto :end
)

echo.
echo 🔑 بيانات الدخول المتاحة:
echo.
echo 🔧 حساب المطور (جميع الصلاحيات):
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo.
echo 👔 حساب المدير (صلاحيات إدارية):
echo    اسم المستخدم: admin
echo    كلمة المرور: 123
echo.
echo 🤝 حساب مندوب المبيعات (صلاحيات أساسية):
echo    اسم المستخدم: user
echo    كلمة المرور: pass
echo.

echo 🆕 نظام التفعيل المتقدم:
echo.
echo ✅ نسخة تجريبية مجانية (30 يوم):
echo    • تفعيل فوري بدون مفتاح ترخيص
echo    • جميع الميزات متاحة
echo    • مناسبة للتقييم والاختبار
echo.
echo ✅ تراخيص مدفوعة:
echo    • شهري: MONTH-XXXXXX-XXXXXX
echo    • ربع سنوي: QUARTER-XXXXXX-XXXXXX (خصم 10%%)
echo    • سنوي: YEAR-XXXXXX-XXXXXX (خصم 25%%)
echo    • مدى الحياة: LIFE-XXXXXX-XXXXXX (حتى 10 مستخدمين)
echo.

echo 🎯 الميزات الرئيسية:
echo.
echo 📦 إدارة المخزون المتقدمة:
echo    • إدارة شاملة للسيارات مع نظام ضمان سلامة البيانات المالية
echo    • إدارة ملفات ومرفقات السيارات
echo    • تصنيف وفلترة متقدمة
echo.
echo 💰 نظام المبيعات الذكي:
echo    • بيع نقدي وبالتقسيط مع إدارة الأقساط المتقدمة
echo    • طباعة الفواتير والعقود
echo    • تتبع حالة المدفوعات
echo.
echo 👥 إدارة العملاء والموردين:
echo    • قاعدة بيانات شاملة للعملاء والموردين
echo    • تاريخ المشتريات والمدفوعات
echo    • كشوف حساب مفصلة
echo.
echo 📊 التقارير والإحصائيات:
echo    • تقارير المبيعات والأرباح التفصيلية
echo    • تقارير الأقساط المحسنة
echo    • طباعة وتصدير بـ 5 صيغ مختلفة
echo    • إحصائيات متقدمة ومؤشرات أداء
echo.
echo 👤 إدارة المستخدمين:
echo    • نظام صلاحيات متكامل (64+ صلاحية)
echo    • أدوار متعددة (مطور، مدير، مندوب)
echo    • إدارة كلمات المرور والأمان
echo    • تتبع نشاط المستخدمين
echo.
echo 🔒 الأمان والحماية:
echo    • نسخ احتياطي تلقائي
echo    • تشفير البيانات الحساسة
echo    • نظام تفعيل وترخيص متقدم
echo    • حماية من فقدان البيانات
echo.

echo 💡 نصائح للاستفادة القصوى:
echo.
echo 1. 🔧 استخدم حساب المطور (amrali/braa) للوصول لجميع الميزات
echo 2. 🆓 جرب النسخة التجريبية أولاً لمدة 30 يوم
echo 3. 📋 اختبر جميع الميزات الجديدة المطورة
echo 4. 💾 قم بإنشاء نسخة احتياطية من البيانات بانتظام
echo 5. 🔐 استخدم كلمات مرور قوية للمستخدمين
echo 6. 📊 راجع التقارير دورياً لمتابعة الأداء
echo.

echo 🚀 جاري تشغيل البرنامج...
echo.

start "" "CarDealershipManagement.exe"

echo ✅ تم تشغيل البرنامج بنجاح!
echo.

echo 🔍 إذا لم تجد البرنامج:
echo    • ابحث في شريط المهام السفلي
echo    • استخدم Alt+Tab للتنقل بين النوافذ
echo    • تحقق من أن البرنامج لم يفتح خلف النوافذ الأخرى
echo.

echo 🎊 مبروك! البرنامج الآن يعمل مع:
echo    ✅ أيقونة احترافية مخصصة
echo    ✅ حقوق المطور في جميع الملفات
echo    ✅ نظام تفعيل متقدم
echo    ✅ جميع الميزات المطورة
echo.

echo 📦 للتوزيع:
echo    📁 المجلد المستقل: %STANDALONE_DIR%
echo    💡 يمكنك نسخ هذا المجلد إلى أي مكان أو ضغطه في ملف ZIP
echo.

:end
cd /d "%~dp0"

echo.
echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo اضغط أي مفتاح للخروج...
pause >nul
