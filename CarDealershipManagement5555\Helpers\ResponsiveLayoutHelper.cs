using System;
using System.Drawing;
using System.Reflection;
using System.Windows.Forms;

namespace CarDealershipManagement.Helpers
{
/// <summary>
/// فئة مساعدة لإدارة التخطيط المرن والتكيف مع أحجام الشاشات المختلفة
/// Helper class for managing responsive layout and screen adaptation
/// </summary>
public static class ResponsiveLayoutHelper
{
    /// <summary>
    /// الحد الأدنى لعرض النافذة
    /// </summary>
    public const int MinFormWidth = 800;

    /// <summary>
    /// الحد الأدنى لارتفاع النافذة
    /// </summary>
    public const int MinFormHeight = 600;

    /// <summary>
    /// تطبيق إعدادات النافذة المرنة
    /// Apply responsive form settings
    /// </summary>
    /// <param name="form">النافذة المراد تطبيق الإعدادات عليها</param>
    /// <param name="defaultWidth">العرض الافتراضي</param>
    /// <param name="defaultHeight">الارتفاع الافتراضي</param>
    public static void ApplyResponsiveFormSettings(Form form, int defaultWidth = 1200, int defaultHeight = 800)
    {
        // الحصول على حجم الشاشة الحالية
        var screenBounds = Screen.FromControl(form).WorkingArea;

        // حساب الحجم المناسب (80% من حجم الشاشة كحد أقصى)
        int formWidth = Math.Min(defaultWidth, (int)(screenBounds.Width * 0.8));
        int formHeight = Math.Min(defaultHeight, (int)(screenBounds.Height * 0.8));

        // التأكد من عدم النزول تحت الحد الأدنى
        formWidth = Math.Max(formWidth, MinFormWidth);
        formHeight = Math.Max(formHeight, MinFormHeight);

        // تطبيق الإعدادات
        form.Size = new Size(formWidth, formHeight);
        form.StartPosition = FormStartPosition.CenterScreen;
        form.MinimumSize = new Size(MinFormWidth, MinFormHeight);

        // تمكين تغيير الحجم إذا لم يكن ممكناً
        if(form.FormBorderStyle == FormBorderStyle.FixedDialog ||
                form.FormBorderStyle == FormBorderStyle.FixedSingle)
        {
            form.FormBorderStyle = FormBorderStyle.Sizable;
        }
    }

    /// <summary>
    /// تطبيق إعدادات الجدول المرنة
    /// Apply responsive DataGridView settings
    /// </summary>
    /// <param name="dataGridView">الجدول المراد تطبيق الإعدادات عليه</param>
    /// <param name="parentContainer">الحاوي الأب</param>
    public static void ApplyResponsiveDataGridSettings(DataGridView dataGridView, Control parentContainer)
    {
        // تطبيق الإرساء للملء الكامل
        dataGridView.Dock = DockStyle.Fill;

        // إعدادات التكيف التلقائي للأعمدة
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
        dataGridView.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.DisplayedCellsExceptHeaders;

        // إعدادات التمرير
        dataGridView.ScrollBars = ScrollBars.Both;
        dataGridView.AutoGenerateColumns = true;

        // إعدادات الأداء
        dataGridView.VirtualMode = false;
        // DoubleBuffered is protected, so we'll use reflection to set it
        SetDoubleBuffered(dataGridView, true);

        // إعدادات المظهر المحسنة
        dataGridView.EnableHeadersVisualStyles = false;
        dataGridView.BackgroundColor = Color.White;
        dataGridView.BorderStyle = BorderStyle.Fixed3D;
        dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        dataGridView.GridColor = Color.FromArgb(230, 230, 230);

        // إعدادات الخط
        dataGridView.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

        // إعدادات رأس الأعمدة
        dataGridView.ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
        {
            BackColor = Color.FromArgb(0, 102, 204),
            ForeColor = Color.White,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            Alignment = DataGridViewContentAlignment.MiddleCenter,
            SelectionBackColor = Color.FromArgb(0, 102, 204),
            SelectionForeColor = Color.White
        };

        // إعدادات الخلايا
        dataGridView.DefaultCellStyle = new DataGridViewCellStyle
        {
            BackColor = Color.White,
            ForeColor = Color.Black,
            SelectionBackColor = Color.FromArgb(173, 216, 230),
            SelectionForeColor = Color.Black,
            Alignment = DataGridViewContentAlignment.MiddleCenter,
            WrapMode = DataGridViewTriState.False
        };

        // إعدادات الصفوف المتناوبة
        dataGridView.AlternatingRowsDefaultCellStyle = new DataGridViewCellStyle
        {
            BackColor = Color.FromArgb(248, 249, 250),
            ForeColor = Color.Black,
            SelectionBackColor = Color.FromArgb(173, 216, 230),
            SelectionForeColor = Color.Black
        };

        // ارتفاع الصفوف
        dataGridView.RowTemplate.Height = 35;
        dataGridView.ColumnHeadersHeight = 40;

        // إعدادات التحديد
        dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
        dataGridView.MultiSelect = false;
        dataGridView.ReadOnly = true;
        dataGridView.AllowUserToAddRows = false;
        dataGridView.AllowUserToDeleteRows = false;
        dataGridView.AllowUserToResizeRows = false;
        dataGridView.AllowUserToResizeColumns = true;
    }

    /// <summary>
    /// إنشاء تخطيط مرن للعناصر
    /// Create responsive layout for controls
    /// </summary>
    /// <param name="parentForm">النافذة الأب</param>
    /// <param name="topPanel">اللوحة العلوية للأزرار والبحث</param>
    /// <param name="dataGridView">الجدول</param>
    /// <param name="topPanelHeight">ارتفاع اللوحة العلوية</param>
    public static void CreateResponsiveLayout(Form parentForm, Panel topPanel, DataGridView dataGridView, int topPanelHeight = 120)
    {
        // إعداد اللوحة العلوية
        topPanel.Dock = DockStyle.Top;
        topPanel.Height = topPanelHeight;
        topPanel.BackColor = Color.FromArgb(248, 249, 250);
        topPanel.Padding = new Padding(10);

        // إعداد الجدول
        ApplyResponsiveDataGridSettings(dataGridView, parentForm);

        // إضافة معالج تغيير الحجم
        parentForm.Resize += (sender, e) =>
        {
            parentForm.Refresh();
        };
    }

    /// <summary>
    /// تطبيق إعدادات الأزرار المرنة
    /// Apply responsive button settings
    /// </summary>
    /// <param name="buttons">مصفوفة الأزرار</param>
    /// <param name="parentPanel">اللوحة الأب</param>
    /// <param name="startX">النقطة الأولى للبداية</param>
    /// <param name="startY">النقطة العمودية للبداية</param>
    /// <param name="buttonSpacing">المسافة بين الأزرار</param>
    public static void ArrangeButtonsResponsively(Button[] buttons, Panel parentPanel, int startX = 20, int startY = 20, int buttonSpacing = 10)
    {
        int currentX = startX;
        int buttonWidth = 120;
        int buttonHeight = 35;

        foreach(var button in buttons)
        {
            button.Size = new Size(buttonWidth, buttonHeight);
            button.Location = new Point(currentX, startY);
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 9F, FontStyle.Bold);

            currentX += buttonWidth + buttonSpacing;

            // إذا تجاوز العرض، انتقل للسطر التالي
            if(currentX + buttonWidth > parentPanel.Width - 20)
            {
                currentX = startX;
                startY += buttonHeight + 10;
            }
        }
    }

    /// <summary>
    /// الحصول على حجم الخط المناسب حسب DPI
    /// Get appropriate font size based on DPI
    /// </summary>
    /// <param name="baseFontSize">حجم الخط الأساسي</param>
    /// <returns>حجم الخط المناسب</returns>
    public static float GetScaledFontSize(float baseFontSize = 9f)
    {
        using(var g = Graphics.FromHwnd(IntPtr.Zero))
        {
            var dpiX = g.DpiX;
            var scaleFactor = dpiX / 96f; // 96 DPI is standard
            return baseFontSize * scaleFactor;
        }
    }

    /// <summary>
    /// تعيين خاصية DoubleBuffered باستخدام Reflection
    /// Set DoubleBuffered property using reflection
    /// </summary>
    /// <param name="control">العنصر</param>
    /// <param name="value">القيمة</param>
    private static void SetDoubleBuffered(Control control, bool value)
    {
        try
        {
            PropertyInfo? doubleBufferedProperty = typeof(Control).GetProperty("DoubleBuffered", BindingFlags.NonPublic | BindingFlags.Instance);
            doubleBufferedProperty?.SetValue(control, value, null);
        }
        catch
        {
            // Ignore if reflection fails
        }
    }

    /// <summary>
    /// فئة مساعدة لإنشاء عناصر واجهة المستخدم المحسنة
    /// Helper class for creating enhanced UI elements
    /// </summary>
    public static class UIElementsHelper
    {
        /// <summary>
        /// إنشاء زر محسن للشاشة الكاملة
        /// Create enhanced button for full screen
        /// </summary>
        /// <param name="text">نص الزر</param>
        /// <param name="location">موقع الزر</param>
        /// <param name="backgroundColor">لون الخلفية</param>
        /// <param name="textColor">لون النص</param>
        /// <param name="size">حجم الزر (اختياري)</param>
        /// <returns>الزر المحسن</returns>
        public static Button CreateStyledButton(string text, Point location, Color backgroundColor,
            Color? textColor = null, Size? size = null)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = size ?? new Size(140, 40),
                BackColor = backgroundColor,
                ForeColor = textColor ?? Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // تحسين مظهر الزر
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = ControlPaint.Dark(backgroundColor, 0.3f);
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(backgroundColor, 0.2f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backgroundColor, 0.1f);

            // إضافة تأثير الظل
            button.Paint += (sender, e) =>
            {
                var btn = sender as Button;
                if (btn != null)
                {
                    // رسم ظل خفيف
                    using (var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                    {
                        e.Graphics.FillRectangle(shadowBrush, 2, 2, btn.Width - 2, btn.Height - 2);
                    }
                }
            };

            return button;
        }

        /// <summary>
        /// تطبيق تنسيق محسن على DataGridView للشاشة الكاملة
        /// Apply enhanced styling to DataGridView for full screen
        /// </summary>
        /// <param name="dgv">شبكة البيانات</param>
        public static void StyleDataGridViewForFullScreen(DataGridView dgv)
        {
            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.Fixed3D;
            dgv.EnableHeadersVisualStyles = false;
            dgv.RowHeadersVisible = false;
            dgv.AllowUserToResizeRows = false;
            dgv.GridColor = Color.FromArgb(230, 230, 230);
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            dgv.ColumnHeadersHeight = 50;
            dgv.RowTemplate.Height = 45;
            dgv.Font = new Font("Segoe UI", 11F);

            // تنسيق رؤوس الأعمدة
            dgv.ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Alignment = DataGridViewContentAlignment.MiddleCenter,
                Padding = new Padding(10),
                WrapMode = DataGridViewTriState.True
            };

            // تنسيق الخلايا
            dgv.DefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.White,
                ForeColor = Color.FromArgb(44, 62, 80),
                SelectionBackColor = Color.FromArgb(52, 152, 219),
                SelectionForeColor = Color.White,
                Font = new Font("Segoe UI", 11F),
                Alignment = DataGridViewContentAlignment.MiddleLeft,
                Padding = new Padding(10, 8, 10, 8),
                WrapMode = DataGridViewTriState.False
            };

            // تنسيق الصفوف المتناوبة
            dgv.AlternatingRowsDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(248, 249, 250),
                ForeColor = Color.FromArgb(44, 62, 80),
                SelectionBackColor = Color.FromArgb(52, 152, 219),
                SelectionForeColor = Color.White
            };
        }
    }
}
}
