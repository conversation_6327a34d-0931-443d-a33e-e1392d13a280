using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using CarDealershipManagement.Forms;

namespace CarDealershipManagement.Services
{
public static class ErrorHandlingService
{
    private static readonly string LogDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
    private static readonly string ErrorLogPath = Path.Combine(LogDirectory, "error_log.txt");
    private static readonly string DebugLogPath = Path.Combine(LogDirectory, "debug_log.txt");

    static ErrorHandlingService()
    {
        // Ensure logs directory exists
        if(!Directory.Exists(LogDirectory))
        {
            Directory.CreateDirectory(LogDirectory);
        }
    }

    public static void HandleException(Exception ex, bool showToUser = true, string? userFriendlyMessage = null)
    {
        if(ex == null)
        {
            return;
        }

        // Log the exception
        LogException(ex);

        if(showToUser)
        {
            // Show user-friendly error message
            var message = userFriendlyMessage ?? GetUserFriendlyMessage(ex);
            var details = $"نوع الخطأ: {ex.GetType().Name}\nالرسالة: {ex.Message}\nالتفاصيل الكاملة:\n{ex}";

            using(var errorForm = new ErrorDisplayForm(message, details))
            {
                errorForm.ShowDialog();
            }
        }
    }

    public static void LogException(Exception ex, string? additionalInfo = null)
    {
        try
        {
            var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] - Exception:\n";
            logEntry += $"Type: {ex.GetType().Name}\n";
            logEntry += $"Message: {ex.Message}\n";

            if(!string.IsNullOrEmpty(additionalInfo))
            {
                logEntry += $"Additional Info: {additionalInfo}\n";
            }

            logEntry += $"Stack Trace:\n{ex.StackTrace}\n";
            logEntry += new string('-', 80) + "\n\n";

            File.AppendAllText(ErrorLogPath, logEntry);
        }
        catch
        {
            // If we can't log, don't throw another exception
        }
    }

    public static void LogDebugInfo(string message)
    {
        try
        {
            var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] - DEBUG: {message}\n";
            File.AppendAllText(DebugLogPath, logEntry);
        }
        catch
        {
            // If we can't log, don't throw another exception
        }
    }

    private static string GetUserFriendlyMessage(Exception ex)
    {
        return ex switch
    {
        UnauthorizedAccessException => "ليس لديك الصلاحية الكافية لتنفيذ هذه العملية.",
        FileNotFoundException => "لم يتم العثور على الملف المطلوب.",
        DirectoryNotFoundException => "لم يتم العثور على المجلد المطلوب.",
        IOException => "حدث خطأ أثناء قراءة أو كتابة الملفات.",
        ArgumentException => "تم تمرير قيمة غير صحيحة للعملية.",
        InvalidOperationException => "لا يمكن تنفيذ هذه العملية في الوقت الحالي.",
        NotSupportedException => "هذه العملية غير مدعومة.",
        OutOfMemoryException => "لا توجد ذاكرة كافية لتنفيذ هذه العملية.",
        TimeoutException => "انتهت مهلة العملية. يرجى المحاولة مرة أخرى.",
        _ => "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني."
    };
}

public static bool TryExecute(Action action, string? operationName = null, bool showErrorToUser = true)
    {
        try
        {
            action();
            return true;
        }
        catch(Exception ex)
        {
            var additionalInfo = !string.IsNullOrEmpty(operationName) ? $"Operation: {operationName}" : null;
            HandleException(ex, showErrorToUser, additionalInfo);
            return false;
        }
    }

    public static T TryExecute<T>(Func<T> func, T defaultValue = default, string? operationName = null, bool showErrorToUser = true)
    {
        try
        {
            return func();
        }
        catch(Exception ex)
        {
            var additionalInfo = !string.IsNullOrEmpty(operationName) ? $"Operation: {operationName}" : null;
            HandleException(ex, showErrorToUser, additionalInfo);
            return defaultValue;
        }
    }

    public static async Task<bool> TryExecute(Func<Task> asyncAction, string? operationName = null, bool showErrorToUser = true)
    {
        try
        {
            await asyncAction();
            return true;
        }
        catch(Exception ex)
        {
            var additionalInfo = !string.IsNullOrEmpty(operationName) ? $"Operation: {operationName}" : null;
            HandleException(ex, showErrorToUser, additionalInfo);
            return false;
        }
    }

    public static async Task<T> TryExecute<T>(Func<Task<T>> asyncFunc, T defaultValue = default, string? operationName = null, bool showErrorToUser = true)
    {
        try
        {
            return await asyncFunc();
        }
        catch(Exception ex)
        {
            var additionalInfo = !string.IsNullOrEmpty(operationName) ? $"Operation: {operationName}" : null;
            HandleException(ex, showErrorToUser, additionalInfo);
            return defaultValue;
        }
    }

    public static void CleanupOldLogs(int daysToKeep = 30)
    {
        try
        {
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            var logFiles = Directory.GetFiles(LogDirectory, "*.txt");

            foreach(var logFile in logFiles)
            {
                var fileInfo = new FileInfo(logFile);
                if(fileInfo.CreationTime < cutoffDate)
                {
                    File.Delete(logFile);
                }
            }
        }
        catch
        {
            // If cleanup fails, don't throw an exception
        }
    }
}
}

