@echo off
title إنشاء نسخة نهائية محدثة

echo.
echo ========================================
echo    إنشاء نسخة نهائية محدثة
echo ========================================
echo.

echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.

echo المشكلة: البرنامج المبني قديم ولا يحتوي على التحديثات
echo الحل: إنشاء نسخة جديدة مع الكود والبيانات المحدثة
echo.

echo 1. إيقاف أي نسخة مشغلة...
taskkill /f /im CarDealershipManagement.exe >nul 2>&1

echo 2. إنشاء مجلد النسخة النهائية...
if exist "CarDealership_Final" rmdir /s /q "CarDealership_Final"
mkdir "CarDealership_Final"

echo 3. نسخ الملفات الأساسية...
xcopy "bin\Debug\net8.0-windows\*.*" "CarDealership_Final\" /E /I /Q /Y

echo 4. تطبيق الإصلاحات النهائية على قاعدة البيانات...
cd CarDealership_Final

echo -- الإصلاحات النهائية > final_fixes.sql
echo -- إضافة حقول إيميل وموقع المعرض >> final_fixes.sql
echo ALTER TABLE SystemSettings ADD COLUMN CompanyEmail TEXT DEFAULT ''; >> final_fixes.sql
echo ALTER TABLE SystemSettings ADD COLUMN CompanyWebsite TEXT DEFAULT ''; >> final_fixes.sql
echo -- إضافة صلاحية إدارة مندوبي المبيعات >> final_fixes.sql
echo ALTER TABLE UserPermissions ADD COLUMN CanManageSalesReps INTEGER DEFAULT 0; >> final_fixes.sql
echo -- تحديث صلاحيات المطور >> final_fixes.sql
echo UPDATE UserPermissions SET CanManageSalesReps = 1 WHERE UserId = 1; >> final_fixes.sql
echo -- تحديث العملة الافتراضية >> final_fixes.sql
echo UPDATE SystemSettings SET Currency = 'EGP' WHERE Currency IS NULL OR Currency = ''; >> final_fixes.sql
echo -- تحديث بيانات الشركة >> final_fixes.sql
echo UPDATE SystemSettings SET CompanyEmail = '<EMAIL>', CompanyWebsite = 'www.carshowroom.com' WHERE CompanyEmail IS NULL OR CompanyEmail = ''; >> final_fixes.sql
echo -- التأكد من تفعيل المطور >> final_fixes.sql
echo UPDATE Users SET IsActive = 1 WHERE Username = 'amrali'; >> final_fixes.sql

REM تطبيق الإصلاحات (تجاهل الأخطاء إذا كانت الحقول موجودة)
sqlite3 CarDealership.db < final_fixes.sql >nul 2>&1

REM تنظيف
del final_fixes.sql >nul 2>&1

cd ..

echo 5. إنشاء ملف تشغيل للنسخة النهائية...
echo @echo off > CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo title Car Dealership Management - Final Updated Version >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo. >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo ======================================== >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo    Car Dealership Management System >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo         Final Updated Version >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo ======================================== >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo. >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL> >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo. >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo الإصلاحات المطبقة: >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo [+] إيميل المعرض في الإعدادات >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo [+] تبسيط نموذج العملاء >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo [+] صلاحية إدارة مندوبي المبيعات >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo [+] نظام العملة الشامل >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo. >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo بيانات الدخول: >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo اسم المستخدم: amrali >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo كلمة المرور: braa >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo. >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo echo تشغيل البرنامج... >> CarDealership_Final\تشغيل_النسخة_النهائية.bat
echo CarDealershipManagement.exe >> CarDealership_Final\تشغيل_النسخة_النهائية.bat

echo 6. تشغيل النسخة النهائية...
start "" "CarDealership_Final\CarDealershipManagement.exe"

echo.
echo ✅ تم إنشاء النسخة النهائية المحدثة بنجاح!
echo.

echo 📁 مكان النسخة: CarDealership_Final
echo 🚀 ملف التشغيل: تشغيل_النسخة_النهائية.bat
echo.

echo 📝 الإصلاحات المطبقة:
echo ✅ إيميل المعرض: الإدارة → الإعدادات → معلومات الشركة
echo ✅ العملاء المبسط: إدارة العملاء → إضافة عميل
echo ✅ صلاحية المندوبين: إدارة المستخدمين → صلاحيات المدير
echo ✅ نظام العملة: مطبق على كامل البرنامج
echo.

echo 🔐 بيانات الدخول:
echo اسم المستخدم: amrali
echo كلمة المرور: braa
echo.

echo للدعم: 01285626623 - <EMAIL>
echo.

pause
