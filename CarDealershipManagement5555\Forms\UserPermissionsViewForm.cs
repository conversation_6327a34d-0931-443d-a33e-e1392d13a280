using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class UserPermissionsViewForm : Form
    {
        private Label lblUserInfo = new Label();
        private ListView lvPermissions = new ListView();
        private Button btnClose = new Button();
        private Label lblStatus = null!;

        private int userId;
        private string username = string.Empty;

        public UserPermissionsViewForm(int userId, string username)
        {
            this.userId = userId;
            this.username = username;
            InitializeComponent();
            LoadPermissions();
        }

        private void InitializeComponent()
        {
            this.Text = "صلاحيات المستخدم - User Permissions";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(30, 400),
                Size = new Size(520, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // User info
            lblUserInfo = new Label
            {
                Text = $"المستخدم: {username}",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204),
                Location = new Point(30, 20),
                Size = new Size(400, 25)
            };

            // Permissions list view
            lvPermissions = new ListView
            {
                Location = new Point(30, 60),
                Size = new Size(520, 330),
                View = View.Details,
                FullRowSelect = true,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Font = new Font("Segoe UI", 9F)
            };
            lvPermissions.Columns.Add("الصلاحية", 250, HorizontalAlignment.Left);
            lvPermissions.Columns.Add("الحالة", 100, HorizontalAlignment.Center);

            // Close button
            btnClose = new Button
            {
                Text = "إغلاق",
                Location = new Point(450, 430),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] { lblUserInfo, lvPermissions, btnClose, lblStatus });
        }

        private async void LoadPermissions()
        {
            lblStatus.Text = ""; // Clear previous status messages

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var user = await context.Users.Include(u => u.Permissions).FirstOrDefaultAsync(u => u.UserId == userId);
                if (user != null && user.Permissions != null)
                {
                    AddPermissionToListView("عرض المخزون", user.Permissions.CanViewInventory);
                    AddPermissionToListView("إضافة سيارة", user.Permissions.CanAddCar);
                    AddPermissionToListView("تعديل سيارة", user.Permissions.CanEditCar);
                    AddPermissionToListView("حذف سيارة", user.Permissions.CanDeleteCar);
                    AddPermissionToListView("البيع", user.Permissions.CanSell);
                    AddPermissionToListView("عرض المبيعات", user.Permissions.CanViewSales);
                    AddPermissionToListView("تعديل المبيعات", user.Permissions.CanEditSale);
                    AddPermissionToListView("حذف المبيعات", user.Permissions.CanDeleteSale);
                    AddPermissionToListView("عرض العملاء", user.Permissions.CanViewCustomers);
                    AddPermissionToListView("إضافة عميل", user.Permissions.CanAddCustomer);
                    AddPermissionToListView("تعديل عميل", user.Permissions.CanEditCustomer);
                    AddPermissionToListView("حذف عميل", user.Permissions.CanDeleteCustomer);
                    AddPermissionToListView("تقارير العملاء", user.Permissions.CanViewCustomerReport);
                    AddPermissionToListView("إدارة الموردين", user.Permissions.CanManageSuppliers);
                    AddPermissionToListView("عرض الحسابات", user.Permissions.CanViewAccounts);
                    AddPermissionToListView("التقارير العامة", user.Permissions.CanViewGeneralReports);
                    AddPermissionToListView("إدارة المستخدمين", user.Permissions.CanManageUsers);
                    AddPermissionToListView("إدارة الإعدادات", user.Permissions.CanManageSettings);
                    AddPermissionToListView("طباعة التقارير", user.Permissions.CanPrintReports);
                    AddPermissionToListView("طباعة الكشوفات", user.Permissions.CanPrintStatements);

                    // Developer-specific permissions
                    if (user.Role == UserRole.Developer)
                    {
                        AddPermissionToListView("إضافة مدير النشاط", user.Permissions.CanAddManager);
                        AddPermissionToListView("إدارة كلمة مرور المدير", user.Permissions.CanManageManagerPassword);
                        AddPermissionToListView("تفعيل الاشتراك", user.Permissions.CanActivateSubscription);
                        AddPermissionToListView("تفعيل التثبيت", user.Permissions.CanActivateInstallation);
                        AddPermissionToListView("إعادة تعيين النظام", user.Permissions.CanResetSystem);
                        AddPermissionToListView("استعادة الإعدادات الافتراضية", user.Permissions.CanRestoreDefaults);
                    }

                    // Manager-specific permissions
                    if (user.Role == UserRole.Manager)
                    {
                        AddPermissionToListView("الإدارة الكاملة للنشاط", user.Permissions.CanFullActivityManagement);
                        AddPermissionToListView("نسخ قاعدة البيانات", user.Permissions.CanCopyDatabase);
                        AddPermissionToListView("أرشفة النظام", user.Permissions.CanArchiveSystem);
                        AddPermissionToListView("إضافة مندوب مبيعات", user.Permissions.CanAddSalesRep);
                        AddPermissionToListView("إدارة كلمة مرور مندوب المبيعات", user.Permissions.CanManageSalesRepPassword);
                    }
                }
                else
                {
                    lblStatus.Text = "لم يتم العثور على صلاحيات لهذا المستخدم.";
                }
            }, "تحميل صلاحيات المستخدم");
        }

        private void AddPermissionToListView(string permissionName, bool isGranted)
        {
            var status = isGranted ? "مفعل" : "معطل";
            var item = new ListViewItem(permissionName);
            item.SubItems.Add(status);
            item.ForeColor = isGranted ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69);
            lvPermissions.Items.Add(item);
        }
    }
}


