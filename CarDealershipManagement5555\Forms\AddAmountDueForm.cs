using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics.CodeAnalysis;

namespace CarDealershipManagement.Forms
{
    public partial class AddAmountDueForm : Form
    {
        private readonly int supplierId;
        private Label lblSupplierName;
        private Label lblAmount;
        private TextBox txtAmount;
        private Label lblDescription;
        private TextBox txtDescription;
        private Label lblDate;
        private DateTimePicker dtpDate;
        private Button btnSave;
        private Button btnCancel;
        private Label lblStatus = null!;

        public AddAmountDueForm(int supplierId)
        {
            this.supplierId = supplierId;
            InitializeComponent();
            LoadSupplierInfo();
        }

        [MemberNotNull(nameof(lblSupplierName), nameof(lblAmount), nameof(txtAmount), 
                       nameof(lblDescription), nameof(txtDescription), nameof(lblDate), 
                       nameof(dtpDate), nameof(btnSave), nameof(btnCancel))]
        private void InitializeComponent()
        {
            this.Text = "إضافة مبلغ مستحق للمورد - Add Amount Due to Supplier";
            this.Size = new Size(400, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(20, 320),
                Size = new Size(350, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Supplier name label
            lblSupplierName = new Label
            {
                Text = "المورد: تحميل...",
                Location = new Point(20, 20),
                Size = new Size(350, 25),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204)
            };

            // Amount label and textbox
            lblAmount = new Label
            {
                Text = "المبلغ المستحق:",
                Location = new Point(20, 70),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            txtAmount = new TextBox
            {
                Location = new Point(130, 68),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 9F)
            };

            // Description label and textbox
            lblDescription = new Label
            {
                Text = "الوصف:",
                Location = new Point(20, 110),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            txtDescription = new TextBox
            {
                Location = new Point(130, 108),
                Size = new Size(200, 60),
                Multiline = true,
                Font = new Font("Segoe UI", 9F)
            };

            // Date label and picker
            lblDate = new Label
            {
                Text = "تاريخ الاستحقاق:",
                Location = new Point(20, 180),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            dtpDate = new DateTimePicker
            {
                Location = new Point(130, 178),
                Size = new Size(200, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now,
                Font = new Font("Segoe UI", 9F)
            };

            // Save button
            btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(180, 350),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;

            // Cancel button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(270, 350),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            // Add controls to form
            this.Controls.AddRange(new Control[]
            {
                lblSupplierName, lblAmount, txtAmount, lblDescription, txtDescription,
                lblDate, dtpDate, btnSave, btnCancel, lblStatus
            });
        }

        private async void LoadSupplierInfo()
        {
            lblStatus.Text = ""; // Clear previous status messages
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var supplier = await context.Suppliers.FindAsync(supplierId);
                if (supplier != null)
                {
                    lblSupplierName.Text = $"المورد: {supplier.SupplierName}";
                }
                else
                {
                    lblStatus.Text = "لم يتم العثور على معلومات المورد.";
                }
            }, "تحميل معلومات المورد");
        }

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages

            // Validate input
            if (string.IsNullOrWhiteSpace(txtAmount.Text))
            {
                lblStatus.Text = "يرجى إدخال المبلغ المستحق.";
                txtAmount.Focus();
                return;
            }

            if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
            {
                lblStatus.Text = "يرجى إدخال مبلغ صحيح أكبر من صفر.";
                txtAmount.Focus();
                return;
            }

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                // Get the supplier
                var supplier = await context.Suppliers.FindAsync(supplierId);
                if (supplier == null)
                {
                    lblStatus.Text = "المورد غير موجود.";
                    return;
                }

                // Update supplier's total owed amount
                supplier.TotalOwed += amount;

                // Create a record for tracking this amount due (you might want to create a separate table for this)
                // For now, we'll just update the supplier's total owed amount

                await context.SaveChangesAsync();

                MessageBox.Show($"تم إضافة مبلغ {amount:C} إلى المورد {supplier.SupplierName} بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }, "حفظ المبلغ المستحق");
        }
    }
}


