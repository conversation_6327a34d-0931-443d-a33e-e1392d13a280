using System.ComponentModel.DataAnnotations;

namespace CarDealershipManagement.Models
{
    public class User
    {
        [Key]
        public int UserId
        {
            get;
            set;
        }

        [Required(ErrorMessage = "اسم المستخدم مطلوب.")]
        [StringLength(50, ErrorMessage = "اسم المستخدم لا يمكن أن يتجاوز 50 حرفًا.")]
        public string Username
        {
            get;
            set;
        } = string.Empty; // اسم المستخدم

        [Required(ErrorMessage = "كلمة المرور مطلوبة.")]
        [StringLength(256, ErrorMessage = "كلمة المرور لا يمكن أن تتجاوز 256 حرفًا.")]
        public string PasswordHash
        {
            get;
            set;
        } = string.Empty; // كلمة المرور مشفرة

        [Required(ErrorMessage = "الاسم الكامل مطلوب.")]
        [StringLength(100, ErrorMessage = "الاسم الكامل لا يمكن أن يتجاوز 100 حرفًا.")]
        public string FullName
        {
            get;
            set;
        } = string.Empty; // الاسم الكامل

        [Required(ErrorMessage = "دور المستخدم مطلوب.")]
        public UserRole Role
        {
            get;    // دور المستخدم
            set;
        }

        public bool IsActive
        {
            get;
            set;
        } = true; // نشط/غير نشط

        public DateTime CreatedDate
        {
            get;
            set;
        } = DateTime.Now;
        public DateTime? LastLoginDate
        {
            get;
            set;
        }
        public DateTime? ModifiedDate
        {
            get;
            set;
        }

        // Navigation properties
        public virtual UserPermissions? Permissions
        {
            get;
            set;
        }
    }

    /// <summary>
    /// نموذج صلاحيات المستخدم - منظم حسب الفئات
    /// </summary>
    public class UserPermissions
    {
        [Key]
        public int UserPermissionsId
        {
            get;
            set;
        }

        [Required]
        public int UserId
        {
            get;    // ربط بالمستخدم
            set;
        }

        #region صلاحيات المخزون - Inventory Permissions
        /// <summary>
        /// عرض المخزون - يمكن للمستخدم رؤية قائمة السيارات المتاحة
        /// المطور: ✅ | المدير: ✅ | المندوب: ✅
        /// </summary>
        public bool CanViewInventory
        {
            get;
            set;
        } = false;

        /// <summary>
        /// إضافة سيارة للمخزن - يمكن للمستخدم إضافة سيارات جديدة
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanAddCar
        {
            get;
            set;
        } = false;

        /// <summary>
        /// تعديل سيارة في المخزن - يمكن للمستخدم تعديل بيانات السيارات
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanEditCar
        {
            get;
            set;
        } = false;

        /// <summary>
        /// حذف سيارة من المخزن - يمكن للمستخدم حذف السيارات
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanDeleteCar
        {
            get;
            set;
        } = false;
        #endregion

        #region صلاحيات المبيعات - Sales Permissions
        /// <summary>
        /// البيع - يمكن للمستخدم إنشاء فواتير بيع جديدة
        /// المطور: ✅ | المدير: ✅ | المندوب: ✅
        /// </summary>
        public bool CanSell
        {
            get;
            set;
        } = false;

        /// <summary>
        /// عرض المبيعات - يمكن للمستخدم رؤية قائمة المبيعات
        /// المطور: ✅ | المدير: ✅ | المندوب: ✅ (مبيعاته فقط)
        /// </summary>
        public bool CanViewSales
        {
            get;
            set;
        } = false;

        /// <summary>
        /// تعديل فاتورة بيع - يمكن للمستخدم تعديل فواتير البيع
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanEditSale
        {
            get;
            set;
        } = false;

        /// <summary>
        /// حذف فاتورة بيع - يمكن للمستخدم حذف فواتير البيع
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanDeleteSale
        {
            get;
            set;
        } = false;
        #endregion

        #region صلاحيات العملاء - Customer Permissions
        /// <summary>
        /// عرض العملاء - يمكن للمستخدم رؤية قائمة العملاء
        /// المطور: ✅ | المدير: ✅ | المندوب: ✅
        /// </summary>
        public bool CanViewCustomers
        {
            get;
            set;
        } = false;

        /// <summary>
        /// إضافة عميل - يمكن للمستخدم إضافة عملاء جدد
        /// المطور: ✅ | المدير: ✅ | المندوب: ✅
        /// </summary>
        public bool CanAddCustomer
        {
            get;
            set;
        } = false;

        /// <summary>
        /// تعديل عميل - يمكن للمستخدم تعديل بيانات العملاء
        /// المطور: ✅ | المدير: ✅ | المندوب: ✅
        /// </summary>
        public bool CanEditCustomer
        {
            get;
            set;
        } = false;

        /// <summary>
        /// حذف عميل - يمكن للمستخدم حذف العملاء من النظام
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanDeleteCustomer
        {
            get;
            set;
        } = false;

        /// <summary>
        /// عرض تقرير عن عميل معين - يمكن للمستخدم رؤية تقارير العملاء
        /// المطور: ✅ | المدير: ✅ | المندوب: ✅
        /// </summary>
        public bool CanViewCustomerReport
        {
            get;
            set;
        } = false;
        #endregion

        #region صلاحيات الموردين - Supplier Permissions
        /// <summary>
        /// إدارة الموردين - يمكن للمستخدم إدارة قائمة الموردين
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanManageSuppliers
        {
            get;
            set;
        } = false;
        #endregion

        #region صلاحيات التقارير والحسابات - Reports & Accounting Permissions
        /// <summary>
        /// عرض الحسابات - يمكن للمستخدم رؤية التقارير المالية
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanViewAccounts
        {
            get;
            set;
        } = false;

        /// <summary>
        /// عرض تقارير عامة - يمكن للمستخدم رؤية التقارير العامة للنظام
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanViewGeneralReports
        {
            get;
            set;
        } = false;
        #endregion

        #region صلاحيات الإدارة العامة - General Management Permissions
        /// <summary>
        /// إدارة المستخدمين - يمكن للمستخدم إضافة وتعديل وحذف المستخدمين
        /// المطور: ✅ | المدير: ❌ | المندوب: ❌
        /// </summary>
        public bool CanManageUsers
        {
            get;
            set;
        } = false;

        /// <summary>
        /// إدارة الإعدادات - يمكن للمستخدم تعديل إعدادات النظام
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanManageSettings
        {
            get;
            set;
        } = false;
        #endregion

        #region صلاحيات الطباعة - Printing Permissions
        /// <summary>
        /// طباعة التقارير - يمكن للمستخدم طباعة التقارير المختلفة
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanPrintReports
        {
            get;
            set;
        } = false;

        /// <summary>
        /// طباعة الكشوفات - يمكن للمستخدم طباعة كشوفات الحسابات والفواتير
        /// المطور: ✅ | المدير: ✅ | المندوب: ✅
        /// </summary>
        public bool CanPrintStatements
        {
            get;
            set;
        } = false;
        #endregion

        #region صلاحيات النظام العامة - General System Permissions
        /// <summary>
        /// النسخ الاحتياطي - يمكن للمستخدم إنشاء نسخة احتياطية من قاعدة البيانات
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanBackupDatabase
        {
            get;
            set;
        } = false;

        /// <summary>
        /// استعادة قاعدة البيانات - يمكن للمستخدم استعادة قاعدة البيانات من نسخة احتياطية
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanRestoreDatabase
        {
            get;
            set;
        } = false;

        /// <summary>
        /// أرشفة البيانات - يمكن للمستخدم أرشفة البيانات القديمة وإدارة الأرشيف
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanArchiveData
        {
            get;
            set;
        } = false;

        /// <summary>
        /// تصدير البيانات - يمكن للمستخدم تصدير البيانات إلى ملفات خارجية
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanExportData
        {
            get;
            set;
        } = false;

        /// <summary>
        /// استيراد البيانات - يمكن للمستخدم استيراد البيانات من ملفات خارجية
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanImportData
        {
            get;
            set;
        } = false;
        #endregion

        #region صلاحيات المطور الخاصة - Developer-Specific Permissions
        /// <summary>
        /// إضافة مدير النشاط - يمكن للمطور إضافة مديرين جدد
        /// المطور: ✅ | المدير: ❌ | المندوب: ❌
        /// </summary>
        public bool CanAddManager
        {
            get;
            set;
        } = false;

        /// <summary>
        /// إدارة كلمة مرور المدير - يمكن للمطور تغيير كلمات مرور المديرين
        /// المطور: ✅ | المدير: ❌ | المندوب: ❌
        /// </summary>
        public bool CanManageManagerPassword
        {
            get;
            set;
        } = false;

        /// <summary>
        /// تفعيل الاشتراك - يمكن للمطور تفعيل وإدارة الاشتراكات
        /// المطور: ✅ | المدير: ❌ | المندوب: ❌
        /// </summary>
        public bool CanActivateSubscription
        {
            get;
            set;
        } = false;

        /// <summary>
        /// تفعيل التثبيت - يمكن للمطور تفعيل النظام على أجهزة جديدة
        /// المطور: ✅ | المدير: ❌ | المندوب: ❌
        /// </summary>
        public bool CanActivateInstallation
        {
            get;
            set;
        } = false;

        /// <summary>
        /// إعادة تعيين النظام - يمكن للمطور إعادة تعيين النظام بالكامل
        /// المطور: ✅ | المدير: ❌ | المندوب: ❌
        /// </summary>
        public bool CanResetSystem
        {
            get;
            set;
        } = false;

        /// <summary>
        /// استعادة الإعدادات الافتراضية - يمكن للمطور استعادة الإعدادات الأصلية
        /// المطور: ✅ | المدير: ❌ | المندوب: ❌
        /// </summary>
        public bool CanRestoreDefaults
        {
            get;
            set;
        } = false;
        #endregion

        #region صلاحيات مدير النشاط الخاصة - Manager-Specific Permissions
        /// <summary>
        /// الإدارة الكاملة للنشاط - يمكن للمدير إدارة جميع جوانب النشاط التجاري
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanFullActivityManagement
        {
            get;
            set;
        } = false;

        /// <summary>
        /// نسخ قاعدة البيانات - يمكن للمدير إنشاء نسخ احتياطية من قاعدة البيانات
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanCopyDatabase
        {
            get;
            set;
        } = false;

        /// <summary>
        /// أرشفة النظام - يمكن للمدير أرشفة البيانات القديمة
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanArchiveSystem
        {
            get;
            set;
        } = false;

        /// <summary>
        /// إضافة مندوب مبيعات - يمكن للمدير إضافة مندوبين جدد
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanAddSalesRep
        {
            get;
            set;
        } = false;

        /// <summary>
        /// إدارة مندوبي المبيعات - يمكن للمستخدم إدارة حسابات مندوبي المبيعات
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanManageSalesReps
        {
            get;
            set;
        } = false;

        /// <summary>
        /// إدارة كلمة مرور مندوب المبيعات - يمكن للمدير تغيير كلمات مرور المندوبين
        /// المطور: ✅ | المدير: ✅ | المندوب: ❌
        /// </summary>
        public bool CanManageSalesRepPassword
        {
            get;
            set;
        } = false;
        #endregion

        public DateTime CreatedDate
        {
            get;
            set;
        } = DateTime.Now;
        public DateTime? ModifiedDate
        {
            get;
            set;
        }

        // Navigation properties
        public virtual User User
        {
            get;
            set;
        } = null!;
    }

    public class SystemSettings
    {
        [Key]
        public int SettingsId
        {
            get;
            set;
        }

        // Subscription settings - إعدادات الاشتراك
        public SubscriptionType SubscriptionType
        {
            get;
            set;
        } = SubscriptionType.Yearly; // نوع الاشتراك
        public DateTime? SubscriptionStartDate
        {
            get;    // تاريخ بداية الاشتراك
            set;
        }
        public DateTime? SubscriptionEndDate
        {
            get;    // تاريخ انتهاء الاشتراك
            set;
        }
        public bool IsActive
        {
            get;
            set;
        } = true; // هل الاشتراك نشط

        // Application settings - إعدادات التطبيق
        [StringLength(10, ErrorMessage = "العملة لا يمكن أن تتجاوز 10 أحرف.")]
        public string Currency
        {
            get;
            set;
        } = "USD"; // العملة

        [StringLength(200, ErrorMessage = "اسم الشركة لا يمكن أن يتجاوز 200 حرفًا.")]
        public string CompanyName
        {
            get;
            set;
        } = string.Empty; // اسم الشركة

        [StringLength(500, ErrorMessage = "عنوان الشركة لا يمكن أن يتجاوز 500 حرفًا.")]
        public string CompanyAddress
        {
            get;
            set;
        } = string.Empty; // عنوان الشركة

        [StringLength(20, ErrorMessage = "هاتف الشركة لا يمكن أن يتجاوز 20 حرفًا.")]
        public string CompanyPhone
        {
            get;
            set;
        } = string.Empty; // هاتف الشركة

        [StringLength(100, ErrorMessage = "إيميل المعرض لا يمكن أن يتجاوز 100 حرفًا.")]
        [EmailAddress(ErrorMessage = "صيغة الإيميل غير صحيحة.")]
        public string CompanyEmail
        {
            get;
            set;
        } = string.Empty; // إيميل المعرض

        [StringLength(100, ErrorMessage = "موقع المعرض الإلكتروني لا يمكن أن يتجاوز 100 حرفًا.")]
        public string CompanyWebsite
        {
            get;
            set;
        } = string.Empty; // موقع المعرض الإلكتروني

        [StringLength(500, ErrorMessage = "مسار شعار الشركة لا يمكن أن يتجاوز 500 حرفًا.")]
        public string CompanyLogo
        {
            get;
            set;
        } = string.Empty; // شعار الشركة

        [StringLength(50, ErrorMessage = "رقم السجل التجاري لا يمكن أن يتجاوز 50 حرفًا.")]
        public string CommercialRegister
        {
            get;
            set;
        } = string.Empty; // رقم السجل التجاري

        [StringLength(50, ErrorMessage = "رقم البطاقة الضريبية لا يمكن أن يتجاوز 50 حرفًا.")]
        public string TaxCard
        {
            get;
            set;
        } = string.Empty; // رقم البطاقة الضريبية

        // Gallery settings - إعدادات المعرض
        public bool GalleryDataEnabled
        {
            get;
            set;
        } = false; // تفعيل بيانات المعرض
        public bool ShowCarImages
        {
            get;
            set;
        } = true; // إظهار صور السيارات
        public int MaxImagesPerCar
        {
            get;
            set;
        } = 10; // الحد الأقصى لصور السيارة الواحدة

        // Backup settings - إعدادات النسخ الاحتياطي
        public bool AutoBackupEnabled
        {
            get;
            set;
        } = false; // تفعيل النسخ الاحتياطي التلقائي
        public int BackupIntervalHours
        {
            get;
            set;
        } = 24; // فترة النسخ الاحتياطي بالساعات

        [StringLength(500, ErrorMessage = "مسار النسخ الاحتياطي لا يمكن أن يتجاوز 500 حرفًا.")]
        public string BackupPath
        {
            get;
            set;
        } = string.Empty; // مسار النسخ الاحتياطي

        public DateTime CreatedDate
        {
            get;
            set;
        } = DateTime.Now;
        public DateTime? ModifiedDate
        {
            get;
            set;
        }
    }

    public enum UserRole
    {
        Developer = 1,          // مبرمج - أعلى صلاحيات
        Manager = 2,            // مدير النشاط
        SalesRepresentative = 3 // مندوب مبيعات
    }

    public enum SubscriptionType
    {
        Monthly = 0,     // شهري
        Quarterly = 1,   // ربع سنوي
        SemiAnnual = 2,  // نصف سنوي
        Yearly = 3       // سنوي
    }
}


