-- إصلا<PERSON><PERSON><PERSON> قاعدة البيانات الفورية
-- المطور: Amr <PERSON> - 01285626623 - <EMAIL>

-- إضافة حقول إيميل وموقع المعرض
ALTER TABLE SystemSettings ADD COLUMN CompanyEmail TEXT DEFAULT '';
ALTER TABLE SystemSettings ADD COLUMN CompanyWebsite TEXT DEFAULT '';

-- إضا<PERSON>ة صلاحية إدارة مندوبي المبيعات
ALTER TABLE UserPermissions ADD COLUMN CanManageSalesReps INTEGER DEFAULT 0;

-- تحديث صلاحيات المطور
UPDATE UserPermissions SET CanManageSalesReps = 1 WHERE UserId = 1;

-- تب<PERSON>يط جدول العملاء
-- إنشاء جدول العملاء الجديد المبسط
CREATE TABLE IF NOT EXISTS Customers_New (
    CustomerId INTEGER PRIMARY KEY AUTOINCREMENT,
    FullName TEXT NOT NULL,
    IdNumber TEXT NOT NULL,
    Address TEXT NOT NULL,
    PrimaryPhone TEXT NOT NULL,
    SecondaryPhone TEXT,
    Email TEXT,
    IsActive INTEGER DEFAULT 1,
    IsDeleted INTEGER DEFAULT 0,
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT,
    DeletedDate TEXT
);

-- نقل البيانات الموجودة
INSERT OR IGNORE INTO Customers_New (
    CustomerId, FullName, IdNumber, Address, PrimaryPhone,
    SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate
)
SELECT 
    CustomerId,
    FullName,
    IdNumber,
    COALESCE(Country, '') || ', ' || COALESCE(City, '') || ', ' || COALESCE(Area, '') || ', ' || COALESCE(Street, '') AS Address,
    PrimaryPhone,
    SecondaryPhone,
    Email,
    IsActive,
    IsDeleted,
    CreatedDate,
    ModifiedDate,
    DeletedDate
FROM Customers WHERE EXISTS (SELECT 1 FROM Customers LIMIT 1);

-- حذف الجدول القديم وإعادة تسمية الجديد
DROP TABLE IF EXISTS Customers_Old;
ALTER TABLE Customers RENAME TO Customers_Old;
ALTER TABLE Customers_New RENAME TO Customers;

-- تنظيف العناوين
UPDATE Customers 
SET Address = TRIM(REPLACE(REPLACE(REPLACE(Address, ', , ', ', '), '  ', ' '), ', ,', ','))
WHERE Address IS NOT NULL;

UPDATE Customers 
SET Address = TRIM(Address, ', ')
WHERE Address IS NOT NULL;

UPDATE Customers 
SET Address = 'غير محدد'
WHERE Address IS NULL OR Address = '' OR Address = ', , , ';

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_customers_fullname ON Customers(FullName);
CREATE INDEX IF NOT EXISTS idx_customers_idnumber ON Customers(IdNumber);
CREATE INDEX IF NOT EXISTS idx_customers_active ON Customers(IsActive);
