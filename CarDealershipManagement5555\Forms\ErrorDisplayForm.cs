using System;
using System.Drawing;
using System.Windows.Forms;

namespace CarDealershipManagement.Forms
{
public partial class ErrorDisplayForm : Form
{
    private Label lblErrorMessage;
    private TextBox txtErrorDetails;
    private Button btnClose;
    private Button btnShowDetails;

    public ErrorDisplayForm(string message, string details)
    {
        InitializeComponent();
        lblErrorMessage.Text = message;
        txtErrorDetails.Text = details;
        txtErrorDetails.Visible = false; // Hide details by default
        this.Height = 200; // Initial smaller height
    }

    private void InitializeComponent()
    {
        this.Text = "خطأ في التطبيق";
        this.Size = new Size(500, 200);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(255, 240, 240); // Light red background
        this.Font = new Font("Segoe UI", 10F);
        this.FormBorderStyle = FormBorderStyle.FixedDialog;
        this.MaximizeBox = false;
        this.MinimizeBox = false;

        lblErrorMessage = new Label
        {
            Text = "حدث خطأ غير متوقع في التطبيق.",
            Location = new Point(20, 20),
            Size = new Size(450, 60),
            Font = new Font("Segoe UI", 12F, FontStyle.Bold),
            ForeColor = Color.DarkRed,
            TextAlign = ContentAlignment.MiddleCenter
        };

        txtErrorDetails = new TextBox
        {
            Multiline = true,
            ReadOnly = true,
            ScrollBars = ScrollBars.Vertical,
            Location = new Point(20, 100),
            Size = new Size(450, 150),
            Font = new Font("Consolas", 9F),
            BackColor = Color.WhiteSmoke,
            ForeColor = Color.Black
        };

        btnShowDetails = new Button
        {
            Text = "عرض التفاصيل",
            Location = new Point(20, 70),
            Size = new Size(120, 30),
            BackColor = Color.FromArgb(0, 123, 255), // Blue
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat
        };
        btnShowDetails.FlatAppearance.BorderSize = 0;
        btnShowDetails.Click += (sender, e) =>
        {
            txtErrorDetails.Visible = !txtErrorDetails.Visible;
            btnShowDetails.Text = txtErrorDetails.Visible ? "إخفاء التفاصيل" : "عرض التفاصيل";
            this.Height = txtErrorDetails.Visible ? 350 : 200;
        };

        btnClose = new Button
        {
            Text = "إغلاق",
            Location = new Point(350, 70),
            Size = new Size(120, 30),
            BackColor = Color.FromArgb(108, 117, 125), // Gray
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat
        };
        btnClose.FlatAppearance.BorderSize = 0;
        btnClose.Click += (sender, e) => this.Close();

        this.Controls.Add(lblErrorMessage);
        this.Controls.Add(txtErrorDetails);
        this.Controls.Add(btnShowDetails);
        this.Controls.Add(btnClose);
    }
}
}

