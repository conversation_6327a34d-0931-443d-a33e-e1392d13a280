is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = true
build_property.EnableTrimAnalyzer = 
build_property.IncludeAllContentForSelfExtract = 
build_property.ApplicationManifest = 
build_property.StartupObject = 
build_property.ApplicationDefaultFont = 
build_property.ApplicationHighDpiMode = 
build_property.ApplicationUseCompatibleTextRendering = 
build_property.ApplicationVisualStyles = 
build_property.TargetFramework = net8.0-windows
build_property.TargetFramework = net8.0-windows
build_property.TargetPlatformMinVersion = 7.0
build_property.TargetPlatformMinVersion = 7.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = CarDealershipManagement
build_property.ProjectDir = C:\Users\<USER>\Desktop\CarDealershipManagement_updated\CarDealershipManagement5555\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
