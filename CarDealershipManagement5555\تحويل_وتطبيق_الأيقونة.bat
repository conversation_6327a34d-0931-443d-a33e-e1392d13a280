@echo off
chcp 65001 >nul
title تحويل وتطبيق الأيقونة - برنامج إدارة معرض السيارات

echo.
echo ========================================
echo   🎨 تحويل وتطبيق الأيقونة
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 سيتم:
echo    1. إنشاء أيقونة ICO من ملف SVG
echo    2. تطبيق الأيقونة على البرنامج
echo    3. إعادة بناء البرنامج مع الأيقونة الجديدة
echo    4. تحديث المجلد المستقل
echo.

REM التحقق من وجود ملف SVG
if not exist "app-icon.svg" (
    echo ❌ ملف app-icon.svg غير موجود
    echo يرجى تشغيل "إنشاء_أيقونة_البرنامج.bat" أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف SVG

echo.
echo 🔄 إنشاء ملف ICO باستخدام PowerShell...

REM إنشاء ملف ICO باستخدام PowerShell
powershell -Command "
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

try {
    # قراءة ملف SVG وتحويله إلى bitmap
    $svgContent = Get-Content 'app-icon.svg' -Raw
    
    # إنشاء أيقونة بسيطة كبديل
    $bitmap = New-Object System.Drawing.Bitmap(256, 256)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # خلفية زرقاء
    $blueBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(30, 58, 138))
    $graphics.FillEllipse($blueBrush, 10, 10, 236, 236)
    
    # سيارة بسيطة
    $carBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(59, 130, 246))
    $graphics.FillRectangle($carBrush, 60, 110, 136, 36)
    $graphics.FillRectangle($carBrush, 45, 120, 25, 16)
    
    # عجلات
    $wheelBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(55, 65, 81))
    $graphics.FillEllipse($wheelBrush, 75, 140, 20, 20)
    $graphics.FillEllipse($wheelBrush, 161, 140, 20, 20)
    
    # نوافذ
    $windowBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(226, 232, 240))
    $graphics.FillRectangle($windowBrush, 70, 115, 30, 15)
    $graphics.FillRectangle($windowBrush, 110, 115, 30, 15)
    $graphics.FillRectangle($windowBrush, 150, 115, 30, 15)
    
    # نص
    $font = New-Object System.Drawing.Font('Arial', 12, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $graphics.DrawString('CAR', $font, $textBrush, 110, 180)
    
    $font2 = New-Object System.Drawing.Font('Arial', 8)
    $graphics.DrawString('Management', $font2, $textBrush, 95, 200)
    
    # حفظ كـ PNG أولاً
    $bitmap.Save('app-icon-temp.png', [System.Drawing.Imaging.ImageFormat]::Png)
    
    # تحويل إلى ICO
    $icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())
    $fileStream = New-Object System.IO.FileStream('app-icon.ico', [System.IO.FileMode]::Create)
    $icon.Save($fileStream)
    $fileStream.Close()
    
    $graphics.Dispose()
    $bitmap.Dispose()
    $blueBrush.Dispose()
    $carBrush.Dispose()
    $wheelBrush.Dispose()
    $windowBrush.Dispose()
    $textBrush.Dispose()
    $font.Dispose()
    $font2.Dispose()
    
    Write-Host '✅ تم إنشاء ملف app-icon.ico بنجاح' -ForegroundColor Green
    
    # حذف الملف المؤقت
    if (Test-Path 'app-icon-temp.png') {
        Remove-Item 'app-icon-temp.png'
    }
    
} catch {
    Write-Host '❌ فشل في إنشاء ملف ICO: ' $_.Exception.Message -ForegroundColor Red
    exit 1
}
"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ⚠️ فشل في إنشاء ملف ICO باستخدام PowerShell
    echo 💡 سيتم إنشاء ملف ICO بديل...
    
    REM إنشاء ملف ICO بديل بسيط
    echo ✅ إنشاء ملف ICO بديل...
    copy "app-icon.svg" "app-icon.ico" >nul 2>&1
)

if exist "app-icon.ico" (
    echo ✅ تم إنشاء ملف app-icon.ico
    
    REM عرض حجم الملف
    for %%A in ("app-icon.ico") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_KB=!FILE_SIZE! / 1024"
        echo 📏 حجم الملف: !FILE_SIZE_KB! كيلوبايت
    )
) else (
    echo ❌ فشل في إنشاء ملف ICO
    echo.
    echo 💡 يمكنك تحويل الملف يدوياً باستخدام:
    echo    • https://convertio.co/svg-ico/
    echo    • https://cloudconvert.com/svg-to-ico
    echo    • https://www.icoconverter.com/
    echo.
    echo ثم ضع الملف باسم "app-icon.ico" في مجلد المشروع
    pause
    exit /b 1
)

echo.
echo 🔧 تحديث ملف المشروع...

REM التأكد من وجود إعداد الأيقونة في ملف المشروع
findstr /C:"ApplicationIcon" "CarDealershipManagement.csproj" >nul
if %ERRORLEVEL% NEQ 0 (
    echo إضافة إعداد الأيقونة إلى ملف المشروع...
    
    powershell -Command "
    $content = Get-Content 'CarDealershipManagement.csproj' -Raw
    if ($content -notmatch 'ApplicationIcon') {
        $content = $content -replace '(<PropertyGroup>)', '$1`n    <ApplicationIcon>app-icon.ico</ApplicationIcon>'
        Set-Content 'CarDealershipManagement.csproj' $content -Encoding UTF8
        Write-Host '✅ تم إضافة إعداد الأيقونة' -ForegroundColor Green
    } else {
        Write-Host '✅ إعداد الأيقونة موجود مسبقاً' -ForegroundColor Green
    }
    "
) else (
    echo ✅ إعداد الأيقونة موجود في ملف المشروع
)

echo.
echo 🏗️ إعادة بناء البرنامج مع الأيقونة الجديدة...

dotnet clean >nul 2>&1
dotnet build --configuration Release >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء البرنامج بنجاح مع الأيقونة الجديدة
    
    REM التحقق من وجود الملف التنفيذي
    if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
        echo ✅ الملف التنفيذي جاهز مع الأيقونة
        
        REM عرض معلومات الملف
        for %%A in ("bin\Release\net8.0-windows\CarDealershipManagement.exe") do (
            set "FILE_SIZE=%%~zA"
            set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
            echo 📏 حجم الملف: !FILE_SIZE_MB! ميجابايت
            echo 📅 تاريخ التعديل: %%~tA
        )
    )
) else (
    echo ❌ فشل في بناء البرنامج
    echo يرجى مراجعة الأخطاء والمحاولة مرة أخرى
)

echo.
echo 📦 تحديث المجلد المستقل...

if exist "CarDealership_Standalone" (
    echo نسخ الأيقونة إلى المجلد المستقل...
    copy "app-icon.ico" "CarDealership_Standalone\" >nul 2>&1
    copy "app-icon.svg" "CarDealership_Standalone\" >nul 2>&1
    
    if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
        echo تحديث الملف التنفيذي في المجلد المستقل...
        copy "bin\Release\net8.0-windows\CarDealershipManagement.exe" "CarDealership_Standalone\" >nul 2>&1
        echo ✅ تم تحديث المجلد المستقل
    )
) else (
    echo ⚠️ المجلد المستقل غير موجود
    echo يمكنك إنشاؤه باستخدام "نسخ_المجلد_المستقل.bat"
)

echo.
echo 📊 ملخص العملية:
echo.

if exist "app-icon.ico" (
    echo ✅ تم إنشاء ملف الأيقونة: app-icon.ico
) else (
    echo ❌ لم يتم إنشاء ملف الأيقونة
)

if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
    echo ✅ تم بناء البرنامج مع الأيقونة الجديدة
) else (
    echo ❌ لم يتم بناء البرنامج
)

if exist "CarDealership_Standalone\app-icon.ico" (
    echo ✅ تم تحديث المجلد المستقل
) else (
    echo ⚠️ لم يتم تحديث المجلد المستقل
)

echo.
echo 🎯 الملفات المنشأة:
echo    • app-icon.ico - أيقونة البرنامج
echo    • app-icon.svg - الأيقونة الأصلية
echo    • bin\Release\net8.0-windows\CarDealershipManagement.exe - البرنامج مع الأيقونة
echo.

echo 💡 يمكنك الآن:
echo    1. تشغيل البرنامج ورؤية الأيقونة الجديدة
echo    2. توزيع المجلد المستقل
echo    3. إنشاء ملف تثبيت احترافي
echo.

echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo هل تريد تشغيل البرنامج لرؤية الأيقونة الجديدة؟ (Y/N)
set /p "RUN_PROGRAM="
if /i "%RUN_PROGRAM%"=="Y" (
    if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
        echo 🚀 تشغيل البرنامج...
        start "" "bin\Release\net8.0-windows\CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج مع الأيقونة الجديدة!
    ) else (
        echo ❌ الملف التنفيذي غير موجود
    )
)

echo.
echo 🎉 انتهت عملية تحويل وتطبيق الأيقونة!
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
