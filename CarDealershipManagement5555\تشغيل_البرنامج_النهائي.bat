@echo off
chcp 65001 >nul
title برنامج إدارة معرض السيارات - التشغيل النهائي

echo.
echo ========================================
echo    🚗 برنامج إدارة معرض السيارات 🚗
echo ========================================
echo.

echo 🔧 فحص وإصلاح قاعدة البيانات...
echo.

REM التأكد من وجود قاعدة البيانات
if not exist "CarDealership.db" (
    if exist "bin\Debug\net8.0-windows\CarDealership.db" (
        echo 📋 نسخ قاعدة البيانات...
        copy "bin\Debug\net8.0-windows\CarDealership.db" "CarDealership.db" >nul
        echo ✅ تم نسخ قاعدة البيانات
    )
)

if not exist "bin\Debug\net8.0-windows\CarDealership.db" (
    if exist "CarDealership.db" (
        echo 📋 نسخ قاعدة البيانات إلى مجلد bin...
        if not exist "bin\Debug\net8.0-windows" mkdir "bin\Debug\net8.0-windows"
        copy "CarDealership.db" "bin\Debug\net8.0-windows\CarDealership.db" >nul
        echo ✅ تم نسخ قاعدة البيانات
    )
)

echo.
echo 🚀 تشغيل البرنامج...
echo.

cd /d "%~dp0\bin\Debug\net8.0-windows"

if exist "CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج
    echo.
    echo 🔑 بيانات الدخول:
    echo    اسم المستخدم: amrali
    echo    كلمة المرور: braa
    echo.
    echo 📋 للوصول إلى تبويب الأقساط المحسن:
    echo    1. سجل الدخول باستخدام البيانات أعلاه
    echo    2. اختر "التقارير" من القائمة الرئيسية
    echo    3. انتقل إلى تبويب "📅 تقارير الأقساط"
    echo.
    echo 🎯 الميزات الجديدة:
    echo    • 📊 إنشاء التقرير - تقرير شامل مع فلترة
    echo    • 🖨️ طباعة محسنة - طباعة احترافية
    echo    • 📤 تصدير - 5 صيغ مختلفة
    echo    • 📈 ملخص الأقساط - إحصائيات سريعة
    echo.
    echo 🚀 جاري فتح البرنامج...
    echo.
    
    REM تشغيل البرنامج
    start "" "CarDealershipManagement.exe"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    echo.
    echo إذا لم يظهر البرنامج، تحقق من:
    echo • أن Windows Defender لا يحجب البرنامج
    echo • أن البرنامج لم يفتح خلف النوافذ الأخرى
    echo • جرب تشغيل البرنامج كمسؤول
    echo.
    
) else (
    echo ❌ خطأ: لم يتم العثور على ملف البرنامج
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. تأكد من بناء المشروع في Visual Studio
    echo    2. تحقق من مسار الملف
    echo    3. أعد تشغيل Visual Studio كمسؤول
    echo.
)

cd /d "%~dp0"

echo 📞 للمساعدة أو الدعم الفني، اتصل بالمطور
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
