@echo off
chcp 65001 >nul
title إنشاء أيقونة البرنامج - برنامج إدارة معرض السيارات

echo.
echo ========================================
echo   🎨 إنشاء أيقونة البرنامج
echo ========================================
echo.

echo 👨‍💻 معلومات المطور:
echo    الاسم: Amr Ali Elawamy
echo    الهاتف: 01285626623
echo    البريد الإلكتروني: <EMAIL>
echo.

echo 🎯 سيتم إنشاء:
echo    • أيقونة البرنامج (app-icon.ico)
echo    • شعار الشركة (logo.png)
echo    • ملف معلومات المطور
echo    • تحديث ملفات المشروع بحقوق المطور
echo.

REM إنشاء ملف معلومات المطور
(
echo معلومات المطور - برنامج إدارة معرض السيارات
echo ================================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo.
echo 🏢 الشركة: Car Dealership Management Solutions
echo 🌐 الموقع: www.cardealership.com
echo 📞 الدعم الفني: <EMAIL>
echo.
echo 📅 تاريخ التطوير: ديسمبر 2024
echo 🔢 الإصدار: 1.0.0
echo.
echo 🎯 وصف البرنامج:
echo نظام شامل ومتقدم لإدارة معارض السيارات يتضمن جميع الميزات
echo المطلوبة لإدارة المخزون والمبيعات والعملاء والتقارير مع نظام
echo تفعيل متقدم وحماية قوية للبيانات.
echo.
echo 🔧 التقنيات المستخدمة:
echo    • C# .NET 8.0
echo    • Windows Forms
echo    • Entity Framework Core
echo    • SQLite Database
echo    • Advanced Security System
echo.
echo 🏆 الميزات الرئيسية:
echo    • إدارة شاملة للمخزون والسيارات
echo    • نظام مبيعات متقدم مع الأقساط
echo    • إدارة العملاء والموردين
echo    • تقارير وإحصائيات تفصيلية
echo    • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo    • نظام ضمان سلامة البيانات المالية
echo    • نظام تفعيل وترخيص متقدم
echo    • نسخ احتياطي وأرشفة
echo.
echo 📜 حقوق الطبع والنشر:
echo جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
echo 📞 للتواصل مع المطور:
echo    الهاتف: 01285626623
echo    البريد الإلكتروني: <EMAIL>
) > "معلومات_المطور.txt"

echo ✅ تم إنشاء ملف معلومات المطور

REM إنشاء ملف SVG للأيقونة
(
echo ^<?xml version="1.0" encoding="UTF-8"?^>
echo ^<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"^>
echo   ^<defs^>
echo     ^<linearGradient id="carGradient" x1="0%%" y1="0%%" x2="100%%" y2="100%%"^>
echo       ^<stop offset="0%%" style="stop-color:#1e3a8a;stop-opacity:1" /^>
echo       ^<stop offset="50%%" style="stop-color:#3b82f6;stop-opacity:1" /^>
echo       ^<stop offset="100%%" style="stop-color:#60a5fa;stop-opacity:1" /^>
echo     ^</linearGradient^>
echo     ^<linearGradient id="bgGradient" x1="0%%" y1="0%%" x2="100%%" y2="100%%"^>
echo       ^<stop offset="0%%" style="stop-color:#f8fafc;stop-opacity:1" /^>
echo       ^<stop offset="100%%" style="stop-color:#e2e8f0;stop-opacity:1" /^>
echo     ^</linearGradient^>
echo   ^</defs^>
echo   
echo   ^<!-- خلفية دائرية --^>
echo   ^<circle cx="128" cy="128" r="120" fill="url(#bgGradient)" stroke="#1e3a8a" stroke-width="4"/^>
echo   
echo   ^<!-- السيارة الرئيسية --^>
echo   ^<g transform="translate(128,128)"^>
echo     ^<!-- جسم السيارة --^>
echo     ^<rect x="-60" y="-20" width="120" height="40" rx="8" fill="url(#carGradient)"/^>
echo     ^<!-- مقدمة السيارة --^>
echo     ^<rect x="-70" y="-15" width="20" height="30" rx="10" fill="url(#carGradient)"/^>
echo     ^<!-- نوافذ السيارة --^>
echo     ^<rect x="-50" y="-15" width="25" height="15" rx="3" fill="#e2e8f0"/^>
echo     ^<rect x="-20" y="-15" width="25" height="15" rx="3" fill="#e2e8f0"/^>
echo     ^<rect x="10" y="-15" width="25" height="15" rx="3" fill="#e2e8f0"/^>
echo     ^<!-- العجلات --^>
echo     ^<circle cx="-40" cy="25" r="12" fill="#374151"/^>
echo     ^<circle cx="-40" cy="25" r="8" fill="#6b7280"/^>
echo     ^<circle cx="40" cy="25" r="12" fill="#374151"/^>
echo     ^<circle cx="40" cy="25" r="8" fill="#6b7280"/^>
echo     ^<!-- المصابيح --^>
echo     ^<circle cx="-65" cy="-5" r="4" fill="#fbbf24"/^>
echo     ^<circle cx="-65" cy="5" r="4" fill="#ef4444"/^>
echo   ^</g^>
echo   
echo   ^<!-- أيقونة الإدارة --^>
echo   ^<g transform="translate(180,80)"^>
echo     ^<rect x="-15" y="-10" width="30" height="20" rx="3" fill="#059669" opacity="0.8"/^>
echo     ^<rect x="-12" y="-7" width="24" height="2" fill="#ffffff"/^>
echo     ^<rect x="-12" y="-3" width="24" height="2" fill="#ffffff"/^>
echo     ^<rect x="-12" y="1" width="24" height="2" fill="#ffffff"/^>
echo   ^</g^>
echo   
echo   ^<!-- أيقونة المبيعات --^>
echo   ^<g transform="translate(80,180)"^>
echo     ^<circle cx="0" cy="0" r="15" fill="#dc2626" opacity="0.8"/^>
echo     ^<text x="0" y="5" text-anchor="middle" fill="white" font-size="16" font-weight="bold"^>$^</text^>
echo   ^</g^>
echo   
echo   ^<!-- النص --^>
echo   ^<text x="128" y="220" text-anchor="middle" fill="#1e3a8a" font-size="14" font-weight="bold"^>Car Management^</text^>
echo   ^<text x="128" y="235" text-anchor="middle" fill="#64748b" font-size="10"^>by Amr Ali Elawamy^</text^>
echo ^</svg^>
) > "app-icon.svg"

echo ✅ تم إنشاء ملف SVG للأيقونة

REM إنشاء ملف HTML لعرض الأيقونة
(
echo ^<!DOCTYPE html^>
echo ^<html lang="ar" dir="rtl"^>
echo ^<head^>
echo     ^<meta charset="UTF-8"^>
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^>
echo     ^<title^>أيقونة برنامج إدارة معرض السيارات^</title^>
echo     ^<style^>
echo         body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); }
echo         .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
echo         .header { text-align: center; margin-bottom: 30px; }
echo         .icon-display { text-align: center; margin: 30px 0; }
echo         .icon-sizes { display: flex; justify-content: center; gap: 20px; margin: 20px 0; }
echo         .icon-size { text-align: center; }
echo         .developer-info { background: #f8fafc; padding: 20px; border-radius: 10px; margin: 20px 0; }
echo         .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
echo         .feature { background: #e2e8f0; padding: 15px; border-radius: 8px; }
echo     ^</style^>
echo ^</head^>
echo ^<body^>
echo     ^<div class="container"^>
echo         ^<div class="header"^>
echo             ^<h1 style="color: #1e3a8a; margin-bottom: 10px;"^>🚗 برنامج إدارة معرض السيارات^</h1^>
echo             ^<p style="color: #64748b; font-size: 18px;"^>نظام شامل ومتقدم لإدارة معارض السيارات^</p^>
echo         ^</div^>
echo         
echo         ^<div class="icon-display"^>
echo             ^<h2^>🎨 أيقونة البرنامج^</h2^>
echo             ^<div class="icon-sizes"^>
echo                 ^<div class="icon-size"^>
echo                     ^<img src="app-icon.svg" width="64" height="64" alt="أيقونة 64x64"^>
echo                     ^<p^>64x64^</p^>
echo                 ^</div^>
echo                 ^<div class="icon-size"^>
echo                     ^<img src="app-icon.svg" width="128" height="128" alt="أيقونة 128x128"^>
echo                     ^<p^>128x128^</p^>
echo                 ^</div^>
echo                 ^<div class="icon-size"^>
echo                     ^<img src="app-icon.svg" width="256" height="256" alt="أيقونة 256x256"^>
echo                     ^<p^>256x256^</p^>
echo                 ^</div^>
echo             ^</div^>
echo         ^</div^>
echo         
echo         ^<div class="developer-info"^>
echo             ^<h2 style="color: #1e3a8a;"^>👨‍💻 معلومات المطور^</h2^>
echo             ^<p^>^<strong^>الاسم:^</strong^> Amr Ali Elawamy^</p^>
echo             ^<p^>^<strong^>الهاتف:^</strong^> 01285626623^</p^>
echo             ^<p^>^<strong^>البريد الإلكتروني:^</strong^> <EMAIL>^</p^>
echo             ^<p^>^<strong^>تاريخ التطوير:^</strong^> ديسمبر 2024^</p^>
echo             ^<p^>^<strong^>الإصدار:^</strong^> 1.0.0^</p^>
echo         ^</div^>
echo         
echo         ^<div class="features"^>
echo             ^<div class="feature"^>
echo                 ^<h3^>📦 إدارة المخزون^</h3^>
echo                 ^<p^>إدارة شاملة للسيارات مع نظام ضمان سلامة البيانات المالية^</p^>
echo             ^</div^>
echo             ^<div class="feature"^>
echo                 ^<h3^>💰 نظام المبيعات^</h3^>
echo                 ^<p^>بيع نقدي وبالتقسيط مع إدارة الأقساط المتقدمة^</p^>
echo             ^</div^>
echo             ^<div class="feature"^>
echo                 ^<h3^>👥 إدارة العملاء^</h3^>
echo                 ^<p^>قاعدة بيانات شاملة للعملاء مع كشوف حساب مفصلة^</p^>
echo             ^</div^>
echo             ^<div class="feature"^>
echo                 ^<h3^>📊 التقارير^</h3^>
echo                 ^<p^>تقارير وإحصائيات تفصيلية مع طباعة وتصدير متقدم^</p^>
echo             ^</div^>
echo             ^<div class="feature"^>
echo                 ^<h3^>🔒 الأمان^</h3^>
echo                 ^<p^>نظام صلاحيات متكامل مع تشفير البيانات الحساسة^</p^>
echo             ^</div^>
echo             ^<div class="feature"^>
echo                 ^<h3^>🔑 التفعيل^</h3^>
echo                 ^<p^>نظام تفعيل متقدم مع نسخة تجريبية مجانية 30 يوم^</p^>
echo             ^</div^>
echo         ^</div^>
echo         
echo         ^<div style="text-align: center; margin-top: 30px; padding: 20px; background: #1e3a8a; color: white; border-radius: 10px;"^>
echo             ^<h3^>جميع الحقوق محفوظة © 2024 Amr Ali Elawamy^</h3^>
echo             ^<p^>للتواصل: 01285626623 | <EMAIL>^</p^>
echo         ^</div^>
echo     ^</div^>
echo ^</body^>
echo ^</html^>
) > "عرض_الأيقونة.html"

echo ✅ تم إنشاء ملف عرض الأيقونة

echo.
echo 📋 تحديث ملفات المشروع بحقوق المطور...

REM تحديث ملف المشروع
powershell -Command "
$projectFile = 'CarDealershipManagement.csproj'
if (Test-Path $projectFile) {
    $content = Get-Content $projectFile -Raw
    $content = $content -replace '<AssemblyCompany>.*</AssemblyCompany>', '<AssemblyCompany>Amr Ali Elawamy</AssemblyCompany>'
    $content = $content -replace '<AssemblyCopyright>.*</AssemblyCopyright>', '<AssemblyCopyright>Copyright © 2024 Amr Ali Elawamy</AssemblyCopyright>'
    $content = $content -replace '<AssemblyProduct>.*</AssemblyProduct>', '<AssemblyProduct>Car Dealership Management System</AssemblyProduct>'
    $content = $content -replace '<AssemblyTitle>.*</AssemblyTitle>', '<AssemblyTitle>برنامج إدارة معرض السيارات</AssemblyTitle>'
    $content = $content -replace '<AssemblyDescription>.*</AssemblyDescription>', '<AssemblyDescription>نظام شامل لإدارة معارض السيارات - تطوير: Amr Ali Elawamy</AssemblyDescription>'
    Set-Content $projectFile $content -Encoding UTF8
    Write-Host '✅ تم تحديث ملف المشروع' -ForegroundColor Green
}
"

echo ✅ تم تحديث ملف المشروع

echo.
echo 📊 ملخص ما تم إنشاؤه:
echo.
echo ✅ معلومات_المطور.txt - ملف معلومات المطور الكاملة
echo ✅ app-icon.svg - أيقونة البرنامج بصيغة SVG
echo ✅ عرض_الأيقونة.html - عرض تفاعلي للأيقونة
echo ✅ تحديث ملف المشروع بحقوق المطور
echo.

echo 🎯 الخطوات التالية:
echo.
echo 1. افتح ملف "عرض_الأيقونة.html" لرؤية الأيقونة
echo 2. استخدم أداة تحويل SVG إلى ICO لإنشاء ملف .ico
echo 3. أضف الأيقونة إلى ملف المشروع
echo 4. أعد بناء البرنامج
echo.

echo 💡 مواقع مفيدة لتحويل SVG إلى ICO:
echo    • https://convertio.co/svg-ico/
echo    • https://cloudconvert.com/svg-to-ico
echo    • https://www.icoconverter.com/
echo.

echo هل تريد فتح ملف عرض الأيقونة؟ (Y/N)
set /p "OPEN_PREVIEW="
if /i "%OPEN_PREVIEW%"=="Y" (
    start "" "عرض_الأيقونة.html"
)

echo.
echo 🎉 تم إنشاء الأيقونة وحقوق المطور بنجاح!
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
