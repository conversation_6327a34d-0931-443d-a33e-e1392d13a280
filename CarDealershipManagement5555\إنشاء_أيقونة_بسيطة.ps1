# إنشاء أيقونة بسيطة للبرنامج
# المطور: <PERSON><PERSON> - 01285626623 - <EMAIL>

Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

try {
    Write-Host "🎨 إنشاء أيقونة برنامج إدارة معرض السيارات..." -ForegroundColor Cyan
    Write-Host "👨‍💻 المطور: <PERSON><PERSON> - 📞 01285626623" -ForegroundColor Green
    
    # إنشاء bitmap بحجم 256x256
    $bitmap = New-Object System.Drawing.Bitmap(256, 256)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # خلفية دائرية زرقاء
    $blueBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(30, 58, 138))
    $graphics.FillEllipse($blueBrush, 10, 10, 236, 236)
    
    # حدود الدائرة
    $borderPen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(59, 130, 246), 4)
    $graphics.DrawEllipse($borderPen, 10, 10, 236, 236)
    
    # جسم السيارة الرئيسي
    $carBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(59, 130, 246))
    $graphics.FillRectangle($carBrush, 68, 108, 120, 40)
    
    # مقدمة السيارة
    $graphics.FillRectangle($carBrush, 58, 118, 20, 20)
    
    # مؤخرة السيارة  
    $graphics.FillRectangle($carBrush, 178, 118, 20, 20)
    
    # نوافذ السيارة
    $windowBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(226, 232, 240))
    $graphics.FillRectangle($windowBrush, 78, 113, 25, 15)
    $graphics.FillRectangle($windowBrush, 108, 113, 25, 15)
    $graphics.FillRectangle($windowBrush, 138, 113, 25, 15)
    
    # العجلات
    $wheelBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(55, 65, 81))
    $graphics.FillEllipse($wheelBrush, 88, 143, 24, 24)
    $graphics.FillEllipse($wheelBrush, 144, 143, 24, 24)
    
    # مراكز العجلات
    $centerBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(156, 163, 175))
    $graphics.FillEllipse($centerBrush, 94, 149, 12, 12)
    $graphics.FillEllipse($centerBrush, 150, 149, 12, 12)
    
    # المصابيح الأمامية
    $lightBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(251, 191, 36))
    $graphics.FillEllipse($lightBrush, 53, 123, 8, 8)
    $graphics.FillEllipse($lightBrush, 53, 133, 8, 8)
    
    # المصابيح الخلفية
    $redLightBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(239, 68, 68))
    $graphics.FillEllipse($redLightBrush, 195, 123, 8, 8)
    $graphics.FillEllipse($redLightBrush, 195, 133, 8, 8)
    
    # أيقونة الإدارة (أعلى يمين)
    $manageBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(5, 150, 105))
    $graphics.FillRectangle($manageBrush, 165, 70, 30, 20)
    
    # خطوط الإدارة
    $whitePen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 2)
    $graphics.DrawLine($whitePen, 168, 75, 192, 75)
    $graphics.DrawLine($whitePen, 168, 80, 192, 80)
    $graphics.DrawLine($whitePen, 168, 85, 192, 85)
    
    # أيقونة المبيعات (أسفل يسار)
    $salesBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(220, 38, 38))
    $graphics.FillEllipse($salesBrush, 65, 165, 30, 30)
    
    # رمز الدولار
    $font = New-Object System.Drawing.Font('Arial', 18, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $graphics.DrawString('$', $font, $textBrush, 74, 172)
    
    # التاج (أعلى)
    $crownBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(251, 191, 36))
    $crownPoints = @(
        [System.Drawing.Point]::new(113, 50),
        [System.Drawing.Point]::new(118, 42),
        [System.Drawing.Point]::new(123, 50),
        [System.Drawing.Point]::new(128, 40),
        [System.Drawing.Point]::new(133, 50),
        [System.Drawing.Point]::new(138, 42),
        [System.Drawing.Point]::new(143, 50),
        [System.Drawing.Point]::new(138, 55),
        [System.Drawing.Point]::new(118, 55)
    )
    $graphics.FillPolygon($crownBrush, $crownPoints)
    
    # النص الرئيسي
    $titleFont = New-Object System.Drawing.Font('Arial', 14, [System.Drawing.FontStyle]::Bold)
    $graphics.DrawString('Car Management', $titleFont, $textBrush, 85, 200)
    
    # اسم المطور
    $devFont = New-Object System.Drawing.Font('Arial', 10)
    $graphics.DrawString('by Amr Ali Elawamy', $devFont, $textBrush, 90, 220)
    
    # رقم الهاتف
    $phoneFont = New-Object System.Drawing.Font('Arial', 8)
    $graphics.DrawString('01285626623', $phoneFont, $textBrush, 105, 235)
    
    # حفظ الأيقونة
    $bitmap.Save('app-icon.png', [System.Drawing.Imaging.ImageFormat]::Png)
    Write-Host "✅ تم إنشاء app-icon.png بنجاح" -ForegroundColor Green
    
    # محاولة تحويل إلى ICO
    try {
        # إنشاء أيقونة ICO
        $icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())
        $fileStream = New-Object System.IO.FileStream('app-icon.ico', [System.IO.FileMode]::Create)
        $icon.Save($fileStream)
        $fileStream.Close()
        Write-Host "✅ تم إنشاء app-icon.ico بنجاح" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ لم يتم إنشاء ملف ICO، لكن PNG متاح" -ForegroundColor Yellow
    }
    
    # تنظيف الموارد
    $graphics.Dispose()
    $bitmap.Dispose()
    $blueBrush.Dispose()
    $borderPen.Dispose()
    $carBrush.Dispose()
    $windowBrush.Dispose()
    $wheelBrush.Dispose()
    $centerBrush.Dispose()
    $lightBrush.Dispose()
    $redLightBrush.Dispose()
    $manageBrush.Dispose()
    $whitePen.Dispose()
    $salesBrush.Dispose()
    $font.Dispose()
    $textBrush.Dispose()
    $titleFont.Dispose()
    $devFont.Dispose()
    $phoneFont.Dispose()
    $crownBrush.Dispose()
    
    Write-Host "🎉 تم إنشاء الأيقونة بنجاح!" -ForegroundColor Cyan
    Write-Host "📁 الملفات المنشأة:" -ForegroundColor White
    Write-Host "   • app-icon.png" -ForegroundColor Gray
    if (Test-Path 'app-icon.ico') {
        Write-Host "   • app-icon.ico" -ForegroundColor Gray
    }
    
} catch {
    Write-Host "❌ خطأ في إنشاء الأيقونة: $($_.Exception.Message)" -ForegroundColor Red
}
