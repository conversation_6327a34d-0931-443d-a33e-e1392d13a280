@echo off
chcp 65001 >nul
title تطبيق الأيقونة الاحترافية الجديدة - Amr Ali Elawamy

echo.
echo ========================================
echo   🎨 تطبيق الأيقونة الاحترافية الجديدة
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo 🎯 سيتم استبدال الأيقونات القديمة بالأيقونة الاحترافية الجديدة
echo.

echo 🔍 التحقق من وجود الأيقونات الجديدة...

if not exist "professional-car-icon.ico" (
    echo ❌ الأيقونة الجديدة غير موجودة: professional-car-icon.ico
    echo يرجى تشغيل "إنشاء_أيقونة_احترافية_جديدة.ps1" أولاً
    pause
    exit /b 1
)

if not exist "professional-car-icon.png" (
    echo ❌ الأيقونة الجديدة غير موجودة: professional-car-icon.png
    echo يرجى تشغيل "إنشاء_أيقونة_احترافية_جديدة.ps1" أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على الأيقونات الجديدة

echo.
echo 📋 استبدال الأيقونات في المجلد الرئيسي...

REM نسخ الأيقونات الجديدة مع الأسماء القديمة
copy "professional-car-icon.ico" "app-icon.ico" >nul
copy "professional-car-icon.png" "app-icon.png" >nul
copy "professional-car-icon.svg" "app-icon.svg" >nul

echo ✅ تم استبدال الأيقونات في المجلد الرئيسي

echo.
echo 📁 استبدال الأيقونات في مجلد Debug...

set "DEBUG_DIR=bin\Debug\net8.0-windows"
if exist "%DEBUG_DIR%" (
    copy "professional-car-icon.ico" "%DEBUG_DIR%\app-icon.ico" >nul
    copy "professional-car-icon.png" "%DEBUG_DIR%\app-icon.png" >nul
    copy "professional-car-icon.svg" "%DEBUG_DIR%\app-icon.svg" >nul
    echo ✅ تم استبدال الأيقونات في مجلد Debug
) else (
    echo ⚠️ مجلد Debug غير موجود
)

echo.
echo 📁 استبدال الأيقونات في مجلد Release...

set "RELEASE_DIR=bin\Release\net8.0-windows"
if exist "%RELEASE_DIR%" (
    copy "professional-car-icon.ico" "%RELEASE_DIR%\app-icon.ico" >nul
    copy "professional-car-icon.png" "%RELEASE_DIR%\app-icon.png" >nul
    copy "professional-car-icon.svg" "%RELEASE_DIR%\app-icon.svg" >nul
    echo ✅ تم استبدال الأيقونات في مجلد Release
) else (
    echo ⚠️ مجلد Release غير موجود
)

echo.
echo 📁 استبدال الأيقونات في النسخة المحدثة...

set "DEBUG_COPY_DIR=CarDealership_Debug_Copy"
if exist "%DEBUG_COPY_DIR%" (
    copy "professional-car-icon.ico" "%DEBUG_COPY_DIR%\app-icon.ico" >nul
    copy "professional-car-icon.png" "%DEBUG_COPY_DIR%\app-icon.png" >nul
    copy "professional-car-icon.svg" "%DEBUG_COPY_DIR%\app-icon.svg" >nul
    echo ✅ تم استبدال الأيقونات في النسخة المحدثة
) else (
    echo ⚠️ النسخة المحدثة غير موجودة
)

echo.
echo 📁 استبدال الأيقونات في المجلد المستقل...

set "STANDALONE_DIR=CarDealership_Standalone"
if exist "%STANDALONE_DIR%" (
    copy "professional-car-icon.ico" "%STANDALONE_DIR%\app-icon.ico" >nul
    copy "professional-car-icon.png" "%STANDALONE_DIR%\app-icon.png" >nul
    copy "professional-car-icon.svg" "%STANDALONE_DIR%\app-icon.svg" >nul
    echo ✅ تم استبدال الأيقونات في المجلد المستقل
) else (
    echo ⚠️ المجلد المستقل غير موجود
)

echo.
echo 📁 استبدال الأيقونات في مجلد التثبيت...

set "INSTALLER_DIR=CarDealership_Installer"
if exist "%INSTALLER_DIR%" (
    copy "professional-car-icon.ico" "%INSTALLER_DIR%\app-icon.ico" >nul
    copy "professional-car-icon.png" "%INSTALLER_DIR%\app-icon.png" >nul
    copy "professional-car-icon.svg" "%INSTALLER_DIR%\app-icon.svg" >nul
    echo ✅ تم استبدال الأيقونات في مجلد التثبيت
) else (
    echo ⚠️ مجلد التثبيت غير موجود
)

echo.
echo 🔧 تحديث ملف المشروع لاستخدام الأيقونة الجديدة...

REM تحديث ملف المشروع
powershell -Command "
$projectFile = 'CarDealershipManagement.csproj'
if (Test-Path $projectFile) {
    $content = Get-Content $projectFile -Raw
    if ($content -match '<ApplicationIcon>.*</ApplicationIcon>') {
        $content = $content -replace '<ApplicationIcon>.*</ApplicationIcon>', '<ApplicationIcon>app-icon.ico</ApplicationIcon>'
    } else {
        $content = $content -replace '(<PropertyGroup[^>]*>)', '$1`n    <ApplicationIcon>app-icon.ico</ApplicationIcon>'
    }
    Set-Content $projectFile $content -Encoding UTF8
    Write-Host '✅ تم تحديث ملف المشروع' -ForegroundColor Green
} else {
    Write-Host '⚠️ ملف المشروع غير موجود' -ForegroundColor Yellow
}
"

echo.
echo 🏗️ إعادة بناء البرنامج مع الأيقونة الجديدة...

dotnet build --configuration Debug >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء البرنامج بنجاح مع الأيقونة الجديدة
) else (
    echo ⚠️ حدث خطأ في بناء البرنامج، لكن الأيقونات تم استبدالها
)

echo.
echo 📊 ملخص العملية:
echo.

set "SUCCESS_COUNT=0"

if exist "app-icon.ico" (
    echo ✅ المجلد الرئيسي - تم الاستبدال
    set /a SUCCESS_COUNT+=1
) else (
    echo ❌ المجلد الرئيسي - فشل الاستبدال
)

if exist "%DEBUG_DIR%\app-icon.ico" (
    echo ✅ مجلد Debug - تم الاستبدال
    set /a SUCCESS_COUNT+=1
) else (
    echo ⚠️ مجلد Debug - غير موجود أو فشل الاستبدال
)

if exist "%RELEASE_DIR%\app-icon.ico" (
    echo ✅ مجلد Release - تم الاستبدال
    set /a SUCCESS_COUNT+=1
) else (
    echo ⚠️ مجلد Release - غير موجود أو فشل الاستبدال
)

if exist "%DEBUG_COPY_DIR%\app-icon.ico" (
    echo ✅ النسخة المحدثة - تم الاستبدال
    set /a SUCCESS_COUNT+=1
) else (
    echo ⚠️ النسخة المحدثة - غير موجودة أو فشل الاستبدال
)

if exist "%STANDALONE_DIR%\app-icon.ico" (
    echo ✅ المجلد المستقل - تم الاستبدال
    set /a SUCCESS_COUNT+=1
) else (
    echo ⚠️ المجلد المستقل - غير موجود أو فشل الاستبدال
)

if exist "%INSTALLER_DIR%\app-icon.ico" (
    echo ✅ مجلد التثبيت - تم الاستبدال
    set /a SUCCESS_COUNT+=1
) else (
    echo ⚠️ مجلد التثبيت - غير موجود أو فشل الاستبدال
)

echo.
echo 📈 النتائج: تم الاستبدال في %SUCCESS_COUNT% مجلدات

echo.
echo 🎨 مميزات الأيقونة الجديدة:
echo    ✅ تصميم احترافي متدرج بألوان زرقاء أنيقة
echo    ✅ سيارة مفصلة مع نوافذ وعجلات ومصابيح
echo    ✅ رموز الإدارة (ترس ذهبي) والمبيعات (رمز $)
echo    ✅ تاج ذهبي في الأعلى يرمز للتميز والجودة
echo    ✅ نصوص "CAR MGMT" واضحة ومقروءة
echo    ✅ معلومات المطور في الأسفل (Amr Ali - 01285626623)
echo    ✅ حدود ذهبية أنيقة حول الأيقونة
echo    ✅ تأثيرات بصرية احترافية ومتناسقة
echo.

echo 💡 الخطوات التالية:
echo    1. اختبر البرنامج للتأكد من ظهور الأيقونة الجديدة
echo    2. أعد إنشاء ملف التثبيت إذا لزم الأمر
echo    3. تحقق من ظهور الأيقونة في شريط المهام
echo    4. تأكد من ظهور الأيقونة في اختصارات سطح المكتب
echo.

echo 🔄 لإعادة إنشاء ملف التثبيت مع الأيقونة الجديدة:
echo    شغل: "إنشاء_حزمة_التثبيت_النهائية.bat"
echo.

echo هل تريد تشغيل البرنامج لاختبار الأيقونة الجديدة؟ (Y/N)
set /p "TEST_PROGRAM="
if /i "%TEST_PROGRAM%"=="Y" (
    echo.
    echo 🚀 تشغيل البرنامج مع الأيقونة الجديدة...
    
    if exist "%DEBUG_COPY_DIR%\CarDealershipManagement.exe" (
        start "" "%DEBUG_COPY_DIR%\CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج من النسخة المحدثة
    ) else if exist "%DEBUG_DIR%\CarDealershipManagement.exe" (
        start "" "%DEBUG_DIR%\CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج من مجلد Debug
    ) else (
        echo ❌ لم يتم العثور على البرنامج
    )
)

echo.
echo 🎉 تم تطبيق الأيقونة الاحترافية الجديدة بنجاح!
echo.

echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
