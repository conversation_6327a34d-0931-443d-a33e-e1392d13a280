using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
public partial class InstallationActivationForm : Form
{
    private DataGridView dgvInstallations;
    private Button btnActivateNew;
    private Button btnDeactivate;
    private Button btnViewDetails;
    private Button btnRefresh;
    private Button btnGenerateCode;
    private TextBox txtDeviceInfo;
    private TextBox txtNotes;
    private Label lblCurrentDevice;
    private Label lblCurrentStatus;
    private Panel pnlCurrentDevice;
    private Panel pnlInstallations;

    private InstallationService installationService;
    private User? currentUser;

    public InstallationActivationForm(User? currentUser = null)
    {
        this.currentUser = currentUser;

        var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                                               .UseSqlite("Data Source=CarDealership.db").Options);
        this.installationService = new InstallationService(context);

        InitializeComponent();
        LoadData();
        LoadCurrentDeviceInfo();
    }

    private void InitializeComponent()
    {
        this.Text = "إدارة تفعيل التثبيت - Installation Activation Management";
        this.Size = new Size(1000, 700);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(240, 248, 255);
        this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

        // Current Device Panel
        pnlCurrentDevice = new Panel
        {
            Location = new Point(20, 20),
            Size = new Size(940, 100),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblCurrentDeviceTitle = new Label
        {
            Text = "معلومات الجهاز الحالي:",
            Location = new Point(10, 10),
            Size = new Size(200, 23),
            Font = new Font("Segoe UI", 10F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204)
        };

        lblCurrentDevice = new Label
        {
            Text = "معرف الجهاز: جاري التحميل...",
            Location = new Point(10, 35),
            Size = new Size(400, 23),
            Font = new Font("Segoe UI", 9F)
        };

        lblCurrentStatus = new Label
        {
            Text = "الحالة: جاري التحميل...",
            Location = new Point(10, 60),
            Size = new Size(400, 23),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };

        btnGenerateCode = new Button
        {
            Text = "إنشاء رمز تفعيل",
            Location = new Point(750, 30),
            Size = new Size(120, 35),
            BackColor = Color.FromArgb(40, 167, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnGenerateCode.FlatAppearance.BorderSize = 0;
        btnGenerateCode.Click += BtnGenerateCode_Click;

        pnlCurrentDevice.Controls.AddRange(new Control[]
        {
            lblCurrentDeviceTitle, lblCurrentDevice, lblCurrentStatus, btnGenerateCode
        });

        // Installations Panel
        pnlInstallations = new Panel
        {
            Location = new Point(20, 140),
            Size = new Size(940, 520),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblInstallationsTitle = new Label
        {
            Text = "التثبيتات المفعلة:",
            Location = new Point(10, 10),
            Size = new Size(200, 23),
            Font = new Font("Segoe UI", 10F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204)
        };

        // Action buttons
        btnActivateNew = new Button
        {
            Text = "تفعيل جديد",
            Location = new Point(10, 40),
            Size = new Size(120, 35),
            BackColor = Color.FromArgb(40, 167, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnActivateNew.FlatAppearance.BorderSize = 0;
        btnActivateNew.Click += BtnActivateNew_Click;

        btnDeactivate = new Button
        {
            Text = "إلغاء التفعيل",
            Location = new Point(140, 40),
            Size = new Size(120, 35),
            BackColor = Color.FromArgb(220, 53, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnDeactivate.FlatAppearance.BorderSize = 0;
        btnDeactivate.Click += BtnDeactivate_Click;

        btnViewDetails = new Button
        {
            Text = "عرض التفاصيل",
            Location = new Point(270, 40),
            Size = new Size(120, 35),
            BackColor = Color.FromArgb(0, 123, 255),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnViewDetails.FlatAppearance.BorderSize = 0;
        btnViewDetails.Click += BtnViewDetails_Click;

        btnRefresh = new Button
        {
            Text = "تحديث",
            Location = new Point(400, 40),
            Size = new Size(120, 35),
            BackColor = Color.FromArgb(108, 117, 125),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnRefresh.FlatAppearance.BorderSize = 0;
        btnRefresh.Click += (s, e) => LoadData();

        // DataGridView
        dgvInstallations = new DataGridView
        {
            Location = new Point(10, 90),
            Size = new Size(920, 420),
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            ReadOnly = true,
            AllowUserToAddRows = false,
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.Fixed3D,
            Font = new Font("Segoe UI", 9F),
            ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(0, 102, 204),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            },
            DefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.White,
                ForeColor = Color.Black,
                SelectionBackColor = Color.FromArgb(173, 216, 230),
                SelectionForeColor = Color.Black
            }
        };

        pnlInstallations.Controls.AddRange(new Control[]
        {
            lblInstallationsTitle, btnActivateNew, btnDeactivate,
            btnViewDetails, btnRefresh, dgvInstallations
        });

        this.Controls.AddRange(new Control[] { pnlCurrentDevice, pnlInstallations });
    }

    private async void LoadCurrentDeviceInfo()
    {
        try
        {
            var deviceId = installationService.GetDeviceId();
            var isActive = await installationService.IsInstallationAuthorizedAsync();

            lblCurrentDevice.Text = $"معرف الجهاز: {deviceId}";
            lblCurrentStatus.Text = $"الحالة: {(isActive ? "مفعل" : "غير مفعل")}";
            lblCurrentStatus.ForeColor = isActive ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69);
        }
        catch(Exception ex)
        {
            lblCurrentDevice.Text = $"خطأ في تحميل معلومات الجهاز: {ex.Message}";
            lblCurrentStatus.Text = "حالة غير معروفة";
        }
    }

    private async void LoadData()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var installations = await context.AuthorizedInstallations
                                .OrderByDescending(i => i.ActivationDate)
                                .ToListAsync();

            dgvInstallations.DataSource = installations.Select(i => new
            {
                معرف_التثبيت = i.InstallationId,
                معرف_الجهاز = i.DeviceId,
                رمز_التفعيل = i.ActivationCode,
                اسم_الجهاز = i.MachineName ?? "غير محدد",
                تاريخ_التفعيل = i.ActivationDate.ToString("yyyy-MM-dd HH:mm"),
                تاريخ_الانتهاء = i.ExpirationDate?.ToString("yyyy-MM-dd") ?? "بدون انتهاء",
                الحالة = i.IsActive ? "نشط" : "معطل",
                المفعل_بواسطة = i.ActivatedBy ?? "غير محدد"
            }).ToList();

            // Color rows based on status
            dgvInstallations.RowsAdded += (s, e) =>
            {
                for(int i = 0; i < dgvInstallations.Rows.Count; i++)
                {
                    var statusCell = dgvInstallations.Rows[i].Cells["الحالة"];
                    if(statusCell.Value?.ToString() == "معطل")
                    {
                        dgvInstallations.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(255, 240, 245);
                        dgvInstallations.Rows[i].DefaultCellStyle.ForeColor = Color.FromArgb(220, 53, 69);
                    }
                    else
                    {
                        dgvInstallations.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(240, 255, 245);
                        dgvInstallations.Rows[i].DefaultCellStyle.ForeColor = Color.FromArgb(40, 167, 69);
                    }
                }
            };
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void BtnGenerateCode_Click(object? sender, EventArgs e)
    {
        try
        {
            var deviceId = installationService.GetDeviceId();
            var activationCode = Guid.NewGuid().ToString("N")[..8].ToUpper();

            var result = MessageBox.Show(
                             $"رمز التفعيل الجديد:\n{activationCode}\n\nهل تريد نسخه إلى الحافظة؟",
                             "رمز التفعيل",
                             MessageBoxButtons.YesNo,
                             MessageBoxIcon.Information);

            if(result == DialogResult.Yes)
            {
                Clipboard.SetText(activationCode);
                MessageBox.Show("تم نسخ رمز التفعيل إلى الحافظة", "تم", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء رمز التفعيل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void BtnActivateNew_Click(object? sender, EventArgs e)
    {
        MessageBox.Show("تفعيل جديد متاح للمطورين فقط", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private async void BtnDeactivate_Click(object? sender, EventArgs e)
    {
        if(dgvInstallations.SelectedRows.Count > 0)
        {
            var installationId = Convert.ToInt32(dgvInstallations.SelectedRows[0].Cells["معرف_التثبيت"].Value);
            var deviceId = dgvInstallations.SelectedRows[0].Cells["معرف_الجهاز"].Value?.ToString();

            var result = MessageBox.Show(
                             $"هل أنت متأكد من إلغاء تفعيل التثبيت؟\nمعرف الجهاز: {deviceId}",
                             "تأكيد إلغاء التفعيل",
                             MessageBoxButtons.YesNo,
                             MessageBoxIcon.Warning);

            if(result == DialogResult.Yes)
            {
                try
                {
                    using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                            .UseSqlite("Data Source=CarDealership.db").Options);

                    var installation = await context.AuthorizedInstallations.FindAsync(installationId);
                    if(installation != null)
                    {
                        installation.IsActive = false;
                        installation.DeactivationDate = DateTime.UtcNow;
                        installation.DeactivatedBy = currentUser?.Username ?? "System";
                        installation.DeactivationReason = "تم إلغاء التفعيل بواسطة المستخدم";
                        installation.ModifiedDate = DateTime.UtcNow;
                        installation.UpdatedBy = currentUser?.Username ?? "System";

                        await context.SaveChangesAsync();

                        MessageBox.Show("تم إلغاء التفعيل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadData();
                        LoadCurrentDeviceInfo();
                    }
                }
                catch(Exception ex)
                {
                    MessageBox.Show($"خطأ في إلغاء التفعيل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار تثبيت لإلغاء تفعيله", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private async void BtnViewDetails_Click(object? sender, EventArgs e)
    {
        if(dgvInstallations.SelectedRows.Count > 0)
        {
            var installationId = Convert.ToInt32(dgvInstallations.SelectedRows[0].Cells["معرف_التثبيت"].Value);
            MessageBox.Show($"تفاصيل التثبيت: {installationId}", "تفاصيل التثبيت", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        else
        {
            MessageBox.Show("يرجى اختيار تثبيت لعرض تفاصيله", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }
}
}
