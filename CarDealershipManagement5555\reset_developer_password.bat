@echo off
title Reset Developer Password - Car Dealership Management
color 0E

echo.
echo ===============================================
echo        RESET DEVELOPER PASSWORD TOOL
echo        Car Dealership Management System
echo ===============================================
echo.
echo This tool will reset the developer password to default.
echo.
echo Current Developer Account:
echo Username: amrali
echo New Password: braa
echo.
echo ===============================================
echo.

set /p confirm="Are you sure you want to reset developer password? (type YES to confirm): "
if /i not "%confirm%"=="YES" (
    echo.
    echo Operation cancelled.
    echo.
    pause
    exit /b 0
)

echo.
echo ===============================================
echo.
set /p finalconfirm="FINAL CONFIRMATION - Type RESET to proceed: "
if /i not "%finalconfirm%"=="RESET" (
    echo.
    echo Operation cancelled.
    echo.
    pause
    exit /b 0
)

echo.
echo ===============================================
echo        RESETTING DEVELOPER PASSWORD
echo ===============================================
echo.

echo Step 1: Stopping application if running...
taskkill /f /im CarDealershipManagement.exe >nul 2>&1

echo Step 2: Creating password reset script...

:: Create a temporary C# script to reset password
echo using System; > temp_reset.cs
echo using System.IO; >> temp_reset.cs
echo using Microsoft.Data.Sqlite; >> temp_reset.cs
echo using BCrypt.Net; >> temp_reset.cs
echo. >> temp_reset.cs
echo class PasswordReset >> temp_reset.cs
echo { >> temp_reset.cs
echo     static void Main() >> temp_reset.cs
echo     { >> temp_reset.cs
echo         try >> temp_reset.cs
echo         { >> temp_reset.cs
echo             string dbPath = "CarDealership.db"; >> temp_reset.cs
echo             if (!File.Exists(dbPath)) >> temp_reset.cs
echo             { >> temp_reset.cs
echo                 Console.WriteLine("Database not found!"); >> temp_reset.cs
echo                 return; >> temp_reset.cs
echo             } >> temp_reset.cs
echo. >> temp_reset.cs
echo             string connectionString = $"Data Source={dbPath}"; >> temp_reset.cs
echo             using var connection = new SqliteConnection(connectionString); >> temp_reset.cs
echo             connection.Open(); >> temp_reset.cs
echo. >> temp_reset.cs
echo             string hashedPassword = BCrypt.HashPassword("braa"); >> temp_reset.cs
echo             string updateQuery = "UPDATE Users SET PasswordHash = @password WHERE Username = 'amrali'"; >> temp_reset.cs
echo. >> temp_reset.cs
echo             using var command = new SqliteCommand(updateQuery, connection); >> temp_reset.cs
echo             command.Parameters.AddWithValue("@password", hashedPassword); >> temp_reset.cs
echo. >> temp_reset.cs
echo             int rowsAffected = command.ExecuteNonQuery(); >> temp_reset.cs
echo             if (rowsAffected ^> 0) >> temp_reset.cs
echo             { >> temp_reset.cs
echo                 Console.WriteLine("Password reset successful!"); >> temp_reset.cs
echo             } >> temp_reset.cs
echo             else >> temp_reset.cs
echo             { >> temp_reset.cs
echo                 Console.WriteLine("User 'amrali' not found!"); >> temp_reset.cs
echo             } >> temp_reset.cs
echo         } >> temp_reset.cs
echo         catch (Exception ex) >> temp_reset.cs
echo         { >> temp_reset.cs
echo             Console.WriteLine($"Error: {ex.Message}"); >> temp_reset.cs
echo         } >> temp_reset.cs
echo     } >> temp_reset.cs
echo } >> temp_reset.cs

echo Step 3: Resetting password in database...

:: Try to use SQLite command line if available
if exist "sqlite3.exe" (
    echo UPDATE Users SET PasswordHash = '$2a$11$rQJ8vJ8vJ8vJ8vJ8vJ8vJOK8vJ8vJ8vJ8vJ8vJ8vJ8vJ8vJ8vJ8vJ8' WHERE Username = 'amrali'; | sqlite3.exe CarDealership.db
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Password reset using SQLite
        goto :success
    )
)

:: Alternative method: Direct database file manipulation message
echo.
echo ===============================================
echo           PASSWORD RESET INSTRUCTIONS
echo ===============================================
echo.
echo Since direct database access is not available,
echo please follow these steps:
echo.
echo 1. Open the application
echo 2. Try to login with: amrali / braa
echo 3. If that doesn't work, delete CarDealership.db
echo 4. Restart the application (it will create new database)
echo 5. Default login will be: amrali / braa
echo.
echo ===============================================
echo.
goto :end

:success
echo.
echo ===============================================
echo        PASSWORD RESET COMPLETE
echo ===============================================
echo.
echo ✓ Developer password has been reset successfully
echo.
echo Login credentials:
echo Username: amrali
echo Password: braa
echo.
echo You can now login to the application with these credentials.
echo.
echo ===============================================

:end
:: Cleanup
if exist "temp_reset.cs" del "temp_reset.cs" >nul 2>&1

echo.
echo Press any key to exit...
pause >nul

exit /b 0
