using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Management;
using Microsoft.Win32;

namespace CarDealershipManagement.Services
{
    public static class LicenseManager
    {
        private const string REGISTRY_KEY = @"SOFTWARE\CarDealershipManagement";
        private const string LICENSE_STATUS_KEY = "LicenseStatus";
        private const string DEVICE_ID_KEY = "DeviceId";
        private const string LICENSE_TYPE_KEY = "LicenseType";
        private const string EXPIRY_DATE_KEY = "ExpiryDate";
        private const string ACTIVATION_DATE_KEY = "ActivationDate";
        
        public enum LicenseType
        {
            Monthly,
            Yearly,
            Permanent
        }
        
        public enum LicenseStatus
        {
            NotActivated,
            Active,
            Expired
        }

        public static string GetDeviceId()
        {
            try
            {
                string deviceInfo = "";
                
                // الحصول على معرف المعالج
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        deviceInfo += obj["ProcessorId"]?.ToString() ?? "";
                        break;
                    }
                }

                // الحصول على الرقم التسلسلي للقرص الصلب
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_PhysicalMedia"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        deviceInfo += obj["SerialNumber"]?.ToString()?.Trim() ?? "";
                        break;
                    }
                }

                // الحصول على معرف اللوحة الأم
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        deviceInfo += obj["SerialNumber"]?.ToString()?.Trim() ?? "";
                        break;
                    }
                }

                // تشفير معرف الجهاز
                using (var sha256 = SHA256.Create())
                {
                    byte[] hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(deviceInfo));
                    return Convert.ToBase64String(hashedBytes).Substring(0, 16);
                }
            }
            catch
            {
                // في حالة فشل الحصول على معرف الجهاز، استخدم معرف بديل
                return Environment.MachineName.GetHashCode().ToString("X8");
            }
        }

        public static void InitializeInstallation()
        {
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    // تسجيل معرف الجهاز إذا لم يكن موجوداً
                    if (key.GetValue(DEVICE_ID_KEY) == null)
                    {
                        key.SetValue(DEVICE_ID_KEY, GetDeviceId());
                        key.SetValue(LICENSE_STATUS_KEY, LicenseStatus.NotActivated.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, false, "خطأ في تهيئة نظام الترخيص");
            }
        }

        public static LicenseCheckResult CheckLicense()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    if (key == null)
                    {
                        return new LicenseCheckResult
                        {
                            IsValid = false,
                            Message = "لم يتم تفعيل البرنامج بعد",
                            DaysRemaining = 0
                        };
                    }

                    var licenseStatus = key.GetValue(LICENSE_STATUS_KEY)?.ToString();
                    
                    if (licenseStatus == LicenseStatus.Active.ToString())
                    {
                        var licenseType = key.GetValue(LICENSE_TYPE_KEY)?.ToString();
                        var expiryDateValue = key.GetValue(EXPIRY_DATE_KEY);
                        
                        if (licenseType == LicenseType.Permanent.ToString())
                        {
                            return new LicenseCheckResult
                            {
                                IsValid = true,
                                Message = "البرنامج مُفعل بترخيص دائم",
                                DaysRemaining = -1
                            };
                        }
                        else if (expiryDateValue != null)
                        {
                            var expiryDate = DateTime.FromBinary((long)expiryDateValue);
                            var daysRemaining = (expiryDate - DateTime.Now).Days;
                            
                            if (daysRemaining > 0)
                            {
                                var typeText = licenseType == LicenseType.Monthly.ToString() ? "شهري" : "سنوي";
                                return new LicenseCheckResult
                                {
                                    IsValid = true,
                                    Message = $"البرنامج مُفعل بترخيص {typeText} - المتبقي: {daysRemaining} يوم",
                                    DaysRemaining = daysRemaining
                                };
                            }
                            else
                            {
                                // انتهى الترخيص
                                using (var writeKey = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY, true))
                                {
                                    writeKey?.SetValue(LICENSE_STATUS_KEY, LicenseStatus.Expired.ToString());
                                }
                                
                                return new LicenseCheckResult
                                {
                                    IsValid = false,
                                    Message = "انتهت صلاحية الترخيص! يرجى تجديد الاشتراك",
                                    DaysRemaining = 0
                                };
                            }
                        }
                    }

                    return new LicenseCheckResult
                    {
                        IsValid = false,
                        Message = "لم يتم تفعيل البرنامج بعد",
                        DaysRemaining = 0
                    };
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, false, "خطأ في التحقق من الترخيص");
                return new LicenseCheckResult
                {
                    IsValid = false,
                    Message = "خطأ في التحقق من الترخيص",
                    DaysRemaining = 0
                };
            }
        }

        public static bool ActivateLicense(LicenseType licenseType, string licenseKey)
        {
            try
            {
                var deviceId = GetDeviceId();
                
                using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    key.SetValue(LICENSE_STATUS_KEY, LicenseStatus.Active.ToString());
                    key.SetValue(LICENSE_TYPE_KEY, licenseType.ToString());
                    key.SetValue(ACTIVATION_DATE_KEY, DateTime.Now.ToBinary());
                    
                    // تحديد تاريخ انتهاء الصلاحية
                    if (licenseType == LicenseType.Monthly)
                    {
                        var expiryDate = DateTime.Now.AddMonths(1);
                        key.SetValue(EXPIRY_DATE_KEY, expiryDate.ToBinary());
                    }
                    else if (licenseType == LicenseType.Yearly)
                    {
                        var expiryDate = DateTime.Now.AddYears(1);
                        key.SetValue(EXPIRY_DATE_KEY, expiryDate.ToBinary());
                    }
                    // للترخيص الدائم لا نحتاج تاريخ انتهاء
                }

                return true;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, false, "خطأ في تفعيل الترخيص");
                return false;
            }
        }

        public static void CopyDeviceIdToClipboard()
        {
            try
            {
                var deviceId = GetDeviceId();
                System.Windows.Forms.Clipboard.SetText(deviceId);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, true, "خطأ في نسخ معرف الجهاز");
            }
        }

        public static string GetLicenseInfo()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    if (key == null) return "غير مُفعل";

                    var licenseStatus = key.GetValue(LICENSE_STATUS_KEY)?.ToString();
                    var licenseType = key.GetValue(LICENSE_TYPE_KEY)?.ToString();
                    var expiryDateValue = key.GetValue(EXPIRY_DATE_KEY);

                    if (licenseStatus == LicenseStatus.Active.ToString())
                    {
                        if (licenseType == LicenseType.Permanent.ToString())
                        {
                            return "ترخيص دائم";
                        }
                        else if (expiryDateValue != null)
                        {
                            var expiryDate = DateTime.FromBinary((long)expiryDateValue);
                            var typeText = licenseType == LicenseType.Monthly.ToString() ? "شهري" : "سنوي";
                            return $"ترخيص {typeText} - ينتهي في: {expiryDate:yyyy/MM/dd}";
                        }
                    }

                    return "غير مُفعل";
                }
            }
            catch
            {
                return "خطأ في قراءة معلومات الترخيص";
            }
        }
    }

    public class LicenseCheckResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; } = "";
        public int DaysRemaining { get; set; }
    }
}
