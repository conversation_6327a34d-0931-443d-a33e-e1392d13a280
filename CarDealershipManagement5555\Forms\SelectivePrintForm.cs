using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
public partial class SelectivePrintForm : Form
{
    private RadioButton rbPrintAll;
    private RadioButton rbPrintSelected;
    private ComboBox cmbCustomers;
    private ComboBox cmbSuppliers;
    private DateTimePicker dtpFromDate;
    private DateTimePicker dtpToDate;
    private CheckBox chkDateFilter;
    private Button btnPrint;
    private Button btnCancel;
    private Label lblTitle;
    private Panel pnlOptions;
    private Panel pnlButtons;

    public enum PrintType
    {
        Customers,
        Suppliers,
        Sales,
        Inventory,
        Installments
    }

    public PrintType CurrentPrintType
    {
        get;
        set;
    }
    public bool PrintAll
    {
        get;
        private set;
    }
    public int? SelectedCustomerId
    {
        get;
        private set;
    }
    public int? SelectedSupplierId
    {
        get;
        private set;
    }
    public DateTime? FromDate
    {
        get;
        private set;
    }
    public DateTime? ToDate
    {
        get;
        private set;
    }
    public bool UseDateFilter
    {
        get;
        private set;
    }

    public SelectivePrintForm(PrintType printType)
    {
        CurrentPrintType = printType;
        InitializeComponent();
        LoadData();
    }

    private void InitializeComponent()
    {
        this.Text = "خيارات الطباعة";
        this.Size = new Size(450, 400);
        this.StartPosition = FormStartPosition.CenterParent;
        this.FormBorderStyle = FormBorderStyle.FixedDialog;
        this.MaximizeBox = false;
        this.MinimizeBox = false;
        this.BackColor = Color.FromArgb(240, 248, 255);
        this.Font = new Font("Segoe UI", 9F);

        // Title
        lblTitle = new Label
        {
            Text = GetTitleText(),
            Location = new Point(20, 20),
            Size = new Size(400, 30),
            Font = new Font("Segoe UI", 14F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            TextAlign = ContentAlignment.MiddleCenter
        };

        // Options Panel
        pnlOptions = new Panel
        {
            Location = new Point(20, 60),
            Size = new Size(390, 250),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        // Print All Option
        rbPrintAll = new RadioButton
        {
            Text = "طباعة الكل",
            Location = new Point(20, 20),
            Size = new Size(150, 25),
            Font = new Font("Segoe UI", 10F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            Checked = true
        };

        // Print Selected Option
        rbPrintSelected = new RadioButton
        {
            Text = GetSelectedOptionText(),
            Location = new Point(20, 55),
            Size = new Size(200, 25),
            Font = new Font("Segoe UI", 10F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204)
        };

        // Customer/Supplier ComboBox
        if(CurrentPrintType == PrintType.Customers || CurrentPrintType == PrintType.Sales)
        {
            cmbCustomers = new ComboBox
            {
                Location = new Point(40, 85),
                Size = new Size(300, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F),
                Enabled = false
            };
            pnlOptions.Controls.Add(cmbCustomers);
        }

        if(CurrentPrintType == PrintType.Suppliers)
        {
            cmbSuppliers = new ComboBox
            {
                Location = new Point(40, 85),
                Size = new Size(300, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F),
                Enabled = false
            };
            pnlOptions.Controls.Add(cmbSuppliers);
        }

        // Date Filter
        chkDateFilter = new CheckBox
        {
            Text = "تصفية حسب التاريخ",
            Location = new Point(20, 125),
            Size = new Size(150, 25),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204)
        };

        var lblFromDate = new Label
        {
            Text = "من تاريخ:",
            Location = new Point(40, 155),
            Size = new Size(60, 25),
            Font = new Font("Segoe UI", 9F),
            TextAlign = ContentAlignment.MiddleLeft
        };

        dtpFromDate = new DateTimePicker
        {
            Location = new Point(110, 155),
            Size = new Size(120, 25),
            Font = new Font("Segoe UI", 9F),
            Enabled = false
        };

        var lblToDate = new Label
        {
            Text = "إلى تاريخ:",
            Location = new Point(240, 155),
            Size = new Size(60, 25),
            Font = new Font("Segoe UI", 9F),
            TextAlign = ContentAlignment.MiddleLeft
        };

        dtpToDate = new DateTimePicker
        {
            Location = new Point(310, 155),
            Size = new Size(120, 25),
            Font = new Font("Segoe UI", 9F),
            Enabled = false
        };

        // Add controls to options panel
        pnlOptions.Controls.AddRange(new Control[]
        {
            rbPrintAll, rbPrintSelected, chkDateFilter, lblFromDate, dtpFromDate, lblToDate, dtpToDate
        });

        // Buttons Panel
        pnlButtons = new Panel
        {
            Location = new Point(20, 320),
            Size = new Size(390, 50),
            BackColor = Color.Transparent
        };

        btnPrint = new Button
        {
            Text = "طباعة",
            Location = new Point(200, 10),
            Size = new Size(90, 35),
            BackColor = Color.FromArgb(40, 167, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnPrint.FlatAppearance.BorderSize = 0;

        btnCancel = new Button
        {
            Text = "إلغاء",
            Location = new Point(300, 10),
            Size = new Size(90, 35),
            BackColor = Color.FromArgb(220, 53, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnCancel.FlatAppearance.BorderSize = 0;

        pnlButtons.Controls.AddRange(new Control[] { btnPrint, btnCancel });

        // Add all panels to form
        this.Controls.AddRange(new Control[] { lblTitle, pnlOptions, pnlButtons });

        // Event handlers
        rbPrintAll.CheckedChanged += RbPrintAll_CheckedChanged;
        rbPrintSelected.CheckedChanged += RbPrintSelected_CheckedChanged;
        chkDateFilter.CheckedChanged += ChkDateFilter_CheckedChanged;
        btnPrint.Click += BtnPrint_Click;
        btnCancel.Click += BtnCancel_Click;
    }

    private string GetTitleText()
    {
        return CurrentPrintType switch
    {
        PrintType.Customers => "خيارات طباعة العملاء",
        PrintType.Suppliers => "خيارات طباعة الموردين",
        PrintType.Sales => "خيارات طباعة المبيعات",
        PrintType.Inventory => "خيارات طباعة المخزون",
        PrintType.Installments => "خيارات طباعة الأقساط",
        _ => "خيارات الطباعة"
    };
}

private string GetSelectedOptionText()
    {
        return CurrentPrintType switch
    {
        PrintType.Customers => "طباعة عميل محدد",
        PrintType.Suppliers => "طباعة مورد محدد",
        PrintType.Sales => "طباعة مبيعات عميل محدد",
        PrintType.Inventory => "طباعة عناصر محددة",
        PrintType.Installments => "طباعة أقساط عميل محدد",
        _ => "طباعة محدد"
    };
}

private void LoadData()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            if(cmbCustomers != null)
            {
                var customers = context.Customers
                                .Select(c => new
                {
                    Id = c.CustomerId, DisplayName = c.FullName
                })
                .ToList();

                cmbCustomers.DisplayMember = "DisplayName";
                cmbCustomers.ValueMember = "Id";
                cmbCustomers.DataSource = customers;
            }

            if(cmbSuppliers != null)
            {
                var suppliers = context.Suppliers
                                .Select(s => new
                {
                    Id = s.SupplierId, DisplayName = s.SupplierName
                })
                .ToList();

                cmbSuppliers.DisplayMember = "DisplayName";
                cmbSuppliers.ValueMember = "Id";
                cmbSuppliers.DataSource = suppliers;
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void RbPrintAll_CheckedChanged(object sender, EventArgs e)
    {
        if(rbPrintAll.Checked)
        {
            if(cmbCustomers != null)
            {
                cmbCustomers.Enabled = false;
            }
            if(cmbSuppliers != null)
            {
                cmbSuppliers.Enabled = false;
            }
        }
    }

    private void RbPrintSelected_CheckedChanged(object sender, EventArgs e)
    {
        if(rbPrintSelected.Checked)
        {
            if(cmbCustomers != null)
            {
                cmbCustomers.Enabled = true;
            }
            if(cmbSuppliers != null)
            {
                cmbSuppliers.Enabled = true;
            }
        }
    }

    private void ChkDateFilter_CheckedChanged(object sender, EventArgs e)
    {
        dtpFromDate.Enabled = chkDateFilter.Checked;
        dtpToDate.Enabled = chkDateFilter.Checked;
    }

    private void BtnPrint_Click(object sender, EventArgs e)
    {
        // Validate selection
        if(rbPrintSelected.Checked)
        {
            if(cmbCustomers != null && cmbCustomers.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار عميل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            if(cmbSuppliers != null && cmbSuppliers.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار مورد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
        }

        if(chkDateFilter.Checked && dtpFromDate.Value > dtpToDate.Value)
        {
            MessageBox.Show("تاريخ البداية يجب أن يكون أقل من تاريخ النهاية", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // Set properties
        PrintAll = rbPrintAll.Checked;
        SelectedCustomerId = cmbCustomers?.SelectedValue as int?;
        SelectedSupplierId = cmbSuppliers?.SelectedValue as int?;
        UseDateFilter = chkDateFilter.Checked;
        FromDate = chkDateFilter.Checked ? dtpFromDate.Value : null;
        ToDate = chkDateFilter.Checked ? dtpToDate.Value : null;

        this.DialogResult = DialogResult.OK;
        this.Close();
    }

    private void BtnCancel_Click(object sender, EventArgs e)
    {
        this.DialogResult = DialogResult.Cancel;
        this.Close();
    }
}
}
