using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class CustomerSelectionFormForStatement : Form
    {
        private DataGridView dgvCustomers = new DataGridView();
        private Button btnSelect = new Button();
        private Button btnCancel = new Button();
        private Label lblStatus = new Label();
        private TextBox txtSearch = new TextBox();

        public int SelectedCustomerId { get; private set; }

        public CustomerSelectionFormForStatement()
        {
            InitializeComponent();
            LoadCustomers();
        }

        private void InitializeComponent()
        {
            this.Text = "اختيار العميل لكشف الحساب - Select Customer for Statement";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(20, 520),
                Size = new Size(760, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Search TextBox
            var lblSearch = new Label
            {
                Text = "البحث:",
                Location = new Point(20, 20),
                Size = new Size(50, 25),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            txtSearch = new TextBox
            {
                Location = new Point(80, 18),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 9F)
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;

            // Customers DataGridView
            dgvCustomers = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(760, 400),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F),
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(0, 102, 204),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Padding = new Padding(5)
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.Black,
                    SelectionBackColor = Color.FromArgb(173, 216, 230),
                    SelectionForeColor = Color.Black,
                    Font = new Font("Segoe UI", 9F),
                    Padding = new Padding(5)
                }
            };

            dgvCustomers.DoubleClick += DgvCustomers_DoubleClick;

            // Select Button
            btnSelect = new Button
            {
                Text = "اختيار",
                Location = new Point(580, 470),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnSelect.FlatAppearance.BorderSize = 0;
            btnSelect.Click += BtnSelect_Click;

            // Cancel Button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(690, 470),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            this.Controls.AddRange(new Control[]
            {
                lblSearch, txtSearch, dgvCustomers, btnSelect, btnCancel, lblStatus
            });
        }

        private async void LoadCustomers()
        {
            lblStatus.Text = ""; // Clear previous status messages
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var customers = await context.Customers
                                .Include(c => c.Sales)
                                .Where(c => c.Sales.Any()) // Only customers with sales
                                .Select(c => new
                                {
                                    c.CustomerId,
                                    الاسم_الكامل = c.FullName,
                                    رقم_الهوية = c.IdNumber,
                                    الهاتف = c.PrimaryPhone,
                                    عدد_المبيعات = c.Sales.Count
                                })
                                .ToListAsync();

                dgvCustomers.DataSource = customers;

                if (!customers.Any())
                {
                    lblStatus.Text = "لا توجد عملاء لديهم مبيعات مسجلة.";
                }
            }, "تحميل قائمة العملاء");
        }

        private async void TxtSearch_TextChanged(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var searchTerm = txtSearch.Text.Trim();
                var customers = await context.Customers
                                .Include(c => c.Sales)
                                .Where(c => c.Sales.Any() && 
                                       (string.IsNullOrEmpty(searchTerm) ||
                                        c.FullName.Contains(searchTerm) ||
                                        c.IdNumber.Contains(searchTerm) ||
                                        c.PrimaryPhone.Contains(searchTerm)))
                                .Select(c => new
                                {
                                    c.CustomerId,
                                    الاسم_الكامل = c.FullName,
                                    رقم_الهوية = c.IdNumber,
                                    الهاتف = c.PrimaryPhone,
                                    عدد_المبيعات = c.Sales.Count
                                })
                                .ToListAsync();

                dgvCustomers.DataSource = customers;
            }, "البحث في العملاء");
        }

        private void BtnSelect_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvCustomers.SelectedRows[0];
                SelectedCustomerId = (int)selectedRow.Cells["CustomerId"].Value;
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                lblStatus.Text = "يرجى اختيار عميل من القائمة.";
            }
        }

        private void DgvCustomers_DoubleClick(object? sender, EventArgs e)
        {
            BtnSelect_Click(sender, e);
        }
    }
}

