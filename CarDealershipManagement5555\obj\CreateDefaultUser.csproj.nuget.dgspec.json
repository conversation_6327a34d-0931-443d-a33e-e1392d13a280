{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\CarDealershipManagement_updated\\CarDealershipManagement5555\\CreateDefaultUser.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\CarDealershipManagement_updated\\CarDealershipManagement5555\\CreateDefaultUser.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\CarDealershipManagement_updated\\CarDealershipManagement5555\\CreateDefaultUser.csproj", "projectName": "CreateDefaultUser", "projectPath": "C:\\Users\\<USER>\\Desktop\\CarDealershipManagement_updated\\CarDealershipManagement5555\\CreateDefaultUser.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\CarDealershipManagement_updated\\CarDealershipManagement5555\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.Data.Sqlite": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}