@echo off
chcp 65001 >nul
title تشغيل برنامج إدارة معرض السيارات - المكان الصحيح

echo.
echo ========================================
echo   📍 مكان البرنامج الصحيح
echo ========================================
echo.

echo 🎯 البرنامج موجود في المكان التالي:
echo.

set "CURRENT_DIR=%~dp0"
set "RELEASE_DIR=%CURRENT_DIR%bin\Release\net8.0-windows"
set "DEBUG_DIR=%CURRENT_DIR%bin\Debug\net8.0-windows"
set "EXE_FILE=CarDealershipManagement.exe"

echo 📁 المجلد الحالي: %CURRENT_DIR%
echo.

echo 🔍 البحث عن البرنامج...
echo.

REM التحقق من وجود البرنامج في مجلد Release
if exist "%RELEASE_DIR%\%EXE_FILE%" (
    echo ✅ تم العثور على البرنامج في مجلد Release:
    echo    📂 %RELEASE_DIR%\%EXE_FILE%
    echo.
    
    REM عرض معلومات الملف
    for %%A in ("%RELEASE_DIR%\%EXE_FILE%") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📊 معلومات الملف:
        echo    📏 الحجم: !FILE_SIZE_MB! ميجابايت
        echo    📅 تاريخ الإنشاء: %%~tA
    )
    echo.
    
    echo 🚀 تشغيل البرنامج من مجلد Release...
    echo.
    
    cd /d "%RELEASE_DIR%"
    start "" "%EXE_FILE%"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    
) else if exist "%DEBUG_DIR%\%EXE_FILE%" (
    echo ✅ تم العثور على البرنامج في مجلد Debug:
    echo    📂 %DEBUG_DIR%\%EXE_FILE%
    echo.
    
    REM عرض معلومات الملف
    for %%A in ("%DEBUG_DIR%\%EXE_FILE%") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📊 معلومات الملف:
        echo    📏 الحجم: !FILE_SIZE_MB! ميجابايت
        echo    📅 تاريخ الإنشاء: %%~tA
    )
    echo.
    
    echo 🚀 تشغيل البرنامج من مجلد Debug...
    echo.
    
    cd /d "%DEBUG_DIR%"
    start "" "%EXE_FILE%"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    
) else (
    echo ❌ لم يتم العثور على البرنامج في أي من المجلدات!
    echo.
    echo 🔧 يرجى بناء البرنامج أولاً باستخدام:
    echo    1. Visual Studio: Build → Build Solution
    echo    2. أو تشغيل: dotnet build --configuration Release
    echo.
    echo 📁 المجلدات المتوقعة:
    echo    • %RELEASE_DIR%
    echo    • %DEBUG_DIR%
    echo.
    goto :end
)

echo.
echo 🔑 بيانات الدخول للبرنامج:
echo.
echo 🔧 حساب المطور (صلاحيات كاملة):
echo    اسم المستخدم: developer
echo    كلمة المرور: dev123
echo.
echo 👔 حساب المدير:
echo    اسم المستخدم: admin
echo    كلمة المرور: 123
echo.
echo 🤝 حساب مندوب المبيعات:
echo    اسم المستخدم: user
echo    كلمة المرور: pass
echo.

echo 🆕 ميزات نظام التفعيل:
echo.
echo ✅ نسخة تجريبية مجانية (30 يوم):
echo    • تفعيل فوري بدون مفتاح ترخيص
echo    • جميع الميزات متاحة
echo    • مناسبة للتقييم والاختبار
echo.
echo ✅ تراخيص مدفوعة:
echo    • شهري: MONTH-XXXXXX-XXXXXX
echo    • ربع سنوي: QUARTER-XXXXXX-XXXXXX  
echo    • سنوي: YEAR-XXXXXX-XXXXXX
echo    • مدى الحياة: LIFE-XXXXXX-XXXXXX
echo.

echo 🎯 الميزات المتاحة:
echo.
echo 📦 إدارة المخزون:
echo    • إضافة وتعديل وحذف السيارات
echo    • نظام ضمان سلامة البيانات المالية عند الحذف
echo    • إدارة ملفات ومرفقات السيارات
echo.
echo 💰 نظام المبيعات:
echo    • بيع نقدي وبالتقسيط
echo    • إدارة الأقساط المتقدمة
echo    • تقارير مبيعات شاملة
echo.
echo 👥 إدارة المستخدمين:
echo    • نظام صلاحيات متكامل (64+ صلاحية)
echo    • أدوار متعددة (مطور، مدير، مندوب)
echo    • إدارة كلمات المرور والأمان
echo.
echo 📊 التقارير والإحصائيات:
echo    • تبويب الأقساط المحسن
echo    • طباعة وتصدير بـ 5 صيغ مختلفة
echo    • إحصائيات متقدمة ومؤشرات أداء
echo.

echo 🔍 إذا لم تجد البرنامج:
echo    • ابحث في شريط المهام السفلي
echo    • استخدم Alt+Tab للتنقل بين النوافذ
echo    • تحقق من أن البرنامج لم يفتح خلف النوافذ الأخرى
echo.

echo 💡 نصائح مهمة:
echo    • استخدم حساب المطور للوصول لجميع الميزات
echo    • جرب النسخة التجريبية أولاً
echo    • اختبر جميع الميزات الجديدة
echo    • راجع دليل المستخدم للاستفادة القصوى
echo.

:end
cd /d "%CURRENT_DIR%"

echo 📞 للدعم الفني أو المساعدة:
echo    📧 <EMAIL>
echo    🌐 www.cardealership.com
echo.

echo اضغط أي مفتاح للخروج...
pause >nul
