# إنشاء ملف تثبيت مباشر لبرنامج إدارة معرض السيارات
# المطور: Amr <PERSON> - 01285626623 - <EMAIL>

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   📦 إنشاء ملف التثبيت المباشر" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "👨‍💻 المطور: Amr Ali Elawamy" -ForegroundColor Green
Write-Host "📞 الهاتف: 01285626623" -ForegroundColor Green
Write-Host "📧 البريد: <EMAIL>" -ForegroundColor Green
Write-Host ""

$sourceDir = "CarDealership_Debug_Copy"
$setupDir = "CarDealership_Installer"
$zipFile = "CarDealershipManagement_Installer.zip"

Write-Host "🔍 التحقق من المتطلبات..." -ForegroundColor Yellow

if (-not (Test-Path "$sourceDir\CarDealershipManagement.exe")) {
    Write-Host "❌ البرنامج غير موجود في: $sourceDir" -ForegroundColor Red
    Write-Host "يرجى تشغيل 'إنشاء_نسخة_من_Debug.bat' أولاً" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ تم العثور على البرنامج" -ForegroundColor Green

Write-Host ""
Write-Host "📁 إنشاء مجلد التثبيت..." -ForegroundColor Yellow

if (Test-Path $setupDir) {
    Remove-Item $setupDir -Recurse -Force
}
New-Item -ItemType Directory -Path $setupDir | Out-Null

Write-Host "✅ تم إنشاء مجلد التثبيت" -ForegroundColor Green

Write-Host ""
Write-Host "📋 نسخ ملفات البرنامج..." -ForegroundColor Yellow

try {
    Copy-Item "$sourceDir\*" "$setupDir\" -Recurse -Force
    Write-Host "✅ تم نسخ ملفات البرنامج" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في نسخ ملفات البرنامج: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "📝 إنشاء ملف التثبيت التلقائي..." -ForegroundColor Yellow

# إنشاء ملف التثبيت
$installScript = @'
@echo off
chcp 65001 >nul
title تثبيت برنامج إدارة معرض السيارات - Amr Ali Elawamy

echo.
echo ========================================
echo    📦 تثبيت برنامج إدارة معرض السيارات
echo ========================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo 🎯 مرحباً بك في برنامج إدارة معرض السيارات
echo.
echo الميزات الرئيسية:
echo    • إدارة شاملة للمخزون والسيارات
echo    • نظام مبيعات متقدم مع الأقساط
echo    • إدارة العملاء والموردين
echo    • تقارير وإحصائيات تفصيلية
echo    • نظام صلاحيات متكامل (64+ صلاحية)
echo    • نظام ضمان سلامة البيانات المالية
echo.

set "INSTALL_DIR=%PROGRAMFILES%\Car Dealership Management"

echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo.
echo هل تريد المتابعة مع التثبيت؟ (Y/N)
set /p "CONTINUE="
if /i not "%CONTINUE%"=="Y" (
    echo تم إلغاء التثبيت
    pause
    exit /b 0
)

echo.
echo 📁 إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📋 نسخ ملفات البرنامج...
xcopy "%~dp0*" "%INSTALL_DIR%\" /E /I /Y /EXCLUDE:%~dp0install.bat >nul

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم نسخ الملفات بنجاح
) else (
    echo ❌ فشل في نسخ الملفات
    pause
    exit /b 1
)

echo 🔗 إنشاء اختصارات...

REM اختصار سطح المكتب
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\برنامج إدارة معرض السيارات.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CarDealershipManagement.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\app-icon.ico'; $Shortcut.Description = 'برنامج إدارة معرض السيارات - Amr Ali Elawamy'; $Shortcut.Save()"

REM اختصار قائمة البدء
set "START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%START_MENU%\برنامج إدارة معرض السيارات" mkdir "%START_MENU%\برنامج إدارة معرض السيارات"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\برنامج إدارة معرض السيارات\برنامج إدارة معرض السيارات.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CarDealershipManagement.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\app-icon.ico'; $Shortcut.Description = 'برنامج إدارة معرض السيارات - Amr Ali Elawamy'; $Shortcut.Save()"

echo ✅ تم إنشاء الاختصارات

echo 🎉 تم تثبيت البرنامج بنجاح!
echo.
echo 🔑 بيانات الدخول:
echo    المطور: amrali / braa (جميع الصلاحيات)
echo    المدير: admin / 123 (صلاحيات إدارية)
echo    المندوب: user / pass (صلاحيات أساسية)
echo.
echo 💡 يمكنك الآن:
echo    1. تشغيل البرنامج من سطح المكتب
echo    2. أو من قائمة البدء
echo    3. اختيار "نسخة تجريبية" للبدء فوراً
echo.
echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
set /p "RUN_NOW="
if /i "%RUN_NOW%"=="Y" (
    start "" "%INSTALL_DIR%\CarDealershipManagement.exe"
)

echo.
echo 👨‍💻 شكراً لاختيارك برنامج إدارة معرض السيارات!
echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.
pause
'@

Set-Content "$setupDir\install.bat" $installScript -Encoding UTF8

Write-Host "✅ تم إنشاء ملف التثبيت" -ForegroundColor Green

Write-Host ""
Write-Host "📝 إنشاء ملف README..." -ForegroundColor Yellow

$readmeContent = @"
برنامج إدارة معرض السيارات - ملف التثبيت
==========================================

👨‍💻 المطور: Amr Ali Elawamy
📞 الهاتف: 01285626623
📧 البريد الإلكتروني: <EMAIL>

🚀 طريقة التثبيت:
   1. اضغط مرتين على install.bat
   2. اتبع التعليمات على الشاشة
   3. سيتم إنشاء اختصارات تلقائياً

🔑 بيانات الدخول:
   المطور: amrali / braa (جميع الصلاحيات)
   المدير: admin / 123 (صلاحيات إدارية)
   المندوب: user / pass (صلاحيات أساسية)

🎯 الميزات:
   • إدارة شاملة للمخزون والسيارات
   • نظام مبيعات متقدم مع الأقساط
   • إدارة العملاء والموردين
   • تقارير وإحصائيات تفصيلية
   • نظام صلاحيات متكامل (64+ صلاحية)
   • نظام ضمان سلامة البيانات المالية

📞 للدعم الفني: 01285626623
📧 البريد الإلكتروني: <EMAIL>

🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
"@

Set-Content "$setupDir\README.txt" $readmeContent -Encoding UTF8

Write-Host "✅ تم إنشاء ملف README" -ForegroundColor Green

Write-Host ""
Write-Host "📦 إنشاء ملف ZIP للتوزيع..." -ForegroundColor Yellow

try {
    if (Test-Path $zipFile) {
        Remove-Item $zipFile -Force
    }
    
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::CreateFromDirectory($setupDir, $zipFile)
    
    $fileSize = (Get-Item $zipFile).Length / 1MB
    Write-Host "✅ تم إنشاء ملف ZIP بنجاح" -ForegroundColor Green
    Write-Host "📏 حجم الملف: $([math]::Round($fileSize, 1)) ميجابايت" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ فشل في إنشاء ملف ZIP: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "📊 ملخص العملية:" -ForegroundColor Cyan
Write-Host ""

if (Test-Path $zipFile) {
    Write-Host "✅ تم إنشاء ملف التثبيت بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📦 الملفات المنشأة:" -ForegroundColor White
    Write-Host "   📁 مجلد التثبيت: $setupDir" -ForegroundColor Gray
    Write-Host "   📄 ملف ZIP: $zipFile" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "🎯 محتويات ملف التثبيت:" -ForegroundColor White
    Write-Host "   ✅ install.bat - ملف التثبيت الرئيسي" -ForegroundColor Gray
    Write-Host "   ✅ جميع ملفات البرنامج والمكتبات" -ForegroundColor Gray
    Write-Host "   ✅ قاعدة البيانات والإعدادات" -ForegroundColor Gray
    Write-Host "   ✅ الأيقونات والموارد" -ForegroundColor Gray
    Write-Host "   ✅ README.txt - دليل التثبيت" -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "💡 للتوزيع:" -ForegroundColor White
    Write-Host "   1. أرسل ملف $zipFile للعملاء" -ForegroundColor Gray
    Write-Host "   2. اطلب منهم استخراج الملفات" -ForegroundColor Gray
    Write-Host "   3. تشغيل install.bat كمدير" -ForegroundColor Gray
    Write-Host ""
    
    $openFolder = Read-Host "هل تريد فتح مجلد التثبيت؟ (Y/N)"
    if ($openFolder -eq "Y" -or $openFolder -eq "y") {
        Start-Process $setupDir
    }
    
    $testInstall = Read-Host "هل تريد اختبار التثبيت؟ (Y/N)"
    if ($testInstall -eq "Y" -or $testInstall -eq "y") {
        Write-Host "🚀 تشغيل ملف التثبيت..." -ForegroundColor Yellow
        Start-Process "$setupDir\install.bat"
    }
    
} else {
    Write-Host "❌ فشل في إنشاء ملف التثبيت" -ForegroundColor Red
}

Write-Host ""
Write-Host "👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy" -ForegroundColor Green
Write-Host "📞 للتواصل: 01285626623" -ForegroundColor Green
Write-Host "📧 البريد: <EMAIL>" -ForegroundColor Green
Write-Host "🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy" -ForegroundColor Green

Write-Host ""
Read-Host "اضغط Enter للخروج"
