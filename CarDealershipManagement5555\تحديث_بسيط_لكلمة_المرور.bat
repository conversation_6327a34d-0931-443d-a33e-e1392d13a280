@echo off
chcp 65001 >nul
title تحديث كلمة مرور المطور - Amr Ali Elawamy

echo.
echo ========================================
echo   🔐 تحديث كلمة مرور المطور
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🔑 البيانات الجديدة:
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo.

echo 📝 تحديث ملفات التوثيق...

REM تحديث README في المجلد المستقل
if exist "CarDealership_Standalone\README.txt" (
    echo تحديث README...
    powershell -Command "
    try {
        $content = Get-Content 'CarDealership_Standalone\README.txt' -Raw -Encoding UTF8
        $content = $content -replace 'المطور: developer / dev123', 'المطور: amrali / braa'
        $content = $content -replace 'developer / dev123', 'amrali / braa'
        Set-Content 'CarDealership_Standalone\README.txt' $content -Encoding UTF8
        Write-Host '✅ تم تحديث README' -ForegroundColor Green
    } catch {
        Write-Host '⚠️ خطأ في تحديث README' -ForegroundColor Yellow
    }
    "
)

REM تحديث ملف تشغيل البرنامج في المجلد المستقل
if exist "CarDealership_Standalone\تشغيل_البرنامج.bat" (
    echo تحديث ملف التشغيل...
    powershell -Command "
    try {
        $content = Get-Content 'CarDealership_Standalone\تشغيل_البرنامج.bat' -Raw -Encoding UTF8
        $content = $content -replace 'المطور: developer / dev123', 'المطور: amrali / braa'
        $content = $content -replace 'developer / dev123', 'amrali / braa'
        Set-Content 'CarDealership_Standalone\تشغيل_البرنامج.bat' $content -Encoding UTF8
        Write-Host '✅ تم تحديث ملف التشغيل' -ForegroundColor Green
    } catch {
        Write-Host '⚠️ خطأ في تحديث ملف التشغيل' -ForegroundColor Yellow
    }
    "
)

echo.
echo 📋 إنشاء ملف بيانات الدخول المحدثة...

REM إنشاء ملف بيانات الدخول الجديدة
(
echo بيانات الدخول المحدثة - برنامج إدارة معرض السيارات
echo ========================================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo.
echo 🔑 بيانات الدخول المحدثة:
echo.
echo 🔧 حساب المطور ^(جميع الصلاحيات^):
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo    الصلاحيات: جميع الصلاحيات ^(64+ صلاحية^)
echo    الوصول: كامل لجميع أجزاء النظام
echo.
echo 👔 حساب المدير ^(صلاحيات إدارية^):
echo    اسم المستخدم: admin
echo    كلمة المرور: 123
echo    الصلاحيات: إدارية ^(بدون إعدادات النظام^)
echo    الوصول: إدارة المخزون والمبيعات والتقارير
echo.
echo 🤝 حساب مندوب المبيعات ^(صلاحيات أساسية^):
echo    اسم المستخدم: user
echo    كلمة المرور: pass
echo    الصلاحيات: أساسية ^(مبيعات وعملاء^)
echo    الوصول: المبيعات وإدارة العملاء فقط
echo.
echo 🆕 نظام التفعيل:
echo    • نسخة تجريبية مجانية ^(30 يوم^)
echo    • تراخيص شهرية وسنوية ومدى الحياة
echo    • حماية بمعرف الجهاز
echo    • تشفير ملفات الترخيص
echo.
echo 💡 نصائح مهمة:
echo    • استخدم حساب المطور للوصول لجميع الميزات
echo    • قم بتغيير كلمات المرور بعد أول تسجيل دخول
echo    • احتفظ بنسخة احتياطية من بيانات الدخول
echo    • لا تشارك بيانات حساب المطور مع الآخرين
echo.
echo 📞 للدعم الفني:
echo    الهاتف: 01285626623
echo    البريد الإلكتروني: <EMAIL>
echo.
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
echo تاريخ التحديث: %date% %time%
) > "بيانات_الدخول_المحدثة.txt"

echo ✅ تم إنشاء ملف بيانات الدخول المحدثة

echo.
echo 🔧 إعادة بناء البرنامج مع البيانات الجديدة...

dotnet clean >nul 2>&1
dotnet build --configuration Release >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء البرنامج بنجاح
    
    REM تحديث المجلد المستقل
    if exist "CarDealership_Standalone" (
        echo 📦 تحديث المجلد المستقل...
        if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
            copy "bin\Release\net8.0-windows\CarDealershipManagement.exe" "CarDealership_Standalone\" >nul 2>&1
            echo ✅ تم تحديث المجلد المستقل
        )
    )
) else (
    echo ⚠️ تحذير: قد تكون هناك أخطاء في البناء
)

echo.
echo 📊 ملخص التحديثات:
echo.

echo ✅ تم تحديث بيانات الدخول:
echo    🔧 المطور: amrali / braa ^(جميع الصلاحيات^)
echo    👔 المدير: admin / 123 ^(صلاحيات إدارية^)
echo    🤝 المندوب: user / pass ^(صلاحيات أساسية^)
echo.

echo ✅ الملفات المحدثة:
if exist "CarDealership_Standalone\README.txt" echo    • README في المجلد المستقل
if exist "CarDealership_Standalone\تشغيل_البرنامج.bat" echo    • ملف التشغيل في المجلد المستقل
echo    • ملف بيانات الدخول المحدثة
echo    • كود البرنامج الرئيسي
echo.

echo 💡 يمكنك الآن:
echo    1. تشغيل البرنامج باستخدام البيانات الجديدة
echo    2. تسجيل الدخول بحساب المطور: amrali / braa
echo    3. الوصول لجميع الميزات والصلاحيات
echo    4. توزيع المجلد المستقل
echo.

echo 👨‍💻 تم التطوير والتحديث بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo هل تريد تشغيل البرنامج لاختبار البيانات الجديدة؟ (Y/N)
set /p "TEST_LOGIN="
if /i "%TEST_LOGIN%"=="Y" (
    if exist "CarDealership_Standalone\CarDealershipManagement.exe" (
        echo 🚀 تشغيل البرنامج من المجلد المستقل...
        start "" "CarDealership_Standalone\CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج!
        echo 🔑 استخدم: amrali / braa للدخول كمطور
    ) else if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
        echo 🚀 تشغيل البرنامج من مجلد Release...
        start "" "bin\Release\net8.0-windows\CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج!
        echo 🔑 استخدم: amrali / braa للدخول كمطور
    ) else (
        echo ❌ البرنامج غير موجود، يرجى بناؤه أولاً
    )
)

echo.
echo 🎉 تم تحديث كلمة مرور المطور بنجاح!
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
