using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Drawing;
using System.Windows.Forms;

namespace CarDealershipManagement.Forms
{
    public partial class AddEditSupplierForm : Form
    {
        private TextBox txtSupplierName;
        private TextBox txtCommercialRegistrationNumber;
        private TextBox txtResponsiblePerson;
        private TextBox txtPhone;
        private TextBox txtEmail;
        private TextBox txtAddress;
        private NumericUpDown nudAmountDue;
        private NumericUpDown nudAmountPaid;
        private Label lblRemainingAmount;
        private Button btnSave;
        private Button btnCancel;
        private Label lblStatus;

        private int? supplierId;

        public AddEditSupplierForm()
        {
            InitializeComponent();
        }

        public AddEditSupplierForm(int supplierId)
        {
            this.supplierId = supplierId;
            InitializeComponent();
            LoadSupplierData(supplierId);
        }

        [MemberNotNull(nameof(txtSupplierName), nameof(txtCommercialRegistrationNumber),
                      nameof(txtResponsiblePerson), nameof(txtPhone), nameof(txtEmail),
                      nameof(txtAddress), nameof(nudAmountDue), nameof(nudAmountPaid),
                      nameof(lblRemainingAmount), nameof(btnSave), nameof(btnCancel), nameof(lblStatus))]
        private void InitializeComponent()
        {
            this.Text = supplierId == null ? "إضافة مورد جديد" : "تعديل بيانات المورد";
            this.Size = new Size(650, 550);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Font = new Font("Segoe UI", 9F);
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Common styling for labels
            Action<Label> styleLabel = (lbl) =>
            {
                lbl.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                lbl.ForeColor = Color.FromArgb(52, 58, 64);
                lbl.AutoSize = true;
            };

            // Common styling for textboxes
            Action<Control> styleInput = (ctrl) =>
            {
                ctrl.Font = new Font("Segoe UI", 9F);
                ctrl.BackColor = Color.White;
                ctrl.ForeColor = Color.Black;
            };

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(30, 420),
                Size = new Size(540, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Supplier Name
            var lblSupplierName = new Label { Text = "اسم المورد:", Location = new Point(30, 30) };
            styleLabel(lblSupplierName);
            txtSupplierName = new TextBox { Location = new Point(150, 28), Size = new Size(400, 23) };
            styleInput(txtSupplierName);

            // Commercial Registration Number
            var lblCommercialRegistrationNumber = new Label { Text = "رقم السجل التجاري:", Location = new Point(30, 70) };
            styleLabel(lblCommercialRegistrationNumber);
            txtCommercialRegistrationNumber = new TextBox { Location = new Point(150, 68), Size = new Size(200, 23) };
            styleInput(txtCommercialRegistrationNumber);

            // Responsible Person
            var lblResponsiblePerson = new Label { Text = "الشخص المسؤول:", Location = new Point(30, 110) };
            styleLabel(lblResponsiblePerson);
            txtResponsiblePerson = new TextBox { Location = new Point(150, 108), Size = new Size(300, 23) };
            styleInput(txtResponsiblePerson);

            // Phone
            var lblPhone = new Label { Text = "الهاتف:", Location = new Point(30, 150) };
            styleLabel(lblPhone);
            txtPhone = new TextBox { Location = new Point(150, 148), Size = new Size(150, 23) };
            styleInput(txtPhone);

            // Email
            var lblEmail = new Label { Text = "البريد الإلكتروني:", Location = new Point(30, 190) };
            styleLabel(lblEmail);
            txtEmail = new TextBox { Location = new Point(150, 188), Size = new Size(300, 23) };
            styleInput(txtEmail);

            // Address
            var lblAddress = new Label { Text = "العنوان:", Location = new Point(30, 230) };
            styleLabel(lblAddress);
            txtAddress = new TextBox { Location = new Point(150, 228), Size = new Size(400, 23) };
            styleInput(txtAddress);

            // Amount Due
            var lblAmountDue = new Label { Text = "المبلغ المستحق:", Location = new Point(30, 270) };
            styleLabel(lblAmountDue);
            nudAmountDue = new NumericUpDown
            {
                Location = new Point(150, 268),
                Size = new Size(150, 23),
                Maximum = 999999999,
                DecimalPlaces = 2,
                ThousandsSeparator = true
            };
            nudAmountDue.Font = new Font("Segoe UI", 9F);
            nudAmountDue.ValueChanged += NudAmountDue_ValueChanged;

            // Amount Paid
            var lblAmountPaid = new Label { Text = "المبلغ المدفوع:", Location = new Point(320, 270) };
            styleLabel(lblAmountPaid);
            nudAmountPaid = new NumericUpDown
            {
                Location = new Point(440, 268),
                Size = new Size(150, 23),
                Maximum = 999999999,
                DecimalPlaces = 2,
                ThousandsSeparator = true
            };
            nudAmountPaid.Font = new Font("Segoe UI", 9F);
            nudAmountPaid.ValueChanged += NudAmountPaid_ValueChanged;

            // Remaining Amount (calculated)
            lblRemainingAmount = new Label
            {
                Text = "المبلغ المتبقي: 0.00",
                Location = new Point(30, 310),
                Size = new Size(300, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(220, 53, 69) // Red color for remaining amount
            };

            // Save Button
            btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(150, 400),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(0, 123, 255), // Blue
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;

            // Cancel Button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(260, 400),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125), // Gray
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[]
            {
                lblSupplierName, txtSupplierName, lblCommercialRegistrationNumber, txtCommercialRegistrationNumber,
                lblResponsiblePerson, txtResponsiblePerson, lblPhone, txtPhone,
                lblEmail, txtEmail, lblAddress, txtAddress,
                lblAmountDue, nudAmountDue, lblAmountPaid, nudAmountPaid, lblRemainingAmount,
                btnSave, btnCancel, lblStatus
            });
        }

        private void NudAmountDue_ValueChanged(object sender, EventArgs e)
        {
            CalculateRemainingAmount();
        }

        private void NudAmountPaid_ValueChanged(object sender, EventArgs e)
        {
            CalculateRemainingAmount();
        }

        private void CalculateRemainingAmount()
        {
            decimal amountDue = nudAmountDue.Value;
            decimal amountPaid = nudAmountPaid.Value;
            decimal remaining = amountDue - amountPaid;

            lblRemainingAmount.Text = $"المبلغ المتبقي: {remaining:C}";

            // تغيير لون النص حسب الحالة
            if (remaining > 0)
            {
                lblRemainingAmount.ForeColor = Color.FromArgb(220, 53, 69); // أحمر للمبلغ المتبقي
            }
            else if (remaining == 0)
            {
                lblRemainingAmount.ForeColor = Color.FromArgb(40, 167, 69); // أخضر للمدفوع بالكامل
            }
            else
            {
                lblRemainingAmount.ForeColor = Color.FromArgb(255, 193, 7); // أصفر للدفع الزائد
                lblRemainingAmount.Text = $"دفع زائد: {Math.Abs(remaining):C}";
            }
        }

        private async void LoadSupplierData(int supplierId)
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var supplier = await context.Suppliers.FindAsync(supplierId);
                if (supplier != null)
                {
                    txtSupplierName.Text = supplier.SupplierName;
                    txtCommercialRegistrationNumber.Text = supplier.CommercialRegistrationNumber ?? string.Empty;
                    txtResponsiblePerson.Text = supplier.ResponsiblePerson;
                    txtPhone.Text = supplier.Phone;
                    txtEmail.Text = supplier.Email ?? string.Empty;
                    txtAddress.Text = supplier.Address;

                    // تحميل البيانات المالية
                    nudAmountDue.Value = supplier.TotalOwed;
                    nudAmountPaid.Value = supplier.TotalPaid;
                    CalculateRemainingAmount();
                }
                else
                {
                    lblStatus.Text = "المورد غير موجود.";
                    lblStatus.ForeColor = Color.Red;
                    this.Close();
                }
            }, "تحميل بيانات المورد");
        }

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages

            if (string.IsNullOrWhiteSpace(txtSupplierName.Text) ||
                string.IsNullOrWhiteSpace(txtResponsiblePerson.Text) ||
                string.IsNullOrWhiteSpace(txtPhone.Text) ||
                string.IsNullOrWhiteSpace(txtAddress.Text))
            {
                lblStatus.Text = "يرجى إدخال جميع البيانات المطلوبة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            // Validate phone number
            if (!string.IsNullOrEmpty(txtPhone.Text) && !IsValidPhoneNumber(txtPhone.Text))
            {
                lblStatus.Text = "صيغة رقم الهاتف غير صحيحة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            // Validate email
            if (!string.IsNullOrEmpty(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            {
                lblStatus.Text = "صيغة البريد الإلكتروني غير صحيحة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                Supplier supplier;

                if (supplierId == null)  // New supplier
                {
                    supplier = new Supplier();
                    context.Suppliers.Add(supplier);
                }
                else
                {
                    supplier = await context.Suppliers.FindAsync(supplierId);
                    if (supplier == null)
                    {
                        lblStatus.Text = "المورد غير موجود.";
                        lblStatus.ForeColor = Color.Red;
                        return;
                    }
                }

                supplier.SupplierName = txtSupplierName.Text;
                supplier.CommercialRegistrationNumber = string.IsNullOrWhiteSpace(txtCommercialRegistrationNumber.Text) ? null : txtCommercialRegistrationNumber.Text;
                supplier.ResponsiblePerson = txtResponsiblePerson.Text;
                supplier.Phone = txtPhone.Text;
                supplier.Email = string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text;
                supplier.Address = txtAddress.Text;

                // حفظ البيانات المالية
                supplier.TotalOwed = nudAmountDue.Value;
                supplier.TotalPaid = nudAmountPaid.Value;
                supplier.Balance = supplier.TotalOwed - supplier.TotalPaid;

                supplier.ModifiedDate = DateTime.Now;

                await context.SaveChangesAsync();
                MessageBox.Show("تم حفظ المورد بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }, "حفظ بيانات المورد");
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private bool IsValidPhoneNumber(string phoneNumber)
        {
            // A simple regex for phone number validation. 
            // This can be made more robust based on specific regional requirements.
            return System.Text.RegularExpressions.Regex.IsMatch(phoneNumber, @"^\+?[0-9]{7,15}$");
        }
    }
}




