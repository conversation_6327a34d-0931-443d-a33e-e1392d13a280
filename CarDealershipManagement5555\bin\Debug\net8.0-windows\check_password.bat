@echo off
title Password Checker - Car Dealership Management
color 0A

echo.
echo ===============================================
echo           PASSWORD CHECKER TOOL
echo        Car Dealership Management System
echo ===============================================
echo.
echo This tool will check the current password for user 'amrali'
echo.

if not exist "CarDealership.db" (
    echo ❌ Database file not found!
    echo Please run the application first to create the database.
    echo.
    pause
    exit /b 1
)

echo ✅ Database found. Checking password...
echo.

:: Try to use SQLite command line if available
if exist "sqlite3.exe" (
    echo Querying database...
    echo SELECT Username, PasswordHash FROM Users WHERE Username = 'amrali'; | sqlite3.exe CarDealership.db
    echo.
) else (
    echo SQLite command line tool not found.
    echo.
)

echo ===============================================
echo           CURRENT LOGIN CREDENTIALS
echo ===============================================
echo.
echo Username: amrali
echo Password: braa
echo.
echo If you cannot login with these credentials:
echo 1. Run 'fix_login.bat' to reset the database
echo 2. Run 'reset_developer_password.bat' to reset password only
echo 3. Run 'factory_reset.bat' for complete system reset
echo.
echo ===============================================
echo.
pause

exit /b 0
