using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class CustomerSelectionForm : Form
    {
        private readonly CarDealershipContext dbContext;
        
        // UI Controls
        private Panel mainPanel;
        private Panel searchPanel;
        private Panel buttonPanel;
        private TextBox txtSearch;
        private Button btnSearch;
        private DataGridView dgvCustomers;
        private Button btnSelect;
        private Button btnCancel;
        private Label lblStatus;
        
        // Properties
        public int SelectedCustomerId { get; private set; }
        public string SelectedCustomerName { get; private set; } = "";

        public CustomerSelectionForm()
        {
            var optionsBuilder = new DbContextOptionsBuilder<CarDealershipContext>();
            optionsBuilder.UseSqlite("Data Source=CarDealership.db");
            this.dbContext = new CarDealershipContext(optionsBuilder.Options);
            InitializeComponent();
            LoadCustomers();
        }

        private void InitializeComponent()
        {
            // Form settings
            Text = "اختيار العميل - Customer Selection";
            Size = new Size(800, 600);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            BackColor = Color.FromArgb(240, 248, 255);
            Font = new Font("Segoe UI", 10F);

            CreateLayoutStructure();
        }

        private void CreateLayoutStructure()
        {
            // Main Panel
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(15)
            };

            // Search Panel
            searchPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            var lblTitle = new Label
            {
                Text = "اختر العميل لعرض كشف الحساب",
                Location = new Point(10, 5),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204)
            };

            var lblSearch = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 30),
                Size = new Size(50, 25),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleLeft
            };

            txtSearch = new TextBox
            {
                Location = new Point(70, 28),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 9F),
                PlaceholderText = "ابحث بالاسم أو رقم الهوية..."
            };
            txtSearch.KeyPress += TxtSearch_KeyPress;

            btnSearch = new Button
            {
                Text = "🔍 بحث",
                Location = new Point(330, 27),
                Size = new Size(80, 28),
                BackColor = Color.FromArgb(0, 102, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnSearch.FlatAppearance.BorderSize = 0;
            btnSearch.Click += BtnSearch_Click;

            searchPanel.Controls.AddRange(new Control[] { lblTitle, lblSearch, txtSearch, btnSearch });

            // DataGridView
            dgvCustomers = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F)
            };
            dgvCustomers.DoubleClick += DgvCustomers_DoubleClick;

            StyleDataGridView();

            // Button Panel
            buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(15)
            };

            btnSelect = new Button
            {
                Text = "✅ اختيار",
                Location = new Point(20, 20),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Enabled = false
            };
            btnSelect.FlatAppearance.BorderSize = 0;
            btnSelect.Click += BtnSelect_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Location = new Point(160, 20),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; this.Close(); };

            lblStatus = new Label
            {
                Text = "جاري تحميل قائمة العملاء...",
                Location = new Point(300, 30),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.FromArgb(108, 117, 125)
            };

            buttonPanel.Controls.AddRange(new Control[] { btnSelect, btnCancel, lblStatus });

            // Assembly
            mainPanel.Controls.Add(dgvCustomers);
            mainPanel.Controls.Add(buttonPanel);
            mainPanel.Controls.Add(searchPanel);

            this.Controls.Add(mainPanel);

            // Event handlers
            dgvCustomers.SelectionChanged += DgvCustomers_SelectionChanged;
        }

        private void StyleDataGridView()
        {
            dgvCustomers.ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(0, 102, 204),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Alignment = DataGridViewContentAlignment.MiddleCenter
            };

            dgvCustomers.DefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.White,
                ForeColor = Color.Black,
                SelectionBackColor = Color.FromArgb(173, 216, 230),
                SelectionForeColor = Color.Black,
                Alignment = DataGridViewContentAlignment.MiddleCenter
            };
        }

        private void LoadCustomers(string searchTerm = "")
        {
            try
            {
                lblStatus.Text = "جاري تحميل البيانات...";

                var customers = dbContext.Customers
                    .Where(c => string.IsNullOrEmpty(searchTerm) ||
                               c.FullName.Contains(searchTerm) ||
                               c.IdNumber.Contains(searchTerm))
                    .Select(c => new
                    {
                        رقم_العميل = c.CustomerId,
                        اسم_العميل = c.FullName,
                        رقم_الهوية = c.IdNumber,
                        رقم_الهاتف = c.PrimaryPhone,
                        البريد_الإلكتروني = c.Email
                    })
                    .OrderBy(c => c.اسم_العميل)
                    .ToList();

                dgvCustomers.DataSource = customers;

                lblStatus.Text = $"تم تحميل {customers.Count} عميل";

                if (customers.Count == 0)
                {
                    lblStatus.Text = "لا توجد عملاء مطابقين للبحث";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "حدث خطأ أثناء تحميل البيانات";
                MessageBox.Show($"حدث خطأ أثناء تحميل قائمة العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DgvCustomers_SelectionChanged(object sender, EventArgs e)
        {
            btnSelect.Enabled = dgvCustomers.SelectedRows.Count > 0;
        }

        private void DgvCustomers_DoubleClick(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count > 0)
            {
                SelectCustomer();
            }
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            LoadCustomers(txtSearch.Text.Trim());
        }

        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnSearch_Click(sender, e);
                e.Handled = true;
            }
        }

        private void BtnSelect_Click(object sender, EventArgs e)
        {
            SelectCustomer();
        }

        private void SelectCustomer()
        {
            try
            {
                if (dgvCustomers.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار عميل من القائمة.", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dgvCustomers.SelectedRows[0];
                SelectedCustomerId = Convert.ToInt32(selectedRow.Cells["رقم_العميل"].Value);
                SelectedCustomerName = selectedRow.Cells["اسم_العميل"].Value?.ToString() ?? "";

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء اختيار العميل: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
