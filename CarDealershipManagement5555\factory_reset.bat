@echo off
chcp 65001 >nul
title ضبط المصنع - Factory Reset Tool
color 0C

echo.
echo ===============================================
echo           🔧 أداة ضبط المصنع
echo        Car Dealership Management System
echo ===============================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.
echo ⚠️ تحذير: سيتم حذف جميع البيانات نهائياً!
echo.
echo 📋 ما سيتم حذفه:
echo - جميع السيارات والعملاء والمبيعات والأقساط
echo - جميع المستخدمين عدا المطور الافتراضي
echo - جميع الملفات المرفوعة
echo - جميع إعدادات النظام
echo - سجلات النشاط والتقارير
echo.
echo ===============================================
echo.

set /p confirm="هل أنت متأكد من المتابعة؟ (اكتب YES للتأكيد): "
if /i not "%confirm%"=="YES" (
    echo.
    echo تم إلغاء العملية.
    echo.
    pause
    exit /b 0
)

echo.
echo ===============================================
echo.
set /p finalconfirm="التأكيد النهائي - اكتب RESET للمتابعة: "
if /i not "%finalconfirm%"=="RESET" (
    echo.
    echo تم إلغاء العملية.
    echo.
    pause
    exit /b 0
)

echo.
echo ===============================================
echo           🔧 تنفيذ ضبط المصنع
echo ===============================================
echo.

echo الخطوة 1: إيقاف البرنامج إذا كان يعمل...
taskkill /f /im CarDealershipManagement.exe >nul 2>&1

echo الخطوة 2: حذف قاعدة البيانات...
if exist "CarDealership.db" (
    del "CarDealership.db" >nul 2>&1
    echo ✓ تم حذف قاعدة البيانات
) else (
    echo ✓ قاعدة البيانات غير موجودة
)

if exist "CarDealership.db-shm" (
    del "CarDealership.db-shm" >nul 2>&1
)

if exist "CarDealership.db-wal" (
    del "CarDealership.db-wal" >nul 2>&1
)

echo Step 3: Deleting uploaded files...
if exist "Archive" (
    rmdir /s /q "Archive" >nul 2>&1
    echo ✓ Archive folder deleted
) else (
    echo ✓ Archive folder not found
)

echo Step 4: Deleting log files...
if exist "Logs" (
    rmdir /s /q "Logs" >nul 2>&1
    echo ✓ Log files deleted
) else (
    echo ✓ Log files not found
)

echo Step 5: Deleting backup files...
if exist "Backups" (
    rmdir /s /q "Backups" >nul 2>&1
    echo ✓ Backup files deleted
) else (
    echo ✓ Backup files not found
)

echo.
echo ===============================================
echo           FACTORY RESET COMPLETE
echo ===============================================
echo.
echo ✓ All data has been permanently deleted
echo ✓ System has been reset to factory defaults
echo.
echo Default login credentials:
echo Username: amrali
echo Password: braa
echo.
echo The application will create a new database
echo with default settings when you run it next time.
echo.
echo ===============================================
echo.
echo Press any key to exit...
pause >nul

exit /b 0
