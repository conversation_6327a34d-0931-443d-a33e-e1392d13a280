using System;
using System.Data;
using System.IO;
using System.Text;
using System.Windows.Forms;
using CarDealershipManagement.Services;

namespace CarDealershipManagement.Services
{
    /// <summary>
    /// خدمة تصدير التقارير بصيغ مختلفة
    /// </summary>
    public static class ReportExportService
    {
        public enum ExportFormat
        {
            CSV,
            HTML,
            XML,
            JSON,
            TXT
        }

        /// <summary>
        /// تصدير التقرير بالصيغة المحددة
        /// </summary>
        public static async Task<bool> ExportReportAsync(EnhancedReportService.ReportData reportData, string filePath, ExportFormat format)
        {
            try
            {
                switch (format)
                {
                    case ExportFormat.CSV:
                        return await ExportToCSV(reportData, filePath);
                    case ExportFormat.HTML:
                        return await ExportToHTML(reportData, filePath);
                    case ExportFormat.XML:
                        return await ExportToXML(reportData, filePath);
                    case ExportFormat.JSON:
                        return await ExportToJSON(reportData, filePath);
                    case ExportFormat.TXT:
                        return await ExportToTXT(reportData, filePath);
                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// تصدير إلى CSV
        /// </summary>
        private static async Task<bool> ExportToCSV(EnhancedReportService.ReportData reportData, string filePath)
        {
            var csv = new StringBuilder();
            
            // Add header
            csv.AppendLine($"# {reportData.Title}");
            csv.AppendLine($"# {reportData.Subtitle}");
            csv.AppendLine($"# تم الإنشاء في: {reportData.GeneratedAt:yyyy/MM/dd HH:mm:ss}");
            csv.AppendLine();

            // Add summary
            if (reportData.Summary.Any())
            {
                csv.AppendLine("# ملخص التقرير");
                foreach (var item in reportData.Summary)
                {
                    csv.AppendLine($"# {item.Key}: {item.Value}");
                }
                csv.AppendLine();
            }

            // Add column headers
            var headers = new string[reportData.Data.Columns.Count];
            for (int i = 0; i < reportData.Data.Columns.Count; i++)
            {
                headers[i] = reportData.Data.Columns[i].ColumnName;
            }
            csv.AppendLine(string.Join(",", headers.Select(h => $"\"{h}\"")));

            // Add data rows
            foreach (DataRow row in reportData.Data.Rows)
            {
                var values = new string[reportData.Data.Columns.Count];
                for (int i = 0; i < reportData.Data.Columns.Count; i++)
                {
                    values[i] = row[i]?.ToString() ?? "";
                }
                csv.AppendLine(string.Join(",", values.Select(v => $"\"{v}\"")));
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return true;
        }

        /// <summary>
        /// تصدير إلى HTML
        /// </summary>
        private static async Task<bool> ExportToHTML(EnhancedReportService.ReportData reportData, string filePath)
        {
            var html = new StringBuilder();
            var themeColorHex = ColorToHex(reportData.ThemeColor);

            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl' lang='ar'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine("<meta name='viewport' content='width=device-width, initial-scale=1.0'>");
            html.AppendLine($"<title>{reportData.Title}</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f8f9fa; }");
            html.AppendLine(".header { background: linear-gradient(135deg, " + themeColorHex + ", " + ColorToHex(LightenColor(reportData.ThemeColor, 0.2f)) + "); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }");
            html.AppendLine(".title { font-size: 24px; font-weight: bold; margin-bottom: 5px; }");
            html.AppendLine(".subtitle { font-size: 16px; opacity: 0.9; }");
            html.AppendLine(".summary { background: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }");
            html.AppendLine(".summary h3 { color: " + themeColorHex + "; margin-top: 0; }");
            html.AppendLine(".summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }");
            html.AppendLine(".summary-item { background: #f8f9fa; padding: 10px; border-radius: 5px; border-left: 4px solid " + themeColorHex + "; }");
            html.AppendLine(".summary-label { font-weight: bold; color: #495057; }");
            html.AppendLine(".summary-value { font-size: 18px; color: " + themeColorHex + "; font-weight: bold; }");
            html.AppendLine("table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }");
            html.AppendLine("th { background: " + themeColorHex + "; color: white; padding: 12px; text-align: center; font-weight: bold; }");
            html.AppendLine("td { padding: 10px; text-align: center; border-bottom: 1px solid #dee2e6; }");
            html.AppendLine("tr:nth-child(even) { background-color: #f8f9fa; }");
            html.AppendLine("tr:hover { background-color: #e9ecef; }");
            html.AppendLine(".footer { text-align: center; margin-top: 20px; color: #6c757d; font-size: 14px; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // Header
            html.AppendLine("<div class='header'>");
            html.AppendLine($"<div class='title'>{reportData.Title}</div>");
            html.AppendLine($"<div class='subtitle'>{reportData.Subtitle}</div>");
            html.AppendLine("</div>");

            // Summary
            if (reportData.Summary.Any())
            {
                html.AppendLine("<div class='summary'>");
                html.AppendLine("<h3>📊 ملخص التقرير</h3>");
                html.AppendLine("<div class='summary-grid'>");
                foreach (var item in reportData.Summary)
                {
                    html.AppendLine("<div class='summary-item'>");
                    html.AppendLine($"<div class='summary-label'>{item.Key}</div>");
                    html.AppendLine($"<div class='summary-value'>{item.Value}</div>");
                    html.AppendLine("</div>");
                }
                html.AppendLine("</div>");
                html.AppendLine("</div>");
            }

            // Table
            html.AppendLine("<table>");
            html.AppendLine("<thead><tr>");
            foreach (DataColumn column in reportData.Data.Columns)
            {
                html.AppendLine($"<th>{column.ColumnName}</th>");
            }
            html.AppendLine("</tr></thead>");
            html.AppendLine("<tbody>");

            foreach (DataRow row in reportData.Data.Rows)
            {
                html.AppendLine("<tr>");
                foreach (var item in row.ItemArray)
                {
                    html.AppendLine($"<td>{item?.ToString() ?? ""}</td>");
                }
                html.AppendLine("</tr>");
            }

            html.AppendLine("</tbody>");
            html.AppendLine("</table>");

            // Footer
            html.AppendLine("<div class='footer'>");
            html.AppendLine($"تم إنشاء هذا التقرير في {reportData.GeneratedAt:yyyy/MM/dd HH:mm:ss}");
            html.AppendLine("<br>نظام إدارة معرض السيارات");
            html.AppendLine("</div>");

            html.AppendLine("</body>");
            html.AppendLine("</html>");

            await File.WriteAllTextAsync(filePath, html.ToString(), Encoding.UTF8);
            return true;
        }

        /// <summary>
        /// تصدير إلى XML
        /// </summary>
        private static async Task<bool> ExportToXML(EnhancedReportService.ReportData reportData, string filePath)
        {
            var xml = new StringBuilder();
            xml.AppendLine("<?xml version='1.0' encoding='UTF-8'?>");
            xml.AppendLine("<Report>");
            xml.AppendLine($"<Title>{reportData.Title}</Title>");
            xml.AppendLine($"<Subtitle>{reportData.Subtitle}</Subtitle>");
            xml.AppendLine($"<GeneratedAt>{reportData.GeneratedAt:yyyy-MM-ddTHH:mm:ss}</GeneratedAt>");

            // Summary
            if (reportData.Summary.Any())
            {
                xml.AppendLine("<Summary>");
                foreach (var item in reportData.Summary)
                {
                    xml.AppendLine($"<Item key='{item.Key}'>{item.Value}</Item>");
                }
                xml.AppendLine("</Summary>");
            }

            // Data
            xml.AppendLine("<Data>");
            foreach (DataRow row in reportData.Data.Rows)
            {
                xml.AppendLine("<Row>");
                for (int i = 0; i < reportData.Data.Columns.Count; i++)
                {
                    var columnName = reportData.Data.Columns[i].ColumnName;
                    var value = row[i]?.ToString() ?? "";
                    xml.AppendLine($"<{columnName}>{value}</{columnName}>");
                }
                xml.AppendLine("</Row>");
            }
            xml.AppendLine("</Data>");
            xml.AppendLine("</Report>");

            await File.WriteAllTextAsync(filePath, xml.ToString(), Encoding.UTF8);
            return true;
        }

        /// <summary>
        /// تصدير إلى JSON
        /// </summary>
        private static async Task<bool> ExportToJSON(EnhancedReportService.ReportData reportData, string filePath)
        {
            var json = new StringBuilder();
            json.AppendLine("{");
            json.AppendLine($"  \"title\": \"{reportData.Title}\",");
            json.AppendLine($"  \"subtitle\": \"{reportData.Subtitle}\",");
            json.AppendLine($"  \"generatedAt\": \"{reportData.GeneratedAt:yyyy-MM-ddTHH:mm:ss}\",");

            // Summary
            if (reportData.Summary.Any())
            {
                json.AppendLine("  \"summary\": {");
                var summaryItems = reportData.Summary.Select(s => $"    \"{s.Key}\": \"{s.Value}\"");
                json.AppendLine(string.Join(",\n", summaryItems));
                json.AppendLine("  },");
            }

            // Data
            json.AppendLine("  \"data\": [");
            var rows = new List<string>();
            foreach (DataRow row in reportData.Data.Rows)
            {
                var rowJson = new StringBuilder("    {");
                var items = new List<string>();
                for (int i = 0; i < reportData.Data.Columns.Count; i++)
                {
                    var columnName = reportData.Data.Columns[i].ColumnName;
                    var value = row[i]?.ToString() ?? "";
                    items.Add($"\"{columnName}\": \"{value}\"");
                }
                rowJson.Append(string.Join(", ", items));
                rowJson.Append("}");
                rows.Add(rowJson.ToString());
            }
            json.AppendLine(string.Join(",\n", rows));
            json.AppendLine("  ]");
            json.AppendLine("}");

            await File.WriteAllTextAsync(filePath, json.ToString(), Encoding.UTF8);
            return true;
        }

        /// <summary>
        /// تصدير إلى TXT
        /// </summary>
        private static async Task<bool> ExportToTXT(EnhancedReportService.ReportData reportData, string filePath)
        {
            var txt = new StringBuilder();
            txt.AppendLine("=".PadRight(80, '='));
            txt.AppendLine(reportData.Title.PadLeft((80 + reportData.Title.Length) / 2));
            txt.AppendLine(reportData.Subtitle.PadLeft((80 + reportData.Subtitle.Length) / 2));
            txt.AppendLine($"تم الإنشاء في: {reportData.GeneratedAt:yyyy/MM/dd HH:mm:ss}".PadLeft(70));
            txt.AppendLine("=".PadRight(80, '='));
            txt.AppendLine();

            // Summary
            if (reportData.Summary.Any())
            {
                txt.AppendLine("ملخص التقرير:");
                txt.AppendLine("-".PadRight(40, '-'));
                foreach (var item in reportData.Summary)
                {
                    txt.AppendLine($"{item.Key}: {item.Value}");
                }
                txt.AppendLine();
            }

            // Data
            txt.AppendLine("بيانات التقرير:");
            txt.AppendLine("-".PadRight(40, '-'));

            // Calculate column widths
            var columnWidths = new int[reportData.Data.Columns.Count];
            for (int i = 0; i < reportData.Data.Columns.Count; i++)
            {
                columnWidths[i] = Math.Max(reportData.Data.Columns[i].ColumnName.Length, 15);
                foreach (DataRow row in reportData.Data.Rows)
                {
                    var cellLength = row[i]?.ToString()?.Length ?? 0;
                    columnWidths[i] = Math.Max(columnWidths[i], cellLength);
                }
            }

            // Headers
            for (int i = 0; i < reportData.Data.Columns.Count; i++)
            {
                txt.Append(reportData.Data.Columns[i].ColumnName.PadRight(columnWidths[i] + 2));
            }
            txt.AppendLine();

            // Separator
            for (int i = 0; i < reportData.Data.Columns.Count; i++)
            {
                txt.Append("-".PadRight(columnWidths[i] + 2, '-'));
            }
            txt.AppendLine();

            // Data rows
            foreach (DataRow row in reportData.Data.Rows)
            {
                for (int i = 0; i < reportData.Data.Columns.Count; i++)
                {
                    var value = row[i]?.ToString() ?? "";
                    txt.Append(value.PadRight(columnWidths[i] + 2));
                }
                txt.AppendLine();
            }

            txt.AppendLine();
            txt.AppendLine("=".PadRight(80, '='));

            await File.WriteAllTextAsync(filePath, txt.ToString(), Encoding.UTF8);
            return true;
        }

        #region Helper Methods

        private static string ColorToHex(System.Drawing.Color color)
        {
            return $"#{color.R:X2}{color.G:X2}{color.B:X2}";
        }

        private static System.Drawing.Color LightenColor(System.Drawing.Color color, float factor)
        {
            return System.Drawing.Color.FromArgb(
                color.A,
                Math.Min(255, (int)(color.R + (255 - color.R) * factor)),
                Math.Min(255, (int)(color.G + (255 - color.G) * factor)),
                Math.Min(255, (int)(color.B + (255 - color.B) * factor))
            );
        }

        #endregion
    }
}
