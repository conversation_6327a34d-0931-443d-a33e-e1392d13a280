using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using System.ComponentModel;

namespace CarDealershipManagement.Forms
{
    public partial class InstallmentDetailsForm : Form
    {
        private Label lblTotalPrice = null!;
        private Label lblDownPayment = null!;
        private Label lblRemainingAmount = null!;
        private Label lblInstallmentAmount = null!;
        private NumericUpDown numTotalPrice = null!;
        private NumericUpDown numDownPayment = null!;
        private NumericUpDown numRemainingAmount = null!;
        private NumericUpDown numInstallmentAmount = null!;
        private NumericUpDown numNumberOfInstallments = null!;
        private ComboBox cmbInstallmentPeriod = null!;
        private DateTimePicker dtpFirstInstallmentDate = null!;
        private DataGridView dgvPaymentSchedule = null!;
        private Button btnCalculate = null!;
        private Button btnConfirm = null!;
        private Button btnCancel = null!;
        private Label lblStatus = null!;

        public decimal TotalPrice
        {
            get;
            set;
        }
        public decimal DownPayment
        {
            get;
            set;
        }
        public decimal RemainingAmount
        {
            get;
            set;
        }
        public decimal InstallmentAmount
        {
            get;
            set;
        }
        public int NumberOfInstallments
        {
            get;
            set;
        }
        public InstallmentPeriod InstallmentPeriod
        {
            get;
            set;
        }
        public DateTime FirstInstallmentDate
        {
            get;
            set;
        }
        public List<InstallmentScheduleItem> PaymentSchedule
        {
            get;
            set;
        } = new();

        public InstallmentDetailsForm(decimal totalPrice)
        {
            InitializeComponent();
            TotalPrice = totalPrice;
            numTotalPrice.Value = totalPrice;

            // Set defaults
            FirstInstallmentDate = DateTime.Now.AddMonths(1);
            NumberOfInstallments = 12;
            InstallmentPeriod = InstallmentPeriod.Monthly;
            DownPayment = totalPrice * 0.20m; // Default 20% down payment

            LoadDefaults();
            CalculatePaymentSchedule();
        }

        private void InitializeComponent()
        {
            this.Text = "تفاصيل الدفع بالتقسيط - Installment Payment Details";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Segoe UI", 9F);

            // Common styling for labels
            Action<Label> styleLabel = (lbl) =>
            {
                lbl.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                lbl.ForeColor = Color.FromArgb(52, 58, 64);
                lbl.AutoSize = true;
            };

            // Common styling for textboxes and numeric up-downs
            Action<Control> styleInput = (ctrl) =>
            {
                ctrl.Font = new Font("Segoe UI", 9F);
                ctrl.BackColor = Color.White;
                ctrl.ForeColor = Color.Black;
            };

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(30, 580),
                Size = new Size(840, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Title
            var titleLabel = new Label
            {
                Text = "تفاصيل الدفع بالتقسيط",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64),
                Location = new Point(30, 20),
                Size = new Size(400, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Payment Details Group
            var paymentDetailsGroup = new GroupBox
            {
                Text = "تفاصيل الدفع",
                Location = new Point(30, 60),
                Size = new Size(400, 280),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64)
            };

            // Total Price - Allow manual editing for installments
            lblTotalPrice = new Label { Text = "إجمالي سعر السيارة:", Location = new Point(20, 30) };
            styleLabel(lblTotalPrice);
            numTotalPrice = new NumericUpDown
            {
                Location = new Point(150, 28),
                Size = new Size(150, 23),
                DecimalPlaces = 2,
                Maximum = 10000000,
                ReadOnly = false,
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };
            styleInput(numTotalPrice);
            numTotalPrice.ValueChanged += NumTotalPrice_ValueChanged;

            // Down Payment
            lblDownPayment = new Label { Text = "الدفعة المقدمة:", Location = new Point(20, 65) };
            styleLabel(lblDownPayment);
            numDownPayment = new NumericUpDown
            {
                Location = new Point(150, 63),
                Size = new Size(150, 23),
                DecimalPlaces = 2,
                Maximum = 10000000
            };
            styleInput(numDownPayment);
            numDownPayment.ValueChanged += NumDownPayment_ValueChanged;

            // Remaining Amount
            lblRemainingAmount = new Label { Text = "المبلغ المتبقي:", Location = new Point(20, 100) };
            styleLabel(lblRemainingAmount);
            numRemainingAmount = new NumericUpDown
            {
                Location = new Point(150, 98),
                Size = new Size(150, 23),
                DecimalPlaces = 2,
                Maximum = 10000000,
                ReadOnly = true,
                BackColor = SystemColors.Control,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(220, 53, 69)
            };
            styleInput(numRemainingAmount);

            // Number of Installments
            var lblNumberOfInstallments = new Label { Text = "عدد الأقساط:", Location = new Point(20, 135) };
            styleLabel(lblNumberOfInstallments);
            numNumberOfInstallments = new NumericUpDown
            {
                Location = new Point(150, 133),
                Size = new Size(100, 23),
                Minimum = 1,
                Maximum = 120,
                Value = 12
            };
            styleInput(numNumberOfInstallments);
            numNumberOfInstallments.ValueChanged += NumNumberOfInstallments_ValueChanged;

            // Installment Period
            var lblInstallmentPeriod = new Label { Text = "فترة الأقساط:", Location = new Point(20, 170) };
            styleLabel(lblInstallmentPeriod);
            cmbInstallmentPeriod = new ComboBox
            {
                Location = new Point(150, 168),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            styleInput(cmbInstallmentPeriod);
            cmbInstallmentPeriod.Items.AddRange(new[] { "شهري", "ربع سنوي", "نصف سنوي", "سنوي" });
            cmbInstallmentPeriod.SelectedIndex = 0; // Default to Monthly
            cmbInstallmentPeriod.SelectedIndexChanged += CmbInstallmentPeriod_SelectedIndexChanged;

            // First Installment Date
            var lblFirstInstallmentDate = new Label { Text = "تاريخ أول قسط:", Location = new Point(20, 205) };
            styleLabel(lblFirstInstallmentDate);
            dtpFirstInstallmentDate = new DateTimePicker
            {
                Location = new Point(150, 203),
                Size = new Size(150, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddMonths(1)
            };
            styleInput(dtpFirstInstallmentDate);
            dtpFirstInstallmentDate.ValueChanged += DtpFirstInstallmentDate_ValueChanged;

            // Installment Amount
            lblInstallmentAmount = new Label { Text = "قيمة القسط الشهري:", Location = new Point(20, 240), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
            styleLabel(lblInstallmentAmount);
            numInstallmentAmount = new NumericUpDown
            {
                Location = new Point(150, 238),
                Size = new Size(150, 23),
                DecimalPlaces = 2,
                Maximum = 10000000,
                ReadOnly = true,
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };
            styleInput(numInstallmentAmount);

            // Add controls to payment details group
            paymentDetailsGroup.Controls.AddRange(new Control[]
            {
                lblTotalPrice, numTotalPrice, lblDownPayment, numDownPayment,
                lblRemainingAmount, numRemainingAmount, lblNumberOfInstallments, numNumberOfInstallments,
                lblInstallmentPeriod, cmbInstallmentPeriod, lblFirstInstallmentDate, dtpFirstInstallmentDate,
                lblInstallmentAmount, numInstallmentAmount
            });

            // Payment Schedule Group
            var scheduleGroup = new GroupBox
            {
                Text = "جدول الأقساط",
                Location = new Point(450, 60),
                Size = new Size(420, 450),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64)
            };

            // Payment Schedule DataGridView
            dgvPaymentSchedule = new DataGridView
            {
                Location = new Point(10, 25),
                Size = new Size(400, 415),
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                GridColor = Color.FromArgb(222, 226, 230),
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.FromArgb(52, 58, 64),
                    SelectionBackColor = Color.FromArgb(0, 123, 255),
                    SelectionForeColor = Color.White,
                    Font = new Font("Segoe UI", 8)
                },
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 58, 64),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 9, FontStyle.Bold)
                }
            };

            scheduleGroup.Controls.Add(dgvPaymentSchedule);

            // Calculate Button
            btnCalculate = new Button
            {
                Text = "إعادة حساب",
                Location = new Point(30, 360),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            btnCalculate.FlatAppearance.BorderSize = 0;
            btnCalculate.Click += BtnCalculate_Click;

            // Action Buttons
            btnConfirm = new Button
            {
                Text = "تأكيد وحفظ",
                Location = new Point(670, 610),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            btnConfirm.FlatAppearance.BorderSize = 0;
            btnConfirm.Click += BtnConfirm_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(780, 610),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;

            // Add all controls to form
            this.Controls.AddRange(new Control[]
            {
                titleLabel, paymentDetailsGroup, scheduleGroup, btnCalculate, btnConfirm, btnCancel, lblStatus
            });

            // Setup DataGridView columns
            SetupDataGridViewColumns();
        }

        private void LoadDefaults()
        {
            numTotalPrice.Value = TotalPrice;
            numDownPayment.Value = DownPayment;
            numNumberOfInstallments.Value = NumberOfInstallments;
            dtpFirstInstallmentDate.Value = FirstInstallmentDate;
            CalculateRemainingAmount();
        }

        private void SetupDataGridViewColumns()
        {
            dgvPaymentSchedule.Columns.Clear();

            dgvPaymentSchedule.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InstallmentNumber",
                HeaderText = "رقم القسط",
                Width = 70,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvPaymentSchedule.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DueDate",
                HeaderText = "تاريخ الاستحقاق",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvPaymentSchedule.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Amount",
                HeaderText = "مبلغ القسط",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "C2"
                }
            });

            dgvPaymentSchedule.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });
        }

        private void NumDownPayment_ValueChanged(object? sender, EventArgs e)
        {
            CalculateRemainingAmount();
            CalculatePaymentSchedule();
        }

        private void NumNumberOfInstallments_ValueChanged(object? sender, EventArgs e)
        {
            CalculatePaymentSchedule();
        }

        private void CmbInstallmentPeriod_SelectedIndexChanged(object? sender, EventArgs e)
        {
            CalculatePaymentSchedule();
        }

        private void DtpFirstInstallmentDate_ValueChanged(object? sender, EventArgs e)
        {
            CalculatePaymentSchedule();
        }

        private void NumTotalPrice_ValueChanged(object? sender, EventArgs e)
        {
            CalculateRemainingAmount();
            CalculatePaymentSchedule();
        }

        private void CalculateRemainingAmount()
        {
            decimal remaining = numTotalPrice.Value - numDownPayment.Value;
            numRemainingAmount.Value = Math.Max(0, remaining);
            TotalPrice = numTotalPrice.Value; // Update the property when manually changed
        }

        private void CalculatePaymentSchedule()
        {
            lblStatus.Text = ""; // Clear previous status messages

            ErrorHandlingService.TryExecute(() =>
            {
                CalculateRemainingAmount();

                decimal remainingAmount = numRemainingAmount.Value;
                int numberOfInstallments = (int)numNumberOfInstallments.Value;

                if (remainingAmount <= 0 || numberOfInstallments <= 0)
                {
                    numInstallmentAmount.Value = 0;
                    PaymentSchedule.Clear();
                    RefreshPaymentScheduleGrid();
                    return;
                }

                decimal exactInstallmentAmount = remainingAmount / numberOfInstallments;
                decimal baseInstallmentAmount = Math.Round(exactInstallmentAmount, 2);

                decimal totalOfBaseInstallments = baseInstallmentAmount * numberOfInstallments;
                decimal difference = remainingAmount - totalOfBaseInstallments;

                int differenceCents = (int)Math.Round(difference * 100);

                numInstallmentAmount.Value = baseInstallmentAmount;

                PaymentSchedule.Clear();
                DateTime currentDate = dtpFirstInstallmentDate.Value;

                for (int i = 1; i <= numberOfInstallments; i++)
                {
                    decimal installmentAmount = baseInstallmentAmount;

                    if (differenceCents > 0)
                    {
                        installmentAmount += 0.01m;
                        differenceCents--;
                    }
                    else if (differenceCents < 0)
                    {
                        installmentAmount -= 0.01m;
                        differenceCents++;
                    }

                    PaymentSchedule.Add(new InstallmentScheduleItem
                    {
                        InstallmentNumber = i,
                        DueDate = currentDate,
                        Amount = Math.Round(installmentAmount, 2),
                        Status = "مستحق"
                    });

                    currentDate = (InstallmentPeriod)(cmbInstallmentPeriod.SelectedIndex + 1) switch
                    {
                        InstallmentPeriod.Monthly => currentDate.AddMonths(1),
                        InstallmentPeriod.Quarterly => currentDate.AddMonths(3),
                        InstallmentPeriod.SixMonths => currentDate.AddMonths(6),
                        InstallmentPeriod.Yearly => currentDate.AddYears(1),
                        _ => currentDate.AddMonths(1)
                    };
                }

                VerifyAndAdjustInstallments(remainingAmount);

                RefreshPaymentScheduleGrid();
            }, "حساب جدول الأقساط");
        }

        private void VerifyAndAdjustInstallments(decimal targetTotal)
        {
            decimal currentTotal = PaymentSchedule.Sum(p => p.Amount);
            decimal difference = targetTotal - currentTotal;

            if (Math.Abs(difference) >= 0.01m && PaymentSchedule.Count > 0)
            {
                var lastInstallment = PaymentSchedule.Last();
                lastInstallment.Amount = Math.Round(lastInstallment.Amount + difference, 2);

                if (PaymentSchedule.Count > 1)
                {
                    var amounts = PaymentSchedule.Select(p => p.Amount).ToList();
                    var mostCommonAmount = amounts.GroupBy(a => a)
                                           .OrderByDescending(g => g.Count())
                                           .First().Key;
                    numInstallmentAmount.Value = mostCommonAmount;
                }
            }
        }

        private void RefreshPaymentScheduleGrid()
        {
            dgvPaymentSchedule.Rows.Clear();

            foreach (var item in PaymentSchedule)
            {
                dgvPaymentSchedule.Rows.Add(
                    item.InstallmentNumber,
                    item.DueDate.ToString("yyyy-MM-dd"),
                    item.Amount.ToString("C2"),
                    item.Status
                );
            }

            for (int i = 0; i < dgvPaymentSchedule.Rows.Count; i++)
            {
                if (i % 2 == 1)
                {
                    dgvPaymentSchedule.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
                }
            }
        }

        private void BtnCalculate_Click(object? sender, EventArgs e)
        {
            CalculatePaymentSchedule();
        }

        private void BtnConfirm_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages

            if (numDownPayment.Value > numTotalPrice.Value)
            {
                lblStatus.Text = "الدفعة المقدمة لا يمكن أن تكون أكبر من سعر البيع.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            if (numNumberOfInstallments.Value < 1)
            {
                lblStatus.Text = "عدد الأقساط يجب أن يكون أكبر من صفر.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            if (cmbInstallmentPeriod.SelectedIndex == -1)
            {
                lblStatus.Text = "يرجى اختيار فترة الأقساط.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            // Set properties
            TotalPrice = numTotalPrice.Value;
            DownPayment = numDownPayment.Value;
            RemainingAmount = numRemainingAmount.Value;
            InstallmentAmount = numInstallmentAmount.Value;
            NumberOfInstallments = (int)numNumberOfInstallments.Value;
            InstallmentPeriod = (InstallmentPeriod)(cmbInstallmentPeriod.SelectedIndex + 1);
            FirstInstallmentDate = dtpFirstInstallmentDate.Value;

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }

    public class InstallmentScheduleItem
    {
        public int InstallmentNumber
        {
            get;
            set;
        }
        public DateTime DueDate
        {
            get;
            set;
        }
        public decimal Amount
        {
            get;
            set;
        }
        public string Status
        {
            get;
            set;
        } = "مستحق";
    }
}


