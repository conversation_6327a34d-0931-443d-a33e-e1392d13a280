﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>

    <!-- معلومات التطبيق للنشر -->
    <AssemblyTitle>برنامج إدارة معرض السيارات</AssemblyTitle>
    <AssemblyDescription>نظام شامل لإدارة معارض السيارات - تطوير: Amr <PERSON> | الهاتف: 01285626623 | البريد: <EMAIL></AssemblyDescription>
    <AssemblyCompany>Amr <PERSON></AssemblyCompany>
    <AssemblyProduct>Car Dealership Management System by Amr <PERSON></AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024 Amr <PERSON> - الهاتف: 01285626623 - البريد: <EMAIL></AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ProductVersion>1.0.0</ProductVersion>

    <!-- معلومات المطور -->
    <Authors>Amr <PERSON> Elawamy</Authors>
    <Company>Amr Ali Elawamy</Company>
    <Copyright>Copyright © 2024 Amr Ali Elawamy</Copyright>
    <PackageProjectUrl>https://github.com/amrelawamy/CarDealershipManagement</PackageProjectUrl>
    <RepositoryUrl>https://github.com/amrelawamy/CarDealershipManagement</RepositoryUrl>
    <PackageTags>car;dealership;management;sales;inventory;arabic;amr;elawamy</PackageTags>
    <NeutralLanguage>ar</NeutralLanguage>

    <!-- أيقونة البرنامج -->
    <ApplicationIcon>app-icon.ico</ApplicationIcon>

    <!-- إعدادات النشر -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <PublishTrimmed>false</PublishTrimmed>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
    <DebugType>embedded</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.Management" Version="9.0.6" />
    <PackageReference Include="Microsoft.VisualBasic" Version="10.3.0" />
  </ItemGroup>

</Project>