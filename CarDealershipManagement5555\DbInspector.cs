using Microsoft.EntityFrameworkCore;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;

namespace CarDealershipManagement
{
public class DbInspector
{
    public static void InspectUsers()
    {
        var options = new DbContextOptionsBuilder<CarDealershipContext>()
        .UseSqlite("Data Source=CarDealership.db")
        .Options;
        using var context = new CarDealershipContext(options);

        Console.WriteLine("=== Database Users Inspection ===");

        var users = context.Users.Include(u => u.Permissions).ToList();

        Console.WriteLine($"Total users found: {users.Count}");
        Console.WriteLine();

        foreach(var user in users)
        {
            Console.WriteLine($"Username: '{user.Username}'");
            Console.WriteLine($"Full Name: '{user.FullName}'");
            Console.WriteLine($"IsActive: {user.IsActive}");
            Console.WriteLine($"Role: {user.Role}");
            Console.WriteLine($"Password Hash (first 20 chars): {user.PasswordHash?.Substring(0, Math.Min(20, user.PasswordHash.Length))}...");

            if(user.Permissions != null)
            {
                Console.WriteLine("Permissions:");
                var props = typeof(UserPermissions).GetProperties()
                            .Where(p => p.PropertyType == typeof(bool))
                            .Where(p => (bool)p.GetValue(user.Permissions)!)
                            .Select(p => p.Name);
                Console.WriteLine($"  {string.Join(", ", props)}");
            }

            Console.WriteLine("---");
        }

        // Test the specific login credentials
        Console.WriteLine("\n=== Testing Login Credentials ===");
        string testUsername = "amrali";
        string testPassword = "braa";

        var testUser = context.Users.FirstOrDefault(u => u.Username == testUsername);
        if(testUser != null)
        {
            Console.WriteLine($"Found user with username '{testUsername}'");
            bool passwordMatches = BCrypt.Net.BCrypt.Verify(testPassword, testUser.PasswordHash);
            Console.WriteLine($"Password verification result: {passwordMatches}");
        }
        else
        {
            Console.WriteLine($"No user found with username '{testUsername}'");
        }
    }
}
}
