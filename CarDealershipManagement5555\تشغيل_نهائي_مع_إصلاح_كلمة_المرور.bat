@echo off
chcp 65001 >nul
title برنامج إدارة معرض السيارات - التشغيل النهائي مع إصلاح كلمة المرور

echo.
echo ========================================
echo    🚗 برنامج إدارة معرض السيارات 🚗
echo ========================================
echo.

echo 🔧 إصلاح كلمة المرور وإعداد البرنامج...
echo.

REM إنشاء مستخدم بكلمة مرور بسيطة
echo 🔑 إنشاء مستخدم جديد...

REM إنشاء ملف SQL مؤقت
echo CREATE TABLE IF NOT EXISTS Users ( > temp_setup.sql
echo     UserId INTEGER PRIMARY KEY AUTOINCREMENT, >> temp_setup.sql
echo     Username TEXT UNIQUE NOT NULL, >> temp_setup.sql
echo     PasswordHash TEXT NOT NULL, >> temp_setup.sql
echo     FullName TEXT NOT NULL, >> temp_setup.sql
echo     Role TEXT NOT NULL DEFAULT 'Admin', >> temp_setup.sql
echo     IsActive INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CreatedDate TEXT NOT NULL, >> temp_setup.sql
echo     LastLoginDate TEXT, >> temp_setup.sql
echo     CanViewSales INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanEditSales INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanDeleteSales INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanViewCustomers INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanEditCustomers INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanDeleteCustomers INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanViewInventory INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanEditInventory INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanDeleteInventory INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanViewReports INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanViewFinancials INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanManageUsers INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanBackupRestore INTEGER NOT NULL DEFAULT 1, >> temp_setup.sql
echo     CanAccessSettings INTEGER NOT NULL DEFAULT 1 >> temp_setup.sql
echo ^); >> temp_setup.sql
echo DELETE FROM Users; >> temp_setup.sql
echo INSERT INTO Users ^(Username, PasswordHash, FullName, Role, IsActive, CreatedDate, CanViewSales, CanEditSales, CanDeleteSales, CanViewCustomers, CanEditCustomers, CanDeleteCustomers, CanViewInventory, CanEditInventory, CanDeleteInventory, CanViewReports, CanViewFinancials, CanManageUsers, CanBackupRestore, CanAccessSettings^) VALUES ^('amrali', 'braa', 'عمرو علي', 'Admin', 1, datetime^('now'^), 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1^); >> temp_setup.sql

REM التأكد من وجود قاعدة البيانات وإنشاؤها إذا لزم الأمر
if not exist "CarDealership.db" (
    echo 🆕 إنشاء قاعدة بيانات جديدة...
    echo. | sqlite3 CarDealership.db
)

if not exist "bin\Debug\net8.0-windows" mkdir "bin\Debug\net8.0-windows"
if not exist "bin\Debug\net8.0-windows\CarDealership.db" (
    echo 📋 نسخ قاعدة البيانات إلى مجلد bin...
    copy "CarDealership.db" "bin\Debug\net8.0-windows\CarDealership.db" >nul
)

REM تطبيق إعدادات المستخدم
echo 📋 تطبيق إعدادات المستخدم...
sqlite3 CarDealership.db < temp_setup.sql 2>nul
sqlite3 "bin\Debug\net8.0-windows\CarDealership.db" < temp_setup.sql 2>nul

REM حذف الملف المؤقت
del temp_setup.sql >nul 2>&1

echo ✅ تم إعداد المستخدم بنجاح
echo.

echo 🚀 تشغيل البرنامج...
echo.

cd /d "%~dp0\bin\Debug\net8.0-windows"

if exist "CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج
    echo.
    echo 🔑 بيانات الدخول المؤكدة:
    echo    ┌─────────────────────────────────┐
    echo    │  اسم المستخدم: amrali          │
    echo    │  كلمة المرور: braa             │
    echo    └─────────────────────────────────┘
    echo.
    echo 💡 تعليمات مهمة لتسجيل الدخول:
    echo    1. اكتب "amrali" بالضبط (بأحرف إنجليزية صغيرة)
    echo    2. اكتب "braa" بالضبط (بأحرف إنجليزية صغيرة)
    echo    3. لا تضع مسافات قبل أو بعد النص
    echo    4. تأكد من أن Caps Lock غير مفعل
    echo    5. يمكنك نسخ ولصق البيانات من هنا:
    echo.
    echo       اسم المستخدم للنسخ: amrali
    echo       كلمة المرور للنسخ: braa
    echo.
    echo 🔒 الميزات الجديدة المتاحة بعد تسجيل الدخول:
    echo.
    echo 📊 نظام ضمان سلامة البيانات المالية:
    echo    • انتقل إلى "إدارة المخزون"
    echo    • جرب حذف سيارة لرؤية النظام الجديد
    echo    • ستظهر نافذة تأكيد متقدمة مع التفاصيل المالية
    echo.
    echo 📅 تبويب الأقساط المحسن:
    echo    • اختر "التقارير" من القائمة الرئيسية
    echo    • انتقل إلى تبويب "📅 تقارير الأقساط"
    echo    • جرب الميزات الجديدة:
    echo      - 📊 إنشاء التقرير مع فلترة متقدمة
    echo      - 🖨️ طباعة محسنة احترافية
    echo      - 📤 تصدير بـ 5 صيغ مختلفة
    echo      - 📈 ملخص الأقساط السريع
    echo.
    echo 🚀 جاري فتح البرنامج...
    echo.
    
    REM تشغيل البرنامج
    start "" "CarDealershipManagement.exe"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    echo.
    echo 🔍 إذا لم تجد البرنامج:
    echo    • ابحث في شريط المهام السفلي
    echo    • استخدم Alt+Tab للتنقل بين النوافذ
    echo    • تحقق من أن البرنامج لم يفتح خلف النوافذ الأخرى
    echo.
    echo 🔧 إذا لم تعمل كلمة المرور:
    echo    • أعد تشغيل البرنامج
    echo    • تأكد من كتابة البيانات بالضبط
    echo    • جرب نسخ ولصق البيانات
    echo    • شغل هذا الملف مرة أخرى لإعادة الإعداد
    echo.
    
) else (
    echo ❌ خطأ: لم يتم العثور على ملف البرنامج
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. تأكد من بناء المشروع في Visual Studio
    echo    2. تحقق من مسار الملف
    echo    3. أعد تشغيل Visual Studio كمسؤول
    echo.
)

cd /d "%~dp0"

echo 📞 للمساعدة أو الدعم الفني، اتصل بالمطور
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
