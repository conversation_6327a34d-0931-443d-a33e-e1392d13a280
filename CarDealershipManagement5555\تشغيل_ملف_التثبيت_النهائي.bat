@echo off
chcp 65001 >nul
title ملف التثبيت النهائي - برنامج إدارة معرض السيارات

echo.
echo ========================================
echo   📦 ملف التثبيت النهائي جاهز!
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo 🎉 تم إنشاء ملف التثبيت بنجاح!
echo.

set "INSTALLER_DIR=%~dp0CarDealership_Installer"

if exist "%INSTALLER_DIR%\install.bat" (
    echo ✅ ملف التثبيت موجود وجاهز
    echo 📁 المكان: %INSTALLER_DIR%
    echo.
    
    REM عرض حجم المجلد
    echo 📊 معلومات ملف التثبيت:
    
    for /f "tokens=3" %%a in ('dir "%INSTALLER_DIR%" /-c ^| find "File(s)"') do (
        set "TOTAL_SIZE=%%a"
        set /a "SIZE_MB=!TOTAL_SIZE! / 1024 / 1024"
        echo    📏 حجم المجلد: !SIZE_MB! ميجابايت
    )
    
    echo.
    echo 🎯 محتويات ملف التثبيت:
    echo    ✅ install.bat - ملف التثبيت الرئيسي
    echo    ✅ CarDealershipManagement.exe - البرنامج الكامل
    echo    ✅ جميع المكتبات المطلوبة (DLL files)
    echo    ✅ قاعدة البيانات والإعدادات
    echo    ✅ الأيقونات والموارد
    echo    ✅ ملفات اللغات والترجمة
    echo    ✅ مجلدات البيانات والملفات
    echo.
    
    echo 🔑 بيانات الدخول للعملاء:
    echo    🔧 المطور: amrali / braa (جميع الصلاحيات)
    echo    👔 المدير: admin / 123 (صلاحيات إدارية)
    echo    🤝 المندوب: user / pass (صلاحيات أساسية)
    echo.
    
    echo 🆕 نظام التفعيل:
    echo    • نسخة تجريبية مجانية (30 يوم)
    echo    • تراخيص شهرية وسنوية ومدى الحياة
    echo    • حماية بمعرف الجهاز
    echo    • تشفير ملفات الترخيص
    echo.
    
    echo 💡 للتوزيع:
    echo    1. انسخ مجلد CarDealership_Installer كاملاً
    echo    2. أو اضغطه في ملف ZIP
    echo    3. أرسله للعملاء
    echo    4. اطلب منهم تشغيل install.bat كمدير
    echo.
    
    echo 📋 تعليمات للعملاء:
    echo    1. استخرج الملفات إذا كانت مضغوطة
    echo    2. اضغط بالزر الأيمن على install.bat
    echo    3. اختر "تشغيل كمدير" (Run as administrator)
    echo    4. اتبع التعليمات على الشاشة
    echo    5. سيتم إنشاء اختصارات تلقائياً
    echo.
    
    echo هل تريد اختبار ملف التثبيت؟ (Y/N)
    set /p "TEST_INSTALL="
    if /i "%TEST_INSTALL%"=="Y" (
        echo.
        echo 🚀 تشغيل ملف التثبيت للاختبار...
        echo ⚠️ سيتم تثبيت البرنامج على هذا الجهاز
        echo.
        echo هل تريد المتابعة؟ (Y/N)
        set /p "CONTINUE_TEST="
        if /i "%CONTINUE_TEST%"=="Y" (
            start "" "%INSTALLER_DIR%\install.bat"
            echo ✅ تم تشغيل ملف التثبيت!
        ) else (
            echo تم إلغاء الاختبار
        )
    )
    
    echo.
    echo هل تريد فتح مجلد التثبيت؟ (Y/N)
    set /p "OPEN_FOLDER="
    if /i "%OPEN_FOLDER%"=="Y" (
        start "" "%INSTALLER_DIR%"
    )
    
    echo.
    echo هل تريد إنشاء ملف ZIP للتوزيع؟ (Y/N)
    set /p "CREATE_ZIP="
    if /i "%CREATE_ZIP%"=="Y" (
        echo 📦 إنشاء ملف ZIP...
        
        powershell -Command "
        try {
            Add-Type -AssemblyName System.IO.Compression.FileSystem
            $source = '%INSTALLER_DIR%'
            $destination = '%~dp0CarDealershipManagement_Installer_Final.zip'
            
            if (Test-Path $destination) {
                Remove-Item $destination -Force
            }
            
            [System.IO.Compression.ZipFile]::CreateFromDirectory($source, $destination)
            Write-Host '✅ تم إنشاء ملف ZIP بنجاح' -ForegroundColor Green
            
            $fileSize = (Get-Item $destination).Length / 1MB
            Write-Host ('📏 حجم الملف: {0:N1} ميجابايت' -f $fileSize) -ForegroundColor Cyan
            
        } catch {
            Write-Host '❌ فشل في إنشاء ملف ZIP' -ForegroundColor Red
            Write-Host $_.Exception.Message -ForegroundColor Red
        }
        "
        
        if exist "CarDealershipManagement_Installer_Final.zip" (
            echo.
            echo ✅ تم إنشاء ملف ZIP للتوزيع!
            echo 📄 الملف: CarDealershipManagement_Installer_Final.zip
            echo.
            
            REM عرض حجم الملف
            for %%A in ("CarDealershipManagement_Installer_Final.zip") do (
                set "FILE_SIZE=%%~zA"
                set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
                echo 📏 حجم ملف ZIP: !FILE_SIZE_MB! ميجابايت
            )
            
            echo.
            echo 💡 يمكنك الآن:
            echo    1. إرسال الملف للعملاء عبر البريد الإلكتروني
            echo    2. رفعه على موقعك الإلكتروني
            echo    3. نسخه على فلاشة USB
            echo    4. مشاركته عبر التطبيقات
        )
    )
    
) else (
    echo ❌ ملف التثبيت غير موجود
    echo يرجى تشغيل أحد الملفات التالية أولاً:
    echo    • إنشاء_ملف_التثبيت_الاحترافي.bat
    echo    • إنشاء_ملف_تثبيت_بسيط.bat
    echo    • إنشاء_حزمة_التثبيت_النهائية.bat
)

echo.
echo 📊 ملخص ملف التثبيت:
echo.

if exist "%INSTALLER_DIR%\install.bat" (
    echo ✅ ملف التثبيت جاهز للتوزيع
    echo ✅ يحتوي على البرنامج الكامل
    echo ✅ تثبيت تلقائي مع اختصارات
    echo ✅ نظام إلغاء تثبيت متكامل
    echo ✅ دعم جميع إصدارات Windows
) else (
    echo ❌ ملف التثبيت غير جاهز
)

echo.
echo 🎯 المميزات النهائية:
echo    ✅ أيقونة احترافية مخصصة
echo    ✅ حقوق المطور في جميع الملفات
echo    ✅ نظام تفعيل متقدم
echo    ✅ بيانات دخول محدثة
echo    ✅ جميع الميزات المطورة
echo    ✅ ملف تثبيت احترافي
echo    ✅ جاهز للبيع والتوزيع
echo.

echo 💰 قيمة البرنامج:
echo    • نظام شامل لإدارة معارض السيارات
echo    • أكثر من 64 صلاحية مختلفة
echo    • تقارير متقدمة بـ 5 صيغ تصدير
echo    • نظام أقساط ومبيعات ذكي
echo    • حماية وأمان متقدم
echo    • دعم فني من المطور
echo.

echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy

echo.
echo 🎉 البرنامج جاهز للإطلاق التجاري!
echo.

echo اضغط أي مفتاح للخروج...
pause >nul
