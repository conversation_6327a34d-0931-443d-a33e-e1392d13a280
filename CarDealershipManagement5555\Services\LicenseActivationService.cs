using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Win32;

namespace CarDealershipManagement.Services
{
    /// <summary>
    /// خدمة تفعيل الترخيص والاشتراك
    /// </summary>
    public static class LicenseActivationService
    {
        private const string REGISTRY_KEY = @"SOFTWARE\CarDealershipManagement";
        private const string LICENSE_FILE = "license.dat";
        private const string ACTIVATION_KEY = "ActivationKey";
        private const string INSTALL_DATE_KEY = "InstallDate";
        private const string LAST_CHECK_KEY = "LastCheck";
        private const string MACHINE_ID_KEY = "MachineId";
        
        /// <summary>
        /// معلومات الترخيص
        /// </summary>
        public class LicenseInfo
        {
            public string LicenseKey { get; set; } = string.Empty;
            public string CustomerName { get; set; } = string.Empty;
            public string CustomerEmail { get; set; } = string.Empty;
            public DateTime IssueDate { get; set; }
            public DateTime ExpiryDate { get; set; }
            public LicenseType LicenseType { get; set; }
            public string MachineId { get; set; } = string.Empty;
            public bool IsActivated { get; set; }
            public int MaxUsers { get; set; } = 1;
            public string Version { get; set; } = "1.0.0";
        }

        public enum LicenseType
        {
            Trial = 0,      // تجريبي - 30 يوم
            Monthly = 1,    // شهري
            Quarterly = 2,  // ربع سنوي
            Yearly = 3,     // سنوي
            Lifetime = 4    // مدى الحياة
        }

        /// <summary>
        /// نتيجة فحص الترخيص
        /// </summary>
        public class LicenseCheckResult
        {
            public bool IsValid { get; set; }
            public string Message { get; set; } = string.Empty;
            public int DaysRemaining { get; set; }
            public LicenseInfo? LicenseInfo { get; set; }
        }

        /// <summary>
        /// الحصول على معرف الجهاز الفريد
        /// </summary>
        public static string GetMachineId()
        {
            try
            {
                var machineInfo = Environment.MachineName + 
                                Environment.UserName + 
                                Environment.OSVersion.ToString();
                
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo));
                return Convert.ToBase64String(hash)[..16]; // أول 16 حرف
            }
            catch
            {
                return "DEFAULT_MACHINE_ID";
            }
        }

        /// <summary>
        /// تثبيت البرنامج لأول مرة
        /// </summary>
        public static bool InstallProgram()
        {
            try
            {
                using var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY);
                if (key != null)
                {
                    var machineId = GetMachineId();
                    var installDate = DateTime.Now;
                    
                    key.SetValue(MACHINE_ID_KEY, machineId);
                    key.SetValue(INSTALL_DATE_KEY, installDate.ToBinary());
                    key.SetValue(LAST_CHECK_KEY, installDate.ToBinary());
                    
                    // إنشاء ترخيص تجريبي لمدة 30 يوم
                    var trialLicense = new LicenseInfo
                    {
                        LicenseKey = GenerateTrialKey(machineId),
                        CustomerName = "مستخدم تجريبي",
                        CustomerEmail = "<EMAIL>",
                        IssueDate = installDate,
                        ExpiryDate = installDate.AddDays(30),
                        LicenseType = LicenseType.Trial,
                        MachineId = machineId,
                        IsActivated = true,
                        MaxUsers = 1,
                        Version = "1.0.0"
                    };
                    
                    SaveLicenseToFile(trialLicense);
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تثبيت البرنامج: {ex.Message}");
            }
            
            return false;
        }

        /// <summary>
        /// فحص حالة الترخيص
        /// </summary>
        public static LicenseCheckResult CheckLicense()
        {
            try
            {
                var license = LoadLicenseFromFile();
                if (license == null)
                {
                    return new LicenseCheckResult
                    {
                        IsValid = false,
                        Message = "❌ لم يتم العثور على ترخيص صالح.\nيرجى تفعيل البرنامج أو الاتصال بالدعم الفني.",
                        DaysRemaining = 0
                    };
                }

                // التحقق من معرف الجهاز
                var currentMachineId = GetMachineId();
                if (license.MachineId != currentMachineId)
                {
                    return new LicenseCheckResult
                    {
                        IsValid = false,
                        Message = "❌ الترخيص غير صالح لهذا الجهاز.\nيرجى الاتصال بالدعم الفني لنقل الترخيص.",
                        DaysRemaining = 0
                    };
                }

                // التحقق من تاريخ انتهاء الصلاحية
                var now = DateTime.Now;
                var daysRemaining = (license.ExpiryDate - now).Days;

                if (now > license.ExpiryDate)
                {
                    return new LicenseCheckResult
                    {
                        IsValid = false,
                        Message = $"❌ انتهت صلاحية الترخيص في {license.ExpiryDate:yyyy/MM/dd}.\nيرجى تجديد الاشتراك.",
                        DaysRemaining = 0,
                        LicenseInfo = license
                    };
                }

                // ترخيص صالح
                var licenseTypeText = license.LicenseType switch
                {
                    LicenseType.Trial => "تجريبي",
                    LicenseType.Monthly => "شهري",
                    LicenseType.Quarterly => "ربع سنوي",
                    LicenseType.Yearly => "سنوي",
                    LicenseType.Lifetime => "مدى الحياة",
                    _ => "غير محدد"
                };

                var message = license.LicenseType == LicenseType.Lifetime 
                    ? $"✅ الترخيص صالح ({licenseTypeText})\nمرحباً {license.CustomerName}"
                    : $"✅ الترخيص صالح ({licenseTypeText})\nمرحباً {license.CustomerName}\nمتبقي: {daysRemaining} يوم";

                return new LicenseCheckResult
                {
                    IsValid = true,
                    Message = message,
                    DaysRemaining = daysRemaining,
                    LicenseInfo = license
                };
            }
            catch (Exception ex)
            {
                return new LicenseCheckResult
                {
                    IsValid = false,
                    Message = $"❌ خطأ في فحص الترخيص: {ex.Message}",
                    DaysRemaining = 0
                };
            }
        }

        /// <summary>
        /// تفعيل ترخيص جديد
        /// </summary>
        public static bool ActivateLicense(string licenseKey, string customerName, string customerEmail)
        {
            try
            {
                // التحقق من صحة مفتاح الترخيص
                if (!ValidateLicenseKey(licenseKey))
                {
                    return false;
                }

                var machineId = GetMachineId();
                var licenseInfo = ParseLicenseKey(licenseKey, customerName, customerEmail, machineId);
                
                if (licenseInfo != null)
                {
                    SaveLicenseToFile(licenseInfo);
                    
                    // تحديث السجل
                    using var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY);
                    key?.SetValue(ACTIVATION_KEY, licenseKey);
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تفعيل الترخيص: {ex.Message}");
            }
            
            return false;
        }

        /// <summary>
        /// إنشاء مفتاح تجريبي
        /// </summary>
        private static string GenerateTrialKey(string machineId)
        {
            var data = $"TRIAL-{machineId}-{DateTime.Now:yyyyMMdd}";
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
            return $"TRIAL-{Convert.ToBase64String(hash)[..12]}";
        }

        /// <summary>
        /// التحقق من صحة مفتاح الترخيص
        /// </summary>
        private static bool ValidateLicenseKey(string licenseKey)
        {
            if (string.IsNullOrWhiteSpace(licenseKey))
                return false;

            // التحقق من تنسيق المفتاح
            var parts = licenseKey.Split('-');
            return parts.Length >= 3;
        }

        /// <summary>
        /// تحليل مفتاح الترخيص
        /// </summary>
        private static LicenseInfo? ParseLicenseKey(string licenseKey, string customerName, string customerEmail, string machineId)
        {
            try
            {
                var parts = licenseKey.Split('-');
                if (parts.Length < 3) return null;

                var licenseType = parts[0] switch
                {
                    "TRIAL" => LicenseType.Trial,
                    "MONTH" => LicenseType.Monthly,
                    "QUARTER" => LicenseType.Quarterly,
                    "YEAR" => LicenseType.Yearly,
                    "LIFE" => LicenseType.Lifetime,
                    _ => LicenseType.Trial
                };

                var issueDate = DateTime.Now;
                var expiryDate = licenseType switch
                {
                    LicenseType.Trial => issueDate.AddDays(30),
                    LicenseType.Monthly => issueDate.AddMonths(1),
                    LicenseType.Quarterly => issueDate.AddMonths(3),
                    LicenseType.Yearly => issueDate.AddYears(1),
                    LicenseType.Lifetime => issueDate.AddYears(100),
                    _ => issueDate.AddDays(30)
                };

                return new LicenseInfo
                {
                    LicenseKey = licenseKey,
                    CustomerName = customerName,
                    CustomerEmail = customerEmail,
                    IssueDate = issueDate,
                    ExpiryDate = expiryDate,
                    LicenseType = licenseType,
                    MachineId = machineId,
                    IsActivated = true,
                    MaxUsers = licenseType == LicenseType.Lifetime ? 10 : 1,
                    Version = "1.0.0"
                };
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// حفظ الترخيص في ملف
        /// </summary>
        private static void SaveLicenseToFile(LicenseInfo license)
        {
            try
            {
                var json = JsonSerializer.Serialize(license, new JsonSerializerOptions { WriteIndented = true });
                var encrypted = EncryptString(json);
                File.WriteAllText(LICENSE_FILE, encrypted);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الترخيص: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الترخيص من ملف
        /// </summary>
        private static LicenseInfo? LoadLicenseFromFile()
        {
            try
            {
                if (!File.Exists(LICENSE_FILE))
                    return null;

                var encrypted = File.ReadAllText(LICENSE_FILE);
                var json = DecryptString(encrypted);
                return JsonSerializer.Deserialize<LicenseInfo>(json);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تشفير النص
        /// </summary>
        private static string EncryptString(string text)
        {
            try
            {
                var key = Encoding.UTF8.GetBytes("CarDealership2024Key!")[..16];
                using var aes = Aes.Create();
                aes.Key = key;
                aes.GenerateIV();
                
                using var encryptor = aes.CreateEncryptor();
                var encrypted = encryptor.TransformFinalBlock(Encoding.UTF8.GetBytes(text), 0, text.Length);
                
                var result = new byte[aes.IV.Length + encrypted.Length];
                Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                Array.Copy(encrypted, 0, result, aes.IV.Length, encrypted.Length);
                
                return Convert.ToBase64String(result);
            }
            catch
            {
                return text; // في حالة فشل التشفير، إرجاع النص كما هو
            }
        }

        /// <summary>
        /// فك تشفير النص
        /// </summary>
        private static string DecryptString(string encryptedText)
        {
            try
            {
                var key = Encoding.UTF8.GetBytes("CarDealership2024Key!")[..16];
                var data = Convert.FromBase64String(encryptedText);
                
                using var aes = Aes.Create();
                aes.Key = key;
                
                var iv = new byte[16];
                var encrypted = new byte[data.Length - 16];
                Array.Copy(data, 0, iv, 0, 16);
                Array.Copy(data, 16, encrypted, 0, encrypted.Length);
                
                aes.IV = iv;
                using var decryptor = aes.CreateDecryptor();
                var decrypted = decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length);
                
                return Encoding.UTF8.GetString(decrypted);
            }
            catch
            {
                return encryptedText; // في حالة فشل فك التشفير، إرجاع النص كما هو
            }
        }

        /// <summary>
        /// إلغاء تفعيل الترخيص
        /// </summary>
        public static bool DeactivateLicense()
        {
            try
            {
                if (File.Exists(LICENSE_FILE))
                {
                    File.Delete(LICENSE_FILE);
                }
                
                using var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY, true);
                key?.DeleteValue(ACTIVATION_KEY, false);
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على معلومات التثبيت
        /// </summary>
        public static DateTime? GetInstallDate()
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY);
                var value = key?.GetValue(INSTALL_DATE_KEY);
                if (value != null && long.TryParse(value.ToString(), out var binary))
                {
                    return DateTime.FromBinary(binary);
                }
            }
            catch { }
            
            return null;
        }
    }
}
