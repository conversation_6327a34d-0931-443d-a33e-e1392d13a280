using CarDealershipManagement.Services;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace CarDealershipManagement.Forms
{
    /// <summary>
    /// نافذة تأكيد حذف السيارة مع عرض تفاصيل العملية المالية
    /// </summary>
    public partial class CarDeletionConfirmationForm : Form
    {
        private readonly CarDeletionValidationResult validationResult;
        private Label lblTitle;
        private Panel pnlCarInfo;
        private Panel pnlSaleInfo;
        private Panel pnlInstallmentInfo;
        private Panel pnlWarning;
        private Panel pnlButtons;
        private Button btnConfirm;
        private Button btnCancel;
        private RichTextBox rtbDetails;

        public CarDeletionConfirmationForm(CarDeletionValidationResult validation)
        {
            validationResult = validation;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "تأكيد حذف السيارة مع ضمان سلامة البيانات المالية";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Segoe UI", 9F);

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // Title
            lblTitle = new Label
            {
                Text = "🔒 حذف آمن للسيارة مع ضمان سلامة البيانات المالية",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40,
                BackColor = Color.White
            };

            // Details RichTextBox
            rtbDetails = new RichTextBox
            {
                ReadOnly = true,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White,
                Font = new Font("Segoe UI", 9F),
                Location = new Point(20, 60),
                Size = new Size(640, 400)
            };

            // Warning Panel
            pnlWarning = new Panel
            {
                Location = new Point(20, 480),
                Size = new Size(640, 60),
                BackColor = Color.FromArgb(255, 243, 205),
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblWarning = new Label
            {
                Text = "⚠️ تحذير: سيتم تسجيل العملية في سجل المبيعات المؤرشف قبل الحذف لضمان سلامة البيانات المالية والمحاسبية.",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(133, 100, 4),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            pnlWarning.Controls.Add(lblWarning);

            // Buttons
            btnConfirm = new Button
            {
                Text = "✅ تأكيد الحذف",
                Size = new Size(120, 35),
                Location = new Point(420, 550),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                DialogResult = DialogResult.OK
            };
            btnConfirm.FlatAppearance.BorderSize = 0;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Size = new Size(120, 35),
                Location = new Point(550, 550),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                DialogResult = DialogResult.Cancel
            };
            btnCancel.FlatAppearance.BorderSize = 0;
        }

        private void LayoutControls()
        {
            this.Controls.AddRange(new Control[]
            {
                lblTitle,
                rtbDetails,
                pnlWarning,
                btnConfirm,
                btnCancel
            });
        }

        private void LoadData()
        {
            var details = BuildDetailedMessage();
            rtbDetails.Text = details;

            // Apply formatting
            FormatRichTextBox();
        }

        private string BuildDetailedMessage()
        {
            var message = "";

            // Car Information
            if (validationResult.CarInfo != null)
            {
                message += "🚗 معلومات السيارة:\n";
                message += "═══════════════════════════════════════\n";
                message += $"رقم الشاسيه: {validationResult.CarInfo.ChassisNumber}\n";
                message += $"الماركة والموديل: {validationResult.CarInfo.Brand} {validationResult.CarInfo.Model}\n";
                message += $"السنة: {validationResult.CarInfo.Year}\n";
                message += $"سعر الشراء: {validationResult.CarInfo.PurchasePrice:N0} ج.م\n";
                message += $"سعر البيع: {validationResult.CarInfo.SellPrice:N0} ج.م\n";
                message += $"حالة البيع: {(validationResult.CarInfo.IsSold ? "مباعة" : "غير مباعة")}\n\n";
            }

            // Sale Information
            if (validationResult.SaleInfo != null)
            {
                message += "💰 معلومات البيع:\n";
                message += "═══════════════════════════════════════\n";
                message += $"رقم البيع: {validationResult.SaleInfo.SaleId}\n";
                message += $"تاريخ البيع: {validationResult.SaleInfo.SaleDate:yyyy/MM/dd}\n";
                message += $"اسم العميل: {validationResult.SaleInfo.CustomerName}\n";
                message += $"سعر البيع الفعلي: {validationResult.SaleInfo.ActualSellPrice:N0} ج.م\n";
                message += $"طريقة الدفع: {(validationResult.SaleInfo.PaymentMethod == Models.PaymentMethod.Cash ? "نقدي" : "تقسيط")}\n";
                message += $"إجمالي المدفوع: {validationResult.SaleInfo.TotalPaid:N0} ج.م\n";
                message += $"المبلغ المتبقي: {validationResult.SaleInfo.RemainingAmount:N0} ج.م\n";
                message += $"حالة الدفع: {GetPaymentStatusText(validationResult.SaleInfo.PaymentStatus)}\n\n";
            }

            // Installment Information
            if (validationResult.InstallmentInfo != null)
            {
                message += "📅 معلومات الأقساط:\n";
                message += "═══════════════════════════════════════\n";
                message += $"إجمالي عدد الأقساط: {validationResult.InstallmentInfo.TotalInstallments}\n";
                message += $"الأقساط المدفوعة: {validationResult.InstallmentInfo.PaidInstallments}\n";
                message += $"الأقساط المتبقية: {validationResult.InstallmentInfo.PendingInstallments}\n";
                message += $"إجمالي قيمة الأقساط: {validationResult.InstallmentInfo.TotalAmount:N0} ج.م\n";
                message += $"المبلغ المدفوع: {validationResult.InstallmentInfo.PaidAmount:N0} ج.م\n";
                message += $"المبلغ المتبقي: {validationResult.InstallmentInfo.RemainingAmount:N0} ج.م\n";
                message += $"حالة الأقساط: {(validationResult.InstallmentInfo.IsFullyPaid ? "مدفوعة بالكامل" : "غير مكتملة")}\n\n";
            }

            // Action Required
            message += "📋 الإجراء المطلوب:\n";
            message += "═══════════════════════════════════════\n";
            message += $"{validationResult.Reason}\n\n";

            // Warning Message
            if (!string.IsNullOrEmpty(validationResult.WarningMessage))
            {
                message += "⚠️ تحذير مهم:\n";
                message += "═══════════════════════════════════════\n";
                message += $"{validationResult.WarningMessage}\n\n";
            }

            // Financial Integrity Assurance
            message += "🔒 ضمان سلامة البيانات المالية:\n";
            message += "═══════════════════════════════════════\n";
            message += "• سيتم تسجيل جميع بيانات البيع في سجل المبيعات المؤرشف\n";
            message += "• سيتم حفظ تفاصيل الأقساط والمدفوعات\n";
            message += "• سيتم تسجيل تاريخ ووقت العملية\n";
            message += "• سيتم تسجيل المستخدم المسؤول عن العملية\n";
            message += "• ستبقى البيانات متاحة للمراجعة المحاسبية\n\n";

            message += "هذا الإجراء يضمن عدم فقدان أي بيانات مالية مهمة ويحافظ على سلامة النظام المحاسبي.";

            return message;
        }

        private void FormatRichTextBox()
        {
            // Apply different colors for different sections
            var text = rtbDetails.Text;
            
            // Headers in blue
            FormatSection("🚗 معلومات السيارة:", Color.FromArgb(0, 102, 204), FontStyle.Bold);
            FormatSection("💰 معلومات البيع:", Color.FromArgb(0, 102, 204), FontStyle.Bold);
            FormatSection("📅 معلومات الأقساط:", Color.FromArgb(0, 102, 204), FontStyle.Bold);
            FormatSection("📋 الإجراء المطلوب:", Color.FromArgb(0, 102, 204), FontStyle.Bold);
            FormatSection("⚠️ تحذير مهم:", Color.FromArgb(220, 53, 69), FontStyle.Bold);
            FormatSection("🔒 ضمان سلامة البيانات المالية:", Color.FromArgb(40, 167, 69), FontStyle.Bold);
        }

        private void FormatSection(string sectionTitle, Color color, FontStyle style)
        {
            var startIndex = rtbDetails.Text.IndexOf(sectionTitle);
            if (startIndex >= 0)
            {
                rtbDetails.Select(startIndex, sectionTitle.Length);
                rtbDetails.SelectionColor = color;
                rtbDetails.SelectionFont = new Font(rtbDetails.Font, style);
            }
        }

        private string GetPaymentStatusText(Models.PaymentStatus status)
        {
            return status switch
            {
                Models.PaymentStatus.Pending => "مستحقة",
                Models.PaymentStatus.PartiallyPaid => "مدفوعة جزئياً",
                Models.PaymentStatus.FullyPaid => "مدفوعة بالكامل",
                _ => "غير محدد"
            };
        }
    }
}
