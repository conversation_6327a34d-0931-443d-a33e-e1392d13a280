using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Helpers;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;

namespace CarDealershipManagement.Forms
{
    public partial class UserManagementForm : Form
    {
        private DataGridView dgvUsers = new DataGridView();
        private TextBox txtSearch = new TextBox();
        private Button btnSearch = new Button();
        private Button btnAddUser = new Button();
        private Button btnEditUser = new Button();
        private Button btnDeleteUser = new Button();
        private Button btnResetPassword = new Button();
        private Button btnToggleStatus = new Button();
        private Button btnViewPermissions = new Button();
        private Button btnRefresh = new Button();
        private Button btnPrintUsers = new Button();
        private ComboBox cmbRoleFilter = new ComboBox();
        private ComboBox cmbStatusFilter = new ComboBox();
        private Label lblTotalUsers = new Label();
        private Label lblActiveUsers = new Label();
        private Label lblInactiveUsers = new Label();
        private Panel pnlMain = new Panel();
        private Panel pnlHeader = new Panel();
        private Panel pnlSearch = new Panel();
        private Panel pnlActions = new Panel();
        private Panel pnlStats = new Panel();
        private PictureBox pbUserIcon = new PictureBox();
        private User? currentUser;

        public UserManagementForm(User? currentUser = null)
        {
            this.currentUser = currentUser;
            InitializeComponent();
            _ = Task.Run(async () =>
            {
                await LoadData();
                await LoadStatistics();
            });
        }

        private void InitializeComponent()
        {
            this.Text = "👥 إدارة المستخدمين - User Management";
            this.Font = new Font("Segoe UI", 10F);
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1200, 700);

            // تطبيق إعدادات النافذة المرنة للشاشة الكاملة
            ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this,
                Screen.PrimaryScreen.WorkingArea.Width,
                Screen.PrimaryScreen.WorkingArea.Height);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Main Panel - نفس تنسيق InventoryForm
            pnlMain = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(15)
            };

            // Header Panel (Logo + Title) - نفس تنسيق InventoryForm
            pnlHeader = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            // Logo - نفس تنسيق InventoryForm
            pbUserIcon = new PictureBox
            {
                Location = new Point(20, 10),
                Size = new Size(60, 60),
                SizeMode = PictureBoxSizeMode.Zoom,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.LightGray
            };

            // Title Label - نفس تنسيق InventoryForm
            var lblTitle = new Label
            {
                Text = "إدارة المستخدمين",
                Location = new Point(100, 25),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204),
                BackColor = Color.Transparent
            };

            // Statistics panel
            pnlStats = new Panel
            {
                Location = new Point(300, 10),
                Size = new Size(800, 50),
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            lblTotalUsers = new Label
            {
                Text = "إجمالي المستخدمين: 0",
                Location = new Point(10, 15),
                Size = new Size(180, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204)
            };

            lblActiveUsers = new Label
            {
                Text = "المستخدمين النشطين: 0",
                Location = new Point(200, 15),
                Size = new Size(180, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(40, 167, 69)
            };

            lblInactiveUsers = new Label
            {
                Text = "المستخدمين المعطلين: 0",
                Location = new Point(390, 15),
                Size = new Size(180, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            pnlStats.Controls.AddRange(new Control[] { lblTotalUsers, lblActiveUsers, lblInactiveUsers });
            pnlHeader.Controls.AddRange(new Control[] { pbUserIcon, lblTitle, pnlStats });

            // Search and Filter panel
            pnlSearch = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(10)
            };

            var lblSearch = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 15),
                Size = new Size(60, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204)
            };

            txtSearch = new TextBox
            {
                Location = new Point(80, 12),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 9F),
                BorderStyle = BorderStyle.FixedSingle
            };

            btnSearch = CreateStyledButton("بحث", 290, 11, 80, 28, Color.FromArgb(0, 102, 204));
            btnSearch.Click += BtnSearch_Click;

            var lblRole = new Label
            {
                Text = "الدور:",
                Location = new Point(390, 15),
                Size = new Size(60, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204)
            };

            cmbRoleFilter = new ComboBox
            {
                Location = new Point(460, 12),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F)
            };
            cmbRoleFilter.Items.AddRange(new[] { "الكل", "مطور النظام", "مدير النشاط", "مندوب مبيعات" });
            cmbRoleFilter.SelectedIndex = 0;
            cmbRoleFilter.SelectedIndexChanged += async (s, e) => await LoadData();

            var lblStatus = new Label
            {
                Text = "الحالة:",
                Location = new Point(630, 15),
                Size = new Size(60, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204)
            };

            cmbStatusFilter = new ComboBox
            {
                Location = new Point(700, 12),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F)
            };
            cmbStatusFilter.Items.AddRange(new[] { "الكل", "نشط", "معطل" });
            cmbStatusFilter.SelectedIndex = 0;
            cmbStatusFilter.SelectedIndexChanged += async (s, e) => await LoadData();

            pnlSearch.Controls.AddRange(new Control[]
            {
                lblSearch, txtSearch, btnSearch, lblRole, cmbRoleFilter, lblStatus, cmbStatusFilter
            });

            // Action buttons panel
            pnlActions = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(10)
            };

            btnAddUser = CreateStyledButton("إضافة مستخدم", 10, 8, 120, 35, Color.FromArgb(40, 167, 69));
            btnAddUser.Click += BtnAddUser_Click;

            btnEditUser = CreateStyledButton("تعديل", 140, 8, 120, 35, Color.FromArgb(255, 193, 7));
            btnEditUser.ForeColor = Color.Black;
            btnEditUser.Click += BtnEditUser_Click;

            btnDeleteUser = CreateStyledButton("حذف", 270, 8, 120, 35, Color.FromArgb(220, 53, 69));
            btnDeleteUser.Click += BtnDeleteUser_Click;

            btnResetPassword = CreateStyledButton("إعادة تعيين كلمة المرور", 400, 8, 150, 35, Color.FromArgb(108, 117, 125));
            btnResetPassword.Click += BtnResetPassword_Click;

            btnToggleStatus = CreateStyledButton("تغيير الحالة", 560, 8, 120, 35, Color.FromArgb(23, 162, 184));
            btnToggleStatus.Click += BtnToggleStatus_Click;

            btnViewPermissions = CreateStyledButton("عرض الصلاحيات", 690, 8, 120, 35, Color.FromArgb(0, 123, 255));
            btnViewPermissions.Click += BtnViewPermissions_Click;

            btnRefresh = CreateStyledButton("تحديث", 820, 8, 120, 35, Color.FromArgb(0, 102, 204));
            btnRefresh.Click += async (s, e) =>
            {
                await LoadData();
                await LoadStatistics();
            };

            btnPrintUsers = CreateStyledButton("🖨️ طباعة", 950, 8, 120, 35, Color.FromArgb(75, 0, 130));
            btnPrintUsers.Click += BtnPrintUsers_Click;

            pnlActions.Controls.AddRange(new Control[]
            {
                btnAddUser, btnEditUser, btnDeleteUser, btnResetPassword,
                btnToggleStatus, btnViewPermissions, btnRefresh, btnPrintUsers
            });

            // DataGridView
            dgvUsers = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F),
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(0, 102, 204),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Padding = new Padding(5)
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.Black,
                    SelectionBackColor = Color.FromArgb(173, 216, 230),
                    SelectionForeColor = Color.Black,
                    Font = new Font("Segoe UI", 9F),
                    Padding = new Padding(5)
                }
            };

            // Add event handlers
            dgvUsers.SelectionChanged += DgvUsers_SelectionChanged;

            // Add panels to main container
            pnlMain.Controls.Add(dgvUsers);
            pnlMain.Controls.Add(pnlActions);
            pnlMain.Controls.Add(pnlSearch);
            pnlMain.Controls.Add(pnlHeader);

            this.Controls.Add(pnlMain);

            // Apply responsive layout
            ResponsiveLayoutHelper.ApplyResponsiveDataGridSettings(dgvUsers, pnlMain);
            ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this);
        }

        private async Task LoadData()
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var query = context.Users.Include(u => u.Permissions).AsQueryable();

                // Apply search filter
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    var searchTerm = txtSearch.Text.ToLower();
                    query = query.Where(u => u.Username.ToLower().Contains(searchTerm) ||
                                        u.FullName.ToLower().Contains(searchTerm));
                }

                // Apply role filter
                if (cmbRoleFilter.SelectedIndex > 0)
                {
                    var role = cmbRoleFilter.SelectedIndex switch
                    {
                        1 => UserRole.Developer,
                        2 => UserRole.Manager,
                        3 => UserRole.SalesRepresentative,
                        _ => UserRole.SalesRepresentative
                    };
                    query = query.Where(u => u.Role == role);
                }

                // Apply status filter
                if (cmbStatusFilter.SelectedIndex > 0)
                {
                    var isActive = cmbStatusFilter.SelectedIndex == 1;
                    query = query.Where(u => u.IsActive == isActive);
                }

                var users = await query.OrderBy(u => u.Username).ToListAsync();

                dgvUsers.DataSource = users.Select(u => new
                {
                    معرف_المستخدم = u.UserId,
                    اسم_المستخدم = u.Username,
                    الاسم_الكامل = u.FullName,
                    الدور = GetRoleDisplayName(u.Role),
                    الحالة = u.IsActive ? "نشط" : "معطل",
                    تاريخ_الإنشاء = u.CreatedDate.ToString("yyyy-MM-dd"),
                    آخر_تسجيل_دخول = u.LastLoginDate?.ToString("yyyy-MM-dd HH:mm") ?? "لم يسجل دخول"
                }).ToList();

                // Color rows based on status
                dgvUsers.RowsAdded += (s, e) =>
                {
                    for (int i = 0; i < dgvUsers.Rows.Count; i++)
                    {
                        var statusCell = dgvUsers.Rows[i].Cells["الحالة"];
                        if (statusCell.Value?.ToString() == "معطل")
                        {
                            dgvUsers.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(255, 240, 245);
                            dgvUsers.Rows[i].DefaultCellStyle.ForeColor = Color.FromArgb(220, 53, 69);
                        }
                        else
                        {
                            dgvUsers.Rows[i].DefaultCellStyle.BackColor = Color.FromArgb(240, 255, 245);
                            dgvUsers.Rows[i].DefaultCellStyle.ForeColor = Color.FromArgb(40, 167, 69);
                        }
                    }
                };

                // Update button states after loading data
                UpdateButtonStates();
            }, "تحميل بيانات المستخدمين");
        }

        private async Task LoadStatistics()
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var totalUsers = await context.Users.CountAsync();
                var activeUsers = await context.Users.CountAsync(u => u.IsActive);
                var inactiveUsers = totalUsers - activeUsers;

                lblTotalUsers.Text = $"إجمالي المستخدمين: {totalUsers}";
                lblActiveUsers.Text = $"المستخدمين النشطين: {activeUsers}";
                lblInactiveUsers.Text = $"المستخدمين المعطلين: {inactiveUsers}";
            }, "تحميل إحصائيات المستخدمين");
        }

        private string GetRoleDisplayName(UserRole role)
        {
            return role switch
            {
                UserRole.Developer => "مطور النظام",
                UserRole.Manager => "مدير النشاط",
                UserRole.SalesRepresentative => "مندوب مبيعات",
                _ => "غير محدد"
            };
        }

        private void DgvUsers_SelectionChanged(object? sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = dgvUsers.SelectedRows.Count > 0;
            bool canManageSelectedUser = true;

            if (hasSelection)
            {
                var selectedUserId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["معرف_المستخدم"].Value);
                var selectedUsername = dgvUsers.SelectedRows[0].Cells["اسم_المستخدم"].Value?.ToString();
                var selectedRole = dgvUsers.SelectedRows[0].Cells["الدور"].Value?.ToString();

                // Check if current user is Manager trying to manage Developer
                if (currentUser?.Role == UserRole.Manager && selectedRole == "مطور النظام")
                {
                    canManageSelectedUser = false;
                }

                // Check if trying to manage default developer
                if (selectedUsername == "amrali" && selectedRole == "مطور النظام")
                {
                    canManageSelectedUser = false;
                }

                // Check if trying to manage own account for delete/disable
                if (currentUser != null && selectedUserId == currentUser.UserId)
                {
                    canManageSelectedUser = false;
                }
            }

            // Update button states
            btnEditUser.Enabled = hasSelection && canManageSelectedUser;
            btnDeleteUser.Enabled = hasSelection && canManageSelectedUser;
            btnToggleStatus.Enabled = hasSelection && canManageSelectedUser;
            btnResetPassword.Enabled = hasSelection; // Password reset is always allowed
            btnViewPermissions.Enabled = hasSelection;

            // Update button colors based on state
            UpdateButtonAppearance(btnEditUser, btnEditUser.Enabled);
            UpdateButtonAppearance(btnDeleteUser, btnDeleteUser.Enabled);
            UpdateButtonAppearance(btnToggleStatus, btnToggleStatus.Enabled);
        }

        private void UpdateButtonAppearance(Button button, bool enabled)
        {
            if (enabled)
            {
                button.BackColor = Color.FromArgb(0, 102, 204);
                button.ForeColor = Color.White;
            }
            else
            {
                button.BackColor = Color.Gray;
                button.ForeColor = Color.LightGray;
            }
        }

        private async void BtnSearch_Click(object? sender, EventArgs e)
        {
            await LoadData();
        }

        private async void BtnAddUser_Click(object? sender, EventArgs e)
        {
            var addUserForm = new AddEditUserForm(null, currentUser);
            if (addUserForm.ShowDialog() == DialogResult.OK)
            {
                await LoadData();
                await LoadStatistics();
            }
        }

        private async void BtnEditUser_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var userId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["معرف_المستخدم"].Value);
                var editUserForm = new AddEditUserForm(userId, currentUser);
                if (editUserForm.ShowDialog() == DialogResult.OK)
                {
                    await LoadData();
                    await LoadStatistics();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مستخدم للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnDeleteUser_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var userId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["معرف_المستخدم"].Value);
                var username = dgvUsers.SelectedRows[0].Cells["اسم_المستخدم"].Value?.ToString();

                var result = MessageBox.Show($"هل أنت متأكد من حذف المستخدم \'{username}\'؟\nسيتم حذف جميع الصلاحيات المرتبطة به أيضاً.",
                                             "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    await ErrorHandlingService.TryExecute(async () =>
                    {
                        using var context = DbContextFactory.CreateContext();

                        var user = await context.Users.Include(u => u.Permissions).FirstOrDefaultAsync(u => u.UserId == userId);
                        if (user != null)
                        {
                            // Check if it's the developer user (should not be deleted)
                            if (user.Role == UserRole.Developer && user.Username == "amrali")
                            {
                                MessageBox.Show("❌ لا يمكن حذف مستخدم المطور الافتراضي!\n\n" +
                                               "🔒 المطور (amrali) هو أعلى مستوى في النظام ولا يمكن حذفه.\n" +
                                               "هذا الحساب محمي لضمان استمرارية عمل النظام.",
                                               "حماية المطور", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                return;
                            }

                            // Check if current user is Manager trying to delete Developer
                            if (currentUser?.Role == UserRole.Manager && user.Role == UserRole.Developer)
                            {
                                MessageBox.Show("❌ ليس لديك صلاحية لحذف المطورين!\n\n" +
                                               "🔒 مدير النشاط لا يمكنه حذف المطورين.\n" +
                                               "المطورون لديهم صلاحيات أعلى من مدير النشاط.",
                                               "صلاحيات غير كافية", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                return;
                            }

                            // Check if deleting own account
                            if (currentUser != null && user.UserId == currentUser.UserId)
                            {
                                MessageBox.Show("لا يمكنك حذف حسابك الخاص", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                return;
                            }

                            if (user.Permissions != null)
                            {
                                context.UserPermissions.Remove(user.Permissions);
                            }
                            context.Users.Remove(user);
                            await context.SaveChangesAsync();

                            MessageBox.Show("تم حذف المستخدم بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            await LoadData();
                            await LoadStatistics();
                        }
                    }, "حذف المستخدم");
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مستخدم للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnResetPassword_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var userId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["معرف_المستخدم"].Value);
                var username = dgvUsers.SelectedRows[0].Cells["اسم_المستخدم"].Value?.ToString();

                var resetForm = new PasswordResetForm(userId, username!);
                if (resetForm.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم إعادة تعيين كلمة المرور بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مستخدم لإعادة تعيين كلمة المرور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnToggleStatus_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var userId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["معرف_المستخدم"].Value);
                var username = dgvUsers.SelectedRows[0].Cells["اسم_المستخدم"].Value?.ToString();
                var currentStatus = dgvUsers.SelectedRows[0].Cells["الحالة"].Value?.ToString();

                await ErrorHandlingService.TryExecute(async () =>
                {
                    using var context = DbContextFactory.CreateContext();

                    var user = await context.Users.FindAsync(userId);
                    if (user != null)
                    {
                        // Check if it's the developer user (should not be deactivated)
                        if (user.Role == UserRole.Developer && user.Username == "amrali")
                        {
                            MessageBox.Show("❌ لا يمكن تعطيل مستخدم المطور الافتراضي!\n\n" +
                                           "🔒 المطور (amrali) هو أعلى مستوى في النظام ويجب أن يبقى نشطاً.\n" +
                                           "هذا الحساب محمي لضمان استمرارية عمل النظام.",
                                           "حماية المطور", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }

                        // Check if current user is Manager trying to deactivate Developer
                        if (currentUser?.Role == UserRole.Manager && user.Role == UserRole.Developer)
                        {
                            MessageBox.Show("❌ ليس لديك صلاحية لتعطيل المطورين!\n\n" +
                                           "🔒 مدير النشاط لا يمكنه تعطيل المطورين.\n" +
                                           "المطورون لديهم صلاحيات أعلى من مدير النشاط.",
                                           "صلاحيات غير كافية", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }

                        // Check if disabling own account
                        if (currentUser != null && user.UserId == currentUser.UserId && user.IsActive)
                        {
                            MessageBox.Show("لا يمكنك تعطيل حسابك الخاص", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        user.IsActive = !user.IsActive;
                        user.ModifiedDate = DateTime.Now;
                        await context.SaveChangesAsync();

                        var newStatus = user.IsActive ? "تفعيل" : "تعطيل";
                        MessageBox.Show($"تم {newStatus} المستخدم \'{username}\' بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        await LoadData();
                        await LoadStatistics();
                    }
                }, "تغيير حالة المستخدم");
            }
            else
            {
                MessageBox.Show("يرجى اختيار مستخدم لتغيير حالته", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnViewPermissions_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var userId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["معرف_المستخدم"].Value);
                var username = dgvUsers.SelectedRows[0].Cells["اسم_المستخدم"].Value?.ToString();

                var permissionsForm = new UserPermissionsViewForm(userId, username!);
                permissionsForm.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار مستخدم لعرض صلاحياته", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private Button CreateStyledButton(string text, int x, int y, int width, int height, Color backColor)
        {
            var button = new Button
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false
            };
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Dark(backColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backColor, 0.2f);
            return button;
        }

        private void BtnPrintUsers_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using var printForm = new PrintReportForm(dgvUsers);
            printForm.Text = "طباعة تقرير المستخدمين";
            printForm.ShowDialog();
        }
    }
}


