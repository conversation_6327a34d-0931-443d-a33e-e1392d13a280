using System;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;
using System.IO;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class EnhancedReportPrintForm : Form
    {
        private readonly DataGridView sourceDataGridView;
        private readonly string reportTitle;

        private PrintDocument printDocument;
        private PrintPreviewDialog printPreviewDialog;
        private PageSetupDialog pageSetupDialog;
        private PrintDialog printDialog;

        // Company settings
        private SystemSettings companySettings;
        private Image companyLogo;

        // Print settings
        private int currentRow = 0;
        private bool hasMorePages = false;
        private readonly int rowsPerPage = 30;
        private readonly Font headerFont = new Font("Segoe UI", 18F, FontStyle.Bold);
        private readonly Font titleFont = new Font("Segoe UI", 14F, FontStyle.Bold);
        private readonly Font normalFont = new Font("Segoe UI", 10F);
        private readonly Font smallFont = new Font("Segoe UI", 9F);

        public EnhancedReportPrintForm(DataGridView dataGridView, string reportTitle)
        {
            this.sourceDataGridView = dataGridView;
            this.reportTitle = reportTitle;

            LoadCompanySettings();
            InitializeComponent();
            SetupPrinting();
        }

        private void InitializeComponent()
        {
            // Form settings
            Text = $"طباعة {reportTitle}";
            Size = new Size(500, 400);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            BackColor = Color.White;

            // Create main panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // Title
            var lblTitle = new Label
            {
                Text = $"خيارات طباعة {reportTitle}",
                Location = new Point(20, 20),
                Size = new Size(400, 30),
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Preview button
            var btnPreview = new Button
            {
                Text = "👁️ معاينة الطباعة",
                Location = new Point(50, 80),
                Size = new Size(180, 50),
                BackColor = Color.FromArgb(0, 102, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnPreview.FlatAppearance.BorderSize = 0;
            btnPreview.Click += BtnPreview_Click;

            // Print button
            var btnPrint = new Button
            {
                Text = "🖨️ طباعة مباشرة",
                Location = new Point(250, 80),
                Size = new Size(180, 50),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.Click += BtnPrint_Click;

            // Page setup button
            var btnPageSetup = new Button
            {
                Text = "⚙️ إعداد الصفحة",
                Location = new Point(50, 150),
                Size = new Size(180, 50),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnPageSetup.FlatAppearance.BorderSize = 0;
            btnPageSetup.Click += BtnPageSetup_Click;

            // Export to PDF button
            var btnExportPDF = new Button
            {
                Text = "📄 تصدير PDF",
                Location = new Point(250, 150),
                Size = new Size(180, 50),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnExportPDF.FlatAppearance.BorderSize = 0;
            btnExportPDF.Click += BtnExportPDF_Click;

            // Info label
            var lblInfo = new Label
            {
                Text = $"عدد الصفوف: {sourceDataGridView.Rows.Count}\nعدد الأعمدة: {sourceDataGridView.Columns.Count}",
                Location = new Point(50, 220),
                Size = new Size(380, 40),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(108, 117, 125),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Close button
            var btnClose = new Button
            {
                Text = "❌ إغلاق",
                Location = new Point(175, 280),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += (s, e) => this.Close();

            mainPanel.Controls.AddRange(new Control[] 
            { 
                lblTitle, btnPreview, btnPrint, btnPageSetup, btnExportPDF, lblInfo, btnClose 
            });

            this.Controls.Add(mainPanel);
        }

        private void LoadCompanySettings()
        {
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<CarDealershipContext>();
                optionsBuilder.UseSqlite("Data Source=CarDealership.db");

                using var context = new CarDealershipContext(optionsBuilder.Options);
                companySettings = context.SystemSettings.FirstOrDefault();

                // تحميل اللوجو إذا كان موجوداً
                if (companySettings != null && !string.IsNullOrEmpty(companySettings.CompanyLogo))
                {
                    string logoPath = Path.Combine(Application.StartupPath, companySettings.CompanyLogo);
                    if (File.Exists(logoPath))
                    {
                        companyLogo = Image.FromFile(logoPath);
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، استخدم القيم الافتراضية
                companySettings = new SystemSettings
                {
                    CompanyName = "معرض السيارات المتميز",
                    CompanyAddress = "شارع الملك فهد، الرياض، المملكة العربية السعودية",
                    CompanyPhone = "966-11-1234567+"
                };
            }
        }

        private void SetupPrinting()
        {
            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
            printDocument.BeginPrint += PrintDocument_BeginPrint;

            printPreviewDialog = new PrintPreviewDialog
            {
                Document = printDocument,
                WindowState = FormWindowState.Maximized,
                Text = $"معاينة طباعة {reportTitle}"
            };

            pageSetupDialog = new PageSetupDialog
            {
                Document = printDocument
            };

            printDialog = new PrintDialog
            {
                Document = printDocument
            };
        }

        private void PrintDocument_BeginPrint(object sender, PrintEventArgs e)
        {
            currentRow = 0;
            hasMorePages = false;
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;
            float yPosition = 50;
            float leftMargin = 50;
            float rightMargin = e.PageBounds.Width - 50;
            float pageWidth = rightMargin - leftMargin;

            // رسم اللوجو وبيانات المعرض في الأعلى
            DrawCompanyHeader(g, leftMargin, ref yPosition, pageWidth);

            // عنوان التقرير
            SizeF titleSize = g.MeasureString(reportTitle, headerFont);
            g.DrawString(reportTitle, headerFont, Brushes.Black, 
                leftMargin + (pageWidth - titleSize.Width) / 2, yPosition);
            yPosition += titleSize.Height + 20;

            // معلومات التقرير
            DrawReportInfo(g, leftMargin, ref yPosition, pageWidth);

            // رؤوس الأعمدة
            DrawTableHeaders(g, leftMargin, ref yPosition, pageWidth);

            // البيانات
            DrawTableData(g, leftMargin, ref yPosition, pageWidth, e.PageBounds.Height - 100);

            // التذييل
            DrawFooter(g, leftMargin, e.PageBounds.Height - 50, pageWidth);

            // تحديد ما إذا كانت هناك صفحات أخرى
            e.HasMorePages = hasMorePages;
        }

        private void DrawCompanyHeader(Graphics g, float leftMargin, ref float yPosition, float pageWidth)
        {
            // رسم مستطيل للرأسية
            var headerRect = new RectangleF(leftMargin, yPosition, pageWidth, 120);
            g.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), headerRect);
            g.DrawRectangle(new Pen(Color.FromArgb(0, 102, 204), 3), Rectangle.Round(headerRect));

            // رسم اللوجو على اليسار
            var logoRect = new RectangleF(leftMargin + 15, yPosition + 15, 80, 80);
            if (companyLogo != null)
            {
                // رسم اللوجو الفعلي
                g.DrawImage(companyLogo, logoRect);
                g.DrawRectangle(new Pen(Color.FromArgb(0, 102, 204), 2), Rectangle.Round(logoRect));
            }
            else
            {
                // رسم مربع ملون كبديل
                g.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), logoRect);
                g.DrawString("LOGO", new Font("Segoe UI", 12F, FontStyle.Bold),
                    Brushes.White, logoRect.X + 20, logoRect.Y + 30);
            }

            // اسم المعرض في الوسط
            string companyName = companySettings?.CompanyName ?? "معرض السيارات المتميز";
            var companyNameFont = new Font("Segoe UI", 18F, FontStyle.Bold);
            SizeF nameSize = g.MeasureString(companyName, companyNameFont);
            float centerX = leftMargin + (pageWidth - nameSize.Width) / 2;
            g.DrawString(companyName, companyNameFont, Brushes.Black, centerX, yPosition + 25);

            // بيانات الشركة على اليمين (محاذاة من اليمين لليسار)
            string companyAddress = companySettings?.CompanyAddress ?? "شارع الملك فهد، الرياض، المملكة العربية السعودية";
            string companyPhone = companySettings?.CompanyPhone ?? "966-11-1234567+";
            string companyEmail = "<EMAIL>";

            float rightMargin = leftMargin + pageWidth - 15;

            if (!string.IsNullOrEmpty(companyAddress))
            {
                SizeF addressSize = g.MeasureString($"العنوان: {companyAddress}", normalFont);
                g.DrawString($"العنوان: {companyAddress}", normalFont,
                    Brushes.Black, rightMargin - addressSize.Width, yPosition + 55);
            }

            if (!string.IsNullOrEmpty(companyPhone))
            {
                string contactInfo = $"الهاتف: {companyPhone} | البريد الإلكتروني: {companyEmail}";
                SizeF contactSize = g.MeasureString(contactInfo, normalFont);
                g.DrawString(contactInfo, normalFont,
                    Brushes.Black, rightMargin - contactSize.Width, yPosition + 80);
            }

            yPosition += 140;
        }

        private void DrawReportInfo(Graphics g, float leftMargin, ref float yPosition, float pageWidth)
        {
            // معلومات التقرير
            var infoRect = new RectangleF(leftMargin, yPosition, pageWidth, 50);
            g.FillRectangle(new SolidBrush(Color.FromArgb(240, 248, 255)), infoRect);
            g.DrawRectangle(Pens.Gray, Rectangle.Round(infoRect));

            string reportInfo = $"عدد السجلات: {sourceDataGridView.Rows.Count}";
            g.DrawString(reportInfo, titleFont, Brushes.Black, leftMargin + 15, yPosition + 15);
            
            string printDate = $"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}";
            SizeF printDateSize = g.MeasureString(printDate, normalFont);
            g.DrawString(printDate, normalFont, Brushes.Gray, 
                leftMargin + pageWidth - printDateSize.Width - 15, yPosition + 15);

            yPosition += 70;
        }

        private void DrawTableHeaders(Graphics g, float leftMargin, ref float yPosition, float pageWidth)
        {
            if (sourceDataGridView.Columns.Count == 0) return;

            float columnWidth = pageWidth / sourceDataGridView.Columns.Count;
            float currentX = leftMargin;

            var headerRect = new RectangleF(leftMargin, yPosition, pageWidth, 35);
            g.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), headerRect);

            foreach (DataGridViewColumn column in sourceDataGridView.Columns)
            {
                if (column.Visible)
                {
                    g.DrawString(column.HeaderText, new Font("Segoe UI", 10F, FontStyle.Bold), 
                        Brushes.White, currentX + 5, yPosition + 10);
                    g.DrawLine(Pens.White, currentX + columnWidth, yPosition, 
                        currentX + columnWidth, yPosition + 35);
                    currentX += columnWidth;
                }
            }

            yPosition += 40;
        }

        private void DrawTableData(Graphics g, float leftMargin, ref float yPosition, float pageWidth, float maxY)
        {
            if (sourceDataGridView.Rows.Count == 0)
            {
                g.DrawString("لا توجد بيانات للعرض", normalFont, Brushes.Gray, 
                    leftMargin + pageWidth / 2 - 50, yPosition + 20);
                return;
            }

            float columnWidth = pageWidth / sourceDataGridView.Columns.Count;
            int rowsDrawn = 0;

            for (int i = currentRow; i < sourceDataGridView.Rows.Count && yPosition < maxY - 30; i++)
            {
                DataGridViewRow row = sourceDataGridView.Rows[i];
                float currentX = leftMargin;

                // رسم خلفية الصف
                var rowRect = new RectangleF(leftMargin, yPosition, pageWidth, 25);
                if (i % 2 == 0)
                    g.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), rowRect);

                // رسم البيانات
                foreach (DataGridViewColumn column in sourceDataGridView.Columns)
                {
                    if (column.Visible)
                    {
                        string cellValue = row.Cells[column.Index].Value?.ToString() ?? "";
                        g.DrawString(cellValue, normalFont, Brushes.Black, currentX + 5, yPosition + 5);
                        g.DrawLine(Pens.LightGray, currentX + columnWidth, yPosition, 
                            currentX + columnWidth, yPosition + 25);
                        currentX += columnWidth;
                    }
                }

                g.DrawLine(Pens.LightGray, leftMargin, yPosition + 25, leftMargin + pageWidth, yPosition + 25);
                yPosition += 25;
                rowsDrawn++;
                currentRow++;
            }

            // تحديد ما إذا كانت هناك صفحات أخرى
            hasMorePages = currentRow < sourceDataGridView.Rows.Count;
        }

        private void DrawFooter(Graphics g, float leftMargin, float yPosition, float pageWidth)
        {
            string footer = $"تم إنشاء هذا التقرير بواسطة نظام إدارة معرض السيارات - {DateTime.Now:yyyy}";
            SizeF footerSize = g.MeasureString(footer, smallFont);
            g.DrawString(footer, smallFont, Brushes.Gray, 
                leftMargin + (pageWidth - footerSize.Width) / 2, yPosition);
        }

        private void BtnPreview_Click(object sender, EventArgs e)
        {
            try
            {
                currentRow = 0;
                printPreviewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء معاينة الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    currentRow = 0;
                    printDocument.Print();
                    MessageBox.Show("تم إرسال المستند للطباعة بنجاح!", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPageSetup_Click(object sender, EventArgs e)
        {
            try
            {
                pageSetupDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إعداد الصفحة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExportPDF_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "PDF Files|*.pdf",
                    FileName = $"{reportTitle}_{DateTime.Now:yyyy-MM-dd}.pdf"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("سيتم إضافة تصدير PDF في التحديث القادم!", "معلومات", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                printDocument?.Dispose();
                printPreviewDialog?.Dispose();
                pageSetupDialog?.Dispose();
                printDialog?.Dispose();
                headerFont?.Dispose();
                titleFont?.Dispose();
                normalFont?.Dispose();
                smallFont?.Dispose();
                companyLogo?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
