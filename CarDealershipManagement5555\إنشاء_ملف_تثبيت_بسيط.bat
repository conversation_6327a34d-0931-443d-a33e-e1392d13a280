@echo off
chcp 65001 >nul
title إنشاء ملف تثبيت بسيط - Amr Ali Elawamy

echo.
echo ========================================
echo   📦 إنشاء ملف تثبيت بسيط
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 سيتم إنشاء ملف تثبيت بسيط باستخدام PowerShell
echo    ✅ ضغط جميع الملفات في أرشيف واحد
echo    ✅ إنشاء ملف تثبيت تلقائي
echo    ✅ اختصارات سطح المكتب وقائمة البدء
echo.

set "SOURCE_DIR=%~dp0CarDealership_Debug_Copy"
set "SETUP_DIR=%~dp0SimpleSetup"

echo 🔍 التحقق من المتطلبات...

if not exist "%SOURCE_DIR%\CarDealershipManagement.exe" (
    echo ❌ البرنامج غير موجود في: %SOURCE_DIR%
    echo يرجى تشغيل "إنشاء_نسخة_من_Debug.bat" أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على البرنامج

echo.
echo 📁 إنشاء مجلد التثبيت...

if exist "%SETUP_DIR%" (
    rmdir /s /q "%SETUP_DIR%"
)
mkdir "%SETUP_DIR%"

echo ✅ تم إنشاء مجلد التثبيت

echo.
echo 📝 إنشاء ملف التثبيت التلقائي...

REM إنشاء ملف التثبيت الرئيسي
(
echo @echo off
echo chcp 65001 ^>nul
echo title تثبيت برنامج إدارة معرض السيارات - Amr Ali Elawamy
echo.
echo echo ========================================
echo echo    📦 تثبيت برنامج إدارة معرض السيارات
echo echo ========================================
echo echo.
echo echo 👨‍💻 المطور: Amr Ali Elawamy
echo echo 📞 الهاتف: 01285626623
echo echo 📧 البريد: <EMAIL>
echo echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo echo.
echo.
echo echo 🎯 مرحباً بك في برنامج إدارة معرض السيارات
echo echo.
echo echo الميزات الرئيسية:
echo echo    • إدارة شاملة للمخزون والسيارات
echo echo    • نظام مبيعات متقدم مع الأقساط
echo echo    • إدارة العملاء والموردين
echo echo    • تقارير وإحصائيات تفصيلية
echo echo    • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo echo    • نظام ضمان سلامة البيانات المالية
echo echo.
echo.
echo set "INSTALL_DIR=%%PROGRAMFILES%%\Car Dealership Management"
echo.
echo echo 📁 مجلد التثبيت: %%INSTALL_DIR%%
echo echo.
echo echo هل تريد المتابعة مع التثبيت؟ ^(Y/N^)
echo set /p "CONTINUE="
echo if /i not "%%CONTINUE%%"=="Y" ^(
echo     echo تم إلغاء التثبيت
echo     pause
echo     exit /b 0
echo ^)
echo.
echo echo 📁 إنشاء مجلد التثبيت...
echo if not exist "%%INSTALL_DIR%%" mkdir "%%INSTALL_DIR%%"
echo.
echo echo 📋 نسخ ملفات البرنامج...
echo xcopy "CarDealership_Program\*" "%%INSTALL_DIR%%\" /E /I /Y ^>nul
echo.
echo if %%ERRORLEVEL%% EQU 0 ^(
echo     echo ✅ تم نسخ الملفات بنجاح
echo ^) else ^(
echo     echo ❌ فشل في نسخ الملفات
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo 🔗 إنشاء اختصارات...
echo.
echo REM اختصار سطح المكتب
echo powershell -Command "
echo $$WshShell = New-Object -comObject WScript.Shell
echo $$Shortcut = $$WshShell.CreateShortcut^('%%USERPROFILE%%\Desktop\برنامج إدارة معرض السيارات.lnk'^)
echo $$Shortcut.TargetPath = '%%INSTALL_DIR%%\CarDealershipManagement.exe'
echo $$Shortcut.IconLocation = '%%INSTALL_DIR%%\app-icon.ico'
echo $$Shortcut.Description = 'برنامج إدارة معرض السيارات - Amr Ali Elawamy'
echo $$Shortcut.Save^(^)
echo "
echo.
echo REM اختصار قائمة البدء
echo set "START_MENU=%%APPDATA%%\Microsoft\Windows\Start Menu\Programs"
echo if not exist "%%START_MENU%%\برنامج إدارة معرض السيارات" mkdir "%%START_MENU%%\برنامج إدارة معرض السيارات"
echo.
echo powershell -Command "
echo $$WshShell = New-Object -comObject WScript.Shell
echo $$Shortcut = $$WshShell.CreateShortcut^('%%START_MENU%%\برنامج إدارة معرض السيارات\برنامج إدارة معرض السيارات.lnk'^)
echo $$Shortcut.TargetPath = '%%INSTALL_DIR%%\CarDealershipManagement.exe'
echo $$Shortcut.IconLocation = '%%INSTALL_DIR%%\app-icon.ico'
echo $$Shortcut.Description = 'برنامج إدارة معرض السيارات - Amr Ali Elawamy'
echo $$Shortcut.Save^(^)
echo "
echo.
echo powershell -Command "
echo $$WshShell = New-Object -comObject WScript.Shell
echo $$Shortcut = $$WshShell.CreateShortcut^('%%START_MENU%%\برنامج إدارة معرض السيارات\دليل المستخدم.lnk'^)
echo $$Shortcut.TargetPath = '%%INSTALL_DIR%%\README.txt'
echo $$Shortcut.Save^(^)
echo "
echo.
echo powershell -Command "
echo $$WshShell = New-Object -comObject WScript.Shell
echo $$Shortcut = $$WshShell.CreateShortcut^('%%START_MENU%%\برنامج إدارة معرض السيارات\إلغاء التثبيت.lnk'^)
echo $$Shortcut.TargetPath = '%%INSTALL_DIR%%\uninstall.bat'
echo $$Shortcut.Save^(^)
echo "
echo.
echo echo ✅ تم إنشاء الاختصارات
echo.
echo echo 📝 إنشاء ملف إلغاء التثبيت...
echo ^(
echo echo @echo off
echo echo chcp 65001 ^^^>nul
echo echo title إلغاء تثبيت برنامج إدارة معرض السيارات
echo echo.
echo echo هل تريد إلغاء تثبيت برنامج إدارة معرض السيارات؟ ^^^(Y/N^^^)
echo echo set /p "UNINSTALL="
echo echo if /i not "%%%%UNINSTALL%%%%"=="Y" exit /b 0
echo echo.
echo echo جاري إلغاء التثبيت...
echo echo.
echo echo حذف الاختصارات...
echo echo del "%%%%USERPROFILE%%%%\Desktop\برنامج إدارة معرض السيارات.lnk" ^^^>nul 2^^^>^^^&1
echo echo rmdir /s /q "%%%%APPDATA%%%%\Microsoft\Windows\Start Menu\Programs\برنامج إدارة معرض السيارات" ^^^>nul 2^^^>^^^&1
echo echo.
echo echo حذف ملفات البرنامج...
echo echo cd /d "%%%%PROGRAMFILES%%%%"
echo echo rmdir /s /q "Car Dealership Management"
echo echo.
echo echo تم إلغاء التثبيت بنجاح!
echo echo pause
echo ^) ^^^> "%%INSTALL_DIR%%\uninstall.bat"
echo.
echo echo ✅ تم إنشاء ملف إلغاء التثبيت
echo.
echo echo 🎉 تم تثبيت البرنامج بنجاح!
echo echo.
echo echo 🔑 بيانات الدخول:
echo echo    المطور: amrali / braa ^(جميع الصلاحيات^)
echo echo    المدير: admin / 123 ^(صلاحيات إدارية^)
echo echo    المندوب: user / pass ^(صلاحيات أساسية^)
echo echo.
echo echo 💡 يمكنك الآن:
echo echo    1. تشغيل البرنامج من سطح المكتب
echo echo    2. أو من قائمة البدء
echo echo    3. اختيار "نسخة تجريبية" للبدء فوراً
echo echo.
echo echo هل تريد تشغيل البرنامج الآن؟ ^(Y/N^)
echo set /p "RUN_NOW="
echo if /i "%%RUN_NOW%%"=="Y" ^(
echo     start "" "%%INSTALL_DIR%%\CarDealershipManagement.exe"
echo ^)
echo.
echo echo 👨‍💻 شكراً لاختيارك برنامج إدارة معرض السيارات!
echo echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo echo.
echo pause
) > "%SETUP_DIR%\install.bat"

echo ✅ تم إنشاء ملف التثبيت

echo.
echo 📋 نسخ ملفات البرنامج...

mkdir "%SETUP_DIR%\CarDealership_Program"
xcopy "%SOURCE_DIR%\*" "%SETUP_DIR%\CarDealership_Program\" /E /I /Y >nul

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم نسخ ملفات البرنامج
) else (
    echo ❌ فشل في نسخ ملفات البرنامج
    pause
    exit /b 1
)

echo.
echo 📝 إنشاء ملف README...

(
echo برنامج إدارة معرض السيارات - ملف التثبيت البسيط
echo ================================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo.
echo 🚀 طريقة التثبيت:
echo    1. اضغط مرتين على install.bat
echo    2. اتبع التعليمات على الشاشة
echo    3. سيتم إنشاء اختصارات تلقائياً
echo.
echo 🔑 بيانات الدخول:
echo    المطور: amrali / braa ^(جميع الصلاحيات^)
echo    المدير: admin / 123 ^(صلاحيات إدارية^)
echo    المندوب: user / pass ^(صلاحيات أساسية^)
echo.
echo 🎯 الميزات:
echo    • إدارة شاملة للمخزون والسيارات
echo    • نظام مبيعات متقدم مع الأقساط
echo    • إدارة العملاء والموردين
echo    • تقارير وإحصائيات تفصيلية
echo    • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo    • نظام ضمان سلامة البيانات المالية
echo.
echo 📞 للدعم الفني: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo.
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
) > "%SETUP_DIR%\README.txt"

echo ✅ تم إنشاء ملف README

echo.
echo 📦 ضغط ملفات التثبيت...

powershell -Command "
try {
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    $source = '%SETUP_DIR%'
    $destination = '%~dp0CarDealershipManagement_SimpleSetup.zip'
    
    if (Test-Path $destination) {
        Remove-Item $destination -Force
    }
    
    [System.IO.Compression.ZipFile]::CreateFromDirectory($source, $destination)
    Write-Host '✅ تم إنشاء ملف ZIP بنجاح' -ForegroundColor Green
    
    $fileSize = (Get-Item $destination).Length / 1MB
    Write-Host ('📏 حجم الملف: {0:N1} ميجابايت' -f $fileSize) -ForegroundColor Cyan
    
} catch {
    Write-Host '❌ فشل في إنشاء ملف ZIP' -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}
"

echo.
echo 📊 ملخص العملية:
echo.

if exist "%~dp0CarDealershipManagement_SimpleSetup.zip" (
    echo ✅ تم إنشاء ملف التثبيت البسيط بنجاح!
    echo.
    echo 📦 الملفات المنشأة:
    echo    📁 مجلد التثبيت: %SETUP_DIR%
    echo    📄 ملف ZIP: CarDealershipManagement_SimpleSetup.zip
    echo.
    
    REM عرض حجم الملف
    for %%A in ("CarDealershipManagement_SimpleSetup.zip") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo    📏 حجم ملف ZIP: !FILE_SIZE_MB! ميجابايت
    )
    
    echo.
    echo 🎯 محتويات ملف التثبيت:
    echo    ✅ install.bat - ملف التثبيت الرئيسي
    echo    ✅ CarDealership_Program\ - ملفات البرنامج الكاملة
    echo    ✅ README.txt - دليل التثبيت
    echo.
    
    echo 💡 للتوزيع:
    echo    1. أرسل ملف CarDealershipManagement_SimpleSetup.zip للعملاء
    echo    2. اطلب منهم استخراج الملفات
    echo    3. تشغيل install.bat كمدير
    echo.
    
    echo هل تريد فتح مجلد التثبيت؟ ^(Y/N^)
    set /p "OPEN_FOLDER="
    if /i "%OPEN_FOLDER%"=="Y" (
        start "" "%SETUP_DIR%"
    )
    
    echo.
    echo هل تريد اختبار التثبيت؟ ^(Y/N^)
    set /p "TEST_INSTALL="
    if /i "%TEST_INSTALL%"=="Y" (
        echo 🚀 تشغيل ملف التثبيت...
        start "" "%SETUP_DIR%\install.bat"
    )
    
) else (
    echo ❌ فشل في إنشاء ملف التثبيت
)

echo.
echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
