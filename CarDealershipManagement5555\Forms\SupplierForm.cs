using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Helpers;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace CarDealershipManagement.Forms
{
public partial class SupplierForm : Form
{
    private DataGridView dgvSuppliers;
    private TextBox txtSearch;
    private Button btnSearch;
    private Button btnAddSupplier;
    private Button btnEditSupplier;
    private Button btnDeleteSupplier;
    private Button btnPaySupplier;
    private Button btnViewAccount;
    private Button btnRefresh;
    private Button btnPrintSuppliers;
    private Button btnUploadFiles;
    private Button btnViewFiles;
    private PictureBox pbLogo;
    private Panel pnlMain;
    private Panel pnlButtons;

    public SupplierForm()
    {
        InitializeComponent();
        LoadData();
    }

    private void InitializeComponent()
    {
        this.Text = "🏭 إدارة الموردين - Supplier Management";
        this.BackColor = Color.FromArgb(240, 248, 255);
        this.Font = new Font("Segoe UI", 10F);
        this.WindowState = FormWindowState.Maximized;
        this.StartPosition = FormStartPosition.CenterScreen;
        this.MinimumSize = new Size(1200, 700);

        // تطبيق إعدادات النافذة المرنة للشاشة الكاملة
        ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this,
            Screen.PrimaryScreen.WorkingArea.Width,
            Screen.PrimaryScreen.WorkingArea.Height);

        CreateLayoutStructure();
        SetupEventHandlers();
    }

    private void CreateLayoutStructure()
    {
        // Main Panel
        pnlMain = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(15)
        };

        // Header Panel (Logo + Title)
        var pnlHeader = new Panel
        {
            Dock = DockStyle.Top,
            Height = 80,
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Logo
        pbLogo = new PictureBox
        {
            Location = new Point(20, 10),
            Size = new Size(60, 60),
            SizeMode = PictureBoxSizeMode.Zoom,
            BorderStyle = BorderStyle.FixedSingle,
            BackColor = Color.LightGray
        };

        // Title Label
        var lblTitle = new Label
        {
            Text = "إدارة الموردين",
            Location = new Point(100, 25),
            Size = new Size(200, 30),
            Font = new Font("Segoe UI", 14F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            BackColor = Color.Transparent
        };

        // Search and Filter Panel
        var pnlSearchFilter = new Panel
        {
            Dock = DockStyle.Top,
            Height = 60,
            BackColor = Color.White,
            Padding = new Padding(10, 5, 10, 5)
        };

        // Search Section
        var lblSearch = new Label
        {
            Text = "البحث:",
            Location = new Point(20, 20),
            Size = new Size(50, 23),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            TextAlign = ContentAlignment.MiddleLeft
        };

        txtSearch = new TextBox
        {
            Location = new Point(80, 18),
            Size = new Size(200, 25),
            Font = new Font("Segoe UI", 9F),
            BorderStyle = BorderStyle.FixedSingle
        };

        btnSearch = new Button
        {
            Text = "بحث",
            Location = new Point(290, 17),
            Size = new Size(70, 28),
            BackColor = Color.FromArgb(0, 102, 204),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnSearch.FlatAppearance.BorderSize = 0;

        // Action Buttons Panel with fixed positioning (like InventoryForm)
        pnlButtons = new Panel
        {
            Dock = DockStyle.Top,
            Height = 120,
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // First row - Main action buttons with 12px gaps between buttons
        btnAddSupplier = CreateStyledButton("إضافة مورد", new Point(20, 15), Color.FromArgb(40, 167, 69));
        btnEditSupplier = CreateStyledButton("تعديل", new Point(132, 15), Color.FromArgb(255, 193, 7), Color.Black);
        btnDeleteSupplier = CreateStyledButton("حذف", new Point(224, 15), Color.FromArgb(220, 53, 69));
        btnRefresh = CreateStyledButton("تحديث", new Point(316, 15), Color.FromArgb(0, 123, 255));
        btnPaySupplier = CreateStyledButton("دفع للمورد", new Point(428, 15), Color.FromArgb(108, 117, 125));
        btnViewAccount = CreateStyledButton("عرض الحساب", new Point(560, 15), Color.FromArgb(23, 162, 184));

        // Second row - File management and print buttons with same 12px gap as first row
        btnUploadFiles = CreateStyledButton("رفع ملفات", new Point(20, 65), Color.FromArgb(108, 117, 125));
        btnViewFiles = CreateStyledButton("عرض الملفات", new Point(132, 65), Color.FromArgb(23, 162, 184));
        btnPrintSuppliers = CreateStyledButton("🖨️ طباعة", new Point(244, 65), Color.FromArgb(75, 0, 130));
        btnViewFiles.Visible = true;
        btnViewFiles.Enabled = true;

        // Add buttons directly to the panel
        pnlButtons.Controls.AddRange(new Control[]
        {
            btnAddSupplier, btnEditSupplier, btnDeleteSupplier, btnRefresh,
            btnPaySupplier, btnViewAccount, btnUploadFiles, btnViewFiles, btnPrintSuppliers
        });

        // DataGridView - matching InventoryForm style
        dgvSuppliers = new DataGridView
        {
            Dock = DockStyle.Fill,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            ReadOnly = true,
            AllowUserToAddRows = false,
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.Fixed3D,
            Font = new Font("Segoe UI", 9F),
            ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(0, 102, 204),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            DefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.White,
                ForeColor = Color.Black,
                SelectionBackColor = Color.FromArgb(173, 216, 230),
                SelectionForeColor = Color.Black,
                Alignment = DataGridViewContentAlignment.MiddleCenter
            }
        };

        // Add controls to panels
        pnlHeader.Controls.AddRange(new Control[] { pbLogo, lblTitle });
        pnlSearchFilter.Controls.AddRange(new Control[] { lblSearch, txtSearch, btnSearch });

        // Add panels to main panel
        pnlMain.Controls.Add(dgvSuppliers);
        pnlMain.Controls.Add(pnlButtons);
        pnlMain.Controls.Add(pnlSearchFilter);
        pnlMain.Controls.Add(pnlHeader);

        this.Controls.Add(pnlMain);
    }

    private Button CreateStyledButton(string text, Point location, Color backColor, Color? foreColor = null)
    {
        // Calculate button width based on text length
        int baseWidth = 80;
        int textLength = text.Length;
        int calculatedWidth = Math.Max(baseWidth, textLength * 8 + 20);

        var button = new Button
        {
            Text = text,
            Location = location,
            Size = new Size(calculatedWidth, 35),
            BackColor = backColor,
            ForeColor = foreColor ?? Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            TextAlign = ContentAlignment.MiddleCenter,
            UseVisualStyleBackColor = false
        };
        button.FlatAppearance.BorderSize = 0;
        return button;
    }

    private void SetupEventHandlers()
    {
        btnSearch.Click += BtnSearch_Click;
        btnAddSupplier.Click += BtnAddSupplier_Click;
        btnEditSupplier.Click += BtnEditSupplier_Click;
        btnDeleteSupplier.Click += BtnDeleteSupplier_Click;
        btnRefresh.Click += (s, e) => LoadData();
        btnPaySupplier.Click += BtnPaySupplier_Click;
        btnViewAccount.Click += BtnViewAccount_Click;
        btnUploadFiles.Click += BtnUploadFiles_Click;
        btnViewFiles.Click += BtnViewFiles_Click;
        btnPrintSuppliers.Click += BtnPrintSuppliers_Click;
    }

    private async void LoadData()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var query = context.Suppliers.AsQueryable();

            if(!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                var searchTerm = txtSearch.Text.ToLower();
                query = query.Where(s => s.SupplierName.ToLower().Contains(searchTerm) ||
                                    s.ResponsiblePerson.ToLower().Contains(searchTerm) ||
                                    s.Phone.ToLower().Contains(searchTerm));
            }

            var suppliers = await query.ToListAsync();

            dgvSuppliers.DataSource = suppliers.Select(s => new
            {
                رقم_المورد = s.SupplierId,
                اسم_المورد = s.SupplierName,
                رقم_السجل_التجاري = s.CommercialRegistrationNumber ?? "غير محدد",
                الشخص_المسؤول = s.ResponsiblePerson,
                الهاتف = s.Phone,
                البريد_الإلكتروني = s.Email ?? "غير محدد",
                العنوان = s.Address,
                إجمالي_المستحق = s.TotalOwed.ToString("C"),
                المدفوع = s.TotalPaid.ToString("C"),
                المتبقي = s.Balance.ToString("C"),
                حالة_الحساب = s.AccountStatus switch
            {
                SupplierAccountStatus.Creditor => "دائن",
                SupplierAccountStatus.Debtor => "مدين",
                SupplierAccountStatus.Neutral => "متوازن",
                _ => "غير محدد"
            },
            تاريخ_الإضافة = s.CreatedDate.ToString("yyyy-MM-dd")
            }).ToList();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void BtnSearch_Click(object? sender, EventArgs e)
    {
        LoadData();
    }

    private void BtnAddSupplier_Click(object? sender, EventArgs e)
    {
        var addForm = new AddEditSupplierForm();
        if(addForm.ShowDialog() == DialogResult.OK)
        {
            LoadData();
        }
    }

    private void BtnEditSupplier_Click(object? sender, EventArgs e)
    {
        if(dgvSuppliers.SelectedRows.Count > 0)
        {
            var supplierId = Convert.ToInt32(dgvSuppliers.SelectedRows[0].Cells["رقم_المورد"].Value);
            var editForm = new AddEditSupplierForm(supplierId);
            if(editForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار مورد للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private async void BtnDeleteSupplier_Click(object? sender, EventArgs e)
    {
        if(dgvSuppliers.SelectedRows.Count > 0)
        {
            var supplierId = Convert.ToInt32(dgvSuppliers.SelectedRows[0].Cells["رقم_المورد"].Value);
            var result = MessageBox.Show("هل أنت متأكد من حذف هذا المورد؟", "تأكيد الحذف",
                                         MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if(result == DialogResult.Yes)
            {
                try
                {
                    using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                            .UseSqlite("Data Source=CarDealership.db").Options);

                    var supplier = await context.Suppliers.FindAsync(supplierId);
                    if(supplier != null)
                    {
                        // Check if supplier has payments
                        var hasPayments = await context.SupplierPayments.AnyAsync(sp => sp.SupplierId == supplierId);
                        if(hasPayments)
                        {
                            MessageBox.Show("لا يمكن حذف مورد له معاملات مالية مسجلة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        context.Suppliers.Remove(supplier);
                        await context.SaveChangesAsync();
                        MessageBox.Show("تم حذف المورد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadData();
                    }
                }
                catch(Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار مورد للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnPaySupplier_Click(object? sender, EventArgs e)
    {
        if(dgvSuppliers.SelectedRows.Count > 0)
        {
            var supplierId = Convert.ToInt32(dgvSuppliers.SelectedRows[0].Cells["رقم_المورد"].Value);
            var paymentForm = new SupplierPaymentForm(supplierId);
            if(paymentForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار مورد للدفع", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnViewAccount_Click(object? sender, EventArgs e)
    {
        if(dgvSuppliers.SelectedRows.Count > 0)
        {
            var supplierId = Convert.ToInt32(dgvSuppliers.SelectedRows[0].Cells["رقم_المورد"].Value);
            var accountForm = new SupplierAccountForm(supplierId);
            accountForm.ShowDialog();
        }
        else
        {
            MessageBox.Show("يرجى اختيار مورد لعرض الحساب", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnPrintSuppliers_Click(object? sender, EventArgs e)
    {
        if(dgvSuppliers.Rows.Count == 0)
        {
            MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // استخدام نظام الطباعة بالتحديد
        using var selectiveForm = new SelectivePrintForm(SelectivePrintForm.PrintType.Suppliers);
        if(selectiveForm.ShowDialog() == DialogResult.OK)
        {
            DataGridView dgvToPrint = CreateFilteredSuppliersDataGridView(selectiveForm);

            if(dgvToPrint.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات تطابق المعايير المحددة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using var printForm = new PrintReportForm(dgvToPrint);
            printForm.Text = selectiveForm.PrintAll ? "طباعة جميع الموردين" : "طباعة مورد محدد";
            printForm.ShowDialog();
        }
    }

    private DataGridView CreateFilteredSuppliersDataGridView(SelectivePrintForm selectiveForm)
    {
        var filteredDgv = new DataGridView();

        // نسخ هيكل الأعمدة
        foreach(DataGridViewColumn col in dgvSuppliers.Columns)
        {
            filteredDgv.Columns.Add((DataGridViewColumn)col.Clone());
        }

        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);
            var query = context.Suppliers.AsQueryable();

            // تطبيق فلتر المورد المحدد
            if(!selectiveForm.PrintAll && selectiveForm.SelectedSupplierId.HasValue)
            {
                query = query.Where(s => s.SupplierId == selectiveForm.SelectedSupplierId.Value);
            }

            var suppliers = query.ToList();

            // إضافة البيانات المفلترة
            foreach(var supplier in suppliers)
            {
                var row = new DataGridViewRow();
                row.CreateCells(filteredDgv);

                row.Cells[0].Value = supplier.SupplierId;
                row.Cells[1].Value = supplier.SupplierName;
                row.Cells[2].Value = supplier.CommercialRegistrationNumber ?? "غير محدد";
                row.Cells[3].Value = supplier.ResponsiblePerson;
                row.Cells[4].Value = supplier.Phone;
                row.Cells[5].Value = supplier.Email ?? "غير محدد";
                row.Cells[6].Value = supplier.Address;
                row.Cells[7].Value = supplier.TotalOwed.ToString("C");
                row.Cells[8].Value = supplier.TotalPaid.ToString("C");
                row.Cells[9].Value = supplier.Balance.ToString("C");
                row.Cells[10].Value = supplier.AccountStatus switch
            {
                SupplierAccountStatus.Creditor => "دائن",
                SupplierAccountStatus.Debtor => "مدين",
                SupplierAccountStatus.Neutral => "متوازن",
                _ => "غير محدد"
            };

            filteredDgv.Rows.Add(row);
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        return filteredDgv;
    }
    private void BtnUploadFiles_Click(object? sender, EventArgs e)
    {
        if(dgvSuppliers.SelectedRows.Count > 0)
        {
            var supplierId = Convert.ToInt32(dgvSuppliers.SelectedRows[0].Cells["رقم_المورد"].Value);
            using var openFileDialog = new OpenFileDialog
            {
                Filter = "All Files (*.*)|*.*|Images (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|Documents (*.pdf;*.doc;*.docx)|*.pdf;*.doc;*.docx",
                Multiselect = true,
                Title = "اختيار الملفات للرفع"
            };

            if(openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // Create supplier files directory if it doesn't exist
                    var supplierFilesDir = Path.Combine("SupplierFiles", supplierId.ToString());
                    Directory.CreateDirectory(supplierFilesDir);

                    foreach(var file in openFileDialog.FileNames)
                    {
                        var fileName = Path.GetFileName(file);
                        var destinationPath = Path.Combine(supplierFilesDir, fileName);

                        // Copy file to supplier directory
                        File.Copy(file, destinationPath, true);
                    }

                    MessageBox.Show($"تم رفع {openFileDialog.FileNames.Length} ملف بنجاح", "نجح",
                                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch(Exception ex)
                {
                    MessageBox.Show($"خطأ في رفع الملفات: {ex.Message}", "خطأ",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار مورد أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnViewFiles_Click(object? sender, EventArgs e)
    {
        if(dgvSuppliers.SelectedRows.Count > 0)
        {
            var supplierId = Convert.ToInt32(dgvSuppliers.SelectedRows[0].Cells["رقم_المورد"].Value);
            var supplierFilesDir = Path.Combine("SupplierFiles", supplierId.ToString());

            if(Directory.Exists(supplierFilesDir))
            {
                try
                {
                    // Open the directory in Windows Explorer
                    Process.Start("explorer.exe", supplierFilesDir);
                }
                catch(Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح مجلد الملفات: {ex.Message}", "خطأ",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("لا توجد ملفات مرفوعة لهذا المورد", "تنبيه",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار مورد أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }
}
}
