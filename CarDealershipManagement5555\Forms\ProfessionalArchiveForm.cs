using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class ProfessionalArchiveForm : Form
    {
        private readonly CarDealershipContext dbContext;
        private TreeView treeViewFolders;
        private ListView listViewFiles;
        private Panel panelPreview;
        private PictureBox pictureBoxPreview;
        private TextBox textBoxPreview;
        private ToolStrip toolStrip;
        private StatusStrip statusStrip;
        private SplitContainer splitContainer;
        
        private string currentPath = "";
        private readonly string archiveRootPath;

        public ProfessionalArchiveForm()
        {
            var optionsBuilder = new DbContextOptionsBuilder<CarDealershipContext>();
            optionsBuilder.UseSqlite("Data Source=CarDealership.db");
            this.dbContext = new CarDealershipContext(optionsBuilder.Options);
            
            // إنشاء مجلد الأرشيف إذا لم يكن موجوداً
            archiveRootPath = Path.Combine(Application.StartupPath, "Archive");
            if (!Directory.Exists(archiveRootPath))
            {
                Directory.CreateDirectory(archiveRootPath);
                CreateDefaultFolders();
            }

            InitializeComponent();
            LoadFolderTree();
        }

        private void InitializeComponent()
        {
            // إعدادات النموذج
            Text = "الأرشيف الاحترافي - إدارة الملفات";
            Size = new Size(1200, 800);
            StartPosition = FormStartPosition.CenterScreen;
            WindowState = FormWindowState.Maximized;
            Icon = SystemIcons.Application;

            // شريط الأدوات
            CreateToolStrip();
            
            // الحاوي الرئيسي
            splitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                SplitterDistance = 300,
                FixedPanel = FixedPanel.Panel1
            };

            // شجرة المجلدات
            treeViewFolders = new TreeView
            {
                Dock = DockStyle.Fill,
                ImageList = CreateImageList(),
                Font = new Font("Segoe UI", 10F),
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.None
            };
            treeViewFolders.NodeMouseClick += TreeViewFolders_NodeMouseClick;

            // الحاوي الثانوي للملفات والمعاينة
            var rightSplitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Horizontal,
                SplitterDistance = 400
            };

            // قائمة الملفات
            listViewFiles = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Font = new Font("Segoe UI", 9F),
                BackColor = Color.White
            };
            SetupFileListColumns();
            listViewFiles.SelectedIndexChanged += ListViewFiles_SelectedIndexChanged;
            listViewFiles.DoubleClick += ListViewFiles_DoubleClick;

            // لوحة المعاينة
            CreatePreviewPanel();

            // شريط الحالة
            CreateStatusStrip();

            // تجميع العناصر
            splitContainer.Panel1.Controls.Add(treeViewFolders);
            rightSplitContainer.Panel1.Controls.Add(listViewFiles);
            rightSplitContainer.Panel2.Controls.Add(panelPreview);
            splitContainer.Panel2.Controls.Add(rightSplitContainer);

            Controls.AddRange(new Control[] { splitContainer, toolStrip, statusStrip });
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip
            {
                ImageScalingSize = new Size(24, 24),
                BackColor = Color.FromArgb(0, 102, 204),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F)
            };

            var btnUpload = new ToolStripButton
            {
                Text = "📁 رفع ملفات",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnUpload.Click += BtnUpload_Click;

            var btnNewFolder = new ToolStripButton
            {
                Text = "📂 مجلد جديد",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnNewFolder.Click += BtnNewFolder_Click;

            var btnDelete = new ToolStripButton
            {
                Text = "🗑️ حذف",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnDelete.Click += BtnDelete_Click;

            var btnRefresh = new ToolStripButton
            {
                Text = "🔄 تحديث",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnRefresh.Click += BtnRefresh_Click;

            var btnBackup = new ToolStripButton
            {
                Text = "💾 نسخ احتياطي",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnBackup.Click += BtnBackup_Click;

            var btnRestore = new ToolStripButton
            {
                Text = "📥 استرجاع",
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnRestore.Click += BtnRestore_Click;

            toolStrip.Items.AddRange(new ToolStripItem[]
            {
                btnUpload,
                new ToolStripSeparator(),
                btnNewFolder,
                new ToolStripSeparator(),
                btnDelete,
                new ToolStripSeparator(),
                btnRefresh,
                new ToolStripSeparator(),
                btnBackup,
                new ToolStripSeparator(),
                btnRestore
            });
        }

        private ImageList CreateImageList()
        {
            var imageList = new ImageList { ImageSize = new Size(16, 16) };
            
            // إضافة أيقونات للمجلدات والملفات
            imageList.Images.Add("folder", SystemIcons.Shield.ToBitmap());
            imageList.Images.Add("file", SystemIcons.Application.ToBitmap());
            
            return imageList;
        }

        private void SetupFileListColumns()
        {
            listViewFiles.Columns.Add("الاسم", 200);
            listViewFiles.Columns.Add("النوع", 100);
            listViewFiles.Columns.Add("الحجم", 100);
            listViewFiles.Columns.Add("تاريخ التعديل", 150);
            listViewFiles.Columns.Add("الوصف", 200);
        }

        private void CreatePreviewPanel()
        {
            panelPreview = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            var lblPreviewTitle = new Label
            {
                Text = "معاينة الملف",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204),
                Location = new Point(10, 10),
                Size = new Size(200, 25)
            };

            pictureBoxPreview = new PictureBox
            {
                Location = new Point(10, 45),
                Size = new Size(300, 200),
                SizeMode = PictureBoxSizeMode.Zoom,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };

            textBoxPreview = new TextBox
            {
                Location = new Point(10, 45),
                Size = new Size(400, 200),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Both,
                Font = new Font("Segoe UI", 9F),
                Visible = false
            };

            panelPreview.Controls.AddRange(new Control[] { lblPreviewTitle, pictureBoxPreview, textBoxPreview });
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(248, 249, 250)
            };

            var lblStatus = new ToolStripStatusLabel
            {
                Text = "جاهز",
                Font = new Font("Segoe UI", 9F)
            };

            statusStrip.Items.Add(lblStatus);
        }

        private void CreateDefaultFolders()
        {
            string[] defaultFolders = {
                "العملاء",
                "السيارات", 
                "المبيعات",
                "الموردين",
                "المستندات الرسمية",
                "الصور",
                "التقارير",
                "النسخ الاحتياطية"
            };

            foreach (string folder in defaultFolders)
            {
                string folderPath = Path.Combine(archiveRootPath, folder);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }
            }
        }

        private void LoadFolderTree()
        {
            treeViewFolders.Nodes.Clear();
            
            var rootNode = new TreeNode("الأرشيف")
            {
                Tag = archiveRootPath,
                ImageIndex = 0,
                SelectedImageIndex = 0
            };

            LoadSubFolders(rootNode, archiveRootPath);
            treeViewFolders.Nodes.Add(rootNode);
            rootNode.Expand();
        }

        private void LoadSubFolders(TreeNode parentNode, string path)
        {
            try
            {
                foreach (string directory in Directory.GetDirectories(path))
                {
                    var dirInfo = new DirectoryInfo(directory);
                    var node = new TreeNode(dirInfo.Name)
                    {
                        Tag = directory,
                        ImageIndex = 0,
                        SelectedImageIndex = 0
                    };

                    LoadSubFolders(node, directory);
                    parentNode.Nodes.Add(node);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجلدات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TreeViewFolders_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node.Tag != null)
            {
                currentPath = e.Node.Tag.ToString();
                LoadFiles(currentPath);
                UpdateStatusBar($"المجلد الحالي: {e.Node.Text}");
            }
        }

        private void LoadFiles(string path)
        {
            listViewFiles.Items.Clear();

            try
            {
                foreach (string file in Directory.GetFiles(path))
                {
                    var fileInfo = new FileInfo(file);
                    var item = new ListViewItem(fileInfo.Name);
                    item.SubItems.Add(fileInfo.Extension);
                    item.SubItems.Add(FormatFileSize(fileInfo.Length));
                    item.SubItems.Add(fileInfo.LastWriteTime.ToString("yyyy/MM/dd HH:mm"));
                    item.SubItems.Add(""); // وصف الملف
                    item.Tag = file;
                    
                    listViewFiles.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملفات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "بايت", "كيلوبايت", "ميجابايت", "جيجابايت" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private void UpdateStatusBar(string message)
        {
            if (statusStrip.Items.Count > 0)
            {
                ((ToolStripStatusLabel)statusStrip.Items[0]).Text = message;
            }
        }

        private void ListViewFiles_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewFiles.SelectedItems.Count > 0)
            {
                string filePath = listViewFiles.SelectedItems[0].Tag.ToString();
                PreviewFile(filePath);
            }
            else
            {
                ClearPreview();
            }
        }

        private void PreviewFile(string filePath)
        {
            try
            {
                string extension = Path.GetExtension(filePath).ToLower();

                // إخفاء جميع عناصر المعاينة
                pictureBoxPreview.Visible = false;
                textBoxPreview.Visible = false;

                if (IsImageFile(extension))
                {
                    // معاينة الصور
                    pictureBoxPreview.Image?.Dispose();
                    pictureBoxPreview.Image = Image.FromFile(filePath);
                    pictureBoxPreview.Visible = true;
                }
                else if (IsTextFile(extension))
                {
                    // معاينة الملفات النصية
                    textBoxPreview.Text = File.ReadAllText(filePath);
                    textBoxPreview.Visible = true;
                }
                else
                {
                    // ملفات أخرى
                    textBoxPreview.Text = $"نوع الملف: {extension}\nالحجم: {FormatFileSize(new FileInfo(filePath).Length)}\nلا يمكن معاينة هذا النوع من الملفات";
                    textBoxPreview.Visible = true;
                }
            }
            catch (Exception ex)
            {
                textBoxPreview.Text = $"خطأ في معاينة الملف: {ex.Message}";
                textBoxPreview.Visible = true;
            }
        }

        private bool IsImageFile(string extension)
        {
            string[] imageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff" };
            return imageExtensions.Contains(extension);
        }

        private bool IsTextFile(string extension)
        {
            string[] textExtensions = { ".txt", ".log", ".xml", ".json", ".csv" };
            return textExtensions.Contains(extension);
        }

        private void ClearPreview()
        {
            pictureBoxPreview.Image?.Dispose();
            pictureBoxPreview.Image = null;
            textBoxPreview.Text = "";
            pictureBoxPreview.Visible = false;
            textBoxPreview.Visible = false;
        }

        private void ListViewFiles_DoubleClick(object sender, EventArgs e)
        {
            if (listViewFiles.SelectedItems.Count > 0)
            {
                string filePath = listViewFiles.SelectedItems[0].Tag.ToString();
                try
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnUpload_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(currentPath))
            {
                MessageBox.Show("يرجى اختيار مجلد أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using var openFileDialog = new OpenFileDialog
            {
                Title = "اختر الملفات للرفع",
                Multiselect = true,
                Filter = "جميع الملفات (*.*)|*.*|الصور (*.jpg;*.png;*.gif)|*.jpg;*.png;*.gif|المستندات (*.pdf;*.doc;*.docx)|*.pdf;*.doc;*.docx"
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                foreach (string sourceFile in openFileDialog.FileNames)
                {
                    try
                    {
                        string fileName = Path.GetFileName(sourceFile);
                        string destinationFile = Path.Combine(currentPath, fileName);

                        // التحقق من وجود الملف
                        if (File.Exists(destinationFile))
                        {
                            var result = MessageBox.Show($"الملف '{fileName}' موجود بالفعل. هل تريد استبداله؟",
                                "تأكيد الاستبدال", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                            if (result != DialogResult.Yes)
                                continue;
                        }

                        File.Copy(sourceFile, destinationFile, true);
                        UpdateStatusBar($"تم رفع الملف: {fileName}");
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في رفع الملف {Path.GetFileName(sourceFile)}: {ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }

                LoadFiles(currentPath);
                MessageBox.Show("تم رفع الملفات بنجاح!", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnNewFolder_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(currentPath))
            {
                MessageBox.Show("يرجى اختيار مجلد أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string folderName = Microsoft.VisualBasic.Interaction.InputBox(
                "أدخل اسم المجلد الجديد:", "مجلد جديد", "مجلد جديد");

            if (!string.IsNullOrEmpty(folderName))
            {
                try
                {
                    string newFolderPath = Path.Combine(currentPath, folderName);
                    Directory.CreateDirectory(newFolderPath);
                    LoadFolderTree();
                    UpdateStatusBar($"تم إنشاء المجلد: {folderName}");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إنشاء المجلد: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (listViewFiles.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار ملف للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف الملف المحدد؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    string filePath = listViewFiles.SelectedItems[0].Tag.ToString();
                    File.Delete(filePath);
                    LoadFiles(currentPath);
                    ClearPreview();
                    UpdateStatusBar("تم حذف الملف");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الملف: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadFolderTree();
            if (!string.IsNullOrEmpty(currentPath))
            {
                LoadFiles(currentPath);
            }
            UpdateStatusBar("تم تحديث الأرشيف");
        }

        private async void BtnBackup_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatusBar("جاري إنشاء النسخة الاحتياطية...");

                using var folderDialog = new FolderBrowserDialog
                {
                    Description = "اختر مجلد حفظ النسخة الاحتياطية",
                    UseDescriptionForTitle = true
                };

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    string backupPath = Path.Combine(folderDialog.SelectedPath, $"Archive_Backup_{DateTime.Now:yyyyMMdd_HHmmss}");
                    Directory.CreateDirectory(backupPath);

                    // نسخ ملفات الأرشيف
                    await CopyDirectoryAsync(archiveRootPath, Path.Combine(backupPath, "Archive"));

                    // نسخ قاعدة البيانات
                    await BackupDatabaseAsync(backupPath);

                    // إنشاء ملف معلومات النسخة الاحتياطية
                    await CreateBackupInfoFile(backupPath);

                    UpdateStatusBar($"تم إنشاء النسخة الاحتياطية في: {backupPath}");
                    MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح!\n\nالمسار: {backupPath}",
                        "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                UpdateStatusBar("فشل في إنشاء النسخة الاحتياطية");
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnRestore_Click(object sender, EventArgs e)
        {
            try
            {
                using var folderDialog = new FolderBrowserDialog
                {
                    Description = "اختر مجلد النسخة الاحتياطية للاسترجاع",
                    UseDescriptionForTitle = true
                };

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    string backupPath = folderDialog.SelectedPath;

                    // التحقق من وجود ملفات النسخة الاحتياطية
                    if (!Directory.Exists(Path.Combine(backupPath, "Archive")) ||
                        !File.Exists(Path.Combine(backupPath, "CarDealership_Backup.db")))
                    {
                        MessageBox.Show("المجلد المحدد لا يحتوي على نسخة احتياطية صحيحة!",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    var result = MessageBox.Show(
                        "تحذير: سيتم استبدال جميع البيانات الحالية بالنسخة الاحتياطية.\n\nهل أنت متأكد من المتابعة؟",
                        "تأكيد الاسترجاع", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        UpdateStatusBar("جاري استرجاع النسخة الاحتياطية...");

                        // استرجاع ملفات الأرشيف
                        await RestoreArchiveAsync(backupPath);

                        // استرجاع قاعدة البيانات
                        await RestoreDatabaseAsync(backupPath);

                        // تحديث الواجهة
                        LoadFolderTree();

                        UpdateStatusBar("تم استرجاع النسخة الاحتياطية بنجاح");
                        MessageBox.Show("تم استرجاع النسخة الاحتياطية بنجاح!\n\nسيتم إعادة تشغيل البرنامج.",
                            "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // إعادة تشغيل البرنامج
                        Application.Restart();
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatusBar("فشل في استرجاع النسخة الاحتياطية");
                MessageBox.Show($"خطأ في استرجاع النسخة الاحتياطية: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task CopyDirectoryAsync(string sourceDir, string destDir)
        {
            Directory.CreateDirectory(destDir);

            foreach (string file in Directory.GetFiles(sourceDir))
            {
                string fileName = Path.GetFileName(file);
                string destFile = Path.Combine(destDir, fileName);
                File.Copy(file, destFile, true);
            }

            foreach (string subDir in Directory.GetDirectories(sourceDir))
            {
                string dirName = Path.GetFileName(subDir);
                string destSubDir = Path.Combine(destDir, dirName);
                await CopyDirectoryAsync(subDir, destSubDir);
            }
        }

        private async Task BackupDatabaseAsync(string backupPath)
        {
            try
            {
                string sourcePath = Path.Combine(Application.StartupPath, "CarDealership.db");
                string destPath = Path.Combine(backupPath, "CarDealership_Backup.db");

                if (File.Exists(sourcePath))
                {
                    File.Copy(sourcePath, destPath, true);
                }

                // نسخ ملفات إضافية إذا وجدت
                string[] additionalFiles = { "CarDealership.db-shm", "CarDealership.db-wal" };
                foreach (string file in additionalFiles)
                {
                    string sourceFile = Path.Combine(Application.StartupPath, file);
                    if (File.Exists(sourceFile))
                    {
                        string destFile = Path.Combine(backupPath, file);
                        File.Copy(sourceFile, destFile, true);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في نسخ قاعدة البيانات: {ex.Message}");
            }
        }

        private async Task CreateBackupInfoFile(string backupPath)
        {
            var backupInfo = new
            {
                BackupDate = DateTime.Now,
                Version = "1.0",
                Description = "نسخة احتياطية شاملة من نظام إدارة معرض السيارات",
                Files = new
                {
                    Archive = "مجلد الأرشيف الكامل",
                    Database = "قاعدة البيانات الرئيسية"
                }
            };

            string infoPath = Path.Combine(backupPath, "backup_info.txt");
            string infoContent = $@"معلومات النسخة الاحتياطية
====================
تاريخ الإنشاء: {backupInfo.BackupDate:yyyy-MM-dd HH:mm:ss}
الإصدار: {backupInfo.Version}
الوصف: {backupInfo.Description}

الملفات المشمولة:
- {backupInfo.Files.Archive}
- {backupInfo.Files.Database}

تم إنشاء هذه النسخة الاحتياطية بواسطة نظام إدارة معرض السيارات
";

            await File.WriteAllTextAsync(infoPath, infoContent);
        }

        private async Task RestoreArchiveAsync(string backupPath)
        {
            string sourceArchive = Path.Combine(backupPath, "Archive");

            if (Directory.Exists(archiveRootPath))
            {
                Directory.Delete(archiveRootPath, true);
            }

            await CopyDirectoryAsync(sourceArchive, archiveRootPath);
        }

        private async Task RestoreDatabaseAsync(string backupPath)
        {
            try
            {
                // إغلاق اتصالات قاعدة البيانات
                dbContext?.Dispose();

                string sourcePath = Path.Combine(backupPath, "CarDealership_Backup.db");
                string destPath = Path.Combine(Application.StartupPath, "CarDealership.db");

                if (File.Exists(sourcePath))
                {
                    File.Copy(sourcePath, destPath, true);
                }

                // استرجاع ملفات إضافية إذا وجدت
                string[] additionalFiles = { "CarDealership.db-shm", "CarDealership.db-wal" };
                foreach (string file in additionalFiles)
                {
                    string sourceFile = Path.Combine(backupPath, file);
                    if (File.Exists(sourceFile))
                    {
                        string destFile = Path.Combine(Application.StartupPath, file);
                        File.Copy(sourceFile, destFile, true);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في استرجاع قاعدة البيانات: {ex.Message}");
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                dbContext?.Dispose();
                pictureBoxPreview?.Image?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
