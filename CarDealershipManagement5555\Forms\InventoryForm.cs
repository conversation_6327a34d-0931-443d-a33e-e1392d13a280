using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Helpers;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace CarDealershipManagement.Forms
{
public partial class InventoryForm : Form
{
    private DataGridView dgvCars;
    private TextBox txtSearch;
    private Button btnSearch;
    private Button btnAddCar;
    private Button btnEditCar;
    private Button btnDeleteCar;
    private Button btnRefresh;
    private ComboBox cmbBrand;
    private ComboBox cmbCondition;
    private Label lblSearch;
    private Label lblBrand;
    private Label lblCondition;
    private Button btnUploadFiles;
    private Button btnViewFiles;
    private Button btnPrint;
    private PictureBox pbLogo;
    private Panel pnlMain;
    private User? currentUser;

    public InventoryForm(User? currentUser = null)
    {
        this.currentUser = currentUser;
        InitializeComponent();
        LoadData();
    }

    private void InitializeComponent()
    {
        this.Text = "📦 إدارة المخزون - Car Inventory Management";
        this.BackColor = Color.FromArgb(240, 248, 255);
        this.Font = new Font("Segoe UI", 10F);
        this.WindowState = FormWindowState.Maximized;
        this.StartPosition = FormStartPosition.CenterScreen;
        this.MinimumSize = new Size(1200, 700);

        // تطبيق إعدادات النافذة المرنة للشاشة الكاملة
        ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this,
            Screen.PrimaryScreen.WorkingArea.Width,
            Screen.PrimaryScreen.WorkingArea.Height);

        CreateLayoutStructure();
        SetupEventHandlers();
    }

    private void CreateLayoutStructure()
    {
        // Main Panel
        pnlMain = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(15)
        };

        // Header Panel (Logo + Title)
        var pnlHeader = new Panel
        {
            Dock = DockStyle.Top,
            Height = 80,
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Logo
        pbLogo = new PictureBox
        {
            Location = new Point(20, 10),
            Size = new Size(60, 60),
            SizeMode = PictureBoxSizeMode.Zoom,
            BorderStyle = BorderStyle.FixedSingle,
            BackColor = Color.LightGray
        };

        // Title Label
        var lblTitle = new Label
        {
            Text = "إدارة المخزون",
            Location = new Point(100, 25),
            Size = new Size(200, 30),
            Font = new Font("Segoe UI", 14F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            BackColor = Color.Transparent
        };

        // Search and Filter Panel
        var pnlSearchFilter = new Panel
        {
            Dock = DockStyle.Top,
            Height = 60,
            BackColor = Color.White,
            Padding = new Padding(10, 5, 10, 5)
        };

        // Search Section
        lblSearch = new Label
        {
            Text = "البحث:",
            Location = new Point(20, 20),
            Size = new Size(50, 23),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            TextAlign = ContentAlignment.MiddleLeft
        };

        txtSearch = new TextBox
        {
            Location = new Point(80, 18),
            Size = new Size(200, 25),
            Font = new Font("Segoe UI", 9F),
            BorderStyle = BorderStyle.FixedSingle
        };

        btnSearch = new Button
        {
            Text = "بحث",
            Location = new Point(290, 17),
            Size = new Size(70, 28),
            BackColor = Color.FromArgb(0, 102, 204),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnSearch.FlatAppearance.BorderSize = 0;

        // Filter Section
        lblBrand = new Label
        {
            Text = "الماركة:",
            Location = new Point(380, 20),
            Size = new Size(50, 23),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            TextAlign = ContentAlignment.MiddleLeft
        };

        cmbBrand = new ComboBox
        {
            Location = new Point(440, 18),
            Size = new Size(120, 25),
            DropDownStyle = ComboBoxStyle.DropDownList,
            Font = new Font("Segoe UI", 9F)
        };

        lblCondition = new Label
        {
            Text = "الحالة:",
            Location = new Point(580, 20),
            Size = new Size(50, 23),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            TextAlign = ContentAlignment.MiddleLeft
        };

        cmbCondition = new ComboBox
        {
            Location = new Point(640, 18),
            Size = new Size(100, 25),
            DropDownStyle = ComboBoxStyle.DropDownList,
            Font = new Font("Segoe UI", 9F)
        };

        // Action Buttons Panel
        var pnlButtons = new Panel
        {
            Dock = DockStyle.Top,
            Height = 120,
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // First row - Main action buttons with 12px gaps between buttons
        btnAddCar = CreateStyledButton("إضافة سيارة", new Point(20, 15), Color.FromArgb(40, 167, 69));
        btnEditCar = CreateStyledButton("تعديل", new Point(132, 15), Color.FromArgb(255, 193, 7), Color.Black);
        btnDeleteCar = CreateStyledButton("حذف", new Point(214, 15), Color.FromArgb(220, 53, 69));
        btnRefresh = CreateStyledButton("تحديث", new Point(296, 15), Color.FromArgb(0, 123, 255));

        // Second row - File management and print buttons with same 12px gap as first row
        btnUploadFiles = CreateStyledButton("رفع ملفات", new Point(20, 65), Color.FromArgb(108, 117, 125));
        btnViewFiles = CreateStyledButton("عرض الملفات", new Point(132, 65), Color.FromArgb(23, 162, 184));
        btnPrint = CreateStyledButton("طباعة", new Point(244, 65), Color.FromArgb(232, 62, 140));
        btnPrint.Click += BtnPrint_Click;

        // DataGridView
        dgvCars = new DataGridView
        {
            Dock = DockStyle.Fill,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            ReadOnly = true,
            AllowUserToAddRows = false,
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.Fixed3D,
            Font = new Font("Segoe UI", 9F),
            ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(0, 102, 204),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            DefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.White,
                ForeColor = Color.Black,
                SelectionBackColor = Color.FromArgb(173, 216, 230),
                SelectionForeColor = Color.Black,
                Alignment = DataGridViewContentAlignment.MiddleCenter
            }
        };

        // Setup condition combobox
        cmbCondition.Items.AddRange(new[] { "الكل", "جديدة", "مستعملة" });
        cmbCondition.SelectedIndex = 0;

        // Add controls to panels
        pnlHeader.Controls.AddRange(new Control[] { pbLogo, lblTitle });
        pnlSearchFilter.Controls.AddRange(new Control[]
        {
            lblSearch, txtSearch, btnSearch, lblBrand, cmbBrand, lblCondition, cmbCondition
        });
        pnlButtons.Controls.AddRange(new Control[]
        {
            btnAddCar, btnEditCar, btnDeleteCar, btnRefresh, btnUploadFiles, btnViewFiles, btnPrint
        });

        // Add panels to main panel
        pnlMain.Controls.Add(dgvCars);
        pnlMain.Controls.Add(pnlButtons);
        pnlMain.Controls.Add(pnlSearchFilter);
        pnlMain.Controls.Add(pnlHeader);

        this.Controls.Add(pnlMain);
    }

    private Button CreateStyledButton(string text, Point location, Color backColor, Color? foreColor = null)
    {
        // Calculate button width based on text length
        int baseWidth = 80;
        int textLength = text.Length;
        int calculatedWidth = Math.Max(baseWidth, textLength * 8 + 20);

        var button = new Button
        {
            Text = text,
            Location = location,
            Size = new Size(calculatedWidth, 35),
            BackColor = backColor,
            ForeColor = foreColor ?? Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            TextAlign = ContentAlignment.MiddleCenter,
            UseVisualStyleBackColor = false
        };
        button.FlatAppearance.BorderSize = 0;
        return button;
    }

    private void SetupEventHandlers()
    {
        btnSearch.Click += BtnSearch_Click;
        btnAddCar.Click += BtnAddCar_Click;
        btnEditCar.Click += BtnEditCar_Click;
        btnDeleteCar.Click += BtnDeleteCar_Click;
        btnRefresh.Click += (s, e) => LoadData();
        btnUploadFiles.Click += BtnUploadFiles_Click;
        btnViewFiles.Click += BtnViewFiles_Click;
        cmbCondition.SelectedIndexChanged += (s, e) => LoadData();
        cmbBrand.SelectedIndexChanged += (s, e) => LoadData();
    }

    private async void LoadData()
    {
        try
        {
            using var context = DbContextFactory.CreateContext();

            var query = context.Cars.AsQueryable();

            // Apply filters
            if(!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                var searchTerm = txtSearch.Text.ToLower();
                query = query.Where(c => c.ChassisNumber.ToLower().Contains(searchTerm) ||
                                    c.Brand.ToLower().Contains(searchTerm) ||
                                    c.Model.ToLower().Contains(searchTerm));
            }

            if(cmbBrand.SelectedItem != null && cmbBrand.SelectedItem.ToString() != "الكل")
            {
                query = query.Where(c => c.Brand == cmbBrand.SelectedItem.ToString());
            }

            if(cmbCondition.SelectedIndex > 0)
            {
                var condition = cmbCondition.SelectedIndex == 1 ? CarCondition.New : CarCondition.Used;
                query = query.Where(c => c.Condition == condition);
            }

            var cars = await query.ToListAsync();

            dgvCars.DataSource = cars.Select(c => new
            {
                رقم_الشاسيه = c.ChassisNumber,
                النوع = c.Type,
                الماركة = c.Brand,
                الموديل = c.Model,
                السنة = c.Year,
                اللون = c.Color,
                رقم_اللوحة = c.PlateNumber ?? "غير محدد",
                الكيلومترات = c.Mileage.ToString("N0"),
                سعر_الشراء = c.PurchasePrice.ToString("C"),
                سعر_البيع_المقترح = c.SuggestedSellPrice.ToString("C"),
                سعر_البيع_بالتقسيط = c.InstallmentSellPrice.ToString("C"),
                الحالة = c.Condition == CarCondition.New ? "جديدة" : "مستعملة",
                تاريخ_الشراء = c.PurchaseDate.ToString("yyyy-MM-dd"),
                مباعة = c.IsSold ? "نعم" : "لا"
            }).ToList();

            // Load brands for filter
            if(cmbBrand.Items.Count == 0)
            {
                var brands = await context.Cars.Select(c => c.Brand).Distinct().ToListAsync();
                cmbBrand.Items.Add("الكل");
                cmbBrand.Items.AddRange(brands.ToArray());
                cmbBrand.SelectedIndex = 0;
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void BtnSearch_Click(object? sender, EventArgs e)
    {
        LoadData();
    }

    private void BtnAddCar_Click(object? sender, EventArgs e)
    {
        var addForm = new AddEditCarForm();
        if(addForm.ShowDialog() == DialogResult.OK)
        {
            LoadData();
        }
    }

    private void BtnEditCar_Click(object? sender, EventArgs e)
    {
        if(dgvCars.SelectedRows.Count > 0)
        {
            var chassisNumber = dgvCars.SelectedRows[0].Cells["رقم_الشاسيه"].Value?.ToString();
            if(!string.IsNullOrEmpty(chassisNumber))
            {
                var editForm = new AddEditCarForm(chassisNumber);
                if(editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار سيارة للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private async void BtnDeleteCar_Click(object? sender, EventArgs e)
    {
        if(dgvCars.SelectedRows.Count > 0)
        {
            var chassisNumber = dgvCars.SelectedRows[0].Cells["رقم_الشاسيه"].Value?.ToString();
            if(!string.IsNullOrEmpty(chassisNumber))
            {
                // التحقق من الصلاحيات
                if(currentUser?.Permissions?.CanDeleteCar != true)
                {
                    MessageBox.Show("ليس لديك الصلاحيات اللازمة لحذف السيارة.", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                try
                {
                    // التحقق من صحة العملية باستخدام خدمة سلامة البيانات المالية
                    var validation = await Services.FinancialIntegrityService.ValidateCarDeletionAsync(chassisNumber);

                    if (!validation.CanDelete)
                    {
                        // عرض رسالة توضيحية للمستخدم
                        var message = $"❌ لا يمكن حذف السيارة\n\n" +
                                     $"السبب: {validation.Reason}\n\n";

                        if (validation.InstallmentInfo != null)
                        {
                            message += $"📊 تفاصيل الأقساط:\n" +
                                      $"• إجمالي الأقساط: {validation.InstallmentInfo.TotalInstallments}\n" +
                                      $"• المدفوع: {validation.InstallmentInfo.PaidInstallments}\n" +
                                      $"• المتبقي: {validation.InstallmentInfo.PendingInstallments}\n" +
                                      $"• المبلغ المتبقي: {validation.InstallmentInfo.RemainingAmount:N0} ج.م\n\n";
                        }

                        message += validation.WarningMessage ?? "";

                        MessageBox.Show(message, "لا يمكن حذف السيارة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // عرض نافذة التأكيد المتقدمة
                    using var confirmationForm = new CarDeletionConfirmationForm(validation);
                    var result = confirmationForm.ShowDialog();

                    if (result == DialogResult.Yes)
                    {
                        // تنفيذ الحذف الآمن
                        var deletionResult = await Services.FinancialIntegrityService.SafeDeleteCarAsync(
                            chassisNumber,
                            currentUser?.Username ?? "مجهول");

                        if (deletionResult.Success)
                        {
                            MessageBox.Show(
                                $"✅ {deletionResult.Message}\n\n" +
                                "تم ضمان سلامة البيانات المالية والمحاسبية.",
                                "تم الحذف بنجاح",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);

                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show(
                                $"❌ فشل في حذف السيارة\n\n{deletionResult.Message}",
                                "خطأ في الحذف",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Error);
                        }
                    }
                }
                catch(Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف السيارة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار سيارة للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnUploadFiles_Click(object? sender, EventArgs e)
    {
        if(dgvCars.SelectedRows.Count > 0)
        {
            var chassisNumber = dgvCars.SelectedRows[0].Cells["رقم_الشاسيه"].Value?.ToString();
            if(!string.IsNullOrEmpty(chassisNumber))
            {
                using var openFileDialog = new OpenFileDialog
                {
                    Filter = "All Files (*.*)|*.*|Images (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|Documents (*.pdf;*.doc;*.docx)|*.pdf;*.doc;*.docx",
                    Multiselect = true,
                    Title = "اختيار الملفات للرفع"
                };

                if(openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        // Create car files directory if it doesn't exist
                        var carFilesDir = Path.Combine("CarFiles", chassisNumber);
                        Directory.CreateDirectory(carFilesDir);

                        foreach(var file in openFileDialog.FileNames)
                        {
                            var fileName = Path.GetFileName(file);
                            var destinationPath = Path.Combine(carFilesDir, fileName);

                            // Copy file to car directory
                            File.Copy(file, destinationPath, true);
                        }

                        MessageBox.Show($"تم رفع {openFileDialog.FileNames.Length} ملف بنجاح", "نجح",
                                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch(Exception ex)
                    {
                        MessageBox.Show($"خطأ في رفع الملفات: {ex.Message}", "خطأ",
                                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار سيارة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnViewFiles_Click(object? sender, EventArgs e)
    {
        if(dgvCars.SelectedRows.Count > 0)
        {
            var chassisNumber = dgvCars.SelectedRows[0].Cells["رقم_الشاسيه"].Value?.ToString();
            if(!string.IsNullOrEmpty(chassisNumber))
            {
                var carFilesDir = Path.Combine("CarFiles", chassisNumber);

                if(Directory.Exists(carFilesDir))
                {
                    try
                    {
                        // Open the directory in Windows Explorer
                        Process.Start("explorer.exe", carFilesDir);
                    }
                    catch(Exception ex)
                    {
                        MessageBox.Show($"خطأ في فتح مجلد الملفات: {ex.Message}", "خطأ",
                                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show("لا توجد ملفات مرفوعة لهذه السيارة", "تنبيه",
                                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار سيارة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnPrint_Click(object? sender, EventArgs e)
    {
        if(dgvCars.Rows.Count == 0)
        {
            MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // استخدام نظام الطباعة بالتحديد
        using var selectiveForm = new SelectivePrintForm(SelectivePrintForm.PrintType.Inventory);
        if(selectiveForm.ShowDialog() == DialogResult.OK)
        {
            DataGridView dgvToPrint = CreateFilteredInventoryDataGridView(selectiveForm);

            if(dgvToPrint.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات تطابق المعايير المحددة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using var printForm = new PrintReportForm(dgvToPrint);
            printForm.Text = selectiveForm.PrintAll ? "طباعة جميع المخزون" : "طباعة عناصر محددة";
            printForm.ShowDialog();
        }
    }

    private DataGridView CreateFilteredInventoryDataGridView(SelectivePrintForm selectiveForm)
    {
        var filteredDgv = new DataGridView();

        // نسخ هيكل الأعمدة
        foreach(DataGridViewColumn col in dgvCars.Columns)
        {
            filteredDgv.Columns.Add((DataGridViewColumn)col.Clone());
        }

        try
        {
            using var context = DbContextFactory.CreateContext();
            var query = context.Cars.AsQueryable();

            // تطبيق فلتر التاريخ إذا كان محدداً
            if(selectiveForm.UseDateFilter)
            {
                if(selectiveForm.FromDate.HasValue)
                {
                    query = query.Where(c => c.CreatedDate >= selectiveForm.FromDate.Value);
                }
                if(selectiveForm.ToDate.HasValue)
                {
                    query = query.Where(c => c.CreatedDate <= selectiveForm.ToDate.Value);
                }
            }

            var cars = query.ToList();

            // إضافة البيانات المفلترة
            foreach(var car in cars)
            {
                var row = new DataGridViewRow();
                row.CreateCells(filteredDgv);

                row.Cells[0].Value = car.ChassisNumber;
                row.Cells[1].Value = car.Brand;
                row.Cells[2].Value = car.Model;
                row.Cells[3].Value = car.Year;
                row.Cells[4].Value = car.Color;
                row.Cells[5].Value = car.SuggestedSellPrice.ToString("C");
                row.Cells[6].Value = car.Condition == CarCondition.New ? "جديدة" : "مستعملة";
                row.Cells[7].Value = car.Status ?? "غير محدد";
                row.Cells[8].Value = car.CreatedDate.ToString("yyyy-MM-dd");

                filteredDgv.Rows.Add(row);
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        return filteredDgv;
    }

    /// <summary>
    /// بناء رسالة التأكيد لحذف السيارة
    /// </summary>
    private string BuildConfirmationMessage(CarDeletionValidationResult validation)
    {
        var message = "🚗 تفاصيل السيارة:\n";

        if (validation.CarInfo != null)
        {
            message += $"• رقم الشاسيه: {validation.CarInfo.ChassisNumber}\n";
            message += $"• الماركة والموديل: {validation.CarInfo.Brand} {validation.CarInfo.Model}\n";
            message += $"• السنة: {validation.CarInfo.Year}\n";
            message += $"• سعر الشراء: {validation.CarInfo.PurchasePrice:N0} ج.م\n";
            message += $"• سعر البيع: {validation.CarInfo.SellPrice:N0} ج.م\n\n";
        }

        if (validation.SaleInfo != null)
        {
            message += "💰 تفاصيل البيع:\n";
            message += $"• رقم البيع: {validation.SaleInfo.SaleId}\n";
            message += $"• تاريخ البيع: {validation.SaleInfo.SaleDate:yyyy/MM/dd}\n";
            message += $"• العميل: {validation.SaleInfo.CustomerName}\n";
            message += $"• سعر البيع الفعلي: {validation.SaleInfo.ActualSellPrice:N0} ج.م\n";
            message += $"• طريقة الدفع: {(validation.SaleInfo.PaymentMethod == Models.PaymentMethod.Cash ? "نقدي" : "تقسيط")}\n";
            message += $"• إجمالي المدفوع: {validation.SaleInfo.TotalPaid:N0} ج.م\n";
            message += $"• المبلغ المتبقي: {validation.SaleInfo.RemainingAmount:N0} ج.م\n\n";
        }

        if (validation.InstallmentInfo != null)
        {
            message += "📅 تفاصيل الأقساط:\n";
            message += $"• إجمالي الأقساط: {validation.InstallmentInfo.TotalInstallments}\n";
            message += $"• الأقساط المدفوعة: {validation.InstallmentInfo.PaidInstallments}\n";
            message += $"• الأقساط المتبقية: {validation.InstallmentInfo.PendingInstallments}\n";
            message += $"• إجمالي قيمة الأقساط: {validation.InstallmentInfo.TotalAmount:N0} ج.م\n";
            message += $"• المبلغ المدفوع: {validation.InstallmentInfo.PaidAmount:N0} ج.م\n";
            message += $"• المبلغ المتبقي: {validation.InstallmentInfo.RemainingAmount:N0} ج.م\n\n";
        }

        message += $"📋 الإجراء المطلوب: {validation.Reason}\n\n";

        if (!string.IsNullOrEmpty(validation.WarningMessage))
        {
            message += $"{validation.WarningMessage}\n\n";
        }

        message += "✅ سيتم تسجيل العملية في سجل المبيعات المؤرشف قبل الحذف لضمان سلامة البيانات المالية.\n\n";
        message += "هل تريد المتابعة؟";

        return message;
    }
}
}
