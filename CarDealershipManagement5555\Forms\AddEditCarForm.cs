using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace CarDealershipManagement.Forms
{
    public partial class AddEditCarForm : Form
    {
        private TextBox txtChassisNumber = null!;
        private TextBox txtType = null!;
        private TextBox txtBrand = null!;
        private TextBox txtModel = null!;
        private NumericUpDown numYear = null!;
        private TextBox txtColor = null!;
        private TextBox txtPlateNumber = null!;
        private NumericUpDown numMileage = null!;
        private NumericUpDown numPurchasePrice = null!;
        private NumericUpDown numSuggestedSellPrice = null!;
        private NumericUpDown numInstallmentSellPrice = null!;
        private ComboBox cmbCondition = null!;
        private DateTimePicker dtpPurchaseDate = null!;
        private Button btnSave = null!;
        private Button btnCancel = null!;
        private Label lblStatus = null!;

        private string? chassisNumber;

        public AddEditCarForm()
        {
            InitializeComponent();
        }

        public AddEditCarForm(string chassisNumber)
        {
            this.chassisNumber = chassisNumber;
            InitializeComponent();
            LoadCarData(chassisNumber);
        }

        private void InitializeComponent()
        {
            this.Text = chassisNumber == null ? "🚗 إضافة سيارة جديدة" : "✏️ تعديل بيانات السيارة";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Font = new Font("Segoe UI", 11F);
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimumSize = new Size(800, 700);
            this.MinimizeBox = false;

            // إنشاء لوحة رئيسية مع تمرير
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(50)
            };

            // Common styling for labels - محسن للشاشة الكاملة
            Action<Label> styleLabel = (lbl) =>
            {
                lbl.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
                lbl.ForeColor = Color.FromArgb(52, 58, 64);
                lbl.AutoSize = true;
            };

            // Common styling for textboxes and numeric up-downs - محسن للشاشة الكاملة
            Action<Control> styleInput = (ctrl) =>
            {
                ctrl.Font = new Font("Segoe UI", 11F);
                ctrl.BackColor = Color.White;
                ctrl.ForeColor = Color.Black;
                if (ctrl is TextBox || ctrl is NumericUpDown)
                {
                    ctrl.Height = 35;
                }
            };

            // عنوان النموذج
            var titleLabel = new Label
            {
                Text = chassisNumber == null ? "🚗 إضافة سيارة جديدة" : "✏️ تعديل بيانات السيارة",
                Location = new Point(50, 20),
                Size = new Size(600, 40),
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(50, 700),
                Size = new Size(800, 30),
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // الصف الأول - رقم الشاسيه
            var lblChassisNumber = new Label { Text = "🔢 رقم الشاسيه:", Location = new Point(50, 80) };
            styleLabel(lblChassisNumber);
            txtChassisNumber = new TextBox { Location = new Point(200, 78), Size = new Size(400, 35) };
            styleInput(txtChassisNumber);

            // الصف الثاني - النوع والماركة
            var lblType = new Label { Text = "🚙 النوع:", Location = new Point(50, 140) };
            styleLabel(lblType);
            txtType = new TextBox { Location = new Point(200, 138), Size = new Size(250, 35) };
            styleInput(txtType);

            var lblBrand = new Label { Text = "🏷️ الماركة:", Location = new Point(500, 140) };
            styleLabel(lblBrand);
            txtBrand = new TextBox { Location = new Point(600, 138), Size = new Size(250, 35) };
            styleInput(txtBrand);

            // الصف الثالث - الموديل والسنة
            var lblModel = new Label { Text = "📋 الموديل:", Location = new Point(50, 200) };
            styleLabel(lblModel);
            txtModel = new TextBox { Location = new Point(200, 198), Size = new Size(250, 35) };
            styleInput(txtModel);

            var lblYear = new Label { Text = "📅 السنة:", Location = new Point(500, 200) };
            styleLabel(lblYear);
            numYear = new NumericUpDown { Location = new Point(600, 198), Size = new Size(150, 35), Minimum = 1900, Maximum = DateTime.Now.Year };
            styleInput(numYear);

            // الصف الرابع - اللون ورقم اللوحة
            var lblColor = new Label { Text = "🎨 اللون:", Location = new Point(50, 260) };
            styleLabel(lblColor);
            txtColor = new TextBox { Location = new Point(200, 258), Size = new Size(250, 35) };
            styleInput(txtColor);

            var lblPlateNumber = new Label { Text = "🔢 رقم اللوحة:", Location = new Point(500, 260) };
            styleLabel(lblPlateNumber);
            txtPlateNumber = new TextBox { Location = new Point(650, 258), Size = new Size(200, 35) };
            styleInput(txtPlateNumber);

            // الصف الخامس - العداد وسعر الشراء
            var lblMileage = new Label { Text = "🛣️ العداد (كم):", Location = new Point(50, 320) };
            styleLabel(lblMileage);
            numMileage = new NumericUpDown { Location = new Point(200, 318), Size = new Size(200, 35), Maximum = 1000000 };
            styleInput(numMileage);

            var lblPurchasePrice = new Label { Text = "💰 سعر الشراء:", Location = new Point(500, 320) };
            styleLabel(lblPurchasePrice);
            numPurchasePrice = new NumericUpDown { Location = new Point(650, 318), Size = new Size(200, 35), DecimalPlaces = 2, Maximum = 1000000 };
            styleInput(numPurchasePrice);

            // الصف السادس - أسعار البيع
            var lblSuggestedSellPrice = new Label { Text = "💵 سعر البيع نقداً:", Location = new Point(50, 380) };
            styleLabel(lblSuggestedSellPrice);
            numSuggestedSellPrice = new NumericUpDown { Location = new Point(200, 378), Size = new Size(200, 35), DecimalPlaces = 2, Maximum = 1000000 };
            styleInput(numSuggestedSellPrice);

            var lblInstallmentSellPrice = new Label { Text = "💳 سعر البيع تقسيط:", Location = new Point(500, 380) };
            styleLabel(lblInstallmentSellPrice);
            numInstallmentSellPrice = new NumericUpDown { Location = new Point(700, 378), Size = new Size(200, 35), DecimalPlaces = 2, Maximum = 1000000 };
            styleInput(numInstallmentSellPrice);

            // الصف السابع - الحالة وتاريخ الشراء
            var lblCondition = new Label { Text = "⚙️ الحالة:", Location = new Point(50, 440) };
            styleLabel(lblCondition);
            cmbCondition = new ComboBox { Location = new Point(200, 438), Size = new Size(200, 35), DropDownStyle = ComboBoxStyle.DropDownList };
            cmbCondition.Items.AddRange(new[] { "جديدة", "مستعملة" });
            styleInput(cmbCondition);

            var lblPurchaseDate = new Label { Text = "📅 تاريخ الشراء:", Location = new Point(500, 440) };
            styleLabel(lblPurchaseDate);
            dtpPurchaseDate = new DateTimePicker { Location = new Point(650, 438), Size = new Size(200, 35), Format = DateTimePickerFormat.Short };
            styleInput(dtpPurchaseDate);

            // لوحة الأزرار
            var buttonPanel = new Panel
            {
                Location = new Point(50, 520),
                Size = new Size(800, 80),
                BackColor = Color.Transparent
            };

            // Save Button - محسن
            btnSave = new Button
            {
                Text = "💾 حفظ",
                Location = new Point(200, 20),
                Size = new Size(150, 45),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.FlatAppearance.MouseOverBackColor = Color.FromArgb(34, 139, 58);
            btnSave.Click += BtnSave_Click;

            // Cancel Button - محسن
            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Location = new Point(400, 20),
                Size = new Size(150, 45),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.FlatAppearance.MouseOverBackColor = Color.FromArgb(200, 35, 51);
            btnCancel.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });

            // إضافة العناصر للوحة الرئيسية
            mainPanel.Controls.AddRange(new Control[]
            {
                titleLabel, lblChassisNumber, txtChassisNumber,
                lblType, txtType, lblBrand, txtBrand,
                lblModel, txtModel, lblYear, numYear,
                lblColor, txtColor, lblPlateNumber, txtPlateNumber,
                lblMileage, numMileage, lblPurchasePrice, numPurchasePrice,
                lblSuggestedSellPrice, numSuggestedSellPrice,
                lblInstallmentSellPrice, numInstallmentSellPrice,
                lblCondition, cmbCondition, lblPurchaseDate, dtpPurchaseDate,
                buttonPanel, lblStatus
            });

            // إضافة اللوحة الرئيسية للنموذج
            this.Controls.Add(mainPanel);

            if (chassisNumber != null)
            {
                txtChassisNumber.Enabled = false;
            }
        }

        private async void LoadCarData(string chassisNumber)
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                var car = await context.Cars.FindAsync(chassisNumber);

                if (car != null)
                {
                    txtChassisNumber.Text = car.ChassisNumber;
                    txtType.Text = car.Type;
                    txtBrand.Text = car.Brand;
                    txtModel.Text = car.Model;
                    numYear.Value = car.Year;
                    txtColor.Text = car.Color;
                    txtPlateNumber.Text = car.PlateNumber ?? string.Empty;
                    numMileage.Value = car.Mileage;
                    numPurchasePrice.Value = car.PurchasePrice;
                    numSuggestedSellPrice.Value = car.SuggestedSellPrice;
                    numInstallmentSellPrice.Value = car.InstallmentSellPrice;
                    cmbCondition.SelectedIndex = car.Condition == CarCondition.New ? 0 : 1;
                    dtpPurchaseDate.Value = car.PurchaseDate;
                }
                else
                {
                    lblStatus.Text = "السيارة غير موجودة.";
                    lblStatus.ForeColor = Color.Red;
                    this.Close();
                }
            }, "تحميل بيانات السيارة");
        }

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages

            // Manual validation for required fields
            if (string.IsNullOrWhiteSpace(txtChassisNumber.Text) ||
                string.IsNullOrWhiteSpace(txtType.Text) ||
                string.IsNullOrWhiteSpace(txtBrand.Text) ||
                string.IsNullOrWhiteSpace(txtModel.Text) ||
                string.IsNullOrWhiteSpace(txtColor.Text))
            {
                lblStatus.Text = "يرجى إدخال جميع البيانات المطلوبة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            // Validate numeric values
            if (numYear.Value < numYear.Minimum || numYear.Value > numYear.Maximum ||
                numMileage.Value < numMileage.Minimum || numMileage.Value > numMileage.Maximum ||
                numPurchasePrice.Value < numPurchasePrice.Minimum || numPurchasePrice.Value > numPurchasePrice.Maximum ||
                numSuggestedSellPrice.Value < numSuggestedSellPrice.Minimum || numSuggestedSellPrice.Value > numSuggestedSellPrice.Maximum ||
                numInstallmentSellPrice.Value < numInstallmentSellPrice.Minimum || numInstallmentSellPrice.Value > numInstallmentSellPrice.Maximum)
            {
                lblStatus.Text = "يرجى إدخال قيم صحيحة للحقول الرقمية.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            // Validate CarCondition selection
            if (cmbCondition.SelectedIndex == -1)
            {
                lblStatus.Text = "يرجى اختيار حالة السيارة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                Car car;

                if (chassisNumber == null)  // New car
                {
                    // Check for duplicate chassis number for new cars
                    if (await context.Cars.AnyAsync(c => c.ChassisNumber == txtChassisNumber.Text))
                    {
                        lblStatus.Text = "رقم الشاسيه موجود بالفعل. يرجى إدخال رقم شاسيه فريد.";
                        lblStatus.ForeColor = Color.Red;
                        return;
                    }
                    car = new Car { ChassisNumber = txtChassisNumber.Text };
                    context.Cars.Add(car);
                }
                else // Existing car
                {
                    car = await context.Cars.FindAsync(chassisNumber);
                    if (car == null)
                    {
                        lblStatus.Text = "السيارة غير موجودة.";
                        lblStatus.ForeColor = Color.Red;
                        return;
                    }
                }

                car.Type = txtType.Text;
                car.Brand = txtBrand.Text;
                car.Model = txtModel.Text;
                car.Year = (int)numYear.Value;
                car.Color = txtColor.Text;
                car.PlateNumber = string.IsNullOrEmpty(txtPlateNumber.Text) ? null : txtPlateNumber.Text;
                car.Mileage = (int)numMileage.Value;
                car.PurchasePrice = numPurchasePrice.Value;
                car.SuggestedSellPrice = numSuggestedSellPrice.Value;
                car.InstallmentSellPrice = numInstallmentSellPrice.Value;
                car.Condition = cmbCondition.SelectedIndex == 0 ? CarCondition.New : CarCondition.Used;
                car.PurchaseDate = dtpPurchaseDate.Value;
                car.ModifiedDate = DateTime.Now;

                await context.SaveChangesAsync();
                MessageBox.Show("تم حفظ السيارة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }, "حفظ بيانات السيارة");
        }
    }
}


