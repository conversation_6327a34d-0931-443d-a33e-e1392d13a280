# Car Dealership Management System - Testing Script
# Test Date: July 4, 2025
# Version: 1.0.0

param(
    [string]$TestType = "Full",
    [switch]$GenerateReport,
    [string]$OutputPath = "TestResults"
)

Write-Host "============================================" -ForegroundColor Cyan
Write-Host "Car Dealership Management System Testing" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan

# Initialize test results
$TestResults = @()
$PassedTests = 0
$FailedTests = 0

function Write-TestResult {
    param($TestName, $Result, $Details = "")
    
    $TestResults += [PSCustomObject]@{
        TestName = $TestName
        Result = $Result
        Details = $Details
        Timestamp = Get-Date
    }
    
    if ($Result -eq "PASS") {
        Write-Host "✅ $TestName" -ForegroundColor Green
        $script:PassedTests++
    } else {
        Write-Host "❌ $TestName - $Details" -ForegroundColor Red
        $script:FailedTests++
    }
}

function Test-ApplicationBuild {
    Write-Host "`n📦 Testing Application Build..." -ForegroundColor Yellow
    
    try {
        $buildOutput = dotnet build --configuration Release 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-TestResult "Application Build" "PASS" "Build successful"
        } else {
            Write-TestResult "Application Build" "FAIL" "Build failed: $buildOutput"
        }
    } catch {
        Write-TestResult "Application Build" "FAIL" "Build exception: $_.Exception.Message"
    }
}

function Test-DatabaseConnectivity {
    Write-Host "`n🗄️  Testing Database Connectivity..." -ForegroundColor Yellow
    
    if (Test-Path "CarDealership.db") {
        Write-TestResult "Database File Exists" "PASS" "Database file found"
        
        # Test database size (should be > 0)
        $dbSize = (Get-Item "CarDealership.db").Length
        if ($dbSize -gt 0) {
            Write-TestResult "Database Size Check" "PASS" "Database size: $dbSize bytes"
        } else {
            Write-TestResult "Database Size Check" "FAIL" "Database file is empty"
        }
    } else {
        Write-TestResult "Database File Exists" "FAIL" "Database file not found"
    }
}

function Test-ApplicationStartup {
    Write-Host "`n🚀 Testing Application Startup..." -ForegroundColor Yellow
    
    try {
        # Start the application process
        $process = Start-Process -FilePath "bin\Release\net8.0-windows\CarDealershipManagement.exe" -PassThru -WindowStyle Minimized
        
        Start-Sleep -Seconds 3
        
        if ($process -and !$process.HasExited) {
            Write-TestResult "Application Startup" "PASS" "Application started successfully"
            
            # Try to gracefully close the application
            $process.CloseMainWindow()
            Start-Sleep -Seconds 2
            
            if (!$process.HasExited) {
                $process.Kill()
            }
            Write-TestResult "Application Shutdown" "PASS" "Application closed successfully"
        } else {
            Write-TestResult "Application Startup" "FAIL" "Application failed to start or crashed immediately"
        }
    } catch {
        Write-TestResult "Application Startup" "FAIL" "Exception: $_.Exception.Message"
    }
}

function Test-SecurityFeatures {
    Write-Host "`n🔐 Testing Security Features..." -ForegroundColor Yellow
    
    # Test BCrypt password hashing
    try {
        Add-Type -Path "bin\Release\net8.0-windows\BCrypt.Net-Next.dll"
        $testPassword = "TestPassword123!"
        $hash = [BCrypt.Net.BCrypt]::HashPassword($testPassword)
        $verify = [BCrypt.Net.BCrypt]::Verify($testPassword, $hash)
        
        if ($verify) {
            Write-TestResult "BCrypt Password Hashing" "PASS" "Password hashing and verification working"
        } else {
            Write-TestResult "BCrypt Password Hashing" "FAIL" "Password verification failed"
        }
    } catch {
        Write-TestResult "BCrypt Password Hashing" "FAIL" "Exception: $_.Exception.Message"
    }
    
    # Test default credentials security
    if ((Get-Content "Data\CarDealershipContext.cs" | Select-String "dev123").Count -gt 0) {
        Write-TestResult "Default Password Security" "FAIL" "Default weak password still in use"
    } else {
        Write-TestResult "Default Password Security" "PASS" "Default password appears to be changed"
    }
}

function Test-FileStructure {
    Write-Host "`n📁 Testing File Structure..." -ForegroundColor Yellow
    
    $requiredFiles = @(
        "CarDealershipManagement.csproj",
        "Program.cs",
        "Data\CarDealershipContext.cs",
        "Models\Car.cs",
        "Models\Customer.cs",
        "Models\Sale.cs",
        "Forms\LoginForm.cs",
        "Forms\MainDashboard.cs",
        "Forms\SalesForm.cs"
    )
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-TestResult "File: $file" "PASS" "File exists"
        } else {
            Write-TestResult "File: $file" "FAIL" "File missing"
        }
    }
}

function Test-CodeQuality {
    Write-Host "`n🔍 Testing Code Quality..." -ForegroundColor Yellow
    
    # Check for common code issues
    $csharpFiles = Get-ChildItem -Recurse -Filter "*.cs" | Where-Object { $_.Directory.Name -notlike "*bin*" -and $_.Directory.Name -notlike "*obj*" }
    
    $totalLines = 0
    $emptyLines = 0
    $commentLines = 0
    
    foreach ($file in $csharpFiles) {
        $content = Get-Content $file.FullName -ErrorAction SilentlyContinue
        if ($content) {
            $totalLines += $content.Count
            $emptyLines += ($content | Where-Object { $_.Trim() -eq "" }).Count
            $commentLines += ($content | Where-Object { $_.Trim().StartsWith("//") }).Count
        }
    }
    
    $codeLines = $totalLines - $emptyLines
    $commentRatio = if ($codeLines -gt 0) { [math]::Round(($commentLines / $codeLines) * 100, 2) } else { 0 }
    
    Write-TestResult "Code Files Count" "PASS" "$($csharpFiles.Count) C# files found"
    Write-TestResult "Total Lines of Code" "PASS" "$codeLines lines (excluding empty lines)"
    
    if ($commentRatio -gt 10) {
        Write-TestResult "Code Documentation" "PASS" "Comment ratio: $commentRatio%"
    } else {
        Write-TestResult "Code Documentation" "FAIL" "Low comment ratio: $commentRatio% (should be >10%)"
    }
}

function Test-Dependencies {
    Write-Host "`n📦 Testing Dependencies..." -ForegroundColor Yellow
    
    # Check if required NuGet packages are present
    $packagesConfig = Get-Content "CarDealershipManagement.csproj" -ErrorAction SilentlyContinue
    
    $requiredPackages = @(
        "BCrypt.Net-Next",
        "Microsoft.EntityFrameworkCore.Sqlite",
        "Microsoft.EntityFrameworkCore.Tools"
    )
    
    foreach ($package in $requiredPackages) {
        if ($packagesConfig -match $package) {
            Write-TestResult "Package: $package" "PASS" "Package reference found"
        } else {
            Write-TestResult "Package: $package" "FAIL" "Package reference missing"
        }
    }
}

function Test-Performance {
    Write-Host "`n⚡ Testing Performance..." -ForegroundColor Yellow
    
    # Test application file size
    $exePath = "bin\Release\net8.0-windows\CarDealershipManagement.exe"
    if (Test-Path $exePath) {
        $exeSize = (Get-Item $exePath).Length / 1MB
        
        if ($exeSize -lt 50) {
            Write-TestResult "Application Size" "PASS" "Executable size: $([math]::Round($exeSize, 2)) MB"
        } else {
            Write-TestResult "Application Size" "FAIL" "Executable too large: $([math]::Round($exeSize, 2)) MB (should be <50MB)"
        }
    }
    
    # Test startup time (estimated)
    $startTime = Get-Date
    try {
        $process = Start-Process -FilePath $exePath -PassThru -WindowStyle Hidden
        Start-Sleep -Seconds 1
        
        if ($process -and !$process.HasExited) {
            $startupTime = (Get-Date) - $startTime
            if ($startupTime.TotalSeconds -lt 10) {
                Write-TestResult "Startup Performance" "PASS" "Startup time: $([math]::Round($startupTime.TotalSeconds, 2)) seconds"
            } else {
                Write-TestResult "Startup Performance" "FAIL" "Slow startup: $([math]::Round($startupTime.TotalSeconds, 2)) seconds"
            }
            
            $process.Kill()
        }
    } catch {
        Write-TestResult "Startup Performance" "FAIL" "Could not measure startup time"
    }
}

function Test-Localization {
    Write-Host "`n🌐 Testing Localization..." -ForegroundColor Yellow
    
    # Check for Arabic text in forms
    $formFiles = Get-ChildItem "Forms\*.cs"
    $hasArabicText = $false
    
    foreach ($file in $formFiles) {
        $content = Get-Content $file.FullName -Encoding UTF8
        if ($content -match "[\u0600-\u06FF]") {
            $hasArabicText = $true
            break
        }
    }
    
    if ($hasArabicText) {
        Write-TestResult "Arabic Localization" "PASS" "Arabic text found in forms"
    } else {
        Write-TestResult "Arabic Localization" "FAIL" "No Arabic text found"
    }
}

# Run tests based on TestType parameter
switch ($TestType.ToLower()) {
    "quick" {
        Test-ApplicationBuild
        Test-FileStructure
        Test-Dependencies
    }
    "security" {
        Test-SecurityFeatures
    }
    "performance" {
        Test-Performance
    }
    default {
        # Full test suite
        Test-ApplicationBuild
        Test-DatabaseConnectivity
        Test-FileStructure
        Test-Dependencies
        Test-CodeQuality
        Test-SecurityFeatures
        Test-Performance
        Test-Localization
        Test-ApplicationStartup
    }
}

# Generate test report
Write-Host "`n" -ForegroundColor White
Write-Host "============================================" -ForegroundColor Cyan
Write-Host "TEST SUMMARY" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan
Write-Host "✅ Passed: $PassedTests" -ForegroundColor Green
Write-Host "❌ Failed: $FailedTests" -ForegroundColor Red
Write-Host "📊 Total:  $($PassedTests + $FailedTests)" -ForegroundColor Blue

$successRate = if (($PassedTests + $FailedTests) -gt 0) {
    [math]::Round(($PassedTests / ($PassedTests + $FailedTests)) * 100, 2)
} else { 0 }

Write-Host "🎯 Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 75) { "Yellow" } else { "Red" })

if ($GenerateReport) {
    if (!(Test-Path $OutputPath)) {
        New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    }
    
    $reportPath = Join-Path $OutputPath "TestReport_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    $TestResults | ConvertTo-Json -Depth 3 | Out-File $reportPath -Encoding UTF8
    
    Write-Host "`n📄 Test report saved to: $reportPath" -ForegroundColor Cyan
}

if ($FailedTests -gt 0) {
    Write-Host "`n⚠️  Some tests failed. Please review the issues above." -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "`n🎉 All tests passed successfully!" -ForegroundColor Green
    exit 0
}
