@echo off
chcp 65001 >nul
title تطبيق الأيقونة على كامل البرنامج - ما عدا لوجو المعرض

echo.
echo ========================================
echo    🎨 تطبيق الأيقونة على كامل البرنامج
echo ========================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🔍 التحقق من وجود الأيقونة الاحترافية...

if not exist "professional-car-icon.ico" (
    echo ❌ ملف professional-car-icon.ico غير موجود
    echo 🔄 إنشاء الأيقونة أولاً...
    powershell -ExecutionPolicy Bypass -File "إنشاء_أيقونة_احترافية_جديدة.ps1"
)

if not exist "professional-car-icon.png" (
    echo ❌ ملف professional-car-icon.png غير موجود
    pause
    exit /b 1
)

echo ✅ تم العثور على ملفات الأيقونة

echo.
echo 📋 تطبيق الأيقونة على المجلد الرئيسي...

REM نسخ الأيقونات إلى المجلد الرئيسي
copy "professional-car-icon.ico" "app-icon.ico" >nul
copy "professional-car-icon.png" "app-icon.png" >nul
copy "professional-car-icon.svg" "app-icon.svg" >nul

echo ✅ تم تحديث المجلد الرئيسي

echo.
echo 📁 تطبيق الأيقونة على مجلد Debug...

set "DEBUG_DIR=bin\Debug\net8.0-windows"
if exist "%DEBUG_DIR%" (
    copy "app-icon.ico" "%DEBUG_DIR%\app-icon.ico" >nul
    copy "app-icon.png" "%DEBUG_DIR%\app-icon.png" >nul
    copy "app-icon.svg" "%DEBUG_DIR%\app-icon.svg" >nul
    echo ✅ تم تحديث مجلد Debug
) else (
    echo ⚠️ مجلد Debug غير موجود
)

echo.
echo 📁 تطبيق الأيقونة على مجلد Release...

set "RELEASE_DIR=bin\Release\net8.0-windows"
if exist "%RELEASE_DIR%" (
    copy "app-icon.ico" "%RELEASE_DIR%\app-icon.ico" >nul
    copy "app-icon.png" "%RELEASE_DIR%\app-icon.png" >nul
    copy "app-icon.svg" "%RELEASE_DIR%\app-icon.svg" >nul
    echo ✅ تم تحديث مجلد Release
) else (
    echo ⚠️ مجلد Release غير موجود
)

echo.
echo 📁 تطبيق الأيقونة على المجلد المستقل...

set "STANDALONE_DIR=CarDealership_Standalone"
if exist "%STANDALONE_DIR%" (
    copy "app-icon.ico" "%STANDALONE_DIR%\app-icon.ico" >nul
    copy "app-icon.png" "%STANDALONE_DIR%\app-icon.png" >nul
    copy "app-icon.svg" "%STANDALONE_DIR%\app-icon.svg" >nul
    echo ✅ تم تحديث المجلد المستقل
) else (
    echo ⚠️ المجلد المستقل غير موجود
)

echo.
echo 📁 تطبيق الأيقونة على مجلد Debug Copy...

set "DEBUG_COPY_DIR=CarDealership_Debug_Copy"
if exist "%DEBUG_COPY_DIR%" (
    copy "app-icon.ico" "%DEBUG_COPY_DIR%\app-icon.ico" >nul
    copy "app-icon.png" "%DEBUG_COPY_DIR%\app-icon.png" >nul
    copy "app-icon.svg" "%DEBUG_COPY_DIR%\app-icon.svg" >nul
    echo ✅ تم تحديث مجلد Debug Copy
) else (
    echo ⚠️ مجلد Debug Copy غير موجود
)

echo.
echo 📁 تطبيق الأيقونة على مجلد Installer...

set "INSTALLER_DIR=CarDealership_Installer"
if exist "%INSTALLER_DIR%" (
    copy "app-icon.ico" "%INSTALLER_DIR%\app-icon.ico" >nul
    copy "app-icon.png" "%INSTALLER_DIR%\app-icon.png" >nul
    copy "app-icon.svg" "%INSTALLER_DIR%\app-icon.svg" >nul
    echo ✅ تم تحديث مجلد Installer
) else (
    echo ⚠️ مجلد Installer غير موجود
)

echo.
echo 📁 تطبيق الأيقونة على مجلد Resources (إذا كان موجوداً)...

set "RESOURCES_DIR=Resources"
if exist "%RESOURCES_DIR%" (
    copy "app-icon.ico" "%RESOURCES_DIR%\app-icon.ico" >nul
    copy "app-icon.png" "%RESOURCES_DIR%\app-icon.png" >nul
    copy "app-icon.svg" "%RESOURCES_DIR%\app-icon.svg" >nul
    echo ✅ تم تحديث مجلد Resources
) else (
    echo ℹ️ مجلد Resources غير موجود (اختياري)
)

echo.
echo 🔧 التأكد من إعدادات ملف المشروع...

REM التحقق من وجود إعداد الأيقونة في ملف المشروع
findstr /C:"ApplicationIcon" "CarDealershipManagement.csproj" >nul
if %ERRORLEVEL% NEQ 0 (
    echo 📝 إضافة إعداد الأيقونة إلى ملف المشروع...
    powershell -Command "
    $content = Get-Content 'CarDealershipManagement.csproj' -Raw
    if ($content -notmatch 'ApplicationIcon') {
        $content = $content -replace '(<PropertyGroup>)', '$1`n    <ApplicationIcon>app-icon.ico</ApplicationIcon>'
        Set-Content 'CarDealershipManagement.csproj' $content -Encoding UTF8
        Write-Host '✅ تم إضافة إعداد الأيقونة' -ForegroundColor Green
    }
    "
) else (
    echo ✅ إعداد الأيقونة موجود في ملف المشروع
)

echo.
echo 🚫 تجاهل مجلد اللوجو (كما طلبت)...
echo ℹ️ لن يتم تغيير ملفات اللوجو في مجلد Resources أو Images

echo.
echo 🔨 إعادة بناء البرنامج مع الأيقونة الجديدة...

REM محاولة بناء البرنامج
dotnet clean CarDealershipManagement.csproj >nul 2>&1
dotnet build CarDealershipManagement.csproj --configuration Debug >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء البرنامج بنجاح مع الأيقونة الجديدة
) else (
    echo ⚠️ تحذير: قد تكون هناك مشاكل في البناء، لكن الأيقونات تم تطبيقها
)

echo.
echo 📊 ملخص العملية:
echo.

echo ✅ تم تطبيق الأيقونة على:
if exist "app-icon.ico" echo    • المجلد الرئيسي
if exist "%DEBUG_DIR%\app-icon.ico" echo    • مجلد Debug
if exist "%RELEASE_DIR%\app-icon.ico" echo    • مجلد Release
if exist "%STANDALONE_DIR%\app-icon.ico" echo    • المجلد المستقل
if exist "%DEBUG_COPY_DIR%\app-icon.ico" echo    • مجلد Debug Copy
if exist "%INSTALLER_DIR%\app-icon.ico" echo    • مجلد Installer
if exist "%RESOURCES_DIR%\app-icon.ico" echo    • مجلد Resources

echo.
echo 🚫 تم تجاهل (كما طلبت):
echo    • ملفات اللوجو الخاصة بالمعرض
echo    • أي ملفات logo.png أو logo.jpg

echo.
echo 🎯 الملفات المطبقة:
echo    • app-icon.ico - أيقونة Windows الرسمية
echo    • app-icon.png - أيقونة عالية الدقة
echo    • app-icon.svg - أيقونة متجهة قابلة للتحجيم

echo.
echo 🎉 تم تطبيق الأيقونة على كامل البرنامج بنجاح!
echo.
echo 💡 يمكنك الآن:
echo    1. تشغيل البرنامج ورؤية الأيقونة الجديدة
echo    2. إنشاء ملف تثبيت جديد
echo    3. توزيع النسخة المحدثة

echo.
echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy

echo.
pause
