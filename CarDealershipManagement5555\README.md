# Car Dealership Management System
## نظام إدارة معرض السيارات

A comprehensive Windows application for managing car dealership operations, built with C# .NET 8 and Entity Framework Core.

## Features / الميزات

### Core Modules / الوحدات الأساسية
- **Inventory Management / إدارة المخزون**: Add, edit, and track vehicle inventory
- **Sales Management / إدارة المبيعات**: Process sales with cash or installment payments
- **Customer Management / إدارة العملاء**: Maintain customer database with documents
- **Supplier Management / إدارة الموردين**: Track suppliers and their payments
- **User Management / إدارة المستخدمين**: Role-based access control system
- **Financial Reports / التقارير المالية**: Comprehensive reporting system
- **Accounting / المحاسبة**: Track payments and financial transactions

### Security Features / ميزات الأمان
- BCrypt password hashing
- Role-based permissions (<PERSON><PERSON><PERSON>, Manager, Sales Representative)
- User activity logging
- Secure database connections

## System Requirements / متطلبات النظام

### Software Requirements
- Windows 10/11 (64-bit)
- .NET 8.0 Runtime
- SQLite (included with application)
- Minimum 2GB RAM
- 1GB available disk space

### Hardware Requirements
- Intel/AMD processor (x64)
- 1920x1080 screen resolution (recommended)
- Network connection (for updates)

## Installation / التثبيت

### Method 1: Pre-built Release
1. Download the latest release from the releases section
2. Extract the ZIP file to your desired location
3. Run `CarDealershipManagement.exe`

### Method 2: Build from Source
```bash
# Clone the repository
git clone [repository-url]

# Navigate to project directory
cd CarDealershipManagement

# Restore dependencies
dotnet restore

# Build the application
dotnet build --configuration Release

# Run the application
dotnet run --configuration Release
```

## First Time Setup / الإعداد الأولي

### Default Login Credentials
- **Username**: `amrali`
- **Password**: `braa`

⚠️ **Important**: Change the default password immediately after first login!

### Initial Configuration
1. Launch the application
2. Login with default credentials
3. Go to **الإدارة** → **الإعدادات** (Management → Settings)
4. Configure:
   - Company information
   - Currency settings
   - Backup preferences
   - System preferences

## User Guide / دليل المستخدم

### Main Dashboard / الشاشة الرئيسية
The main dashboard provides access to all system modules based on user permissions.

### Inventory Management / إدارة المخزون
- **Add New Car**: المخزن → إضافة سيارة جديدة
- **View Inventory**: المخزن → عرض المخزون
- **Edit Car Details**: Select car from inventory and edit

### Sales Process / عملية البيع
1. Navigate to **المبيعات** → **بيع سيارة**
2. Select car from available inventory
3. Choose or add customer
4. Set sale price and payment method
5. For installments: Configure down payment and schedule
6. Complete the sale

### Customer Management / إدارة العملاء
- Add customer information and documents
- Track customer sales history
- Manage customer payments and installments

### Reports / التقارير
Access comprehensive reports for:
- Sales analytics
- Inventory status
- Financial summaries
- Customer reports

## Database Schema / مخطط قاعدة البيانات

### Core Tables
- **Cars**: Vehicle inventory with specifications
- **Customers**: Customer information and documents
- **Sales**: Sales transactions and details
- **InstallmentPayments**: Payment schedules and tracking
- **Suppliers**: Supplier information and payments
- **Users**: System users and authentication
- **UserPermissions**: Role-based access control

### File Storage
- Car images and documents
- Customer documents
- Supplier documentation

## Backup and Recovery / النسخ الاحتياطي والاستعادة

### Automatic Backup
Configure automatic backups in system settings:
- Backup interval (hours)
- Backup location
- Retention policy

### Manual Backup
1. Go to **الإدارة** → **الإعدادات**
2. Click **إنشاء نسخة احتياطية**
3. Choose backup location

### Database Recovery
1. Close the application
2. Replace `CarDealership.db` with backup file
3. Restart application

## Troubleshooting / استكشاف الأخطاء

### Common Issues

#### Application Won't Start
- Ensure .NET 8.0 Runtime is installed
- Check if antivirus is blocking the application
- Run as administrator if needed

#### Database Errors
- Verify database file permissions
- Check available disk space
- Restore from backup if corrupted

#### Login Issues
- Verify credentials
- Check if user account is active
- Contact administrator for password reset

### Log Files
Application logs are stored in:
```
%AppData%\CarDealershipManagement\Logs\
```

## Security Considerations / اعتبارات الأمان

### Password Policy
- Minimum 8 characters
- Use strong passwords with mixed case, numbers, and symbols
- Change default passwords immediately
- Regular password updates recommended

### User Access
- Assign minimum required permissions
- Regularly review user accounts
- Disable inactive accounts

### Data Protection
- Regular database backups
- Secure file storage
- Network security for multi-user environments

## Support / الدعم الفني

### Documentation
- User manual (this file)
- System administration guide
- API documentation (for developers)

### Getting Help
- Check troubleshooting section
- Review system logs
- Contact system administrator

## Version History / تاريخ الإصدارات

### Version 1.0.0
- Initial release
- Core functionality implemented
- Basic reporting system
- User management with roles

## Development Notes / ملاحظات التطوير

### Architecture
- **Framework**: .NET 8 Windows Forms
- **Database**: SQLite with Entity Framework Core
- **Authentication**: BCrypt password hashing
- **File Storage**: Local file system

### Code Structure
```
CarDealershipManagement/
├── Data/               # Database context and configuration
├── Models/             # Entity models
├── Forms/              # User interface forms
├── Services/           # Business logic (future enhancement)
└── bin/                # Compiled application
```

### Future Enhancements
- Web-based interface
- Multi-location support
- Advanced reporting
- API for third-party integrations
- Mobile application support

## License / الترخيص

This software is proprietary. All rights reserved.

---

## Contact Information / معلومات الاتصال

For technical support or inquiries, please contact the system administrator.

**System Version**: 1.0.0  
**Last Updated**: July 2025  
**Compatible with**: Windows 10/11, .NET 8.0
