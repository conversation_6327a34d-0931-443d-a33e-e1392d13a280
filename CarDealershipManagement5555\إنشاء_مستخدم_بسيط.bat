@echo off
chcp 65001 >nul
title إنشاء مستخدم بسيط

echo.
echo ========================================
echo    🔧 إنشاء مستخدم بسيط
echo ========================================
echo.

echo 🔑 إنشاء مستخدم بكلمة مرور بسيطة...
echo.

REM إنشاء ملف SQL مؤقت
echo CREATE TABLE IF NOT EXISTS Users ( > temp_user.sql
echo     UserId INTEGER PRIMARY KEY AUTOINCREMENT, >> temp_user.sql
echo     Username TEXT UNIQUE NOT NULL, >> temp_user.sql
echo     PasswordHash TEXT NOT NULL, >> temp_user.sql
echo     FullName TEXT NOT NULL, >> temp_user.sql
echo     Role TEXT NOT NULL DEFAULT 'Admin', >> temp_user.sql
echo     IsActive INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CreatedDate TEXT NOT NULL, >> temp_user.sql
echo     LastLoginDate TEXT, >> temp_user.sql
echo     CanViewSales INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanEditSales INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanDeleteSales INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanViewCustomers INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanEditCustomers INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanDeleteCustomers INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanViewInventory INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanEditInventory INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanDeleteInventory INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanViewReports INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanViewFinancials INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanManageUsers INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanBackupRestore INTEGER NOT NULL DEFAULT 1, >> temp_user.sql
echo     CanAccessSettings INTEGER NOT NULL DEFAULT 1 >> temp_user.sql
echo ^); >> temp_user.sql
echo. >> temp_user.sql
echo DELETE FROM Users; >> temp_user.sql
echo. >> temp_user.sql
echo INSERT INTO Users ^( >> temp_user.sql
echo     Username, PasswordHash, FullName, Role, IsActive, CreatedDate, >> temp_user.sql
echo     CanViewSales, CanEditSales, CanDeleteSales, >> temp_user.sql
echo     CanViewCustomers, CanEditCustomers, CanDeleteCustomers, >> temp_user.sql
echo     CanViewInventory, CanEditInventory, CanDeleteInventory, >> temp_user.sql
echo     CanViewReports, CanViewFinancials, CanManageUsers, >> temp_user.sql
echo     CanBackupRestore, CanAccessSettings >> temp_user.sql
echo ^) VALUES ^( >> temp_user.sql
echo     'amrali', 'braa', 'عمرو علي', 'Admin', 1, datetime^('now'^), >> temp_user.sql
echo     1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 >> temp_user.sql
echo ^); >> temp_user.sql

echo ✅ تم إنشاء ملف SQL
echo.

REM تطبيق التغييرات على قاعدة البيانات الرئيسية
if exist "CarDealership.db" (
    echo 📋 تحديث قاعدة البيانات الرئيسية...
    sqlite3 CarDealership.db < temp_user.sql
    echo ✅ تم تحديث قاعدة البيانات الرئيسية
)

REM تطبيق التغييرات على قاعدة البيانات في مجلد bin
if exist "bin\Debug\net8.0-windows\CarDealership.db" (
    echo 📋 تحديث قاعدة البيانات في مجلد bin...
    sqlite3 "bin\Debug\net8.0-windows\CarDealership.db" < temp_user.sql
    echo ✅ تم تحديث قاعدة البيانات في مجلد bin
)

REM إنشاء قاعدة بيانات جديدة إذا لم تكن موجودة
if not exist "CarDealership.db" (
    if not exist "bin\Debug\net8.0-windows\CarDealership.db" (
        echo 🆕 إنشاء قاعدة بيانات جديدة...
        sqlite3 CarDealership.db < temp_user.sql
        echo ✅ تم إنشاء قاعدة بيانات جديدة
        
        REM نسخ إلى مجلد bin
        if not exist "bin\Debug\net8.0-windows" mkdir "bin\Debug\net8.0-windows"
        copy "CarDealership.db" "bin\Debug\net8.0-windows\CarDealership.db" >nul
        echo ✅ تم نسخ قاعدة البيانات إلى مجلد bin
    )
)

REM حذف الملف المؤقت
del temp_user.sql >nul 2>&1

echo.
echo ✅ تم إنشاء المستخدم بنجاح!
echo.
echo 🔑 بيانات الدخول:
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo.
echo 💡 نصائح مهمة:
echo    • اكتب البيانات بالضبط كما هي مكتوبة
echo    • لا تضع مسافات إضافية
echo    • تأكد من أن Caps Lock غير مفعل
echo    • جرب نسخ ولصق البيانات
echo.
echo 🚀 يمكنك الآن:
echo    1. تشغيل البرنامج
echo    2. تسجيل الدخول بالبيانات أعلاه
echo    3. تجربة الميزات الجديدة:
echo       • نظام الحذف الآمن في "إدارة المخزون"
echo       • تبويب الأقساط المحسن في "التقارير"
echo.
echo 📞 للمساعدة أو الدعم الفني، اتصل بالمطور
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
