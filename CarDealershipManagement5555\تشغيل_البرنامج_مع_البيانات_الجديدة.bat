@echo off
chcp 65001 >nul
title برنامج إدارة معرض السيارات - البيانات المحدثة

echo.
echo ========================================
echo    🚗 برنامج إدارة معرض السيارات 🚗
echo ========================================
echo.

echo 🔧 فحص وإصلاح قاعدة البيانات...
echo.

REM التأكد من وجود قاعدة البيانات
if not exist "CarDealership.db" (
    if exist "bin\Debug\net8.0-windows\CarDealership.db" (
        echo 📋 نسخ قاعدة البيانات...
        copy "bin\Debug\net8.0-windows\CarDealership.db" "CarDealership.db" >nul
        echo ✅ تم نسخ قاعدة البيانات
    )
)

if not exist "bin\Debug\net8.0-windows\CarDealership.db" (
    if exist "CarDealership.db" (
        echo 📋 نسخ قاعدة البيانات إلى مجلد bin...
        if not exist "bin\Debug\net8.0-windows" mkdir "bin\Debug\net8.0-windows"
        copy "CarDealership.db" "bin\Debug\net8.0-windows\CarDealership.db" >nul
        echo ✅ تم نسخ قاعدة البيانات
    )
)

echo.
echo 🚀 تشغيل البرنامج...
echo.

cd /d "%~dp0\bin\Debug\net8.0-windows"

if exist "CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج
    echo.
    echo 🔑 بيانات الدخول المحدثة:
    echo    اسم المستخدم: amrali
    echo    كلمة المرور: braa
    echo.
    echo 🔒 الميزات الجديدة المتاحة:
    echo.
    echo 📊 نظام ضمان سلامة البيانات المالية:
    echo    • منع حذف السيارات إلا بعد تسجيل بياناتها في سجل المبيعات
    echo    • التحقق من حالة الأقساط قبل الحذف
    echo    • أرشفة تلقائية للبيانات المالية
    echo    • نافذة تأكيد متقدمة مع التفاصيل المالية
    echo.
    echo 📅 تبويب الأقساط المحسن:
    echo    • تقارير شاملة مع فلترة متقدمة
    echo    • إحصائيات متقدمة (معدل التحصيل، المتأخرات)
    echo    • طباعة محسنة وتصدير بصيغ متعددة
    echo    • ملخص سريع للأقساط
    echo.
    echo 📋 خطوات الاستخدام:
    echo    1. سجل الدخول باستخدام البيانات أعلاه
    echo    2. لتجربة نظام الحذف الآمن: انتقل إلى "إدارة المخزون"
    echo    3. لتجربة تبويب الأقساط المحسن: اختر "التقارير" → "📅 تقارير الأقساط"
    echo.
    echo 🎯 حالات الاختبار المقترحة:
    echo    • جرب حذف سيارة غير مباعة (يجب أن يتم الحذف مباشرة)
    echo    • جرب حذف سيارة مباعة (يجب أن تظهر نافذة التأكيد المتقدمة)
    echo    • استخدم فلاتر تقرير الأقساط المختلفة
    echo    • جرب الطباعة المحسنة والتصدير
    echo.
    echo 🚀 جاري فتح البرنامج...
    echo.
    
    REM تشغيل البرنامج
    start "" "CarDealershipManagement.exe"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    echo.
    echo 💡 نصائح مهمة:
    echo    • إذا لم يظهر البرنامج، تحقق من شريط المهام
    echo    • استخدم Alt+Tab للتنقل بين النوافذ
    echo    • تأكد من أن Windows Defender لا يحجب البرنامج
    echo.
    echo 🔒 ميزات الأمان الجديدة:
    echo    • حماية شاملة للبيانات المالية
    echo    • تتبع جميع العمليات والمستخدمين
    echo    • أرشفة تلقائية للمراجعة المحاسبية
    echo.
    
) else (
    echo ❌ خطأ: لم يتم العثور على ملف البرنامج
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. تأكد من بناء المشروع في Visual Studio
    echo    2. تحقق من مسار الملف
    echo    3. أعد تشغيل Visual Studio كمسؤول
    echo.
)

cd /d "%~dp0"

echo 📞 للمساعدة أو الدعم الفني، اتصل بالمطور
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
