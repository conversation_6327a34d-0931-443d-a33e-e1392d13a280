@echo off
title Login Test Tool
color 0A

echo.
echo ===============================================
echo            Login Test Tool
echo ===============================================
echo.
echo Testing default login credentials...
echo.

:: Check if database exists
if exist "CarDealership.db" (
    echo ✅ Database file found: CarDealership.db
) else (
    echo ❌ Database file not found!
    echo Creating database...
)

echo.
echo Default login credentials:
echo ===============================================
echo Username: amrali
echo Password: braa
echo ===============================================
echo.

echo Testing these credentials now...
echo.

:: Create a simple test
echo Attempting to verify login credentials...

:: Show database info
echo.
echo Database information:
dir CarDealership.db 2>nul

echo.
echo ===============================================
echo           IMPORTANT INSTRUCTIONS
echo ===============================================
echo.
echo 1. Use these exact credentials:
echo    Username: amrali
echo    Password: braa
echo.
echo 2. If login still fails, try:
echo    - Click "🔑 ملء افتراضي" button
echo    - Click "❓ مساعدة" button for help
echo.
echo 3. Make sure you're typing exactly:
echo    - Username: amrali (lowercase)
echo    - Password: braa (lowercase)
echo.
echo 4. If still having issues:
echo    - Close the program completely
echo    - Delete CarDealership.db file
echo    - Restart the program
echo.
echo ===============================================

echo.
echo Press any key to continue...
pause >nul

exit /b 0
