@echo off
chcp 65001 >nul
title حل مشاكل الاتصال بقاعدة البيانات

echo.
echo ========================================
echo    🔧 حل مشاكل الاتصال بقاعدة البيانات
echo ========================================
echo.

echo 🔍 فحص قاعدة البيانات...
echo.

REM التحقق من وجود قاعدة البيانات
if exist "CarDealership.db" (
    echo ✅ تم العثور على قاعدة البيانات في المجلد الرئيسي
) else (
    echo ❌ لم يتم العثور على قاعدة البيانات في المجلد الرئيسي
)

if exist "bin\Debug\net8.0-windows\CarDealership.db" (
    echo ✅ تم العثور على قاعدة البيانات في مجلد bin
) else (
    echo ❌ لم يتم العثور على قاعدة البيانات في مجلد bin
)

echo.
echo 🔧 إصلاح المشاكل...
echo.

REM نسخ قاعدة البيانات إذا كانت موجودة في مكان واحد فقط
if exist "CarDealership.db" (
    if not exist "bin\Debug\net8.0-windows\CarDealership.db" (
        echo 📋 نسخ قاعدة البيانات إلى مجلد bin...
        if not exist "bin\Debug\net8.0-windows" mkdir "bin\Debug\net8.0-windows"
        copy "CarDealership.db" "bin\Debug\net8.0-windows\CarDealership.db" >nul
        echo ✅ تم نسخ قاعدة البيانات بنجاح
    )
)

if exist "bin\Debug\net8.0-windows\CarDealership.db" (
    if not exist "CarDealership.db" (
        echo 📋 نسخ قاعدة البيانات إلى المجلد الرئيسي...
        copy "bin\Debug\net8.0-windows\CarDealership.db" "CarDealership.db" >nul
        echo ✅ تم نسخ قاعدة البيانات بنجاح
    )
)

echo.
echo 🔑 التحقق من المستخدم الافتراضي...
echo.

REM تشغيل أداة إنشاء المستخدم
cd /d "%~dp0\bin\Debug\net8.0-windows"
if exist "create_user_direct.ps1" (
    echo 📋 إنشاء مستخدم افتراضي...
    powershell -ExecutionPolicy Bypass -File "create_user_direct.ps1" -NonInteractive
    echo ✅ تم إنشاء المستخدم الافتراضي
) else (
    echo ⚠️ لم يتم العثور على أداة إنشاء المستخدم
)

cd /d "%~dp0"

echo.
echo ✅ تم إصلاح جميع مشاكل الاتصال!
echo.
echo 🔑 بيانات الدخول:
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo.
echo 🚀 يمكنك الآن تشغيل البرنامج باستخدام:
echo    • النقر المزدوج على "تشغيل_البرنامج.bat"
echo    • أو تشغيل "تشغيل_البرنامج.ps1"
echo.
echo 📋 للوصول إلى تبويب الأقساط المحسن:
echo    1. سجل الدخول باستخدام البيانات أعلاه
echo    2. اختر "التقارير" من القائمة الرئيسية
echo    3. انتقل إلى تبويب "📅 تقارير الأقساط"
echo.
echo 🎯 الميزات الجديدة في تبويب الأقساط:
echo    • 📊 إنشاء التقرير - تقرير شامل مع فلترة
echo    • 🖨️ طباعة محسنة - طباعة احترافية
echo    • 📤 تصدير - 5 صيغ مختلفة
echo    • 📈 ملخص الأقساط - إحصائيات سريعة
echo.
echo 📞 للمساعدة أو الدعم الفني، اتصل بالمطور
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
