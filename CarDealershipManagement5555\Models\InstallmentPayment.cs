using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CarDealershipManagement.Models
{
public class InstallmentPayment
{
    [Key]
    public int InstallmentPaymentId
    {
        get;
        set;
    }

    [Required]
    public int SaleId
    {
        get;    // ربط بالبيع
        set;
    }

    [Required]
    public int InstallmentNumber
    {
        get;    // رقم القسط
        set;
    }

    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal InstallmentAmount
    {
        get;    // قيمة القسط
        set;
    }

    [Required]
    public DateTime DueDate
    {
        get;    // تاريخ الاستحقاق
        set;
    }

    public DateTime? PaidDate
    {
        get;    // تاريخ السداد الفعلي
        set;
    }

    [Column(TypeName = "decimal(18,2)")]
    public decimal AmountPaid
    {
        get;
        set;
    } = 0; // المبلغ المدفوع فعلياً

    public int? ReceivedByUserId
    {
        get;    // المستخدم المسؤول عن تلقي الدفعة
        set;
    }

    public InstallmentStatus Status
    {
        get;
        set;
    } = InstallmentStatus.Pending; // حالة القسط

    public string? Notes
    {
        get;    // ملاحظات
        set;
    }

    public DateTime CreatedDate
    {
        get;
        set;
    } = DateTime.Now;

    // Navigation properties
    [ForeignKey("SaleId")]
    public virtual Sale Sale
    {
        get;
        set;
    } = null!;

    [ForeignKey("ReceivedByUserId")]
    public virtual User? ReceivedByUser
    {
        get;
        set;
    }
}

public enum InstallmentStatus
{
    Pending = 1,    // مستحق
    Paid = 2,       // مدفوع
    Overdue = 3,    // متأخر
    PartiallyPaid = 4 // مدفوع جزئياً
}
}
