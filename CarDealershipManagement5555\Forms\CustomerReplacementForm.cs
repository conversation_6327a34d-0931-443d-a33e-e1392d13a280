using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class CustomerReplacementForm : Form
    {
        private readonly int oldCustomerId;
        private readonly string oldCustomerName;
        private readonly string chassisNumber;
        
        private Label lblTitle;
        private Label lblOldCustomer;
        private Label lblChassisNumber;
        private Label lblNewCustomer;
        private ComboBox cmbNewCustomer;
        private Button btnReplace;
        private Button btnCancel;
        private Label lblStatus;

        public CustomerReplacementForm(int oldCustomerId, string oldCustomerName, string chassisNumber)
        {
            this.oldCustomerId = oldCustomerId;
            this.oldCustomerName = oldCustomerName;
            this.chassisNumber = chassisNumber;
            InitializeComponent();
            LoadAvailableCustomers();
        }

        private void InitializeComponent()
        {
            this.Text = "استبدال العميل";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;

            // Title
            lblTitle = new Label
            {
                Text = "استبدال العميل مع نقل الأقساط",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204),
                Location = new Point(20, 20),
                Size = new Size(450, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Old customer info
            lblOldCustomer = new Label
            {
                Text = $"العميل الحالي: {oldCustomerName}",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(20, 70),
                Size = new Size(450, 25),
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            // Chassis number
            lblChassisNumber = new Label
            {
                Text = $"رقم الشاسيه: {chassisNumber}",
                Font = new Font("Segoe UI", 10F),
                Location = new Point(20, 100),
                Size = new Size(450, 25),
                ForeColor = Color.FromArgb(108, 117, 125)
            };

            // New customer selection
            lblNewCustomer = new Label
            {
                Text = "اختر العميل الجديد:",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(20, 140),
                Size = new Size(200, 25)
            };

            cmbNewCustomer = new ComboBox
            {
                Location = new Point(20, 170),
                Size = new Size(450, 30),
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Status label
            lblStatus = new Label
            {
                Location = new Point(20, 220),
                Size = new Size(450, 60),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.FromArgb(40, 167, 69),
                Text = "سيتم نقل جميع الأقساط والمبيعات من العميل الحالي إلى العميل الجديد.\n" +
                       "العميل الحالي سيتم حذفه نهائياً بعد نقل البيانات."
            };

            // Buttons
            btnReplace = new Button
            {
                Text = "تنفيذ الاستبدال",
                Location = new Point(20, 300),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnReplace.FlatAppearance.BorderSize = 0;
            btnReplace.Click += BtnReplace_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(160, 300),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            // Add controls
            this.Controls.AddRange(new Control[]
            {
                lblTitle, lblOldCustomer, lblChassisNumber, lblNewCustomer,
                cmbNewCustomer, lblStatus, btnReplace, btnCancel
            });
        }

        private async void LoadAvailableCustomers()
        {
            try
            {
                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                var customers = await context.Customers
                    .Where(c => c.CustomerId != oldCustomerId)
                    .Select(c => new { c.CustomerId, c.FullName })
                    .ToListAsync();

                cmbNewCustomer.DisplayMember = "FullName";
                cmbNewCustomer.ValueMember = "CustomerId";
                cmbNewCustomer.DataSource = customers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnReplace_Click(object? sender, EventArgs e)
        {
            if (cmbNewCustomer.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار العميل الجديد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var newCustomerId = Convert.ToInt32(cmbNewCustomer.SelectedValue);
            var newCustomerName = cmbNewCustomer.Text;

            var result = MessageBox.Show(
                $"هل أنت متأكد من استبدال العميل؟\n\n" +
                $"العميل الحالي: {oldCustomerName}\n" +
                $"العميل الجديد: {newCustomerName}\n\n" +
                $"سيتم نقل جميع الأقساط والمبيعات للعميل الجديد.\n" +
                $"العميل الحالي سيتم حذفه نهائياً.",
                "تأكيد الاستبدال", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
                return;

            try
            {
                btnReplace.Enabled = false;
                lblStatus.Text = "جاري تنفيذ الاستبدال...";
                lblStatus.ForeColor = Color.FromArgb(255, 193, 7);

                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                // Update all sales to point to new customer
                var sales = await context.Sales.Where(s => s.CustomerId == oldCustomerId).ToListAsync();
                foreach (var sale in sales)
                {
                    sale.CustomerId = newCustomerId;
                }

                // Delete old customer
                var oldCustomer = await context.Customers.FindAsync(oldCustomerId);
                if (oldCustomer != null)
                {
                    context.Customers.Remove(oldCustomer);
                }

                await context.SaveChangesAsync();

                lblStatus.Text = "تم الاستبدال بنجاح!";
                lblStatus.ForeColor = Color.FromArgb(40, 167, 69);

                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"خطأ: {ex.Message}";
                lblStatus.ForeColor = Color.FromArgb(220, 53, 69);
                btnReplace.Enabled = true;
            }
        }
    }
}
