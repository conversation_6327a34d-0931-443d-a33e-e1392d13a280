using CarDealershipManagement.Services;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace CarDealershipManagement.Forms
{
    /// <summary>
    /// نافذة تفعيل الترخيص
    /// </summary>
    public partial class LicenseActivationForm : Form
    {
        private TextBox txtLicenseKey;
        private TextBox txtCustomerName;
        private TextBox txtCustomerEmail;
        private Button btnActivate;
        private Button btnCancel;
        private Button btnTrial;
        private Label lblStatus;
        private Label lblMachineId;
        private PictureBox picLogo;
        private Panel pnlHeader;
        private Panel pnlContent;
        private Panel pnlFooter;

        public bool IsActivated { get; private set; } = false;

        public LicenseActivationForm()
        {
            InitializeComponent();
            LoadMachineInfo();
        }

        private void InitializeComponent()
        {
            this.Text = "تفعيل برنامج إدارة معرض السيارات";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Segoe UI", 9F);

            CreateControls();
            LayoutControls();
        }

        private void CreateControls()
        {
            // Header Panel
            pnlHeader = new Panel
            {
                Dock = DockStyle.Top,
                Height = 120,
                BackColor = Color.FromArgb(0, 102, 204)
            };

            // Logo
            picLogo = new PictureBox
            {
                Size = new Size(64, 64),
                Location = new Point(20, 28),
                BackColor = Color.White,
                SizeMode = PictureBoxSizeMode.CenterImage
            };
            // يمكن إضافة صورة لوجو هنا
            picLogo.Paint += (s, e) =>
            {
                e.Graphics.DrawString("🚗", new Font("Segoe UI Emoji", 32F), Brushes.DarkBlue, 16, 16);
            };

            var lblTitle = new Label
            {
                Text = "تفعيل برنامج إدارة معرض السيارات",
                Location = new Point(100, 30),
                Size = new Size(350, 30),
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent
            };

            var lblSubtitle = new Label
            {
                Text = "يرجى إدخال مفتاح الترخيص لتفعيل البرنامج",
                Location = new Point(100, 65),
                Size = new Size(350, 20),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(220, 220, 220),
                BackColor = Color.Transparent
            };

            pnlHeader.Controls.AddRange(new Control[] { picLogo, lblTitle, lblSubtitle });

            // Content Panel
            pnlContent = new Panel
            {
                Location = new Point(0, 120),
                Size = new Size(500, 380),
                BackColor = Color.White,
                Padding = new Padding(30)
            };

            // Machine ID
            var lblMachineIdTitle = new Label
            {
                Text = "معرف الجهاز:",
                Location = new Point(30, 20),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            lblMachineId = new Label
            {
                Location = new Point(140, 20),
                Size = new Size(300, 23),
                Font = new Font("Consolas", 9F),
                ForeColor = Color.FromArgb(0, 102, 204),
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = ContentAlignment.MiddleLeft,
                Padding = new Padding(5, 0, 0, 0)
            };

            // Customer Name
            var lblCustomerName = new Label
            {
                Text = "اسم العميل:",
                Location = new Point(30, 70),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            txtCustomerName = new TextBox
            {
                Location = new Point(140, 67),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 9F),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Customer Email
            var lblCustomerEmail = new Label
            {
                Text = "البريد الإلكتروني:",
                Location = new Point(30, 110),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            txtCustomerEmail = new TextBox
            {
                Location = new Point(140, 107),
                Size = new Size(300, 25),
                Font = new Font("Segoe UI", 9F),
                BorderStyle = BorderStyle.FixedSingle
            };

            // License Key
            var lblLicenseKey = new Label
            {
                Text = "مفتاح الترخيص:",
                Location = new Point(30, 150),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41)
            };

            txtLicenseKey = new TextBox
            {
                Location = new Point(140, 147),
                Size = new Size(300, 25),
                Font = new Font("Consolas", 9F),
                BorderStyle = BorderStyle.FixedSingle,
                CharacterCasing = CharacterCasing.Upper
            };

            // Status Label
            lblStatus = new Label
            {
                Location = new Point(30, 190),
                Size = new Size(410, 60),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.FromArgb(220, 53, 69),
                BackColor = Color.Transparent,
                TextAlign = ContentAlignment.TopRight
            };

            // Buttons
            btnActivate = new Button
            {
                Text = "✅ تفعيل الترخيص",
                Location = new Point(30, 270),
                Size = new Size(130, 40),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnActivate.FlatAppearance.BorderSize = 0;
            btnActivate.Click += BtnActivate_Click;

            btnTrial = new Button
            {
                Text = "🕒 نسخة تجريبية (30 يوم)",
                Location = new Point(170, 270),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnTrial.FlatAppearance.BorderSize = 0;
            btnTrial.Click += BtnTrial_Click;

            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Location = new Point(330, 270),
                Size = new Size(110, 40),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                DialogResult = DialogResult.Cancel
            };
            btnCancel.FlatAppearance.BorderSize = 0;

            pnlContent.Controls.AddRange(new Control[]
            {
                lblMachineIdTitle, lblMachineId,
                lblCustomerName, txtCustomerName,
                lblCustomerEmail, txtCustomerEmail,
                lblLicenseKey, txtLicenseKey,
                lblStatus,
                btnActivate, btnTrial, btnCancel
            });

            // Footer Panel
            pnlFooter = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 100,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            var lblFooterInfo = new Label
            {
                Text = "💡 للحصول على مفتاح الترخيص، يرجى الاتصال بالدعم الفني\n" +
                       "📞 هاتف: +20-XXX-XXX-XXXX\n" +
                       "📧 بريد إلكتروني: <EMAIL>",
                Location = new Point(30, 20),
                Size = new Size(440, 60),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.FromArgb(108, 117, 125),
                TextAlign = ContentAlignment.TopCenter
            };

            pnlFooter.Controls.Add(lblFooterInfo);
        }

        private void LayoutControls()
        {
            this.Controls.AddRange(new Control[] { pnlHeader, pnlContent, pnlFooter });
        }

        private void LoadMachineInfo()
        {
            lblMachineId.Text = LicenseActivationService.GetMachineId();
        }

        private async void BtnActivate_Click(object? sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            btnActivate.Enabled = false;
            btnActivate.Text = "جاري التفعيل...";
            lblStatus.Text = "";

            try
            {
                await Task.Delay(1000); // محاكاة عملية التفعيل

                var success = LicenseActivationService.ActivateLicense(
                    txtLicenseKey.Text.Trim(),
                    txtCustomerName.Text.Trim(),
                    txtCustomerEmail.Text.Trim()
                );

                if (success)
                {
                    lblStatus.ForeColor = Color.FromArgb(40, 167, 69);
                    lblStatus.Text = "✅ تم تفعيل الترخيص بنجاح!\nمرحباً بك في برنامج إدارة معرض السيارات.";
                    
                    IsActivated = true;
                    
                    await Task.Delay(2000);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    lblStatus.ForeColor = Color.FromArgb(220, 53, 69);
                    lblStatus.Text = "❌ فشل في تفعيل الترخيص!\nيرجى التحقق من صحة مفتاح الترخيص والمحاولة مرة أخرى.";
                }
            }
            catch (Exception ex)
            {
                lblStatus.ForeColor = Color.FromArgb(220, 53, 69);
                lblStatus.Text = $"❌ خطأ في التفعيل: {ex.Message}";
            }
            finally
            {
                btnActivate.Enabled = true;
                btnActivate.Text = "✅ تفعيل الترخيص";
            }
        }

        private async void BtnTrial_Click(object? sender, EventArgs e)
        {
            btnTrial.Enabled = false;
            btnTrial.Text = "جاري إنشاء النسخة التجريبية...";
            lblStatus.Text = "";

            try
            {
                await Task.Delay(1000);

                var success = LicenseActivationService.InstallProgram();
                if (success)
                {
                    lblStatus.ForeColor = Color.FromArgb(40, 167, 69);
                    lblStatus.Text = "✅ تم تفعيل النسخة التجريبية بنجاح!\nيمكنك استخدام البرنامج لمدة 30 يوماً.";
                    
                    IsActivated = true;
                    
                    await Task.Delay(2000);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    lblStatus.ForeColor = Color.FromArgb(220, 53, 69);
                    lblStatus.Text = "❌ فشل في تفعيل النسخة التجريبية!";
                }
            }
            catch (Exception ex)
            {
                lblStatus.ForeColor = Color.FromArgb(220, 53, 69);
                lblStatus.Text = $"❌ خطأ: {ex.Message}";
            }
            finally
            {
                btnTrial.Enabled = true;
                btnTrial.Text = "🕒 نسخة تجريبية (30 يوم)";
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
            {
                lblStatus.ForeColor = Color.FromArgb(220, 53, 69);
                lblStatus.Text = "❌ يرجى إدخال اسم العميل.";
                txtCustomerName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtCustomerEmail.Text))
            {
                lblStatus.ForeColor = Color.FromArgb(220, 53, 69);
                lblStatus.Text = "❌ يرجى إدخال البريد الإلكتروني.";
                txtCustomerEmail.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtLicenseKey.Text))
            {
                lblStatus.ForeColor = Color.FromArgb(220, 53, 69);
                lblStatus.Text = "❌ يرجى إدخال مفتاح الترخيص.";
                txtLicenseKey.Focus();
                return false;
            }

            return true;
        }
    }
}
