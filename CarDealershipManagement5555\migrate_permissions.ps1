# PowerShell script to migrate permissions to new system
Write-Host "===============================================" -ForegroundColor Green
Write-Host "        PERMISSIONS MIGRATION TOOL" -ForegroundColor Green
Write-Host "        Car Dealership Management System" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

$dbPath = "CarDealership.db"

if (!(Test-Path $dbPath)) {
    Write-Host "❌ Database not found: $dbPath" -ForegroundColor Red
    Write-Host "Please make sure the database exists before running migration." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "📊 Current database size: $((Get-Item $dbPath).Length / 1KB) KB" -ForegroundColor Cyan
Write-Host ""

# Create backup before migration
$backupPath = "CarDealership_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').db"
Write-Host "💾 Creating backup: $backupPath" -ForegroundColor Yellow
Copy-Item $dbPath $backupPath

if (Test-Path $backupPath) {
    Write-Host "✅ Backup created successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to create backup!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "🔄 Starting permissions migration..." -ForegroundColor Yellow
Write-Host ""

# Try to run the migration using the application
try {
    # Check if the executable exists
    if (Test-Path ".\CarDealershipManagement.exe") {
        Write-Host "🚀 Running migration through application..." -ForegroundColor Cyan
        
        # Create a temporary C# file for migration
        $migrationCode = @"
using System;
using System.Threading.Tasks;
using CarDealershipManagement.Tools;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🔍 Validating new permission system...");
        var isValid = await PermissionsMigrationTool.ValidateNewPermissionSystem();
        
        if (!isValid)
        {
            Console.WriteLine("❌ Permission system validation failed!");
            Environment.Exit(1);
        }
        
        Console.WriteLine("📊 Generating current permissions report...");
        var report = await PermissionsMigrationTool.GeneratePermissionsReport();
        Console.WriteLine(report);
        
        Console.WriteLine("🔄 Starting migration...");
        var success = await PermissionsMigrationTool.MigrateExistingUsersPermissions();
        
        if (success)
        {
            Console.WriteLine("✅ Migration completed successfully!");
            Environment.Exit(0);
        }
        else
        {
            Console.WriteLine("❌ Migration failed!");
            Environment.Exit(1);
        }
    }
}
"@
        
        # Save the migration code to a temporary file
        $tempFile = "temp_migration.cs"
        $migrationCode | Out-File -FilePath $tempFile -Encoding UTF8
        
        Write-Host "📝 Migration code prepared." -ForegroundColor Green
        Write-Host "⚠️  Please compile and run the migration manually using the PermissionsMigrationTool class." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Alternative: Run the application and use the migration tool from the admin menu." -ForegroundColor Cyan
        
        # Clean up
        if (Test-Path $tempFile) {
            Remove-Item $tempFile
        }
        
    } else {
        Write-Host "❌ Application executable not found!" -ForegroundColor Red
        Write-Host "Please build the application first." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Error during migration: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "===============================================" -ForegroundColor Green
Write-Host "           MIGRATION SUMMARY" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""
Write-Host "📋 What was done:" -ForegroundColor Cyan
Write-Host "1. ✅ Database backup created: $backupPath" -ForegroundColor White
Write-Host "2. 📝 Migration code prepared" -ForegroundColor White
Write-Host "3. ⚠️  Manual migration required" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 New Permission System Features:" -ForegroundColor Cyan
Write-Host "• 🔧 Developer: Full system access" -ForegroundColor White
Write-Host "• 👔 Manager: Business management (no user management)" -ForegroundColor White  
Write-Host "• 🤝 Sales Rep: Basic sales and customer operations" -ForegroundColor White
Write-Host "• 📊 Organized by categories (Inventory, Sales, Customers, etc.)" -ForegroundColor White
Write-Host "• 🎯 Role-based default permissions" -ForegroundColor White
Write-Host "• 📝 Detailed permission descriptions" -ForegroundColor White
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Build and run the application" -ForegroundColor White
Write-Host "2. Use the PermissionsMigrationTool to migrate existing users" -ForegroundColor White
Write-Host "3. Test the new permission system" -ForegroundColor White
Write-Host "4. Update user roles as needed" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Important Notes:" -ForegroundColor Yellow
Write-Host "• Backup created before migration" -ForegroundColor White
Write-Host "• Existing custom permissions will be preserved" -ForegroundColor White
Write-Host "• New permissions will be added based on user roles" -ForegroundColor White
Write-Host "• Manager role no longer has user management permissions" -ForegroundColor White
Write-Host ""
Write-Host "===============================================" -ForegroundColor Green

Read-Host "Press Enter to exit"
