# إصلاح قاعدة البيانات
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🔧 إصلاح قاعدة البيانات 🔧" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$currentDir = $PSScriptRoot
$dbPath = Join-Path $currentDir "CarDealership.db"
$binDbPath = Join-Path $currentDir "bin\Debug\net8.0-windows\CarDealership.db"

Write-Host "🔍 فحص قاعدة البيانات..." -ForegroundColor Yellow
Write-Host ""

# التحقق من وجود قاعدة البيانات في المجلد الرئيسي
if (Test-Path $dbPath) {
    Write-Host "✅ تم العثور على قاعدة البيانات في المجلد الرئيسي" -ForegroundColor Green
    $dbSize = (Get-Item $dbPath).Length
    Write-Host "📊 حجم قاعدة البيانات: $([math]::Round($dbSize/1KB, 2)) KB" -ForegroundColor White
} else {
    Write-Host "❌ لم يتم العثور على قاعدة البيانات في المجلد الرئيسي" -ForegroundColor Red
}

# التحقق من وجود قاعدة البيانات في مجلد bin
if (Test-Path $binDbPath) {
    Write-Host "✅ تم العثور على قاعدة البيانات في مجلد bin" -ForegroundColor Green
    $binDbSize = (Get-Item $binDbPath).Length
    Write-Host "📊 حجم قاعدة البيانات: $([math]::Round($binDbSize/1KB, 2)) KB" -ForegroundColor White
} else {
    Write-Host "❌ لم يتم العثور على قاعدة البيانات في مجلد bin" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔧 إصلاح قاعدة البيانات..." -ForegroundColor Yellow

# نسخ قاعدة البيانات إلى مجلد bin إذا لم تكن موجودة
if ((Test-Path $dbPath) -and (-not (Test-Path $binDbPath))) {
    Write-Host "📋 نسخ قاعدة البيانات إلى مجلد bin..." -ForegroundColor Cyan
    $binDir = Split-Path $binDbPath
    if (-not (Test-Path $binDir)) {
        New-Item -ItemType Directory -Path $binDir -Force | Out-Null
    }
    Copy-Item $dbPath $binDbPath -Force
    Write-Host "✅ تم نسخ قاعدة البيانات بنجاح" -ForegroundColor Green
}

# نسخ قاعدة البيانات من مجلد bin إلى المجلد الرئيسي إذا لم تكن موجودة
if ((Test-Path $binDbPath) -and (-not (Test-Path $dbPath))) {
    Write-Host "📋 نسخ قاعدة البيانات إلى المجلد الرئيسي..." -ForegroundColor Cyan
    Copy-Item $binDbPath $dbPath -Force
    Write-Host "✅ تم نسخ قاعدة البيانات بنجاح" -ForegroundColor Green
}

# إنشاء قاعدة بيانات جديدة إذا لم تكن موجودة
if ((-not (Test-Path $dbPath)) -and (-not (Test-Path $binDbPath))) {
    Write-Host "🆕 إنشاء قاعدة بيانات جديدة..." -ForegroundColor Cyan
    
    # إنشاء قاعدة بيانات فارغة
    $null = New-Item -ItemType File -Path $dbPath -Force
    $null = New-Item -ItemType File -Path $binDbPath -Force
    
    Write-Host "✅ تم إنشاء قاعدة بيانات جديدة" -ForegroundColor Green
}

Write-Host ""
Write-Host "🔑 إنشاء مستخدم افتراضي..." -ForegroundColor Yellow

# تشغيل أداة إنشاء المستخدم
$createUserScript = Join-Path $currentDir "bin\Debug\net8.0-windows\create_user_direct.ps1"
if (Test-Path $createUserScript) {
    Write-Host "📋 تشغيل أداة إنشاء المستخدم..." -ForegroundColor Cyan
    Set-Location (Split-Path $createUserScript)
    & $createUserScript
    Set-Location $currentDir
} else {
    Write-Host "⚠️ لم يتم العثور على أداة إنشاء المستخدم" -ForegroundColor Yellow
    Write-Host "🔑 بيانات الدخول الافتراضية:" -ForegroundColor Cyan
    Write-Host "   اسم المستخدم: amrali" -ForegroundColor White
    Write-Host "   كلمة المرور: braa" -ForegroundColor White
}

Write-Host ""
Write-Host "✅ تم إصلاح قاعدة البيانات بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 يمكنك الآن تشغيل البرنامج باستخدام:" -ForegroundColor Cyan
Write-Host "   • تشغيل_البرنامج.bat" -ForegroundColor White
Write-Host "   • تشغيل_البرنامج.ps1" -ForegroundColor White
Write-Host ""
Write-Host "📞 للمساعدة أو الدعم الفني، اتصل بالمطور" -ForegroundColor Magenta
Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
