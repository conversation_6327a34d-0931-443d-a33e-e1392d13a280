@echo off
chcp 65001 >nul
title نسخ المجلد المستقل - برنامج إدارة معرض السيارات

echo.
echo ========================================
echo   📦 نسخ المجلد المستقل
echo ========================================
echo.

echo 🧹 حذف المجلد السابق إذا كان موجوداً...
if exist "CarDealership_Standalone" rmdir /s /q "CarDealership_Standalone"

echo 📁 إنشاء المجلد الجديد...
mkdir "CarDealership_Standalone"
mkdir "CarDealership_Standalone\Data"
mkdir "CarDealership_Standalone\Backups"
mkdir "CarDealership_Standalone\Documentation"

echo.
echo 📋 نسخ ملفات البرنامج...

REM نسخ الملف التنفيذي الرئيسي
copy "bin\Release\net8.0-windows\CarDealershipManagement.exe" "CarDealership_Standalone\" >nul
echo ✅ تم نسخ الملف التنفيذي

REM نسخ ملفات DLL المطلوبة
copy "bin\Release\net8.0-windows\*.dll" "CarDealership_Standalone\" >nul
echo ✅ تم نسخ ملفات DLL

REM نسخ ملفات التكوين
copy "bin\Release\net8.0-windows\*.json" "CarDealership_Standalone\" >nul
echo ✅ تم نسخ ملفات التكوين

REM نسخ مجلد runtimes
xcopy "bin\Release\net8.0-windows\runtimes" "CarDealership_Standalone\runtimes\" /E /I /Q >nul
echo ✅ تم نسخ مجلد runtimes

REM نسخ قاعدة البيانات
if exist "CarDealership.db" (
    copy "CarDealership.db" "CarDealership_Standalone\Data\" >nul
    echo ✅ تم نسخ قاعدة البيانات
)

echo.
echo 📋 إنشاء ملفات إضافية...

REM إنشاء ملف README
(
echo برنامج إدارة معرض السيارات - الإصدار 1.0.0
echo ===============================================
echo.
echo 🚗 نظام شامل لإدارة معارض السيارات
echo.
echo 🚀 طريقة التشغيل:
echo    1. اضغط مرتين على CarDealershipManagement.exe
echo    2. اختر "نسخة تجريبية" للبدء فوراً ^(30 يوم^)
echo    3. أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo.
echo 🔑 بيانات الدخول الافتراضية:
echo    المطور: developer / dev123 ^(جميع الصلاحيات^)
echo    المدير: admin / 123 ^(صلاحيات إدارية^)
echo    المندوب: user / pass ^(صلاحيات أساسية^)
echo.
echo 🎯 الميزات الرئيسية:
echo    • إدارة شاملة للمخزون والسيارات
echo    • نظام مبيعات متقدم مع الأقساط
echo    • إدارة العملاء والموردين
echo    • تقارير وإحصائيات تفصيلية
echo    • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo    • نظام ضمان سلامة البيانات المالية
echo    • نسخ احتياطي وأرشفة
echo.
echo 💡 نصائح مهمة:
echo    • قم بإنشاء نسخة احتياطية من مجلد Data بانتظام
echo    • لا تحذف أي ملفات من المجلد
echo    • استخدم كلمات مرور قوية
echo.
echo 📞 للدعم الفني: <EMAIL>
echo 🌐 الموقع: www.cardealership.com
echo.
echo تاريخ الإنشاء: %date% %time%
) > "CarDealership_Standalone\README.txt"

REM إنشاء ملف تشغيل سريع
(
echo @echo off
echo chcp 65001 ^>nul
echo title برنامج إدارة معرض السيارات
echo.
echo echo ========================================
echo echo    🚗 برنامج إدارة معرض السيارات
echo echo ========================================
echo echo.
echo echo 🚀 جاري تشغيل البرنامج...
echo echo.
echo echo 🔑 بيانات الدخول:
echo echo    المطور: developer / dev123
echo echo    المدير: admin / 123  
echo echo    المندوب: user / pass
echo echo.
echo echo 💡 يمكنك أيضاً اختيار "نسخة تجريبية" للبدء فوراً
echo echo.
echo start "" "CarDealershipManagement.exe"
echo echo ✅ تم تشغيل البرنامج!
echo echo.
echo echo اضغط أي مفتاح للخروج...
echo pause ^>nul
) > "CarDealership_Standalone\تشغيل_البرنامج.bat"

REM إنشاء دليل المستخدم
(
echo دليل المستخدم السريع - برنامج إدارة معرض السيارات
echo ========================================================
echo.
echo 🎯 البدء السريع:
echo ================
echo.
echo 1. التشغيل لأول مرة:
echo    • اضغط مرتين على CarDealershipManagement.exe
echo    • اختر "نسخة تجريبية ^(30 يوم^)" للبدء فوراً
echo    • أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo.
echo 2. تسجيل الدخول:
echo    • المطور ^(جميع الصلاحيات^): developer / dev123
echo    • المدير ^(صلاحيات إدارية^): admin / 123
echo    • المندوب ^(صلاحيات أساسية^): user / pass
echo.
echo 🔧 الميزات الرئيسية:
echo ====================
echo.
echo 📦 إدارة المخزون:
echo    • إضافة وتعديل وحذف السيارات
echo    • نظام ضمان سلامة البيانات المالية
echo    • إدارة ملفات ومرفقات السيارات
echo.
echo 💰 نظام المبيعات:
echo    • بيع نقدي وبالتقسيط
echo    • إدارة الأقساط والمدفوعات
echo    • طباعة الفواتير والعقود
echo.
echo 👥 إدارة العملاء:
echo    • قاعدة بيانات شاملة للعملاء
echo    • تاريخ المشتريات والمدفوعات
echo    • كشوف حساب مفصلة
echo.
echo 📊 التقارير والإحصائيات:
echo    • تقارير المبيعات والأرباح
echo    • تقارير الأقساط المحسنة
echo    • إحصائيات الأداء
echo.
echo 👤 إدارة المستخدمين:
echo    • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo    • أدوار متعددة ^(مطور، مدير، مندوب^)
echo    • إدارة كلمات المرور
echo.
echo 🔒 الأمان والحماية:
echo    • نسخ احتياطي تلقائي
echo    • تشفير البيانات الحساسة
echo    • نظام تفعيل متقدم
echo.
echo 💡 نصائح مهمة:
echo ================
echo.
echo • قم بإنشاء نسخة احتياطية من مجلد Data بانتظام
echo • استخدم كلمات مرور قوية للمستخدمين
echo • راجع التقارير دورياً لمتابعة الأداء
echo.
echo 📞 الدعم الفني:
echo ================
echo.
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📞 الهاتف: +20-XXX-XXX-XXXX
echo 🌐 الموقع: www.cardealership.com
echo.
echo شكراً لاختيارك برنامج إدارة معرض السيارات! 🚗
) > "CarDealership_Standalone\Documentation\دليل_المستخدم_السريع.txt"

echo ✅ تم إنشاء الملفات الإضافية

echo.
echo 📊 عرض معلومات المجلد المنشأ...
echo.

if exist "CarDealership_Standalone\CarDealershipManagement.exe" (
    echo ✅ تم إنشاء المجلد المستقل بنجاح!
    echo.
    echo 📁 مكان المجلد: %~dp0CarDealership_Standalone
    echo.
    
    REM عرض حجم الملف التنفيذي
    for %%A in ("CarDealership_Standalone\CarDealershipManagement.exe") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📏 حجم الملف التنفيذي: !FILE_SIZE_MB! ميجابايت
    )
    
    echo.
    echo 📋 محتويات المجلد:
    echo    ✅ CarDealershipManagement.exe - الملف التنفيذي الرئيسي
    echo    ✅ تشغيل_البرنامج.bat - تشغيل سريع
    echo    ✅ README.txt - دليل سريع
    echo    ✅ Data\ - مجلد قواعد البيانات
    echo    ✅ Documentation\ - أدلة المستخدم
    echo    ✅ Backups\ - مجلد النسخ الاحتياطية
    echo    ✅ runtimes\ - ملفات التشغيل المطلوبة
    echo.
    
    echo 🎯 المجلد جاهز للتوزيع!
    echo.
    echo 💡 يمكنك الآن:
    echo    1. نسخ المجلد إلى أي مكان
    echo    2. ضغطه في ملف ZIP للتوزيع
    echo    3. نسخه إلى فلاشة USB
    echo    4. رفعه على الإنترنت للتحميل
    echo.
    
    echo 🚀 لتشغيل البرنامج من المجلد الجديد:
    echo    1. انتقل إلى: CarDealership_Standalone
    echo    2. اضغط مرتين على "تشغيل_البرنامج.bat"
    echo    3. أو اضغط مرتين على "CarDealershipManagement.exe"
    echo.
    
) else (
    echo ❌ فشل في إنشاء المجلد المستقل
    echo يرجى التأكد من وجود البرنامج في مجلد bin\Release
)

echo.
echo 🎉 انتهت العملية!
echo.

echo هل تريد فتح المجلد الجديد؟ (Y/N)
set /p "OPEN_FOLDER="
if /i "%OPEN_FOLDER%"=="Y" (
    start "" "CarDealership_Standalone"
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
