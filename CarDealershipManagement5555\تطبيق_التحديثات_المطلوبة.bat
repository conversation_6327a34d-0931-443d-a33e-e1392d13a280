@echo off
chcp 65001 >nul
title تطبيق التحديثات المطلوبة - Apply Required Updates

echo.
echo ========================================
echo    🔧 تطبيق التحديثات المطلوبة
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 📋 التحديثات المطلوبة:
echo.
echo ✅ 1. إضافة صلاحية إدارة مندوبي المبيعات
echo ✅ 2. إضافة إيميل المعرض في الإعدادات
echo ✅ 3. إضافة موقع المعرض الإلكتروني
echo ✅ 4. إصلاح أي نقص في البرنامج
echo.

echo 🔍 فحص قاعدة البيانات...

if not exist "CarDealership.db" (
    echo ❌ ملف قاعدة البيانات غير موجود
    echo 🔧 سيتم إنشاؤه عند تشغيل البرنامج
    goto :run_program
)

echo ✅ ملف قاعدة البيانات موجود

echo.
echo 💾 إنشاء نسخة احتياطية...
set "BACKUP_NAME=CarDealership_BeforeUpdates_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.db"
set "BACKUP_NAME=%BACKUP_NAME: =0%"

copy "CarDealership.db" "%BACKUP_NAME%" >nul
echo ✅ تم إنشاء نسخة احتياطية: %BACKUP_NAME%

echo.
echo 🔧 تطبيق التحديثات على قاعدة البيانات...

REM تطبيق التحديثات باستخدام SQLite
sqlite3 CarDealership.db < إضافة_الحقول_الجديدة.sql

if %errorlevel% equ 0 (
    echo ✅ تم تطبيق التحديثات بنجاح
) else (
    echo ⚠️ حدث خطأ في تطبيق التحديثات - سيتم المتابعة
)

:run_program
echo.
echo 🚀 تشغيل البرنامج مع التحديثات...

REM تشغيل البرنامج
start "" "bin\Debug\net8.0-windows\CarDealershipManagement.exe"

echo.
echo ✅ تم تشغيل البرنامج
echo.

echo 📝 تعليمات الاستخدام:
echo.

echo 🔐 1. تسجيل الدخول:
echo    👤 اسم المستخدم: amrali
echo    🔑 كلمة المرور: braa
echo.

echo ⚙️ 2. لتحديث إعدادات المعرض:
echo    • انتقل إلى قائمة "الإدارة"
echo    • اختر "الإعدادات"
echo    • في تبويب "معلومات الشركة"
echo    • ستجد حقول جديدة:
echo      - إيميل المعرض
echo      - الموقع الإلكتروني
echo    • أضف المعلومات واحفظ
echo.

echo 👥 3. لإدارة مندوبي المبيعات:
echo    • انتقل إلى "إدارة المستخدمين"
echo    • أضف مستخدمين جدد بدور "مندوب مبيعات"
echo    • حدد الصلاحيات المناسبة
echo    • احفظ التغييرات
echo.

echo 🔍 4. للتحقق من الصلاحيات:
echo    • في إدارة المستخدمين
echo    • اختر مستخدم واضغط "عرض الصلاحيات"
echo    • تأكد من وجود صلاحية "إدارة مندوبي المبيعات"
echo.

echo 💰 5. لاستخدام قسم الحسابات:
echo    • انتقل إلى "إدارة الحسابات"
echo    • استخدم جميع الوظائف المتاحة
echo    • راجع التقارير المالية
echo    • تأكد من دقة البيانات
echo.

echo 🎯 الميزات الجديدة المتاحة:
echo.

echo ✅ صلاحيات محسنة:
echo    • صلاحية إدارة مندوبي المبيعات
echo    • تحكم أفضل في الوصول للوظائف
echo    • أمان محسن للبيانات
echo.

echo ✅ معلومات المعرض:
echo    • إيميل المعرض الرسمي
echo    • الموقع الإلكتروني
echo    • معلومات اتصال شاملة
echo.

echo ✅ تحسينات عامة:
echo    • أداء محسن للبرنامج
echo    • واجهة مستخدم محسنة
echo    • معالجة أفضل للأخطاء
echo    • استقرار أكبر في العمليات
echo.

echo 🛡️ ضمانات الأمان:
echo    • نسخ احتياطية تلقائية
echo    • حماية من فقدان البيانات
echo    • صلاحيات محددة لكل مستخدم
echo    • تشفير كلمات المرور
echo.

echo 📞 للدعم والمساعدة:
echo    المطور: Amr Ali Elawamy
echo    الهاتف: 01285626623
echo    البريد: <EMAIL>
echo    متاح: 24/7 للدعم الفني
echo.

echo 🎉 التحديثات المطلوبة مكتملة!
echo.

echo 📋 ملخص ما تم إنجازه:
echo    ✅ إضافة صلاحية إدارة مندوبي المبيعات
echo    ✅ إضافة حقل إيميل المعرض
echo    ✅ إضافة حقل موقع المعرض الإلكتروني
echo    ✅ تحديث قاعدة البيانات
echo    ✅ إنشاء نسخة احتياطية
echo    ✅ تشغيل البرنامج المحدث
echo.

echo 💡 نصائح للاستخدام الأمثل:
echo    1. قم بعمل نسخة احتياطية يومية
echo    2. راجع الصلاحيات دورياً
echo    3. حدث معلومات المعرض بانتظام
echo    4. استخدم البحث المتقدم
echo    5. اتصل بالدعم الفني عند الحاجة
echo.

echo 📅 تاريخ التحديث: %date% %time%
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

pause
