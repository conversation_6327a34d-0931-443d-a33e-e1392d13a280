using CarDealershipManagement.Forms;
using CarDealershipManagement.Data;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main(string[] args)
    {
        // Setup improved error handling
        AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
                ErrorHandlingService.HandleException(e.ExceptionObject as Exception, true, "حدث خطأ فادح في التطبيق وسيتم إغلاقه.");

        Application.ThreadException += (sender, e) =>
                                       ErrorHandlingService.HandleException(e.Exception, true, "حدث خطأ في واجهة المستخدم.");

        // Clean up old log files on startup
        ErrorHandlingService.CleanupOldLogs();

        try
        {
            // Check if we need to initialize the database
            if(args.Length > 0 && args[0] == "--init-db")
            {
                InitializeDatabase();
                return;
            }

            // Initialize database if it doesn't exist
            if(!ErrorHandlingService.TryExecute(() =>
        {
            EnsureDatabaseExists();
                DatabaseSchemaFixer.CheckAndFixSchema();
            }, "Database Initialization"))
            {
                MessageBox.Show("فشل في تهيئة قاعدة البيانات. يرجى التحقق من ملفات السجل للحصول على مزيد من التفاصيل.",
                                "خطأ في قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();

            // Initialize license system
            try
            {
                LicenseManager.InitializeInstallation();

                // Check license status
                var licenseResult = LicenseManager.CheckLicense();

                // Handle license validation
                if (!HandleLicenseValidation(licenseResult))
                {
                    return; // Exit if license validation fails
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل نظام الترخيص، اعرض رسالة تحذيرية وتابع التشغيل
                MessageBox.Show($"تحذير: مشكلة في نظام الترخيص\n{ex.Message}\n\nسيتم تشغيل البرنامج في الوضع العادي.",
                    "تحذير نظام الترخيص", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }

            // Launch LoginForm for authentication
            using(var loginForm = new LoginForm())
            {
                if(loginForm.ShowDialog() == DialogResult.OK)
                {
                    // Launch MainDashboard with authenticated user
                    Application.Run(new MainDashboard(loginForm.AuthenticatedUser!));
                }
                else
                {
                    // User cancelled login or login failed
                    Application.Exit();
                }
            }
        }
        catch(Exception ex)
        {
            ErrorHandlingService.HandleException(ex, true, "حدث خطأ فادح أثناء بدء تشغيل التطبيق.");
            Environment.Exit(1);
        }
    }

    private static void InitializeDatabase()
    {
        WriteToConsole("Initializing database...");

        ErrorHandlingService.TryExecute(() =>
        {
            var options = new DbContextOptionsBuilder<CarDealershipContext>()
            .UseSqlite("Data Source=CarDealership.db")
            .Options;

            using(var context = new CarDealershipContext(options))
            {
                WriteToConsole("Ensuring database is created...");
                context.Database.EnsureCreated();

                WriteToConsole("Database created successfully!");

                // Run schema fixes
                WriteToConsole("Running schema fixes...");
                DatabaseSchemaFixer.CheckAndFixSchema();

                // Check if we have any users
                var userCount = context.Users.Count();
                WriteToConsole($"Found {userCount} users in database");

                if(userCount == 0)
                {
                    WriteToConsole("Database appears to be empty. Running seed data...");
                    // The seed data should be created automatically via OnModelCreating
                    context.SaveChanges();
                    WriteToConsole("Seed data created successfully!");
                }

                // Test database connection
                WriteToConsole("Testing database connection...");
                var carCount = context.Cars.Count();
                var customerCount = context.Customers.Count();
                var salesCount = context.Sales.Count();

                WriteToConsole($"Database statistics:");
                WriteToConsole($"  Cars: {carCount}");
                WriteToConsole($"  Customers: {customerCount}");
                WriteToConsole($"  Sales: {salesCount}");
                WriteToConsole($"  Users: {userCount}");
            }
        }, "Database Initialization", false);

        WriteToConsole("Database initialization complete.");

        // Only wait for key press if running with --init-db argument
        // This prevents hanging in Windows Forms mode
        if(Environment.GetCommandLineArgs().Contains("--init-db") &&
                Environment.UserInteractive &&
                !Console.IsInputRedirected)
        {
            WriteToConsole("Press any key to exit...");
            try
            {
                Console.ReadKey();
            }
            catch(Exception)
            {
                // Ignore any console-related exceptions
                System.Threading.Thread.Sleep(2000); // Brief pause instead
            }
        }
    }

    private static void WriteToConsole(string message)
    {
        try
        {
            // Only write to console if it's available and we're not in Windows Forms mode
            if(Environment.UserInteractive && !Console.IsOutputRedirected)
            {
                Console.WriteLine(message);
            }

            // Also log to debug log
            ErrorHandlingService.LogDebugInfo(message);
        }
        catch(Exception)
        {
            // Silently ignore console write failures
            // This prevents crashes in Windows Forms applications
        }
    }

    private static bool HandleLicenseValidation(LicenseCheckResult licenseResult)
    {
        if (licenseResult.IsValid)
        {
            // الترخيص صالح
            if (licenseResult.DaysRemaining > 0 && licenseResult.DaysRemaining <= 30)
            {
                // تنبيه قبل انتهاء الترخيص
                MessageBox.Show($"تنبيه: سينتهي ترخيصك خلال {licenseResult.DaysRemaining} يوم\n\nيرجى تجديد الاشتراك قبل انتهاء الصلاحية.",
                    "تنبيه انتهاء الترخيص", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            return true;
        }
        else
        {
            // الترخيص غير صالح أو منتهي
            var result = MessageBox.Show($"{licenseResult.Message}\n\n" +
                "هل تريد تشغيل فحص النظام؟\n\n" +
                "سيتم فتح أداة فحص النظام حيث ستحتاج إلى:\n" +
                "1. إدخال رمز التحقق من النظام\n" +
                "2. اختيار إعدادات النظام\n" +
                "3. إكمال عملية الإعداد",
                "فحص النظام", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    // تشغيل ملف الباتش للتفعيل
                    var batchPath = Path.Combine(Application.StartupPath, "activate.bat");
                    if (File.Exists(batchPath))
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = batchPath,
                            UseShellExecute = true,
                            Verb = "runas" // تشغيل كمدير
                        });

                        MessageBox.Show("تم فتح أداة فحص النظام!\n\n" +
                            "الخطوات:\n" +
                            "1. أدخل رمز التحقق من النظام\n" +
                            "2. سيتم إنشاء بصمة النظام تلقائياً\n" +
                            "3. اختر إعدادات النظام المناسبة\n" +
                            "4. ستظهر رسالة 'تم تطبيق الإعدادات بنجاح!'\n\n" +
                            "بعد الإعداد، أعد تشغيل البرنامج.",
                            "فحص النظام", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("لم يتم العثور على أداة فحص النظام!\nيرجى التواصل مع الدعم الفني.",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تشغيل أداة فحص النظام:\n{ex.Message}",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }

            return false; // منع تشغيل البرنامج
        }
    }



    private static void EnsureDatabaseExists()
    {
        var options = new DbContextOptionsBuilder<CarDealershipContext>()
        .UseSqlite("Data Source=CarDealership.db")
        .Options;

        using(var context = new CarDealershipContext(options))
        {
            context.Database.EnsureCreated();
        }
    }
}

