using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
public partial class SaleDetailsForm : Form
{
    private int saleId;

    public SaleDetailsForm(int saleId)
    {
        this.saleId = saleId;
        InitializeComponent();
        LoadData();
    }

    private void InitializeComponent()
    {
        this.Text = "تفاصيل عملية البيع - Sale Details";
        this.Size = new Size(800, 600);
        this.StartPosition = FormStartPosition.CenterScreen;

        // Create a tab control for organized display
        var tabControl = new TabControl
        {
            Dock = DockStyle.Fill
        };

        // Sale Information Tab
        var saleInfoTab = new TabPage("معلومات البيع");
        var carInfoTab = new TabPage("معلومات السيارة");
        var customerInfoTab = new TabPage("معلومات العميل");
        var paymentInfoTab = new TabPage("معلومات الدفع");

        tabControl.TabPages.AddRange(new[] { saleInfoTab, carInfoTab, customerInfoTab, paymentInfoTab });

        this.Controls.Add(tabControl);

        // Close button
        var btnClose = new Button
        {
            Text = "إغلاق",
            Size = new Size(100, 30),
            Location = new Point(350, 520),
            Anchor = AnchorStyles.Bottom
        };
        btnClose.Click += (s, e) => this.Close();
        this.Controls.Add(btnClose);
    }

    private async void LoadData()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var sale = await context.Sales
                       .Include(s => s.Car)
                       .Include(s => s.Customer)
                       .Include(s => s.InstallmentPayments)
                       .FirstOrDefaultAsync(s => s.SaleId == saleId);

            if(sale != null)
            {
                var tabControl = (TabControl)this.Controls[0];

                // Populate Sale Information Tab
                PopulateSaleInfo(tabControl.TabPages[0], sale);

                // Populate Car Information Tab
                PopulateCarInfo(tabControl.TabPages[1], sale.Car);

                // Populate Customer Information Tab
                PopulateCustomerInfo(tabControl.TabPages[2], sale.Customer);

                // Populate Payment Information Tab
                PopulatePaymentInfo(tabControl.TabPages[3], sale);
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void PopulateSaleInfo(TabPage tab, Sale sale)
    {
        var y = 20;
        AddLabel(tab, "رقم البيع:", sale.SaleId.ToString(), ref y);
        AddLabel(tab, "تاريخ البيع:", sale.SaleDate.ToString("yyyy-MM-dd HH:mm"), ref y);
        AddLabel(tab, "سعر البيع:", sale.ActualSellPrice.ToString("C"), ref y);
        AddLabel(tab, "طريقة الدفع:", sale.PaymentMethod == PaymentMethod.Cash ? "نقدي" : "تقسيط", ref y);
        AddLabel(tab, "المبلغ المدفوع:", sale.TotalPaid.ToString("C"), ref y);
        AddLabel(tab, "المبلغ المتبقي:", sale.RemainingAmount.ToString("C"), ref y);
        AddLabel(tab, "حالة الدفع:", sale.PaymentStatus switch
    {
        PaymentStatus.Pending => "مستحقة",
        PaymentStatus.PartiallyPaid => "مدفوعة جزئياً",
        PaymentStatus.FullyPaid => "مدفوعة بالكامل",
        _ => "غير محدد"
    }, ref y);

        if(sale.PaymentMethod == PaymentMethod.Installment)
        {
            y += 10;
            AddLabel(tab, "الدفعة المقدمة:", sale.DownPayment.ToString("C"), ref y);
            AddLabel(tab, "عدد الأقساط:", sale.NumberOfInstallments.ToString(), ref y);
            AddLabel(tab, "قيمة القسط:", sale.InstallmentAmount.ToString("C"), ref y);
            AddLabel(tab, "فترة الأقساط:", sale.InstallmentPeriod switch
        {
            InstallmentPeriod.Monthly => "شهري",
            InstallmentPeriod.Quarterly => "ربع سنوي",
            InstallmentPeriod.SixMonths => "نصف سنوي",
            InstallmentPeriod.Yearly => "سنوي",
            _ => "غير محدد"
        }, ref y);
            AddLabel(tab, "تاريخ أول قسط:", sale.FirstInstallmentDate?.ToString("yyyy-MM-dd") ?? "غير محدد", ref y);
        }
    }

    private void PopulateCarInfo(TabPage tab, Car car)
    {
        var y = 20;
        AddLabel(tab, "رقم الشاسيه:", car.ChassisNumber, ref y);
        AddLabel(tab, "النوع:", car.Type, ref y);
        AddLabel(tab, "الماركة:", car.Brand, ref y);
        AddLabel(tab, "الموديل:", car.Model, ref y);
        AddLabel(tab, "السنة:", car.Year.ToString(), ref y);
        AddLabel(tab, "اللون:", car.Color, ref y);
        AddLabel(tab, "رقم اللوحة:", car.PlateNumber ?? "غير محدد", ref y);
        AddLabel(tab, "الكيلومترات:", car.Mileage.ToString("N0"), ref y);
        AddLabel(tab, "سعر الشراء:", car.PurchasePrice.ToString("C"), ref y);
        AddLabel(tab, "سعر البيع المقترح:", car.SuggestedSellPrice.ToString("C"), ref y);
        AddLabel(tab, "الحالة:", car.Condition == CarCondition.New ? "جديدة" : "مستعملة", ref y);
        AddLabel(tab, "تاريخ الشراء:", car.PurchaseDate.ToString("yyyy-MM-dd"), ref y);
    }

    private void PopulateCustomerInfo(TabPage tab, Customer customer)
    {
        var y = 20;
        AddLabel(tab, "رقم العميل:", customer.CustomerId.ToString(), ref y);
        AddLabel(tab, "الاسم الكامل:", customer.FullName, ref y);
        AddLabel(tab, "رقم الهوية:", customer.IdNumber, ref y);
        AddLabel(tab, "العنوان:", customer.Address, ref y);
        AddLabel(tab, "الهاتف الأساسي:", customer.PrimaryPhone, ref y);
        AddLabel(tab, "الهاتف الثانوي:", customer.SecondaryPhone ?? "غير محدد", ref y);
        AddLabel(tab, "البريد الإلكتروني:", customer.Email ?? "غير محدد", ref y);
    }

    private void PopulatePaymentInfo(TabPage tab, Sale sale)
    {
        if(sale.PaymentMethod == PaymentMethod.Cash)
        {
            var lblCashPayment = new Label
            {
                Text = "تم الدفع نقداً بالكامل",
                Location = new Point(20, 20),
                Size = new Size(300, 23),
                Font = new Font("Arial", 12, FontStyle.Bold)
            };
            tab.Controls.Add(lblCashPayment);
        }
        else
        {
            // Create a DataGridView for installments
            var dgvInstallments = new DataGridView
            {
                Location = new Point(20, 20),
                Size = new Size(730, 400),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false
            };

            var installments = sale.InstallmentPayments.OrderBy(i => i.InstallmentNumber).ToList();

            dgvInstallments.DataSource = installments.Select(i => new
            {
                رقم_القسط = i.InstallmentNumber,
                قيمة_القسط = i.InstallmentAmount.ToString("C"),
                تاريخ_الاستحقاق = i.DueDate.ToString("yyyy-MM-dd"),
                المدفوع = i.AmountPaid.ToString("C"),
                تاريخ_الدفع = i.PaidDate?.ToString("yyyy-MM-dd") ?? "غير مدفوع",
                الحالة = i.Status switch
            {
                InstallmentStatus.Pending => "مستحق",
                InstallmentStatus.Paid => "مدفوع",
                InstallmentStatus.Overdue => "متأخر",
                InstallmentStatus.PartiallyPaid => "مدفوع جزئياً",
                _ => "غير محدد"
            },
            ملاحظات = i.Notes ?? ""
        }).ToList();

            tab.Controls.Add(dgvInstallments);

            // Summary labels
            var lblSummary = new Label
            {
                Text = $"ملخص الدفعات: إجمالي المبلغ: {sale.ActualSellPrice:C} | المدفوع: {sale.TotalPaid:C} | المتبقي: {sale.RemainingAmount:C}",
                Location = new Point(20, 430),
                Size = new Size(730, 23),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            tab.Controls.Add(lblSummary);
        }
    }

    private void AddLabel(TabPage tab, string title, string value, ref int y)
    {
        var lblTitle = new Label
        {
            Text = title,
            Location = new Point(20, y),
            Size = new Size(150, 23),
            Font = new Font("Arial", 9, FontStyle.Bold)
        };

        var lblValue = new Label
        {
            Text = value,
            Location = new Point(180, y),
            Size = new Size(300, 23),
            Font = new Font("Arial", 9)
        };

        tab.Controls.AddRange(new Control[] { lblTitle, lblValue });
        y += 30;
    }
}
}
