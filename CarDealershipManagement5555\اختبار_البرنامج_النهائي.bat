@echo off
chcp 65001 >nul
title اختبار البرنامج النهائي مع نظام التفعيل

echo.
echo ========================================
echo   🧪 اختبار البرنامج النهائي
echo ========================================
echo.

echo 🎯 هذا الاختبار سيقوم بـ:
echo    1. بناء البرنامج في وضع Release
echo    2. تشغيل البرنامج مع نظام التفعيل
echo    3. اختبار جميع الميزات الجديدة
echo.

echo 🔧 بناء البرنامج...
echo.

REM بناء سريع للاختبار
dotnet build --configuration Release --verbosity quiet
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء البرنامج
    pause
    exit /b 1
)

echo ✅ تم بناء البرنامج بنجاح

echo.
echo 🚀 تشغيل البرنامج...
echo.

cd /d "%~dp0\bin\Release\net8.0-windows"

if exist "CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج
    echo.
    echo 🔑 بيانات الدخول للاختبار:
    echo.
    echo 🔧 حساب المطور (صلاحيات كاملة):
    echo    اسم المستخدم: developer
    echo    كلمة المرور: dev123
    echo.
    echo 👔 حساب المدير:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: 123
    echo.
    echo 🆕 ميزات نظام التفعيل الجديد:
    echo.
    echo ✅ نسخة تجريبية مجانية (30 يوم):
    echo    • تفعيل فوري بدون مفتاح ترخيص
    echo    • جميع الميزات متاحة
    echo    • مناسبة للتقييم والاختبار
    echo.
    echo ✅ تراخيص مدفوعة:
    echo    • شهري: MONTH-XXXXXX-XXXXXX
    echo    • ربع سنوي: QUARTER-XXXXXX-XXXXXX  
    echo    • سنوي: YEAR-XXXXXX-XXXXXX
    echo    • مدى الحياة: LIFE-XXXXXX-XXXXXX
    echo.
    echo ✅ حماية متقدمة:
    echo    • ربط الترخيص بمعرف الجهاز
    echo    • تشفير ملفات الترخيص
    echo    • فحص دوري لصحة الترخيص
    echo.
    echo 🎯 الميزات المتاحة للاختبار:
    echo.
    echo 📦 إدارة المخزون:
    echo    • إضافة وتعديل وحذف السيارات
    echo    • نظام ضمان سلامة البيانات المالية عند الحذف
    echo    • إدارة ملفات ومرفقات السيارات
    echo.
    echo 💰 نظام المبيعات:
    echo    • بيع نقدي وبالتقسيط
    echo    • إدارة الأقساط المتقدمة
    echo    • تقارير مبيعات شاملة
    echo.
    echo 👥 إدارة المستخدمين:
    echo    • نظام صلاحيات متكامل (64+ صلاحية)
    echo    • أدوار متعددة (مطور، مدير، مندوب)
    echo    • إدارة كلمات المرور والأمان
    echo.
    echo 📊 التقارير والإحصائيات:
    echo    • تبويب الأقساط المحسن
    echo    • طباعة وتصدير بـ 5 صيغ مختلفة
    echo    • إحصائيات متقدمة ومؤشرات أداء
    echo.
    echo 🔒 الأمان والحماية:
    echo    • نسخ احتياطي تلقائي
    echo    • تشفير البيانات الحساسة
    echo    • سجل العمليات والمراجعة
    echo.
    echo 🚀 جاري فتح البرنامج...
    echo.
    echo 💡 نصائح للاختبار:
    echo    1. جرب النسخة التجريبية أولاً
    echo    2. اختبر إضافة مستخدمين بأدوار مختلفة
    echo    3. جرب نظام الحذف الآمن للسيارات
    echo    4. استخدم تبويب الأقساط المحسن
    echo    5. اختبر الطباعة والتصدير
    echo.
    
    REM تشغيل البرنامج
    start "" "CarDealershipManagement.exe"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    echo.
    echo 🔍 إذا لم تجد البرنامج:
    echo    • ابحث في شريط المهام السفلي
    echo    • استخدم Alt+Tab للتنقل بين النوافذ
    echo    • تحقق من أن البرنامج لم يفتح خلف النوافذ الأخرى
    echo.
    echo 🧪 سيناريوهات الاختبار المقترحة:
    echo.
    echo 1️⃣ اختبار نظام التفعيل:
    echo    • شغل البرنامج لأول مرة
    echo    • اختر "نسخة تجريبية" 
    echo    • تحقق من رسالة التفعيل
    echo    • راجع معلومات الترخيص في الإعدادات
    echo.
    echo 2️⃣ اختبار نظام الصلاحيات:
    echo    • سجل الدخول بحساب المطور
    echo    • انتقل إلى "الإدارة" → "إدارة المستخدمين"
    echo    • أضف مندوب مبيعات جديد
    echo    • راجع قائمة الصلاحيات الكاملة (64+ صلاحية)
    echo    • اختبر الصلاحيات المختلفة لكل دور
    echo.
    echo 3️⃣ اختبار نظام الحذف الآمن:
    echo    • انتقل إلى "إدارة المخزون"
    echo    • أضف سيارة جديدة
    echo    • قم ببيعها (نقدي أو تقسيط)
    echo    • جرب حذف السيارة
    echo    • راجع نافذة التأكيد المتقدمة
    echo.
    echo 4️⃣ اختبار تبويب الأقساط المحسن:
    echo    • انتقل إلى "التقارير"
    echo    • اختر تبويب "📅 تقارير الأقساط"
    echo    • جرب الفلاتر المختلفة
    echo    • اختبر الطباعة المحسنة
    echo    • جرب التصدير بصيغ مختلفة
    echo    • راجع ملخص الأقساط السريع
    echo.
    echo 5️⃣ اختبار الأمان والحماية:
    echo    • جرب النسخ الاحتياطي
    echo    • راجع سجل العمليات
    echo    • اختبر تشفير البيانات
    echo.
    
) else (
    echo ❌ خطأ: لم يتم العثور على ملف البرنامج
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. تأكد من بناء المشروع بنجاح
    echo    2. تحقق من مسار الملف
    echo    3. أعد بناء المشروع في وضع Release
    echo.
)

cd /d "%~dp0"

echo.
echo 📊 معلومات إضافية:
echo.
echo 🎯 الهدف من الاختبار:
echo    • التأكد من عمل نظام التفعيل بشكل صحيح
echo    • اختبار جميع الميزات الجديدة
echo    • التحقق من استقرار البرنامج
echo    • تجربة واجهة المستخدم المحسنة
echo.

echo 📋 تقرير الاختبار:
echo    بعد الانتهاء من الاختبار، يرجى التحقق من:
echo    ✅ نظام التفعيل يعمل بشكل صحيح
echo    ✅ جميع الصلاحيات تظهر في قائمة إدارة المستخدمين
echo    ✅ نظام الحذف الآمن يحمي البيانات المالية
echo    ✅ تبويب الأقساط يعرض التقارير بشكل صحيح
echo    ✅ الطباعة والتصدير يعملان بجميع الصيغ
echo.

echo 📞 للإبلاغ عن مشاكل أو اقتراحات:
echo    📧 البريد الإلكتروني: <EMAIL>
echo    📝 سجل المشاكل والملاحظات لتحسين البرنامج
echo.

echo اضغط أي مفتاح للخروج...
pause >nul
