@echo off
chcp 65001 >nul
title إنشاء حزمة التثبيت النهائية - Amr Ali Elawamy

echo.
echo ========================================
echo   📦 إنشاء حزمة التثبيت النهائية
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo 🎯 سيتم إنشاء حزمة تثبيت كاملة تحتوي على:
echo    ✅ البرنامج الكامل مع جميع المكتبات
echo    ✅ قاعدة البيانات والإعدادات
echo    ✅ الأيقونات والموارد
echo    ✅ ملف التثبيت التلقائي
echo    ✅ دليل المستخدم والتوثيق
echo    ✅ ملف إلغاء التثبيت
echo.

set "SOURCE_DIR=%~dp0CarDealership_Debug_Copy"
set "PACKAGE_DIR=%~dp0CarDealership_InstallPackage"

echo 🔍 التحقق من المتطلبات...

if not exist "%SOURCE_DIR%\CarDealershipManagement.exe" (
    echo ❌ البرنامج غير موجود في: %SOURCE_DIR%
    echo يرجى تشغيل "إنشاء_نسخة_من_Debug.bat" أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على البرنامج

echo.
echo 🧹 تنظيف المجلد السابق...

if exist "%PACKAGE_DIR%" (
    rmdir /s /q "%PACKAGE_DIR%"
    echo ✅ تم حذف المجلد السابق
)

echo.
echo 📁 إنشاء هيكل حزمة التثبيت...

mkdir "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%\CarDealership_Debug_Copy"

echo ✅ تم إنشاء هيكل المجلدات

echo.
echo 📋 نسخ ملفات البرنامج...

xcopy "%SOURCE_DIR%\*" "%PACKAGE_DIR%\CarDealership_Debug_Copy\" /E /I /Y >nul

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم نسخ ملفات البرنامج
) else (
    echo ❌ فشل في نسخ ملفات البرنامج
    pause
    exit /b 1
)

echo.
echo 📝 إنشاء ملف التثبيت الرئيسي...

copy "%~dp0CarDealershipManagement_Installer.bat" "%PACKAGE_DIR%\install.bat" >nul

echo ✅ تم نسخ ملف التثبيت

echo.
echo 📄 إنشاء ملفات التوثيق...

REM إنشاء ملف README
(
echo برنامج إدارة معرض السيارات - حزمة التثبيت الكاملة
echo =====================================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo 🔢 الإصدار: 1.0.0 - ديسمبر 2024
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
echo 🚗 نظام شامل لإدارة معارض السيارات
echo.
echo 🚀 طريقة التثبيت:
echo    1. اضغط مرتين على install.bat
echo    2. اتبع التعليمات على الشاشة
echo    3. سيتم إنشاء اختصارات تلقائياً
echo    4. تشغيل البرنامج من سطح المكتب أو قائمة البدء
echo.
echo ⚠️ متطلبات النظام:
echo    • Windows 7 SP1 أو أحدث ^(64-bit^)
echo    • .NET 8.0 Runtime ^(سيتم تثبيته تلقائياً إذا لم يكن موجوداً^)
echo    • 200 ميجابايت مساحة فارغة على القرص الصلب
echo    • 2 جيجابايت ذاكرة وصول عشوائي
echo    • صلاحيات المدير لتثبيت البرنامج
echo.
echo 🔑 بيانات الدخول الافتراضية:
echo.
echo    🔧 المطور ^(جميع الصلاحيات^):
echo       اسم المستخدم: amrali
echo       كلمة المرور: braa
echo.
echo    👔 المدير ^(صلاحيات إدارية^):
echo       اسم المستخدم: admin
echo       كلمة المرور: 123
echo.
echo    🤝 مندوب المبيعات ^(صلاحيات أساسية^):
echo       اسم المستخدم: user
echo       كلمة المرور: pass
echo.
echo 🆕 نظام التفعيل المتقدم:
echo    • نسخة تجريبية مجانية ^(30 يوم^)
echo    • تراخيص شهرية: MONTH-XXXXXX-XXXXXX
echo    • تراخيص ربع سنوية: QUARTER-XXXXXX-XXXXXX ^(خصم 10%%^)
echo    • تراخيص سنوية: YEAR-XXXXXX-XXXXXX ^(خصم 25%%^)
echo    • ترخيص مدى الحياة: LIFE-XXXXXX-XXXXXX ^(حتى 10 مستخدمين^)
echo.
echo 🎯 الميزات الرئيسية:
echo.
echo    📦 إدارة المخزون المتقدمة:
echo       • إدارة شاملة للسيارات مع نظام ضمان سلامة البيانات المالية
echo       • إدارة ملفات ومرفقات السيارات
echo       • تصنيف وفلترة متقدمة
echo       • تتبع حالة السيارات ^(متاح، مباع، محجوز^)
echo.
echo    💰 نظام المبيعات الذكي:
echo       • بيع نقدي وبالتقسيط مع إدارة الأقساط المتقدمة
echo       • طباعة الفواتير والعقود
echo       • تتبع حالة المدفوعات
echo       • إدارة المرتجعات والاستبدالات
echo.
echo    👥 إدارة العملاء والموردين:
echo       • قاعدة بيانات شاملة للعملاء والموردين
echo       • تاريخ المشتريات والمدفوعات
echo       • كشوف حساب مفصلة
echo       • نظام تقييم العملاء
echo.
echo    📊 التقارير والإحصائيات:
echo       • تقارير المبيعات والأرباح التفصيلية
echo       • تقارير الأقساط المحسنة
echo       • طباعة وتصدير بـ 5 صيغ مختلفة ^(PDF, Excel, Word, HTML, CSV^)
echo       • إحصائيات متقدمة ومؤشرات أداء
echo       • تقارير مخصصة حسب التاريخ والفئة
echo.
echo    👤 إدارة المستخدمين:
echo       • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo       • أدوار متعددة ^(مطور، مدير، مندوب^)
echo       • إدارة كلمات المرور والأمان
echo       • تتبع نشاط المستخدمين
echo       • نظام تسجيل الدخول المتقدم
echo.
echo    🔒 الأمان والحماية:
echo       • نسخ احتياطي تلقائي ويدوي
echo       • تشفير البيانات الحساسة
echo       • نظام تفعيل وترخيص متقدم
echo       • حماية من فقدان البيانات
echo       • نظام استرداد البيانات
echo.
echo 💡 نصائح مهمة:
echo    • قم بتغيير كلمات المرور بعد أول تسجيل دخول
echo    • أنشئ نسخة احتياطية من البيانات بانتظام
echo    • استخدم حساب المطور للوصول لجميع الميزات
echo    • راجع التقارير دورياً لمتابعة الأداء
echo    • احتفظ بنسخة من مفتاح الترخيص في مكان آمن
echo.
echo 🔧 استكشاف الأخطاء وإصلاحها:
echo    • إذا لم يعمل البرنامج، تأكد من تثبيت .NET 8.0
echo    • إذا ظهرت رسالة خطأ في قاعدة البيانات، شغل البرنامج كمدير
echo    • للدعم الفني اتصل على: 01285626623
echo.
echo 📞 للدعم الفني والمبيعات:
echo    الهاتف: 01285626623
echo    البريد الإلكتروني: <EMAIL>
echo    ساعات العمل: من 9 صباحاً إلى 9 مساءً
echo.
echo 🌐 روابط مفيدة:
echo    • الموقع الرسمي: www.cardealership.com
echo    • دليل المستخدم: www.cardealership.com/manual
echo    • التحديثات: www.cardealership.com/updates
echo    • الدعم الفني: www.cardealership.com/support
echo.
echo 🏆 شكراً لاختيارك برنامج إدارة معرض السيارات!
echo جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
echo تاريخ الإنشاء: %date% %time%
) > "%PACKAGE_DIR%\README.txt"

REM إنشاء ملف معلومات سريعة
(
echo 🚀 تشغيل سريع - برنامج إدارة معرض السيارات
echo ==========================================
echo.
echo 1. شغل install.bat كمدير
echo 2. اتبع التعليمات
echo 3. شغل البرنامج من سطح المكتب
echo 4. سجل دخول: amrali / braa
echo 5. اختر "نسخة تجريبية" للبدء
echo.
echo للدعم: 01285626623
echo المطور: Amr Ali Elawamy
) > "%PACKAGE_DIR%\تشغيل_سريع.txt"

echo ✅ تم إنشاء ملفات التوثيق

echo.
echo 📦 إنشاء ملف ZIP للتوزيع...

powershell -Command "
try {
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    $source = '%PACKAGE_DIR%'
    $destination = '%~dp0CarDealershipManagement_CompleteInstaller.zip'
    
    if (Test-Path $destination) {
        Remove-Item $destination -Force
    }
    
    [System.IO.Compression.ZipFile]::CreateFromDirectory($source, $destination)
    Write-Host '✅ تم إنشاء ملف ZIP بنجاح' -ForegroundColor Green
    
    $fileSize = (Get-Item $destination).Length / 1MB
    Write-Host ('📏 حجم الملف: {0:N1} ميجابايت' -f $fileSize) -ForegroundColor Cyan
    
} catch {
    Write-Host '❌ فشل في إنشاء ملف ZIP' -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}
"

echo.
echo 📊 ملخص العملية:
echo.

if exist "%~dp0CarDealershipManagement_CompleteInstaller.zip" (
    echo ✅ تم إنشاء حزمة التثبيت الكاملة بنجاح!
    echo.
    echo 📦 الملفات المنشأة:
    echo    📁 مجلد الحزمة: %PACKAGE_DIR%
    echo    📄 ملف ZIP: CarDealershipManagement_CompleteInstaller.zip
    echo.
    
    REM عرض حجم الملف
    for %%A in ("CarDealershipManagement_CompleteInstaller.zip") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo    📏 حجم ملف ZIP: !FILE_SIZE_MB! ميجابايت
    )
    
    echo.
    echo 🎯 محتويات حزمة التثبيت:
    echo    ✅ install.bat - ملف التثبيت الرئيسي
    echo    ✅ CarDealership_Debug_Copy\ - البرنامج الكامل
    echo    ✅ README.txt - دليل شامل
    echo    ✅ تشغيل_سريع.txt - تعليمات سريعة
    echo.
    
    echo 💡 للتوزيع:
    echo    1. أرسل ملف CarDealershipManagement_CompleteInstaller.zip للعملاء
    echo    2. اطلب منهم استخراج الملفات
    echo    3. تشغيل install.bat كمدير
    echo    4. اتباع التعليمات على الشاشة
    echo.
    
    echo 🔑 بيانات الدخول للعملاء:
    echo    المطور: amrali / braa ^(جميع الصلاحيات^)
    echo    المدير: admin / 123 ^(صلاحيات إدارية^)
    echo    المندوب: user / pass ^(صلاحيات أساسية^)
    echo.
    
    echo هل تريد فتح مجلد الحزمة؟ ^(Y/N^)
    set /p "OPEN_FOLDER="
    if /i "%OPEN_FOLDER%"=="Y" (
        start "" "%PACKAGE_DIR%"
    )
    
    echo.
    echo هل تريد اختبار التثبيت؟ ^(Y/N^)
    set /p "TEST_INSTALL="
    if /i "%TEST_INSTALL%"=="Y" (
        echo 🚀 تشغيل ملف التثبيت...
        start "" "%PACKAGE_DIR%\install.bat"
    )
    
) else (
    echo ❌ فشل في إنشاء حزمة التثبيت
)

echo.
echo 🎉 انتهت عملية إنشاء حزمة التثبيت!
echo.
echo 📦 الملف النهائي: CarDealershipManagement_CompleteInstaller.zip
echo 💡 جاهز للتوزيع والبيع!
echo.

echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
