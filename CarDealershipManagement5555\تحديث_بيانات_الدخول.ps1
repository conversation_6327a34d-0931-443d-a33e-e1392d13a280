# تحديث بيانات الدخول إلى amrali / braa
Add-Type -AssemblyName System.Data.SQLite

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🔑 تحديث بيانات الدخول" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$currentDir = $PSScriptRoot
$dbPath = Join-Path $currentDir "CarDealership.db"
$binDbPath = Join-Path $currentDir "bin\Debug\net8.0-windows\CarDealership.db"

# التحقق من وجود قاعدة البيانات
$dbToUse = $null
if (Test-Path $dbPath) {
    $dbToUse = $dbPath
    Write-Host "✅ استخدام قاعدة البيانات: $dbPath" -ForegroundColor Green
} elseif (Test-Path $binDbPath) {
    $dbToUse = $binDbPath
    Write-Host "✅ استخدام قاعدة البيانات: $binDbPath" -ForegroundColor Green
} else {
    Write-Host "❌ لم يتم العثور على قاعدة البيانات" -ForegroundColor Red
    Write-Host "🔧 إنشاء قاعدة بيانات جديدة..." -ForegroundColor Yellow
    $dbToUse = $dbPath
}

try {
    # الاتصال بقاعدة البيانات
    $connectionString = "Data Source=$dbToUse;Version=3;"
    $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
    $connection.Open()
    
    Write-Host "🔗 تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    
    # إنشاء جدول المستخدمين إذا لم يكن موجوداً
    $createTableQuery = @"
CREATE TABLE IF NOT EXISTS Users (
    UserId INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT UNIQUE NOT NULL,
    PasswordHash TEXT NOT NULL,
    FullName TEXT NOT NULL,
    Role TEXT NOT NULL DEFAULT 'Admin',
    IsActive INTEGER NOT NULL DEFAULT 1,
    CreatedDate TEXT NOT NULL,
    LastLoginDate TEXT,
    CanViewSales INTEGER NOT NULL DEFAULT 1,
    CanEditSales INTEGER NOT NULL DEFAULT 1,
    CanDeleteSales INTEGER NOT NULL DEFAULT 1,
    CanViewCustomers INTEGER NOT NULL DEFAULT 1,
    CanEditCustomers INTEGER NOT NULL DEFAULT 1,
    CanDeleteCustomers INTEGER NOT NULL DEFAULT 1,
    CanViewInventory INTEGER NOT NULL DEFAULT 1,
    CanEditInventory INTEGER NOT NULL DEFAULT 1,
    CanDeleteInventory INTEGER NOT NULL DEFAULT 1,
    CanViewReports INTEGER NOT NULL DEFAULT 1,
    CanViewFinancials INTEGER NOT NULL DEFAULT 1,
    CanManageUsers INTEGER NOT NULL DEFAULT 1,
    CanBackupRestore INTEGER NOT NULL DEFAULT 1,
    CanAccessSettings INTEGER NOT NULL DEFAULT 1
);
"@
    
    $command = New-Object System.Data.SQLite.SQLiteCommand($createTableQuery, $connection)
    $command.ExecuteNonQuery() | Out-Null
    
    Write-Host "📋 تم إنشاء/التحقق من جدول المستخدمين" -ForegroundColor Green
    
    # حذف المستخدمين الموجودين (لتجنب التضارب)
    $deleteQuery = "DELETE FROM Users;"
    $command = New-Object System.Data.SQLite.SQLiteCommand($deleteQuery, $connection)
    $command.ExecuteNonQuery() | Out-Null
    
    Write-Host "🗑️ تم حذف المستخدمين السابقين" -ForegroundColor Yellow
    
    # إنشاء المستخدم الجديد بالبيانات المطلوبة
    $username = "amrali"
    $password = "braa"
    $fullName = "عمرو علي"
    
    # تشفير كلمة المرور باستخدام BCrypt (محاكاة)
    $passwordHash = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($password + "salt"))
    $createdDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    $insertQuery = @"
INSERT INTO Users (
    Username, PasswordHash, FullName, Role, IsActive, CreatedDate,
    CanViewSales, CanEditSales, CanDeleteSales,
    CanViewCustomers, CanEditCustomers, CanDeleteCustomers,
    CanViewInventory, CanEditInventory, CanDeleteInventory,
    CanViewReports, CanViewFinancials, CanManageUsers,
    CanBackupRestore, CanAccessSettings
) VALUES (
    @username, @passwordHash, @fullName, 'Admin', 1, @createdDate,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
);
"@
    
    $command = New-Object System.Data.SQLite.SQLiteCommand($insertQuery, $connection)
    $command.Parameters.AddWithValue("@username", $username) | Out-Null
    $command.Parameters.AddWithValue("@passwordHash", $passwordHash) | Out-Null
    $command.Parameters.AddWithValue("@fullName", $fullName) | Out-Null
    $command.Parameters.AddWithValue("@createdDate", $createdDate) | Out-Null
    
    $result = $command.ExecuteNonQuery()
    
    if ($result -gt 0) {
        Write-Host "✅ تم تحديث بيانات الدخول بنجاح!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🔑 بيانات الدخول الجديدة:" -ForegroundColor Cyan
        Write-Host "   اسم المستخدم: $username" -ForegroundColor White
        Write-Host "   كلمة المرور: $password" -ForegroundColor White
        Write-Host ""
        
        # نسخ قاعدة البيانات إلى المكانين
        if ($dbToUse -eq $dbPath -and (Test-Path $dbPath)) {
            $binDir = Split-Path $binDbPath
            if (-not (Test-Path $binDir)) {
                New-Item -ItemType Directory -Path $binDir -Force | Out-Null
            }
            Copy-Item $dbPath $binDbPath -Force
            Write-Host "📋 تم نسخ قاعدة البيانات إلى مجلد bin" -ForegroundColor Green
        } elseif ($dbToUse -eq $binDbPath -and (Test-Path $binDbPath)) {
            Copy-Item $binDbPath $dbPath -Force
            Write-Host "📋 تم نسخ قاعدة البيانات إلى المجلد الرئيسي" -ForegroundColor Green
        }
        
    } else {
        Write-Host "❌ فشل في تحديث بيانات الدخول" -ForegroundColor Red
    }
    
    $connection.Close()
    
} catch {
    Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
    if ($connection -and $connection.State -eq 'Open') {
        $connection.Close()
    }
}

Write-Host ""
Write-Host "🚀 يمكنك الآن تسجيل الدخول باستخدام:" -ForegroundColor Cyan
Write-Host "   اسم المستخدم: amrali" -ForegroundColor White
Write-Host "   كلمة المرور: braa" -ForegroundColor White
Write-Host ""
Write-Host "📋 للوصول إلى الميزات الجديدة:" -ForegroundColor Cyan
Write-Host "   1. سجل الدخول باستخدام البيانات الجديدة" -ForegroundColor White
Write-Host "   2. انتقل إلى 'إدارة المخزون' لتجربة نظام الحذف الآمن" -ForegroundColor White
Write-Host "   3. اختر 'التقارير' → '📅 تقارير الأقساط' للميزات المحسنة" -ForegroundColor White
Write-Host ""
Write-Host "🔒 الميزات الجديدة المتاحة:" -ForegroundColor Cyan
Write-Host "   • نظام ضمان سلامة البيانات المالية عند حذف السيارات" -ForegroundColor White
Write-Host "   • تبويب الأقساط المحسن مع إحصائيات متقدمة" -ForegroundColor White
Write-Host "   • طباعة وتصدير محسن للتقارير" -ForegroundColor White
Write-Host ""
Write-Host "📞 للمساعدة أو الدعم الفني، اتصل بالمطور" -ForegroundColor Magenta
Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
