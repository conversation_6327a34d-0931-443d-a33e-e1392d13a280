using System;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;

namespace CarDealershipManagement.Forms
{
    public partial class CustomerStatementPrintForm : Form
    {
        private readonly int customerId;
        private readonly string customerName;
        private readonly DataTable statementData;
        private readonly DateTime fromDate;
        private readonly DateTime toDate;
        
        private PrintDocument printDocument;
        private PrintPreviewDialog printPreviewDialog;
        private PageSetupDialog pageSetupDialog;
        private PrintDialog printDialog;
        
        // Print settings
        private int currentRow = 0;
        private bool hasMorePages = false;
        private readonly int rowsPerPage = 25;
        private readonly Font headerFont = new Font("Segoe UI", 16F, FontStyle.Bold);
        private readonly Font titleFont = new Font("Segoe UI", 14F, FontStyle.Bold);
        private readonly Font normalFont = new Font("Segoe UI", 10F);
        private readonly Font smallFont = new Font("Segoe UI", 9F);

        public CustomerStatementPrintForm(int customerId, string customerName, DataTable statementData, 
            DateTime fromDate, DateTime toDate)
        {
            this.customerId = customerId;
            this.customerName = customerName;
            this.statementData = statementData;
            this.fromDate = fromDate;
            this.toDate = toDate;
            
            InitializeComponent();
            SetupPrinting();
        }

        private void InitializeComponent()
        {
            // Form settings
            Text = "طباعة كشف حساب العميل";
            Size = new Size(400, 300);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            BackColor = Color.White;

            // Create buttons
            var btnPreview = new Button
            {
                Text = "👁️ معاينة الطباعة",
                Location = new Point(50, 50),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(0, 102, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnPreview.FlatAppearance.BorderSize = 0;
            btnPreview.Click += BtnPreview_Click;

            var btnPrint = new Button
            {
                Text = "🖨️ طباعة مباشرة",
                Location = new Point(50, 110),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.Click += BtnPrint_Click;

            var btnPageSetup = new Button
            {
                Text = "⚙️ إعداد الصفحة",
                Location = new Point(50, 170),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnPageSetup.FlatAppearance.BorderSize = 0;
            btnPageSetup.Click += BtnPageSetup_Click;

            var btnClose = new Button
            {
                Text = "❌ إغلاق",
                Location = new Point(220, 110),
                Size = new Size(100, 40),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += (s, e) => this.Close();

            Controls.AddRange(new Control[] { btnPreview, btnPrint, btnPageSetup, btnClose });
        }

        private void SetupPrinting()
        {
            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
            printDocument.BeginPrint += PrintDocument_BeginPrint;

            printPreviewDialog = new PrintPreviewDialog
            {
                Document = printDocument,
                WindowState = FormWindowState.Maximized
            };

            pageSetupDialog = new PageSetupDialog
            {
                Document = printDocument
            };

            printDialog = new PrintDialog
            {
                Document = printDocument
            };
        }

        private void PrintDocument_BeginPrint(object sender, PrintEventArgs e)
        {
            currentRow = 0;
            hasMorePages = false;
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;
            float yPosition = 50;
            float leftMargin = 50;
            float rightMargin = e.PageBounds.Width - 50;
            float pageWidth = rightMargin - leftMargin;

            // رسم اللوجو وبيانات المعرض في الأعلى
            DrawCompanyHeader(g, leftMargin, ref yPosition, pageWidth);

            // عنوان التقرير
            string reportTitle = "كشف حساب العميل";
            SizeF titleSize = g.MeasureString(reportTitle, headerFont);
            g.DrawString(reportTitle, headerFont, Brushes.Black, 
                leftMargin + (pageWidth - titleSize.Width) / 2, yPosition);
            yPosition += titleSize.Height + 20;

            // معلومات العميل والفترة
            DrawCustomerInfo(g, leftMargin, ref yPosition, pageWidth);

            // رؤوس الأعمدة
            DrawTableHeaders(g, leftMargin, ref yPosition, pageWidth);

            // البيانات
            DrawTableData(g, leftMargin, ref yPosition, pageWidth, e.PageBounds.Height - 100);

            // التذييل
            DrawFooter(g, leftMargin, e.PageBounds.Height - 50, pageWidth);

            // تحديد ما إذا كانت هناك صفحات أخرى
            e.HasMorePages = hasMorePages;
        }

        private void DrawCompanyHeader(Graphics g, float leftMargin, ref float yPosition, float pageWidth)
        {
            // رسم مستطيل للرأسية
            var headerRect = new RectangleF(leftMargin, yPosition, pageWidth, 80);
            g.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), headerRect);
            g.DrawRectangle(new Pen(Color.FromArgb(0, 102, 204), 2), Rectangle.Round(headerRect));

            // اللوجو (مربع ملون كبديل)
            var logoRect = new RectangleF(leftMargin + 10, yPosition + 10, 60, 60);
            g.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), logoRect);
            g.DrawString("LOGO", new Font("Segoe UI", 12F, FontStyle.Bold), 
                Brushes.White, logoRect.X + 10, logoRect.Y + 20);

            // بيانات المعرض
            float textX = leftMargin + 90;
            g.DrawString("معرض السيارات المتميز", titleFont, Brushes.Black, textX, yPosition + 10);
            g.DrawString("العنوان: شارع الملك فهد، الرياض، المملكة العربية السعودية", normalFont, 
                Brushes.Black, textX, yPosition + 35);
            g.DrawString("الهاتف: 966-11-1234567+ | البريد الإلكتروني: <EMAIL>", normalFont, 
                Brushes.Black, textX, yPosition + 55);

            yPosition += 100;
        }

        private void DrawCustomerInfo(Graphics g, float leftMargin, ref float yPosition, float pageWidth)
        {
            // معلومات العميل والفترة
            var infoRect = new RectangleF(leftMargin, yPosition, pageWidth, 60);
            g.FillRectangle(new SolidBrush(Color.FromArgb(240, 248, 255)), infoRect);
            g.DrawRectangle(Pens.Gray, Rectangle.Round(infoRect));

            g.DrawString($"اسم العميل: {customerName}", titleFont, Brushes.Black, leftMargin + 10, yPosition + 10);
            g.DrawString($"رقم العميل: {customerId}", normalFont, Brushes.Black, leftMargin + 10, yPosition + 35);
            
            string dateRange = $"الفترة: من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}";
            SizeF dateSize = g.MeasureString(dateRange, normalFont);
            g.DrawString(dateRange, normalFont, Brushes.Black, 
                leftMargin + pageWidth - dateSize.Width - 10, yPosition + 10);

            string printDate = $"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}";
            SizeF printDateSize = g.MeasureString(printDate, smallFont);
            g.DrawString(printDate, smallFont, Brushes.Gray, 
                leftMargin + pageWidth - printDateSize.Width - 10, yPosition + 35);

            yPosition += 80;
        }

        private void DrawTableHeaders(Graphics g, float leftMargin, ref float yPosition, float pageWidth)
        {
            string[] headers = { "التاريخ", "نوع العملية", "الوصف", "مدين", "دائن", "الرصيد" };
            float[] columnWidths = { 80, 100, 200, 80, 80, 80 };
            
            float currentX = leftMargin;
            var headerRect = new RectangleF(leftMargin, yPosition, pageWidth, 30);
            g.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), headerRect);

            for (int i = 0; i < headers.Length; i++)
            {
                g.DrawString(headers[i], new Font("Segoe UI", 10F, FontStyle.Bold), 
                    Brushes.White, currentX + 5, yPosition + 8);
                g.DrawLine(Pens.White, currentX + columnWidths[i], yPosition, 
                    currentX + columnWidths[i], yPosition + 30);
                currentX += columnWidths[i];
            }

            yPosition += 35;
        }

        private void DrawTableData(Graphics g, float leftMargin, ref float yPosition, float pageWidth, float maxY)
        {
            if (statementData == null || statementData.Rows.Count == 0)
            {
                g.DrawString("لا توجد بيانات للعرض", normalFont, Brushes.Gray, 
                    leftMargin + pageWidth / 2 - 50, yPosition + 20);
                return;
            }

            float[] columnWidths = { 80, 100, 200, 80, 80, 80 };
            int rowsDrawn = 0;

            for (int i = currentRow; i < statementData.Rows.Count && yPosition < maxY - 30; i++)
            {
                DataRow row = statementData.Rows[i];
                float currentX = leftMargin;

                // رسم خلفية الصف
                var rowRect = new RectangleF(leftMargin, yPosition, pageWidth, 25);
                if (i % 2 == 0)
                    g.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), rowRect);

                // رسم البيانات
                string[] values = {
                    Convert.ToDateTime(row["التاريخ"]).ToString("yyyy/MM/dd"),
                    row["نوع العملية"].ToString(),
                    row["الوصف"].ToString(),
                    row["مدين"] != DBNull.Value ? Convert.ToDecimal(row["مدين"]).ToString("N2") : "",
                    row["دائن"] != DBNull.Value ? Convert.ToDecimal(row["دائن"]).ToString("N2") : "",
                    row["الرصيد"] != DBNull.Value ? Convert.ToDecimal(row["الرصيد"]).ToString("N2") : ""
                };

                for (int j = 0; j < values.Length; j++)
                {
                    g.DrawString(values[j], normalFont, Brushes.Black, currentX + 5, yPosition + 5);
                    g.DrawLine(Pens.LightGray, currentX + columnWidths[j], yPosition, 
                        currentX + columnWidths[j], yPosition + 25);
                    currentX += columnWidths[j];
                }

                g.DrawLine(Pens.LightGray, leftMargin, yPosition + 25, leftMargin + pageWidth, yPosition + 25);
                yPosition += 25;
                rowsDrawn++;
                currentRow++;
            }

            // تحديد ما إذا كانت هناك صفحات أخرى
            hasMorePages = currentRow < statementData.Rows.Count;
        }

        private void DrawFooter(Graphics g, float leftMargin, float yPosition, float pageWidth)
        {
            string footer = $"تم إنشاء هذا التقرير بواسطة نظام إدارة معرض السيارات - {DateTime.Now:yyyy}";
            SizeF footerSize = g.MeasureString(footer, smallFont);
            g.DrawString(footer, smallFont, Brushes.Gray, 
                leftMargin + (pageWidth - footerSize.Width) / 2, yPosition);
        }

        private void BtnPreview_Click(object sender, EventArgs e)
        {
            try
            {
                currentRow = 0;
                printPreviewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء معاينة الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    currentRow = 0;
                    printDocument.Print();
                    MessageBox.Show("تم إرسال المستند للطباعة بنجاح!", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPageSetup_Click(object sender, EventArgs e)
        {
            try
            {
                pageSetupDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إعداد الصفحة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                printDocument?.Dispose();
                printPreviewDialog?.Dispose();
                pageSetupDialog?.Dispose();
                printDialog?.Dispose();
                headerFont?.Dispose();
                titleFont?.Dispose();
                normalFont?.Dispose();
                smallFont?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
