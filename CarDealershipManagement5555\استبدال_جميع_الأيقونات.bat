@echo off
chcp 65001 >nul
title استبدال جميع الأيقونات - برنامج إدارة معرض السيارات

echo.
echo ========================================
echo   🔄 استبدال جميع الأيقونات
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 سيتم استبدال جميع الأيقونات القديمة بالأيقونة الجديدة:
echo    من: app11_icon.png
echo    إلى: app-icon.ico / app-icon.png
echo.

echo 🔍 البحث عن الأيقونات القديمة...

REM التحقق من وجود الأيقونة الجديدة
if not exist "app-icon.ico" (
    echo ❌ الأيقونة الجديدة غير موجودة: app-icon.ico
    echo يرجى تشغيل "إنشاء_أيقونة_بسيطة.ps1" أولاً
    pause
    exit /b 1
)

if not exist "app-icon.png" (
    echo ❌ الأيقونة الجديدة غير موجودة: app-icon.png
    echo يرجى تشغيل "إنشاء_أيقونة_بسيطة.ps1" أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على الأيقونات الجديدة

echo.
echo 🔄 استبدال الأيقونات في الملفات...

REM استبدال app11_icon بـ app-icon في جميع ملفات الكود
echo البحث في ملفات C#...
for /r %%f in (*.cs) do (
    findstr /l "app11_icon" "%%f" >nul 2>&1
    if !errorlevel! equ 0 (
        echo تحديث: %%f
        powershell -Command "(Get-Content '%%f') -replace 'app11_icon', 'app-icon' | Set-Content '%%f' -Encoding UTF8"
    )
)

echo البحث في ملفات Designer...
for /r %%f in (*.Designer.cs) do (
    findstr /l "app11_icon" "%%f" >nul 2>&1
    if !errorlevel! equ 0 (
        echo تحديث: %%f
        powershell -Command "(Get-Content '%%f') -replace 'app11_icon', 'app-icon' | Set-Content '%%f' -Encoding UTF8"
    )
)

echo البحث في ملفات resx...
for /r %%f in (*.resx) do (
    findstr /l "app11_icon" "%%f" >nul 2>&1
    if !errorlevel! equ 0 (
        echo تحديث: %%f
        powershell -Command "(Get-Content '%%f') -replace 'app11_icon', 'app-icon' | Set-Content '%%f' -Encoding UTF8"
    )
)

echo.
echo 📁 نسخ الأيقونات إلى المجلدات المطلوبة...

REM نسخ الأيقونات إلى مجلد Resources إذا كان موجوداً
if exist "Resources" (
    echo نسخ إلى مجلد Resources...
    copy "app-icon.ico" "Resources\" >nul 2>&1
    copy "app-icon.png" "Resources\" >nul 2>&1
    copy "app-icon.svg" "Resources\" >nul 2>&1
    echo ✅ تم نسخ الأيقونات إلى Resources
)

REM نسخ الأيقونات إلى مجلد bin\Debug
if exist "bin\Debug\net8.0-windows" (
    echo نسخ إلى مجلد Debug...
    copy "app-icon.ico" "bin\Debug\net8.0-windows\" >nul 2>&1
    copy "app-icon.png" "bin\Debug\net8.0-windows\" >nul 2>&1
    copy "app-icon.svg" "bin\Debug\net8.0-windows\" >nul 2>&1
    echo ✅ تم نسخ الأيقونات إلى Debug
)

REM نسخ الأيقونات إلى مجلد bin\Release
if exist "bin\Release\net8.0-windows" (
    echo نسخ إلى مجلد Release...
    copy "app-icon.ico" "bin\Release\net8.0-windows\" >nul 2>&1
    copy "app-icon.png" "bin\Release\net8.0-windows\" >nul 2>&1
    copy "app-icon.svg" "bin\Release\net8.0-windows\" >nul 2>&1
    echo ✅ تم نسخ الأيقونات إلى Release
)

REM نسخ الأيقونات إلى المجلد المستقل
if exist "CarDealership_Standalone" (
    echo نسخ إلى المجلد المستقل...
    copy "app-icon.ico" "CarDealership_Standalone\" >nul 2>&1
    copy "app-icon.png" "CarDealership_Standalone\" >nul 2>&1
    copy "app-icon.svg" "CarDealership_Standalone\" >nul 2>&1
    echo ✅ تم نسخ الأيقونات إلى المجلد المستقل
)

REM نسخ الأيقونات إلى النسخة Debug
if exist "CarDealership_Debug_Copy" (
    echo نسخ إلى النسخة Debug...
    copy "app-icon.ico" "CarDealership_Debug_Copy\" >nul 2>&1
    copy "app-icon.png" "CarDealership_Debug_Copy\" >nul 2>&1
    copy "app-icon.svg" "CarDealership_Debug_Copy\" >nul 2>&1
    echo ✅ تم نسخ الأيقونات إلى النسخة Debug
)

echo.
echo 🗑️ حذف الأيقونات القديمة...

REM حذف app11_icon من جميع المجلدات
if exist "app11_icon.png" (
    del "app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من المجلد الرئيسي
)

if exist "Resources\app11_icon.png" (
    del "Resources\app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من Resources
)

if exist "bin\Debug\net8.0-windows\app11_icon.png" (
    del "bin\Debug\net8.0-windows\app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من Debug
)

if exist "bin\Release\net8.0-windows\app11_icon.png" (
    del "bin\Release\net8.0-windows\app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من Release
)

if exist "CarDealership_Standalone\app11_icon.png" (
    del "CarDealership_Standalone\app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من المجلد المستقل
)

if exist "CarDealership_Debug_Copy\app11_icon.png" (
    del "CarDealership_Debug_Copy\app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من النسخة Debug
)

echo.
echo 🔧 تحديث ملف المشروع...

REM التأكد من إعداد الأيقونة في ملف المشروع
findstr /C:"ApplicationIcon" "CarDealershipManagement.csproj" >nul
if %ERRORLEVEL% NEQ 0 (
    echo إضافة إعداد الأيقونة إلى ملف المشروع...
    powershell -Command "
    $content = Get-Content 'CarDealershipManagement.csproj' -Raw
    if ($content -notmatch 'ApplicationIcon') {
        $content = $content -replace '(<PropertyGroup>)', '$1`n    <ApplicationIcon>app-icon.ico</ApplicationIcon>'
        Set-Content 'CarDealershipManagement.csproj' $content -Encoding UTF8
        Write-Host '✅ تم إضافة إعداد الأيقونة' -ForegroundColor Green
    }
    "
) else (
    echo ✅ إعداد الأيقونة موجود في ملف المشروع
)

echo.
echo 🏗️ إعادة بناء البرنامج...

echo تنظيف المشروع...
dotnet clean >nul 2>&1

echo بناء المشروع مع الأيقونة الجديدة...
dotnet build --configuration Release >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء البرنامج بنجاح مع الأيقونة الجديدة
    
    REM نسخ الملف التنفيذي المحدث إلى المجلدات
    if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
        if exist "CarDealership_Standalone" (
            copy "bin\Release\net8.0-windows\CarDealershipManagement.exe" "CarDealership_Standalone\" >nul 2>&1
            echo ✅ تم تحديث الملف التنفيذي في المجلد المستقل
        )
        
        if exist "CarDealership_Debug_Copy" (
            copy "bin\Release\net8.0-windows\CarDealershipManagement.exe" "CarDealership_Debug_Copy\" >nul 2>&1
            echo ✅ تم تحديث الملف التنفيذي في النسخة Debug
        )
    )
) else (
    echo ⚠️ تحذير: قد تكون هناك أخطاء في البناء
)

echo.
echo 📊 ملخص العملية:
echo.

echo ✅ تم استبدال جميع مراجع app11_icon بـ app-icon
echo ✅ تم نسخ الأيقونات الجديدة إلى جميع المجلدات
echo ✅ تم حذف الأيقونات القديمة
echo ✅ تم تحديث ملف المشروع
echo ✅ تم إعادة بناء البرنامج

echo.
echo 🎯 الأيقونات الجديدة:
echo    • app-icon.ico - أيقونة Windows الرسمية
echo    • app-icon.png - أيقونة عالية الدقة
echo    • app-icon.svg - أيقونة متجهة قابلة للتحجيم

echo.
echo 📁 المجلدات المحدثة:
if exist "Resources" echo    ✅ Resources
if exist "bin\Debug\net8.0-windows" echo    ✅ bin\Debug\net8.0-windows
if exist "bin\Release\net8.0-windows" echo    ✅ bin\Release\net8.0-windows
if exist "CarDealership_Standalone" echo    ✅ CarDealership_Standalone
if exist "CarDealership_Debug_Copy" echo    ✅ CarDealership_Debug_Copy

echo.
echo 💡 يمكنك الآن:
echo    1. تشغيل البرنامج ورؤية الأيقونة الجديدة
echo    2. توزيع المجلدات المحدثة
echo    3. إنشاء ملف تثبيت احترافي

echo.
echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy

echo.
echo هل تريد تشغيل البرنامج لرؤية الأيقونة الجديدة؟ (Y/N)
set /p "RUN_PROGRAM="
if /i "%RUN_PROGRAM%"=="Y" (
    if exist "CarDealership_Standalone\CarDealershipManagement.exe" (
        echo 🚀 تشغيل البرنامج من المجلد المستقل...
        start "" "CarDealership_Standalone\CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج مع الأيقونة الجديدة!
    ) else if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
        echo 🚀 تشغيل البرنامج من مجلد Release...
        start "" "bin\Release\net8.0-windows\CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج مع الأيقونة الجديدة!
    ) else (
        echo ❌ البرنامج غير موجود
    )
)

echo.
echo 🎉 تم استبدال جميع الأيقونات بنجاح!
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
