using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Helpers;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace CarDealershipManagement.Forms
{
public partial class CustomerForm : Form
{
    private User? currentUser;
    private DataGridView dgvCustomers;
    private TextBox txtSearch;
    private Button btnSearch;
    private Button btnAddCustomer;
    private Button btnEditCustomer;
    private Button btnDeleteCustomer;
    private Button btnReplaceCustomer;
    private Button btnRefresh;
    private Button btnPrintCustomers;
    private Button btnUploadFiles;
    private Button btnViewFiles;
    private Panel mainContainerPanel;
    private Panel headerPanel;
    private Panel searchPanel;
    private Panel buttonsPanel;

    public CustomerForm(User? currentUser = null)
    {
        this.currentUser = currentUser;
        InitializeComponent();
        LoadData();
    }

    private void InitializeComponent()
    {
        // Form settings - نفس تنسيق InventoryForm
        Text = "👥 إدارة العملاء - Customer Management";
        BackColor = Color.FromArgb(240, 248, 255);
        Font = new Font("Segoe UI", 10F);
        WindowState = FormWindowState.Maximized;
        StartPosition = FormStartPosition.CenterScreen;
        MinimumSize = new Size(1200, 700);

        // تطبيق إعدادات النافذة المرنة للشاشة الكاملة
        ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this,
            Screen.PrimaryScreen.WorkingArea.Width,
            Screen.PrimaryScreen.WorkingArea.Height);

        CreateLayoutStructure();
    }

    private void CreateLayoutStructure()
    {
        // Main Panel - نفس تنسيق InventoryForm
        mainContainerPanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(15)
        };

        // Header Panel (Logo + Title) - نفس تنسيق InventoryForm
        headerPanel = new Panel
        {
            Dock = DockStyle.Top,
            Height = 80,
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Title Label - نفس تنسيق InventoryForm
        var titleLabel = new Label
        {
            Text = "إدارة العملاء",
            Location = new Point(100, 25),
            Size = new Size(200, 30),
            Font = new Font("Segoe UI", 14F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            BackColor = Color.Transparent
        };
        headerPanel.Controls.Add(titleLabel);

        // Search and Filter Panel - نفس تنسيق InventoryForm
        searchPanel = new Panel
        {
            Dock = DockStyle.Top,
            Height = 60,
            BackColor = Color.White,
            Padding = new Padding(10, 5, 10, 5)
        };

        // Search Section - نفس تنسيق InventoryForm
        var lblSearch = new Label
        {
            Text = "البحث:",
            Location = new Point(20, 20),
            Size = new Size(50, 23),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            TextAlign = ContentAlignment.MiddleLeft
        };

        txtSearch = new TextBox
        {
            Location = new Point(80, 18),
            Size = new Size(200, 25),
            Font = new Font("Segoe UI", 9F),
            BorderStyle = BorderStyle.FixedSingle
        };

        btnSearch = new Button
        {
            Text = "بحث",
            Location = new Point(290, 17),
            Size = new Size(70, 28),
            BackColor = Color.FromArgb(0, 102, 204),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };
        btnSearch.FlatAppearance.BorderSize = 0;
        btnSearch.Click += BtnSearch_Click;

        searchPanel.Controls.AddRange(new Control[] { lblSearch, txtSearch, btnSearch });

        // Action Buttons Panel - نفس تنسيق InventoryForm
        var pnlButtons = new Panel
        {
            Dock = DockStyle.Top,
            Height = 120,
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // First row - Main action buttons - نفس تنسيق InventoryForm
        btnAddCustomer = CreateStyledButton("إضافة عميل", new Point(20, 15), Color.FromArgb(40, 167, 69));
        btnEditCustomer = CreateStyledButton("تعديل", new Point(132, 15), Color.FromArgb(255, 193, 7), Color.Black);
        btnDeleteCustomer = CreateStyledButton("حذف", new Point(214, 15), Color.FromArgb(220, 53, 69));
        btnReplaceCustomer = CreateStyledButton("استبدال عميل", new Point(296, 15), Color.FromArgb(138, 43, 226));
        btnRefresh = CreateStyledButton("تحديث", new Point(408, 15), Color.FromArgb(0, 123, 255));

        // Second row - File management and print buttons - نفس تنسيق InventoryForm
        btnUploadFiles = CreateStyledButton("رفع ملفات", new Point(20, 65), Color.FromArgb(108, 117, 125));
        btnViewFiles = CreateStyledButton("عرض الملفات", new Point(132, 65), Color.FromArgb(23, 162, 184));
        btnPrintCustomers = CreateStyledButton("طباعة", new Point(244, 65), Color.FromArgb(232, 62, 140));

        pnlButtons.Controls.AddRange(new Control[]
        {
            btnAddCustomer, btnEditCustomer, btnDeleteCustomer, btnReplaceCustomer, btnRefresh, btnUploadFiles, btnViewFiles, btnPrintCustomers
        });

        buttonsPanel = pnlButtons;

        // Event handlers
        btnAddCustomer.Click += BtnAddCustomer_Click;
        btnEditCustomer.Click += BtnEditCustomer_Click;
        btnDeleteCustomer.Click += BtnDeleteCustomer_Click;
        btnReplaceCustomer.Click += BtnReplaceCustomer_Click;
        btnRefresh.Click += (s, e) => LoadData();
        btnPrintCustomers.Click += BtnPrintCustomers_Click;

        btnUploadFiles.Click += BtnUploadFiles_Click;
        btnViewFiles.Click += BtnViewFiles_Click;

        // Create and style DataGridView - محسن للشاشة الكاملة
        dgvCustomers = new DataGridView
        {
            Dock = DockStyle.Fill,
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.Fixed3D,
            CellBorderStyle = DataGridViewCellBorderStyle.Single,
            ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single,
            RowHeadersVisible = false,
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            AllowUserToResizeRows = false,
            ReadOnly = true,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None,
            Font = new Font("Segoe UI", 11F),
            GridColor = Color.FromArgb(230, 230, 230),
            ColumnHeadersHeight = 50,
            RowTemplate = { Height = 45 },
            EnableHeadersVisualStyles = false
        };

        // DataGridView styling - نفس تنسيق InventoryForm
        dgvCustomers.ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
        {
            BackColor = Color.FromArgb(0, 102, 204),
            ForeColor = Color.White,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            Alignment = DataGridViewContentAlignment.MiddleCenter
        };

        dgvCustomers.DefaultCellStyle = new DataGridViewCellStyle
        {
            BackColor = Color.White,
            ForeColor = Color.Black,
            SelectionBackColor = Color.FromArgb(173, 216, 230),
            SelectionForeColor = Color.Black,
            Alignment = DataGridViewContentAlignment.MiddleCenter
        };

        dgvCustomers.ColumnHeadersHeight = 40;
        dgvCustomers.RowTemplate.Height = 35;

        // Add panels to main container in correct order
        mainContainerPanel.Controls.Add(dgvCustomers);
        mainContainerPanel.Controls.Add(buttonsPanel);
        mainContainerPanel.Controls.Add(searchPanel);
        mainContainerPanel.Controls.Add(headerPanel);

        // Add main container to form
        Controls.Add(mainContainerPanel);
    }

    private Button CreateStyledButton(string text, Point location, Color backColor, Color? foreColor = null)
    {
        // Calculate button width based on text length
        int baseWidth = 80;
        int textLength = text.Length;
        int calculatedWidth = Math.Max(baseWidth, textLength * 8 + 20);

        var button = new Button
        {
            Text = text,
            Location = location,
            Size = new Size(calculatedWidth, 35),
            BackColor = backColor,
            ForeColor = foreColor ?? Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            Cursor = Cursors.Hand,
            TextAlign = ContentAlignment.MiddleCenter,
            UseVisualStyleBackColor = false,
            FlatAppearance = { BorderSize = 0 }
        };
        return button;
    }

    private async void LoadData()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var query = context.Customers.AsQueryable();

            if(!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                var searchTerm = txtSearch.Text.ToLower();
                query = query.Where(c => c.FullName.ToLower().Contains(searchTerm) ||
                                    c.IdNumber.ToLower().Contains(searchTerm) ||
                                    c.PrimaryPhone.ToLower().Contains(searchTerm));
            }

            var customers = await query.ToListAsync();

            dgvCustomers.DataSource = customers.Select(c => new
            {
                رقم_العميل = c.CustomerId,
                الاسم_الكامل = c.FullName,
                رقم_الهوية = c.IdNumber,
                العنوان = c.Address,
                الهاتف_الأساسي = c.PrimaryPhone,
                الهاتف_الثانوي = c.SecondaryPhone ?? "غير محدد",
                البريد_الإلكتروني = c.Email ?? "غير محدد",
                تاريخ_الإضافة = c.CreatedDate.ToString("yyyy-MM-dd")
            }).ToList();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void BtnSearch_Click(object? sender, EventArgs e)
    {
        LoadData();
    }

    private void BtnAddCustomer_Click(object? sender, EventArgs e)
    {
        var addForm = new AddEditCustomerForm();
        if(addForm.ShowDialog() == DialogResult.OK)
        {
            LoadData();
        }
    }

    private void BtnEditCustomer_Click(object? sender, EventArgs e)
    {
        if(dgvCustomers.SelectedRows.Count > 0)
        {
            var customerId = Convert.ToInt32(dgvCustomers.SelectedRows[0].Cells["رقم_العميل"].Value);
            var editForm = new AddEditCustomerForm(customerId);
            if(editForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private async void BtnDeleteCustomer_Click(object? sender, EventArgs e)
    {
        if(dgvCustomers.SelectedRows.Count > 0)
        {
            var customerId = Convert.ToInt32(dgvCustomers.SelectedRows[0].Cells["رقم_العميل"].Value);
            var customerName = dgvCustomers.SelectedRows[0].Cells["الاسم_الكامل"].Value?.ToString();

            try
            {
                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                var customer = await context.Customers.FindAsync(customerId);
                if(customer != null)
                {
                    // Check if customer has sales
                    var hasSales = await context.Sales.AnyAsync(s => s.CustomerId == customerId);
                    if(hasSales)
                    {
                        // Check if customer has installment payments (تقسيط)
                        var hasInstallments = await context.InstallmentPayments.AnyAsync(ip =>
                            context.Sales.Any(s => s.SaleId == ip.SaleId && s.CustomerId == customerId));

                        if(hasInstallments)
                        {
                            // Only Manager can delete customers with installments
                            if(currentUser?.Role != UserRole.Manager)
                            {
                                MessageBox.Show("لا يمكن حذف عميل له أقساط مسجلة إلا بصلاحيات المدير فقط.\n\n" +
                                    "يمكنك استخدام خاصية 'استبدال العميل' لنقل الأقساط إلى عميل آخر.",
                                    "صلاحيات غير كافية", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                return;
                            }

                            var installmentResult = MessageBox.Show(
                                $"تحذير: العميل '{customerName}' له أقساط مسجلة!\n\n" +
                                "هل تريد حذف العميل مع جميع الأقساط والمبيعات المرتبطة به؟\n" +
                                "تحذير: هذا الإجراء لا يمكن التراجع عنه!",
                                "تأكيد حذف عميل بأقساط",
                                MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                            if(installmentResult != DialogResult.Yes)
                                return;
                        }
                        else
                        {
                            var salesResult = MessageBox.Show(
                                $"العميل '{customerName}' له مبيعات مسجلة.\n\n" +
                                "هل تريد حذف العميل مع جميع المبيعات المرتبطة به؟",
                                "تأكيد حذف عميل بمبيعات",
                                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                            if(salesResult != DialogResult.Yes)
                                return;
                        }

                        // Delete related installment payments first
                        var relatedSales = await context.Sales.Where(s => s.CustomerId == customerId).ToListAsync();
                        foreach(var sale in relatedSales)
                        {
                            var installments = await context.InstallmentPayments.Where(ip => ip.SaleId == sale.SaleId).ToListAsync();
                            context.InstallmentPayments.RemoveRange(installments);
                        }

                        // Delete sales
                        context.Sales.RemoveRange(relatedSales);
                    }
                    else
                    {
                        var result = MessageBox.Show($"هل أنت متأكد من حذف العميل '{customerName}'؟",
                            "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                        if(result != DialogResult.Yes)
                            return;
                    }

                    context.Customers.Remove(customer);
                    await context.SaveChangesAsync();
                    MessageBox.Show("تم حذف العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadData();
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnPrintCustomers_Click(object? sender, EventArgs e)
    {
        if(dgvCustomers.Rows.Count == 0)
        {
            MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // استخدام نظام الطباعة بالتحديد
        using var selectiveForm = new SelectivePrintForm(SelectivePrintForm.PrintType.Customers);
        if(selectiveForm.ShowDialog() == DialogResult.OK)
        {
            DataGridView dgvToPrint = CreateFilteredDataGridView(selectiveForm);

            if(dgvToPrint.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات تطابق المعايير المحددة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using var printForm = new PrintReportForm(dgvToPrint, "Customer Report");
            printForm.Text = selectiveForm.PrintAll ? "طباعة جميع العملاء" : "طباعة عميل محدد";
            printForm.ShowDialog();
        }
    }

    private DataGridView CreateFilteredDataGridView(SelectivePrintForm selectiveForm)
    {
        var filteredDgv = new DataGridView();

        // نسخ هيكل الأعمدة
        foreach(DataGridViewColumn col in dgvCustomers.Columns)
        {
            filteredDgv.Columns.Add((DataGridViewColumn)col.Clone());
        }

        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);
            var query = context.Customers.AsQueryable();

            // تطبيق فلتر العميل المحدد
            if(!selectiveForm.PrintAll && selectiveForm.SelectedCustomerId.HasValue)
            {
                query = query.Where(c => c.CustomerId == selectiveForm.SelectedCustomerId.Value);
            }

            var customers = query.ToList();

            // إضافة البيانات المفلترة
            foreach(var customer in customers)
            {
                var row = new DataGridViewRow();
                row.CreateCells(filteredDgv);

                row.Cells[0].Value = customer.CustomerId;
                row.Cells[1].Value = customer.FullName;
                row.Cells[2].Value = customer.IdNumber;
                row.Cells[3].Value = customer.DateOfBirth.ToString("yyyy-MM-dd");
                row.Cells[4].Value = customer.Country;
                row.Cells[5].Value = customer.City;
                row.Cells[6].Value = customer.Area;
                row.Cells[7].Value = customer.PrimaryPhone;
                row.Cells[8].Value = customer.SecondaryPhone ?? "غير محدد";
                row.Cells[9].Value = customer.Email ?? "غير محدد";
                row.Cells[10].Value = customer.CreatedDate.ToString("yyyy-MM-dd");

                filteredDgv.Rows.Add(row);
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        return filteredDgv;
    }
    private void BtnUploadFiles_Click(object? sender, EventArgs e)
    {
        if(dgvCustomers.SelectedRows.Count > 0)
        {
            var customerId = Convert.ToInt32(dgvCustomers.SelectedRows[0].Cells["رقم_العميل"].Value);
            using var openFileDialog = new OpenFileDialog
            {
                Filter = "All Files (*.*)|*.*|Images (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|Documents (*.pdf;*.doc;*.docx)|*.pdf;*.doc;*.docx",
                Multiselect = true,
                Title = "اختيار الملفات للرفع"
            };

            if(openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // Create customer files directory if it doesn't exist
                    var customerFilesDir = Path.Combine("CustomerFiles", customerId.ToString());
                    Directory.CreateDirectory(customerFilesDir);

                    foreach(var file in openFileDialog.FileNames)
                    {
                        var fileName = Path.GetFileName(file);
                        var destinationPath = Path.Combine(customerFilesDir, fileName);

                        // Copy file to customer directory
                        File.Copy(file, destinationPath, true);
                    }

                    MessageBox.Show($"تم رفع {openFileDialog.FileNames.Length} ملف بنجاح", "نجح",
                                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch(Exception ex)
                {
                    MessageBox.Show($"خطأ في رفع الملفات: {ex.Message}", "خطأ",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار عميل أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnViewFiles_Click(object? sender, EventArgs e)
    {
        if(dgvCustomers.SelectedRows.Count > 0)
        {
            var customerId = Convert.ToInt32(dgvCustomers.SelectedRows[0].Cells["رقم_العميل"].Value);
            var customerFilesDir = Path.Combine("CustomerFiles", customerId.ToString());

            if(Directory.Exists(customerFilesDir))
            {
                try
                {
                    // Open the directory in Windows Explorer
                    Process.Start("explorer.exe", customerFilesDir);
                }
                catch(Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح مجلد الملفات: {ex.Message}", "خطأ",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("لا توجد ملفات مرفوعة لهذا العميل", "تنبيه",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار عميل أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private async void BtnReplaceCustomer_Click(object? sender, EventArgs e)
    {
        if(dgvCustomers.SelectedRows.Count > 0)
        {
            var oldCustomerId = Convert.ToInt32(dgvCustomers.SelectedRows[0].Cells["رقم_العميل"].Value);
            var oldCustomerName = dgvCustomers.SelectedRows[0].Cells["الاسم_الكامل"].Value?.ToString();

            try
            {
                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                // Check if old customer has installments
                var hasInstallments = await context.InstallmentPayments.AnyAsync(ip =>
                    context.Sales.Any(s => s.SaleId == ip.SaleId && s.CustomerId == oldCustomerId));

                if(!hasInstallments)
                {
                    MessageBox.Show("العميل المحدد ليس له أقساط مسجلة.\nخاصية الاستبدال مخصصة للعملاء الذين لديهم أقساط فقط.",
                        "لا توجد أقساط", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Get chassis number from the sale
                var sale = await context.Sales.Include(s => s.Car)
                    .FirstOrDefaultAsync(s => s.CustomerId == oldCustomerId);

                if(sale?.Car?.ChassisNumber == null)
                {
                    MessageBox.Show("لم يتم العثور على رقم الشاسيه للسيارة المرتبطة بهذا العميل.",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var chassisNumber = sale.Car.ChassisNumber;

                // Show replacement form
                var replaceForm = new CustomerReplacementForm(oldCustomerId, oldCustomerName, chassisNumber);
                if(replaceForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                    MessageBox.Show("تم استبدال العميل بنجاح وتم نقل جميع الأقساط والمبيعات.",
                        "نجح الاستبدال", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch(Exception ex)
            {
                MessageBox.Show($"خطأ في استبدال العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار عميل للاستبدال", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

}
}
