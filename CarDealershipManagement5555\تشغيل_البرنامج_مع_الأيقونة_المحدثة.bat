@echo off
chcp 65001 >nul
title برنامج إدارة معرض السيارات - الأيقونة المحدثة

echo.
echo ========================================
echo   🚗 برنامج إدارة معرض السيارات
echo ========================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo 🔢 الإصدار: 1.0.0 - ديسمبر 2024
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo 🎨 تم تحديث الأيقونات بنجاح!
echo    ✅ تم استبدال app11_icon بـ app-icon
echo    ✅ أيقونة احترافية موحدة في جميع المجلدات
echo    ✅ تصميم مخصص يحمل هوية البرنامج
echo.

set "DEBUG_DIR=%~dp0bin\Debug\net8.0-windows"
set "RELEASE_DIR=%~dp0bin\Release\net8.0-windows"
set "STANDALONE_DIR=%~dp0CarDealership_Standalone"
set "DEBUG_COPY_DIR=%~dp0CarDealership_Debug_Copy"

echo 🔍 البحث عن البرنامج...
echo.

if exist "%DEBUG_COPY_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج في النسخة Debug المحدثة
    echo 📁 المكان: %DEBUG_COPY_DIR%
    echo.
    echo 🚀 تشغيل البرنامج من النسخة المحدثة...
    cd /d "%DEBUG_COPY_DIR%"
    set "PROGRAM_PATH=%DEBUG_COPY_DIR%"
    
) else if exist "%STANDALONE_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج في المجلد المستقل
    echo 📁 المكان: %STANDALONE_DIR%
    echo.
    echo 🚀 تشغيل البرنامج من المجلد المستقل...
    cd /d "%STANDALONE_DIR%"
    set "PROGRAM_PATH=%STANDALONE_DIR%"
    
) else if exist "%DEBUG_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج في مجلد Debug
    echo 📁 المكان: %DEBUG_DIR%
    echo.
    echo 🚀 تشغيل البرنامج من مجلد Debug...
    cd /d "%DEBUG_DIR%"
    set "PROGRAM_PATH=%DEBUG_DIR%"
    
) else if exist "%RELEASE_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج في مجلد Release
    echo 📁 المكان: %RELEASE_DIR%
    echo.
    echo 🚀 تشغيل البرنامج من مجلد Release...
    cd /d "%RELEASE_DIR%"
    set "PROGRAM_PATH=%RELEASE_DIR%"
    
) else (
    echo ❌ لم يتم العثور على البرنامج
    echo.
    echo 🔧 يرجى بناء البرنامج أولاً باستخدام:
    echo    dotnet build --configuration Debug
    echo    أو dotnet build --configuration Release
    echo.
    goto :end
)

REM عرض معلومات الملف
for %%A in ("CarDealershipManagement.exe") do (
    set "FILE_SIZE=%%~zA"
    set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
    echo 📊 معلومات الملف:
    echo    📏 الحجم: !FILE_SIZE_MB! ميجابايت
    echo    📅 تاريخ التعديل: %%~tA
)

echo.
echo 🔑 بيانات الدخول المحدثة:
echo.
echo 🔧 حساب المطور (جميع الصلاحيات):
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo    الصلاحيات: جميع الصلاحيات (64+ صلاحية)
echo    الوصول: كامل لجميع أجزاء النظام
echo.
echo 👔 حساب المدير (صلاحيات إدارية):
echo    اسم المستخدم: admin
echo    كلمة المرور: 123
echo    الصلاحيات: إدارية (بدون إعدادات النظام)
echo    الوصول: إدارة المخزون والمبيعات والتقارير
echo.
echo 🤝 حساب مندوب المبيعات (صلاحيات أساسية):
echo    اسم المستخدم: user
echo    كلمة المرور: pass
echo    الصلاحيات: أساسية (مبيعات وعملاء)
echo    الوصول: المبيعات وإدارة العملاء فقط
echo.

echo 🆕 نظام التفعيل المتقدم:
echo    ✅ نسخة تجريبية مجانية (30 يوم)
echo    ✅ تراخيص شهرية وسنوية ومدى الحياة
echo    ✅ حماية بمعرف الجهاز
echo    ✅ تشفير ملفات الترخيص
echo.

echo 🎯 الميزات الرئيسية:
echo    📦 إدارة المخزون المتقدمة
echo    💰 نظام المبيعات الذكي
echo    👥 إدارة العملاء والموردين
echo    📊 التقارير والإحصائيات
echo    👤 إدارة المستخدمين (64+ صلاحية)
echo    🔒 الأمان والحماية
echo.

echo 🎨 الأيقونة الجديدة:
echo    ✅ تصميم احترافي مخصص
echo    ✅ يحمل هوية البرنامج
echo    ✅ معلومات المطور واضحة
echo    ✅ متوافق مع جميع أنظمة Windows
echo.

echo 💡 نصائح مهمة:
echo    1. استخدم حساب المطور (amrali/braa) للوصول لجميع الميزات
echo    2. جرب النسخة التجريبية أولاً لمدة 30 يوم
echo    3. قم بإنشاء نسخة احتياطية من البيانات بانتظام
echo    4. راجع التقارير دورياً لمتابعة الأداء
echo.

echo 🚀 جاري تشغيل البرنامج...
echo.

start "" "CarDealershipManagement.exe"

echo ✅ تم تشغيل البرنامج بنجاح!
echo.

echo 🔍 إذا لم تجد البرنامج:
echo    • ابحث في شريط المهام السفلي
echo    • استخدم Alt+Tab للتنقل بين النوافذ
echo    • تحقق من أن البرنامج لم يفتح خلف النوافذ الأخرى
echo.

echo 🎊 البرنامج الآن يعمل مع:
echo    ✅ أيقونة احترافية محدثة
echo    ✅ حقوق المطور في جميع الملفات
echo    ✅ نظام تفعيل متقدم
echo    ✅ بيانات دخول محدثة للمطور
echo    ✅ جميع الميزات المطورة
echo.

echo 🔑 للدخول كمطور استخدم:
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo.

echo 📦 للتوزيع:
echo    📁 النسخة المحدثة: %DEBUG_COPY_DIR%
echo    📁 المجلد المستقل: %STANDALONE_DIR%
echo    💡 يمكنك نسخ أي من هذين المجلدين للتوزيع
echo.

:end
cd /d "%~dp0"

echo.
echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo اضغط أي مفتاح للخروج...
pause >nul
