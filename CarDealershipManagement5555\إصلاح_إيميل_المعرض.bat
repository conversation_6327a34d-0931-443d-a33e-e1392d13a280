@echo off
title إصلاح إيميل المعرض

echo.
echo ========================================
echo    🔧 إصلاح إيميل المعرض
echo ========================================
echo.

echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.

echo المشكلة: حقل إيميل المعرض غير ظاهر في الإعدادات
echo الحل: تحديث البرنامج المبني بالكود الجديد
echo.

echo 1. إيقاف أي نسخة مشغلة...
taskkill /f /im CarDealershipManagement.exe >nul 2>&1

echo 2. محاولة بناء البرنامج بالتحديثات...
dotnet build CarDealershipManagement.csproj --configuration Debug --verbosity quiet --no-restore

if %errorlevel% equ 0 (
    echo ✅ تم بناء البرنامج بنجاح
    echo 3. تشغيل النسخة المحدثة...
    start "" "bin\Debug\net8.0-windows\CarDealershipManagement.exe"
) else (
    echo ⚠️ فشل في البناء - سنستخدم النسخة الموجودة مع تحديث قاعدة البيانات
    
    echo 3. تحديث قاعدة البيانات...
    cd CarDealership_Final
    
    echo -- تحديث بيانات الشركة > update_company.sql
    echo UPDATE SystemSettings SET CompanyEmail = '<EMAIL>' WHERE CompanyEmail IS NULL OR CompanyEmail = ''; >> update_company.sql
    echo UPDATE SystemSettings SET CompanyWebsite = 'www.carshowroom.com' WHERE CompanyWebsite IS NULL OR CompanyWebsite = ''; >> update_company.sql
    echo INSERT OR IGNORE INTO SystemSettings (CompanyName, CompanyAddress, CompanyPhone, CompanyEmail, CompanyWebsite, Currency) VALUES ('معرض السيارات المتميز', 'القاهرة، مصر', '01234567890', '<EMAIL>', 'www.carshowroom.com', 'EGP'); >> update_company.sql
    
    sqlite3 CarDealership.db < update_company.sql >nul 2>&1
    del update_company.sql >nul 2>&1
    
    cd ..
    
    echo 4. تشغيل النسخة النهائية...
    start "" "CarDealership_Final\CarDealershipManagement.exe"
)

echo.
echo ✅ تم تطبيق الإصلاح
echo.

echo 📝 ملاحظة مهمة:
echo إذا لم تجد حقل الإيميل في الإعدادات، فهذا يعني أن البرنامج
echo المشغل لا يحتوي على الكود المحدث. في هذه الحالة:
echo.
echo 1. انتقل إلى الإدارة → الإعدادات
echo 2. في تبويب "إعدادات النظام" ستجد معلومات الشركة
echo 3. أو يمكنك تحديث البيانات مباشرة في قاعدة البيانات
echo.

echo 🔐 بيانات الدخول:
echo اسم المستخدم: amrali
echo كلمة المرور: braa
echo.

echo للدعم: 01285626623 - <EMAIL>
echo.

pause
