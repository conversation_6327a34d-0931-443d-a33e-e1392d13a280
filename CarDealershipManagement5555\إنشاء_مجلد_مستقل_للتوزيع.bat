@echo off
chcp 65001 >nul
title إنشاء مجلد مستقل للتوزيع - برنامج إدارة معرض السيارات

echo.
echo ========================================
echo   📦 إنشاء مجلد مستقل للتوزيع
echo ========================================
echo.

echo 🎯 سيتم إنشاء مجلد مستقل يحتوي على:
echo    • الملف التنفيذي المستقل
echo    • جميع الملفات المطلوبة
echo    • دليل المستخدم
echo    • ملفات التفعيل
echo    • أمثلة وقوالب
echo.

set "SOURCE_DIR=%~dp0"
set "DIST_DIR=%SOURCE_DIR%CarDealership_Distribution"
set "APP_DIR=%DIST_DIR%\CarDealershipManagement"

echo 🧹 تنظيف المجلد السابق...
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"

echo 📁 إنشاء هيكل المجلدات...
mkdir "%DIST_DIR%"
mkdir "%APP_DIR%"
mkdir "%APP_DIR%\Data"
mkdir "%APP_DIR%\Backups"
mkdir "%APP_DIR%\Reports"
mkdir "%APP_DIR%\Templates"
mkdir "%APP_DIR%\Documentation"
mkdir "%APP_DIR%\CarFiles"
mkdir "%APP_DIR%\SupplierFiles"

echo.
echo 🔨 بناء البرنامج كملف مستقل...
echo.

REM بناء البرنامج كملف مستقل
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "%APP_DIR%" /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true /p:PublishReadyToRun=true /p:EnableCompressionInSingleFile=true

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء البرنامج
    pause
    exit /b 1
)

echo ✅ تم بناء البرنامج بنجاح

echo.
echo 📋 نسخ الملفات الإضافية...

REM نسخ قاعدة البيانات الأساسية
if exist "%SOURCE_DIR%CarDealership.db" (
    copy "%SOURCE_DIR%CarDealership.db" "%APP_DIR%\Data\" >nul
    echo ✅ تم نسخ قاعدة البيانات
)

REM إنشاء ملف README للمجلد
(
echo برنامج إدارة معرض السيارات - الإصدار 1.0.0
echo ===============================================
echo.
echo 🚗 نظام شامل لإدارة معارض السيارات
echo.
echo 📋 محتويات المجلد:
echo    • CarDealershipManagement.exe - الملف التنفيذي الرئيسي
echo    • Data\ - مجلد قواعد البيانات
echo    • Backups\ - مجلد النسخ الاحتياطية
echo    • Reports\ - مجلد التقارير المحفوظة
echo    • Templates\ - قوالب التقارير والفواتير
echo    • Documentation\ - أدلة المستخدم والتوثيق
echo    • CarFiles\ - ملفات ومرفقات السيارات
echo    • SupplierFiles\ - ملفات الموردين
echo.
echo 🚀 طريقة التشغيل:
echo    1. اضغط مرتين على CarDealershipManagement.exe
echo    2. اختر "نسخة تجريبية" للبدء فوراً ^(30 يوم^)
echo    3. أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo.
echo 🔑 بيانات الدخول الافتراضية:
echo    المطور: developer / dev123
echo    المدير: admin / 123
echo    المندوب: user / pass
echo.
echo 💡 نصائح مهمة:
echo    • قم بإنشاء نسخة احتياطية من مجلد Data بانتظام
echo    • لا تحذف أي ملفات من المجلد
echo    • استخدم كلمات مرور قوية
echo.
echo 📞 للدعم الفني: <EMAIL>
echo 🌐 الموقع: www.cardealership.com
echo.
echo تاريخ الإنشاء: %date% %time%
) > "%APP_DIR%\README.txt"

REM إنشاء ملف تشغيل سريع
(
echo @echo off
echo chcp 65001 ^>nul
echo title برنامج إدارة معرض السيارات
echo.
echo echo ========================================
echo echo    🚗 برنامج إدارة معرض السيارات
echo echo ========================================
echo echo.
echo echo 🚀 جاري تشغيل البرنامج...
echo echo.
echo echo 🔑 بيانات الدخول:
echo echo    المطور: developer / dev123
echo echo    المدير: admin / 123  
echo echo    المندوب: user / pass
echo echo.
echo echo 💡 يمكنك أيضاً اختيار "نسخة تجريبية" للبدء فوراً
echo echo.
echo start "" "CarDealershipManagement.exe"
echo echo ✅ تم تشغيل البرنامج!
echo echo.
echo echo اضغط أي مفتاح للخروج...
echo pause ^>nul
) > "%APP_DIR%\تشغيل_البرنامج.bat"

REM إنشاء دليل المستخدم السريع
(
echo دليل المستخدم السريع - برنامج إدارة معرض السيارات
echo ========================================================
echo.
echo 🎯 البدء السريع:
echo ================
echo.
echo 1. التشغيل لأول مرة:
echo    • اضغط مرتين على CarDealershipManagement.exe
echo    • اختر "نسخة تجريبية ^(30 يوم^)" للبدء فوراً
echo    • أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo.
echo 2. تسجيل الدخول:
echo    • المطور ^(جميع الصلاحيات^): developer / dev123
echo    • المدير ^(صلاحيات إدارية^): admin / 123
echo    • المندوب ^(صلاحيات أساسية^): user / pass
echo.
echo 🔧 الميزات الرئيسية:
echo ====================
echo.
echo 📦 إدارة المخزون:
echo    • إضافة وتعديل وحذف السيارات
echo    • نظام ضمان سلامة البيانات المالية
echo    • إدارة ملفات ومرفقات السيارات
echo    • تصنيف وفلترة السيارات
echo.
echo 💰 نظام المبيعات:
echo    • بيع نقدي وبالتقسيط
echo    • إدارة الأقساط والمدفوعات
echo    • طباعة الفواتير والعقود
echo    • تتبع حالة المدفوعات
echo.
echo 👥 إدارة العملاء:
echo    • قاعدة بيانات شاملة للعملاء
echo    • تاريخ المشتريات والمدفوعات
echo    • كشوف حساب مفصلة
echo    • إدارة ملفات العملاء
echo.
echo 📊 التقارير والإحصائيات:
echo    • تقارير المبيعات والأرباح
echo    • تقارير الأقساط المحسنة
echo    • إحصائيات الأداء
echo    • طباعة وتصدير بـ 5 صيغ
echo.
echo 👤 إدارة المستخدمين:
echo    • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo    • أدوار متعددة ^(مطور، مدير، مندوب^)
echo    • إدارة كلمات المرور
echo    • تتبع نشاط المستخدمين
echo.
echo 🔒 الأمان والحماية:
echo    • نسخ احتياطي تلقائي
echo    • تشفير البيانات الحساسة
echo    • نظام تفعيل متقدم
echo    • حماية من فقدان البيانات
echo.
echo 💡 نصائح مهمة:
echo ================
echo.
echo • قم بإنشاء نسخة احتياطية من مجلد Data بانتظام
echo • استخدم كلمات مرور قوية للمستخدمين
echo • راجع التقارير دورياً لمتابعة الأداء
echo • احتفظ بنسخة من ملفات السيارات والعملاء
echo • تأكد من تحديث البرنامج عند توفر إصدارات جديدة
echo.
echo 🆘 حل المشاكل الشائعة:
echo =========================
echo.
echo • إذا لم يعمل البرنامج: تأكد من Windows 10+ وتشغيل كمسؤول
echo • إذا فقدت كلمة المرور: استخدم حساب المطور لإعادة التعيين
echo • إذا تعطلت قاعدة البيانات: استخدم النسخة الاحتياطية
echo • إذا لم تظهر التقارير: تحقق من صلاحيات المستخدم
echo.
echo 📞 الدعم الفني:
echo ================
echo.
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📞 الهاتف: +20-XXX-XXX-XXXX
echo 🌐 الموقع: www.cardealership.com
echo ⏰ ساعات العمل: الأحد - الخميس، 9 ص - 5 م
echo.
echo شكراً لاختيارك برنامج إدارة معرض السيارات! 🚗
) > "%APP_DIR%\Documentation\دليل_المستخدم_السريع.txt"

REM إنشاء ملف معلومات التفعيل
(
echo معلومات التفعيل والترخيص
echo ===========================
echo.
echo 🔑 أنواع التراخيص المتاحة:
echo.
echo 🆓 النسخة التجريبية ^(30 يوم^):
echo    • مجانية تماماً
echo    • جميع الميزات متاحة
echo    • تفعيل فوري بدون مفتاح
echo    • مناسبة للتقييم والاختبار
echo.
echo 💳 التراخيص المدفوعة:
echo.
echo 📅 الترخيص الشهري:
echo    • صالح لمدة شهر واحد
echo    • مفتاح التفعيل: MONTH-XXXXXX-XXXXXX
echo    • مستخدم واحد
echo.
echo 📅 الترخيص ربع السنوي:
echo    • صالح لمدة 3 أشهر
echo    • مفتاح التفعيل: QUARTER-XXXXXX-XXXXXX
echo    • خصم 10%% مقارنة بالشهري
echo.
echo 📅 الترخيص السنوي:
echo    • صالح لمدة سنة كاملة
echo    • مفتاح التفعيل: YEAR-XXXXXX-XXXXXX
echo    • خصم 25%% مقارنة بالشهري
echo.
echo 🏆 الترخيص مدى الحياة:
echo    • صالح مدى الحياة
echo    • مفتاح التفعيل: LIFE-XXXXXX-XXXXXX
echo    • حتى 10 مستخدمين
echo    • تحديثات مجانية لمدة سنتين
echo.
echo 🔧 كيفية التفعيل:
echo.
echo 1. شغل البرنامج لأول مرة
echo 2. ستظهر نافذة التفعيل تلقائياً
echo 3. اختر نوع التفعيل:
echo    • للنسخة التجريبية: اضغط "نسخة تجريبية ^(30 يوم^)"
echo    • للترخيص المدفوع: أدخل مفتاح الترخيص
echo 4. أدخل معلوماتك ^(الاسم والبريد الإلكتروني^)
echo 5. اضغط "تفعيل الترخيص"
echo.
echo 🛡️ حماية الترخيص:
echo.
echo • الترخيص مرتبط بمعرف الجهاز الفريد
echo • لا يمكن نسخ الترخيص إلى جهاز آخر
echo • ملفات الترخيص مشفرة بـ AES-256
echo • فحص دوري لصحة الترخيص
echo.
echo 📞 للحصول على ترخيص أو المساعدة:
echo.
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📞 الهاتف: +20-XXX-XXX-XXXX
echo 🌐 الموقع: www.cardealership.com
) > "%APP_DIR%\Documentation\معلومات_التفعيل.txt"

REM إنشاء ملف النسخ الاحتياطي
(
echo @echo off
echo chcp 65001 ^>nul
echo title إنشاء نسخة احتياطية
echo.
echo echo ========================================
echo echo    💾 إنشاء نسخة احتياطية
echo echo ========================================
echo echo.
echo.
echo set "BACKUP_DIR=Backups\Backup_%%date:~-4,4%%%%date:~-10,2%%%%date:~-7,2%%_%%time:~0,2%%%%time:~3,2%%%%time:~6,2%%"
echo set "BACKUP_DIR=%%BACKUP_DIR: =0%%"
echo.
echo echo 📁 إنشاء مجلد النسخة الاحتياطية...
echo mkdir "%%BACKUP_DIR%%"
echo.
echo echo 💾 نسخ قاعدة البيانات...
echo if exist "Data\CarDealership.db" copy "Data\CarDealership.db" "%%BACKUP_DIR%%\" ^>nul
echo if exist "CarDealership.db" copy "CarDealership.db" "%%BACKUP_DIR%%\" ^>nul
echo.
echo echo 📁 نسخ ملفات السيارات...
echo if exist "CarFiles" xcopy "CarFiles" "%%BACKUP_DIR%%\CarFiles\" /E /I /Q ^>nul
echo.
echo echo 📁 نسخ ملفات الموردين...
echo if exist "SupplierFiles" xcopy "SupplierFiles" "%%BACKUP_DIR%%\SupplierFiles\" /E /I /Q ^>nul
echo.
echo echo ✅ تم إنشاء النسخة الاحتياطية بنجاح!
echo echo 📂 المكان: %%BACKUP_DIR%%
echo echo.
echo echo اضغط أي مفتاح للخروج...
echo pause ^>nul
) > "%APP_DIR%\إنشاء_نسخة_احتياطية.bat"

echo ✅ تم إنشاء الملفات الإضافية

echo.
echo 📊 عرض معلومات المجلد المنشأ...
echo.

if exist "%APP_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم إنشاء المجلد المستقل بنجاح!
    echo.
    echo 📁 مكان المجلد: %APP_DIR%
    echo.
    
    REM عرض حجم الملف التنفيذي
    for %%A in ("%APP_DIR%\CarDealershipManagement.exe") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📏 حجم الملف التنفيذي: !FILE_SIZE_MB! ميجابايت
    )
    
    echo.
    echo 📋 محتويات المجلد:
    echo    ✅ CarDealershipManagement.exe - الملف التنفيذي الرئيسي
    echo    ✅ تشغيل_البرنامج.bat - تشغيل سريع
    echo    ✅ إنشاء_نسخة_احتياطية.bat - أداة النسخ الاحتياطي
    echo    ✅ README.txt - دليل سريع
    echo    ✅ Data\ - مجلد قواعد البيانات
    echo    ✅ Documentation\ - أدلة المستخدم
    echo    ✅ Backups\ - مجلد النسخ الاحتياطية
    echo    ✅ Reports\ - مجلد التقارير
    echo    ✅ CarFiles\ - ملفات السيارات
    echo    ✅ SupplierFiles\ - ملفات الموردين
    echo.
    
    echo 🎯 المجلد جاهز للتوزيع!
    echo.
    echo 💡 يمكنك الآن:
    echo    1. نسخ المجلد إلى أي مكان
    echo    2. ضغطه في ملف ZIP للتوزيع
    echo    3. نسخه إلى فلاشة USB
    echo    4. رفعه على الإنترنت للتحميل
    echo.
    
    echo 🚀 لتشغيل البرنامج من المجلد الجديد:
    echo    1. انتقل إلى: %APP_DIR%
    echo    2. اضغط مرتين على "تشغيل_البرنامج.bat"
    echo    3. أو اضغط مرتين على "CarDealershipManagement.exe"
    echo.
    
) else (
    echo ❌ فشل في إنشاء المجلد المستقل
    echo يرجى مراجعة الأخطاء أعلاه
)

echo.
echo 🎉 انتهت العملية بنجاح!
echo.
echo 📂 المجلد الجديد: %DIST_DIR%
echo 🚗 البرنامج: %APP_DIR%\CarDealershipManagement.exe
echo.

echo هل تريد فتح المجلد الجديد؟ (Y/N)
set /p "OPEN_FOLDER="
if /i "%OPEN_FOLDER%"=="Y" (
    start "" "%APP_DIR%"
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
