using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Helpers;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class SalesForm : Form
    {
        private ComboBox cmbCar = null!;
        private ComboBox cmbCustomer = null!;
        private TextBox txtCustomerSearch = null!;
        private Button btnClearCustomerSearch = null!;
        private NumericUpDown numActualSellPrice = null!;
        private ComboBox cmbPaymentMethod = null!;
        private Button btnInstallmentDetails = null!;
        private Label lblInstallmentSummary = null!;
        private Button btnSave = null!;
        private Button btnCancel = null!;
        private Button btnNewCustomer = null!;
        private Button btnPrintSale = null!;
        private Label lblStatus = null!;

        // Installment details from the popup form
        private decimal installmentDownPayment = 0;
        private int installmentCount = 0;
        private decimal installmentAmount = 0;
        private InstallmentPeriod installmentPeriod = InstallmentPeriod.Monthly;
        private DateTime firstInstallmentDate = DateTime.Now.AddMonths(1);
        private bool includeInterest = false;
        private decimal interestRate = 0;
        private List<InstallmentScheduleItem> paymentSchedule = new();
        private User? currentUser;
        private List<Customer> allCustomers = new();
        private List<Car> allCars = new();

        public SalesForm(User? currentUser = null)
        {
            this.currentUser = currentUser;
            InitializeComponent();
            _ = LoadComboBoxData();
        }

        private void InitializeComponent()
        {
            this.Text = "🚗 بيع سيارة - Car Sale";
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Segoe UI", 10F);
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1200, 700);

            // تطبيق إعدادات النافذة المرنة للشاشة الكاملة
            ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this,
                Screen.PrimaryScreen.WorkingArea.Width,
                Screen.PrimaryScreen.WorkingArea.Height);
            this.MinimizeBox = false;

            // تطبيق إعدادات النافذة المرنة
            ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this, 800, 600);

            // Create main container panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(20)
            };

            // Create header panel
            var headerPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                Margin = new Padding(0, 0, 0, 10)
            };

            // Title label
            var titleLabel = new Label
            {
                Text = "بيع سيارة جديدة",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64),
                Location = new Point(20, 15),
                Size = new Size(400, 30)
            };

            headerPanel.Controls.Add(titleLabel);

            // Create form content panel
            var contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Create buttons panel
            var buttonsPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(20, 10, 20, 10)
            };

            // Common styling for labels
            Action<Label> styleLabel = (lbl) =>
            {
                lbl.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                lbl.ForeColor = Color.FromArgb(52, 58, 64);
                lbl.AutoSize = true;
            };

            // Common styling for textboxes and numeric up-downs
            Action<Control> styleInput = (ctrl) =>
            {
                ctrl.Font = new Font("Segoe UI", 9F);
                ctrl.BackColor = Color.White;
                ctrl.ForeColor = Color.Black;
            };

            // Status Label - repositioned to accommodate new layout
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(20, 360),
                Size = new Size(740, 40),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Create and add buttons
            btnSave = CreateStyledButton("حفظ البيع", Color.FromArgb(40, 167, 69));
            btnCancel = CreateStyledButton("إلغاء", Color.FromArgb(108, 117, 125));
            btnPrintSale = CreateStyledButton("🖨️ طباعة", Color.FromArgb(75, 0, 130));

            btnSave.Click += BtnSave_Click;
            btnCancel.Click += (s, e) => this.Close();
            btnPrintSale.Click += BtnPrintSale_Click;

            // Position buttons using Anchor properties for proper responsive positioning
            btnCancel.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnSave.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnPrintSale.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            buttonsPanel.Controls.AddRange(new Control[] { btnSave, btnCancel, btnPrintSale });
            
            // Position buttons after adding to panel
            buttonsPanel.Resize += (s, e) => PositionButtons();
            this.Load += (s, e) => PositionButtons();

            // Car selection dropdown - positioned at the top with proper styling
            var lblCar = new Label { Text = "🚗 اختر السيارة المراد شراؤها:", Location = new Point(20, 20) };
            styleLabel(lblCar);
            lblCar.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblCar.ForeColor = Color.FromArgb(13, 110, 253);
            
            cmbCar = new ComboBox
            {
                Location = new Point(20, 50),
                Size = new Size(900, 40),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                BackColor = Color.FromArgb(240, 248, 255),
                ForeColor = Color.FromArgb(13, 110, 253),
                ItemHeight = 25,
                DropDownHeight = 300
            };
            cmbCar.FlatStyle = FlatStyle.Flat;

            // تحسين عرض القائمة المنسدلة
            cmbCar.DrawMode = DrawMode.OwnerDrawFixed;
            cmbCar.DrawItem += CmbCar_DrawItem;

            // Customer selection section - moved down with proper alignment
            var lblCustomer = new Label { Text = "👤 العميل:", Location = new Point(20, 110) };
            styleLabel(lblCustomer);
            lblCustomer.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            
            // Customer search label
            var lblCustomerSearch = new Label { Text = "بحث:", Location = new Point(20, 140) };
            styleLabel(lblCustomerSearch);
            
            // Customer search textbox
            txtCustomerSearch = new TextBox 
            { 
                Location = new Point(70, 138), 
                Size = new Size(200, 25),
                PlaceholderText = "ابحث عن العميل...",
                Font = new Font("Segoe UI", 9F)
            };
            styleInput(txtCustomerSearch);
            txtCustomerSearch.TextChanged += TxtCustomerSearch_TextChanged;
            
            // Clear search button
            btnClearCustomerSearch = new Button
            {
                Text = "✕",
                Location = new Point(275, 138),
                Size = new Size(25, 25),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 8F, FontStyle.Bold)
            };
            btnClearCustomerSearch.Click += (s, e) => { txtCustomerSearch.Text = ""; FilterCustomers(""); };
            
            // Customer dropdown
            cmbCustomer = new ComboBox 
            { 
                Location = new Point(310, 138), 
                Size = new Size(300, 25), 
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F)
            };
            styleInput(cmbCustomer);
            
            // New customer button
            btnNewCustomer = CreateStyledButton("عميل جديد", Color.FromArgb(40, 167, 69));
            btnNewCustomer.Size = new Size(100, 27);
            btnNewCustomer.Location = new Point(620, 136);
            btnNewCustomer.Click += BtnNewCustomer_Click;

            // Actual sell price - moved down to accommodate better spacing
            var lblActualSellPrice = new Label { Text = "💰 سعر البيع الفعلي:", Location = new Point(20, 180) };
            styleLabel(lblActualSellPrice);
            numActualSellPrice = new NumericUpDown 
            { 
                Location = new Point(150, 178), 
                Size = new Size(150, 25), 
                DecimalPlaces = 2, 
                Maximum = 1000000,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            styleInput(numActualSellPrice);
            numActualSellPrice.ValueChanged += NumActualSellPrice_ValueChanged;

            // Payment method - moved down with better styling
            var lblPaymentMethod = new Label { Text = "💳 طريقة الدفع:", Location = new Point(20, 220) };
            styleLabel(lblPaymentMethod);
            cmbPaymentMethod = new ComboBox 
            { 
                Location = new Point(150, 218), 
                Size = new Size(150, 25), 
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F)
            };
            styleInput(cmbPaymentMethod);
            cmbPaymentMethod.Items.AddRange(new[] { "نقدي", "تقسيط" });
            cmbPaymentMethod.SelectedIndexChanged += CmbPaymentMethod_SelectedIndexChanged;

            // Installment details button (initially hidden) - repositioned
            btnInstallmentDetails = CreateStyledButton("📋 تفاصيل التقسيط", Color.FromArgb(0, 123, 255));
            btnInstallmentDetails.Size = new Size(140, 27);
            btnInstallmentDetails.Location = new Point(320, 217);
            btnInstallmentDetails.Visible = false;
            btnInstallmentDetails.Click += BtnInstallmentDetails_Click;

            // Installment summary label (initially hidden) - repositioned
            lblInstallmentSummary = new Label
            {
                Text = "لم يتم تحديد تفاصيل التقسيط بعد",
                Location = new Point(20, 260),
                Size = new Size(520, 80),
                BackColor = Color.FromArgb(255, 248, 220),
                ForeColor = Color.FromArgb(133, 77, 14),
                Font = new Font("Segoe UI", 9),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(10),
                Visible = false
            };

            // Add controls to content panel
            contentPanel.Controls.AddRange(new Control[]
            {
                lblCar, cmbCar, 
                lblCustomer, lblCustomerSearch, txtCustomerSearch, btnClearCustomerSearch, cmbCustomer, btnNewCustomer,
                lblActualSellPrice, numActualSellPrice, 
                lblPaymentMethod, cmbPaymentMethod, btnInstallmentDetails, 
                lblInstallmentSummary, lblStatus
            });

            // Add panels to main panel
            mainPanel.Controls.AddRange(new Control[] { headerPanel, contentPanel, buttonsPanel });

            // Add main panel to form
            this.Controls.Add(mainPanel);
        }

        private void PositionButtons()
        {
            // Get the buttons panel
            var buttonsPanel = btnSave.Parent as Panel;
            if (buttonsPanel == null) return;

            // Position buttons from right to left with proper spacing
            int rightMargin = 20;
            int buttonSpacing = 10;
            
            btnCancel.Location = new Point(buttonsPanel.Width - rightMargin - btnCancel.Width, 
                                         (buttonsPanel.Height - btnCancel.Height) / 2);
            
            btnSave.Location = new Point(btnCancel.Left - buttonSpacing - btnSave.Width, 
                                       (buttonsPanel.Height - btnSave.Height) / 2);
            
            btnPrintSale.Location = new Point(btnSave.Left - buttonSpacing - btnPrintSale.Width, 
                                            (buttonsPanel.Height - btnPrintSale.Height) / 2);
        }

        private Button CreateStyledButton(string text, Color backgroundColor)
        {
            return new Button
            {
                Text = text,
                Size = new Size(100, 35),
                BackColor = backgroundColor,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
        }

        private async Task LoadComboBoxData()
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                // Load available cars (not sold) with enhanced details
                allCars = await context.Cars.Where(c => !c.IsSold).ToListAsync();
                var carDisplayData = allCars.Select(c => new
                {
                    Text = $"🚗 {c.ChassisNumber} | {c.Brand} {c.Model} ({c.Year}) | {c.Color} | نقدي: {c.SuggestedSellPrice:C} | تقسيط: {c.InstallmentSellPrice:C} | {GetCarConditionIcon(c.Condition)} {c.Condition}",
                    Value = c.ChassisNumber,
                    Car = c
                }).ToList();

                cmbCar.DataSource = carDisplayData;
                cmbCar.DisplayMember = "Text";
                cmbCar.ValueMember = "Value";

                // Load customers with enhanced search capability
                allCustomers = await context.Customers.ToListAsync();
                FilterCustomers(""); // Initialize with all customers

                // Load suggested price when car is selected
                cmbCar.SelectedIndexChanged += (s, e) =>
                {
                    if (cmbCar.SelectedValue != null)
                    {
                        // Update price based on current payment method selection
                        UpdatePriceFieldBasedOnPaymentMethod();
                        
                        // Show car availability status
                        ShowCarAvailabilityStatus();
                    }
                };
            }, "تحميل بيانات القوائم المنسدلة");
        }

        private string GetCarConditionIcon(CarCondition condition)
        {
            return condition switch
            {
                CarCondition.New => "✨",
                CarCondition.Used => "🔧",
                _ => "❓"
            };
        }

        private void CmbCar_DrawItem(object? sender, DrawItemEventArgs e)
        {
            if (e.Index < 0) return;

            var comboBox = sender as ComboBox;
            if (comboBox?.Items[e.Index] == null) return;

            e.DrawBackground();

            // الحصول على النص
            var item = comboBox.Items[e.Index];
            string text = item.ToString() ?? "";

            // تحديد الألوان
            Color textColor = (e.State & DrawItemState.Selected) == DrawItemState.Selected
                ? Color.White
                : Color.FromArgb(13, 110, 253);

            Color backColor = (e.State & DrawItemState.Selected) == DrawItemState.Selected
                ? Color.FromArgb(0, 123, 255)
                : Color.FromArgb(248, 249, 250);

            // رسم الخلفية
            using (var brush = new SolidBrush(backColor))
            {
                e.Graphics.FillRectangle(brush, e.Bounds);
            }

            // رسم النص مع تحسين التنسيق
            using (var brush = new SolidBrush(textColor))
            {
                var font = new Font("Segoe UI", 10F, FontStyle.Regular);
                var rect = new Rectangle(e.Bounds.X + 5, e.Bounds.Y + 2, e.Bounds.Width - 10, e.Bounds.Height - 4);

                e.Graphics.DrawString(text, font, brush, rect, StringFormat.GenericDefault);
            }

            e.DrawFocusRectangle();
        }

        private async Task ShowCarAvailabilityStatus()
        {
            if (cmbCar.SelectedValue != null)
            {
                var selectedCar = allCars.FirstOrDefault(c => c.ChassisNumber == cmbCar.SelectedValue.ToString());
                if (selectedCar != null)
                {
                    lblStatus.Text = $"✅ السيارة متاحة للبيع | آخر تحديث: {selectedCar.CreatedDate:yyyy-MM-dd}";
                    lblStatus.ForeColor = Color.Green;
                }
            }
        }

        private void TxtCustomerSearch_TextChanged(object? sender, EventArgs e)
        {
            string searchTerm = txtCustomerSearch.Text.Trim();
            FilterCustomers(searchTerm);
        }

        private void FilterCustomers(string searchTerm)
        {
            var filteredCustomers = allCustomers.Where(c =>
                string.IsNullOrEmpty(searchTerm) ||
                c.FullName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                c.IdNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                c.PrimaryPhone.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
            ).Select(c => new
            {
                Text = $"👤 {c.FullName} | 📋 {c.IdNumber} | 📞 {c.PrimaryPhone} | 📍 {c.Street}, {c.Area}",
                Value = c.CustomerId,
                Customer = c
            }).ToList();

            cmbCustomer.DataSource = filteredCustomers;
            cmbCustomer.DisplayMember = "Text";
            cmbCustomer.ValueMember = "Value";

            // Show search results count
            if (!string.IsNullOrEmpty(searchTerm))
            {
                lblStatus.Text = $"🔍 تم العثور على {filteredCustomers.Count} عميل";
                lblStatus.ForeColor = Color.Blue;
            }
        }

        private void CmbPaymentMethod_SelectedIndexChanged(object? sender, EventArgs e)
        {
            bool isInstallment = cmbPaymentMethod.SelectedIndex == 1;

            // Show/hide installment controls
            btnInstallmentDetails.Visible = isInstallment;
            lblInstallmentSummary.Visible = isInstallment;

            // Update price field behavior and set appropriate price
            UpdatePriceFieldBasedOnPaymentMethod();

            // If cash payment, reset installment data
            if (!isInstallment)  // Cash payment
            {
                ResetInstallmentData();
            }
            else // Installment payment
            {
                // Update summary with default message
                UpdateInstallmentSummary();
            }
        }

        private void UpdatePriceFieldBasedOnPaymentMethod()
        {
            if (cmbCar.SelectedValue != null)
            {
                try
                {
                    var chassisNumber = cmbCar.SelectedValue.ToString();
                    var selectedCar = allCars.FirstOrDefault(c => c.ChassisNumber == chassisNumber);
                    if (selectedCar != null)
                    {
                        if (cmbPaymentMethod.SelectedIndex == 0)  // Cash payment
                        {
                            numActualSellPrice.Value = selectedCar.SuggestedSellPrice;
                            numActualSellPrice.ReadOnly = true;
                            numActualSellPrice.BackColor = SystemColors.Control;
                        }
                        else if (cmbPaymentMethod.SelectedIndex == 1)  // Installment payment
                        {
                            numActualSellPrice.Value = selectedCar.InstallmentSellPrice;
                            numActualSellPrice.ReadOnly = false;
                            numActualSellPrice.BackColor = SystemColors.Window;
                        }
                    }
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.HandleException(ex, true, "خطأ في تحديث سعر البيع");
                }
            }
            else
            {
                // If no car is selected, just update the field behavior
                if (cmbPaymentMethod.SelectedIndex == 0)  // Cash payment
                {
                    numActualSellPrice.ReadOnly = true;
                    numActualSellPrice.BackColor = SystemColors.Control;
                }
                else // Installment payment
                {
                    numActualSellPrice.ReadOnly = false;
                    numActualSellPrice.BackColor = SystemColors.Window;
                }
            }
        }

        private void ResetInstallmentData()
        {
            installmentDownPayment = 0;
            installmentCount = 0;
            installmentAmount = 0;
            installmentPeriod = InstallmentPeriod.Monthly;
            firstInstallmentDate = DateTime.Now.AddMonths(1);
            includeInterest = false;
            interestRate = 0;
            paymentSchedule.Clear();
        }

        private void UpdateInstallmentSummary()
        {
            if (installmentCount > 0)
            {
                string periodText = installmentPeriod switch
                {
                    InstallmentPeriod.Monthly => "شهري",
                    InstallmentPeriod.Quarterly => "ربع سنوي",
                    InstallmentPeriod.SixMonths => "نصف سنوي",
                    InstallmentPeriod.Yearly => "سنوي",
                    _ => "شهري"
                };

                lblInstallmentSummary.Text = $"تفاصيل التقسيط:\n" +
                                             $"الدفعة المقدمة: {installmentDownPayment:C2}\n" +
                                             $"عدد الأقساط: {installmentCount} ({periodText})\n" +
                                             $"قيمة القسط: {installmentAmount:C2} (بدون فوائد)\n" +
                                             $"تاريخ أول قسط: {firstInstallmentDate:yyyy-MM-dd}";

                lblInstallmentSummary.BackColor = Color.FromArgb(212, 237, 218);
                lblInstallmentSummary.ForeColor = Color.FromArgb(21, 87, 36);
            }
            else
            {
                lblInstallmentSummary.Text = "لم يتم تحديد تفاصيل التقسيط بعد\nاضغط على \"تفاصيل التقسيط\" لإعداد الأقساط";
                lblInstallmentSummary.BackColor = Color.FromArgb(255, 248, 220);
                lblInstallmentSummary.ForeColor = Color.FromArgb(133, 77, 14);
            }
        }

        private void BtnInstallmentDetails_Click(object? sender, EventArgs e)
        {
            if (numActualSellPrice.Value <= 0)
            {
                lblStatus.Text = "يرجى تحديد سعر البيع أولاً.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            var installmentForm = new InstallmentDetailsForm(numActualSellPrice.Value);

            // Set current values if they exist
            if (installmentCount > 0)
            {
                installmentForm.DownPayment = installmentDownPayment;
                installmentForm.NumberOfInstallments = installmentCount;
                installmentForm.InstallmentPeriod = installmentPeriod;
                installmentForm.FirstInstallmentDate = firstInstallmentDate;
            }

            if (installmentForm.ShowDialog() == DialogResult.OK)
            {
                // Get values from the form
                installmentDownPayment = installmentForm.DownPayment;
                installmentCount = installmentForm.NumberOfInstallments;
                installmentAmount = installmentForm.InstallmentAmount;
                installmentPeriod = installmentForm.InstallmentPeriod;
                firstInstallmentDate = installmentForm.FirstInstallmentDate;
                paymentSchedule = installmentForm.PaymentSchedule;

                // Update the summary
                UpdateInstallmentSummary();
            }
        }

        private void NumActualSellPrice_ValueChanged(object? sender, EventArgs e)
        {
            // Reset installment data when price changes
            if (cmbPaymentMethod.SelectedIndex == 1)  // Installment payment
            {
                ResetInstallmentData();
                UpdateInstallmentSummary();
            }
        }

        private async void BtnNewCustomer_Click(object? sender, EventArgs e)
        {
            var addCustomerForm = new AddEditCustomerForm();
            if (addCustomerForm.ShowDialog() == DialogResult.OK)
            {
                await LoadComboBoxData(); // Reload data to include new customer
            }
        }

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages

            if (cmbCar.SelectedValue == null || cmbCustomer.SelectedValue == null || cmbPaymentMethod.SelectedIndex == -1)
            {
                lblStatus.Text = "يرجى إدخال جميع البيانات المطلوبة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            // Additional validation for installment payments
            if (cmbPaymentMethod.SelectedIndex == 1)  // Installment
            {
                if (numActualSellPrice.Value <= 0)
                {
                    lblStatus.Text = "يجب أن يكون سعر البيع أكبر من صفر.";
                    lblStatus.ForeColor = Color.Red;
                    return;
                }

                if (installmentCount <= 0)
                {
                    lblStatus.Text = "يرجى إعداد تفاصيل التقسيط أولاً.";
                    lblStatus.ForeColor = Color.Red;
                    return;
                }

                if (installmentDownPayment > numActualSellPrice.Value)
                {
                    lblStatus.Text = "الدفعة المقدمة لا يمكن أن تكون أكبر من سعر البيع.";
                    lblStatus.ForeColor = Color.Red;
                    return;
                }
            }

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var chassisNumber = cmbCar.SelectedValue.ToString();
                var customerId = Convert.ToInt32(cmbCustomer.SelectedValue);

                // Check if car is still available
                var car = await context.Cars.FindAsync(chassisNumber);
                if (car == null || car.IsSold)
                {
                    lblStatus.Text = "السيارة غير متاحة للبيع.";
                    lblStatus.ForeColor = Color.Red;
                    return;
                }

                var sale = new Sale
                {
                    CarChassisNumber = chassisNumber!,
                    CustomerId = customerId,
                    ActualSellPrice = numActualSellPrice.Value,
                    PaymentMethod = cmbPaymentMethod.SelectedIndex == 0 ? PaymentMethod.Cash : PaymentMethod.Installment,
                    SaleDate = DateTime.Now
                };

                if (sale.PaymentMethod == PaymentMethod.Cash)
                {
                    sale.TotalPaid = sale.ActualSellPrice;
                    sale.RemainingAmount = 0;
                    sale.PaymentStatus = PaymentStatus.FullyPaid;
                }
                else
                {
                    sale.DownPayment = installmentDownPayment;
                    sale.NumberOfInstallments = installmentCount;
                    sale.InstallmentAmount = installmentAmount;
                    sale.InstallmentPeriod = installmentPeriod;
                    sale.FirstInstallmentDate = firstInstallmentDate;
                    sale.TotalPaid = sale.DownPayment;
                    sale.RemainingAmount = sale.ActualSellPrice - sale.DownPayment;
                    sale.PaymentStatus = sale.TotalPaid >= sale.ActualSellPrice ? PaymentStatus.FullyPaid : PaymentStatus.PartiallyPaid;
                }

                context.Sales.Add(sale);

                // Mark car as sold
                car.IsSold = true;

                // Save changes first to get the generated SaleId
                await context.SaveChangesAsync();

                // Create installment schedule if payment is by installment
                if (sale.PaymentMethod == PaymentMethod.Installment && paymentSchedule.Any())
                {
                    foreach (var scheduleItem in paymentSchedule)
                    {
                        var installment = new InstallmentPayment
                        {
                            SaleId = sale.SaleId,
                            InstallmentNumber = scheduleItem.InstallmentNumber,
                            InstallmentAmount = scheduleItem.Amount,
                            DueDate = scheduleItem.DueDate,
                            Status = InstallmentStatus.Pending
                        };

                        context.InstallmentPayments.Add(installment);
                    }

                    // Save the installment payments
                    await context.SaveChangesAsync();
                }

                // Handle cash payment deletion option
                if (sale.PaymentMethod == PaymentMethod.Cash)
                {
                    var deleteResult = MessageBox.Show(
                                           "تم إتمام البيع نقداً بنجاح! هل تريد حذف السيارة والعميل من النظام؟\n" +
                                           "ملاحظة: سيتم حذف السيارة، العميل، وعملية البيع نهائياً من قاعدة البيانات",
                                           "بيع نقدي مكتمل - حذف البيانات",
                                           MessageBoxButtons.YesNo,
                                           MessageBoxIcon.Question);

                    if (deleteResult == DialogResult.Yes)
                    {
                        if (currentUser?.Permissions?.CanDeleteCar != true ||
                                currentUser?.Permissions?.CanDeleteCustomer != true)
                        {
                            MessageBox.Show("ليس لديك الصلاحيات اللازمة لحذف السيارة أو العميل.", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }

                        // Remove sale first due to foreign key constraints
                        context.Sales.Remove(sale);

                        // Get customer for deletion
                        var customerToDelete = await context.Customers.FindAsync(customerId);
                        if (customerToDelete != null)
                        {
                            // Remove customer
                            context.Customers.Remove(customerToDelete);
                        }

                        // Remove car
                        context.Cars.Remove(car);

                        await context.SaveChangesAsync();
                        MessageBox.Show("تم إتمام البيع بنجاح وحذف جميع البيانات (السيارة، العميل، وعملية البيع)", "تم بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("تم إتمام البيع نقداً بنجاح!", "بيع مكتمل", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("تم إعداد عملية بيع بالتقسيط بنجاح!\nيمكنك متابعة الأقساط من قسم \'المبيعات\'", "تم إعداد التقسيط", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }, "حفظ عملية البيع");
        }

        private void BtnPrintSale_Click(object? sender, EventArgs e)
        {
            // Create a temporary DataGridView with sale information for printing
            var dgvSaleInfo = new DataGridView();
            dgvSaleInfo.Columns.Add("Field", "الحقل");
            dgvSaleInfo.Columns.Add("Value", "القيمة");

            // Add sale information rows
            dgvSaleInfo.Rows.Add("السيارة", cmbCar.Text);
            dgvSaleInfo.Rows.Add("العميل", cmbCustomer.Text);
            dgvSaleInfo.Rows.Add("سعر البيع", numActualSellPrice.Value.ToString("C"));
            dgvSaleInfo.Rows.Add("طريقة الدفع", cmbPaymentMethod.Text);

            if (cmbPaymentMethod.SelectedIndex == 1)  // Installment
            {
                dgvSaleInfo.Rows.Add("الدفعة المقدمة", installmentDownPayment.ToString("C"));
                dgvSaleInfo.Rows.Add("عدد الأقساط", installmentCount.ToString());
                dgvSaleInfo.Rows.Add("قيمة القسط", installmentAmount.ToString("C"));
            }

            using var printForm = new PrintReportForm(dgvSaleInfo);
            printForm.Text = "طباعة فاتورة البيع";
            printForm.ShowDialog();
        }
    }
}


