-- Final fix for Customers table - Remove old fields permanently
-- Developer: <PERSON><PERSON> - 01285626623 - <EMAIL>

-- 1. Check if table exists
SELECT name FROM sqlite_master WHERE type='table' AND name='Customers';

-- 2. Show current structure
PRAGMA table_info(Customers);

-- 3. Create new simplified Customers table (without old fields)
DROP TABLE IF EXISTS Customers_Final;
CREATE TABLE Customers_Final (
    CustomerId INTEGER PRIMARY KEY AUTOINCREMENT,
    FullName TEXT NOT NULL,
    IdNumber TEXT NOT NULL UNIQUE,
    Address TEXT NOT NULL,
    PrimaryPhone TEXT NOT NULL,
    SecondaryPhone TEXT,
    Email TEXT,
    IsActive INTEGER DEFAULT 1,
    IsDeleted INTEGER DEFAULT 0,
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT,
    DeletedDate TEXT
);

-- 4. Copy existing data (merge address if old fields exist)
INSERT INTO Customers_Final (
    CustomerId, <PERSON>Name, <PERSON>d<PERSON><PERSON><PERSON>, Address, PrimaryPhone,
    SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate
)
SELECT 
    CustomerId,
    FullName,
    IdNumber,
    CASE 
        -- If Address field already exists, use it
        WHEN EXISTS(SELECT 1 FROM pragma_table_info('Customers') WHERE name='Address') THEN
            COALESCE(Address, 'غير محدد')
        -- If old fields exist, merge them
        ELSE
            CASE 
                WHEN EXISTS(SELECT 1 FROM pragma_table_info('Customers') WHERE name='Country') THEN
                    TRIM(
                        COALESCE(Country, '') || 
                        CASE WHEN Country IS NOT NULL AND Country != '' AND (City IS NOT NULL OR Area IS NOT NULL OR Street IS NOT NULL) THEN ', ' ELSE '' END ||
                        COALESCE(City, '') || 
                        CASE WHEN City IS NOT NULL AND City != '' AND (Area IS NOT NULL OR Street IS NOT NULL) THEN ', ' ELSE '' END ||
                        COALESCE(Area, '') || 
                        CASE WHEN Area IS NOT NULL AND Area != '' AND Street IS NOT NULL AND Street != '' THEN ', ' ELSE '' END ||
                        COALESCE(Street, ''),
                        ', '
                    )
                ELSE 'غير محدد'
            END
    END AS Address,
    PrimaryPhone,
    SecondaryPhone,
    Email,
    IsActive,
    IsDeleted,
    CreatedDate,
    ModifiedDate,
    DeletedDate
FROM Customers;

-- 5. Drop old table and rename new one
DROP TABLE Customers;
ALTER TABLE Customers_Final RENAME TO Customers;

-- 6. Clean up addresses
UPDATE Customers 
SET Address = TRIM(Address)
WHERE Address IS NOT NULL;

UPDATE Customers 
SET Address = 'غير محدد'
WHERE Address IS NULL OR Address = '' OR Address = ', , ,' OR Address = ', ,' OR Address = ',';

-- 7. Create indexes
CREATE INDEX IF NOT EXISTS idx_customers_fullname ON Customers(FullName);
CREATE INDEX IF NOT EXISTS idx_customers_idnumber ON Customers(IdNumber);
CREATE INDEX IF NOT EXISTS idx_customers_active ON Customers(IsActive);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON Customers(PrimaryPhone);

-- 8. Verify final result
PRAGMA table_info(Customers);
SELECT COUNT(*) as 'Customer Count' FROM Customers;
SELECT 'Fix completed successfully - Customers table simplified' as 'Result';
