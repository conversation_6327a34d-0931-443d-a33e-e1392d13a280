using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CarDealershipManagement.Models
{
// Car Images
public class CarImage
{
    [Key]
    public int CarImageId
    {
        get;
        set;
    }

    [Required]
    [MaxLength(50)]
    public string CarChassisNumber
    {
        get;
        set;
    } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string ImagePath
    {
        get;
        set;
    } = string.Empty; // مسار الصورة

    [Required]
    [MaxLength(255)]
    public string OriginalFileName
    {
        get;
        set;
    } = string.Empty; // اسم الملف الأصلي

    [MaxLength(100)]
    public string? Description
    {
        get;    // وصف الصورة
        set;
    }

    public DateTime UploadDate
    {
        get;
        set;
    } = DateTime.Now;

    // Navigation properties
    [ForeignKey("CarChassisNumber")]
    public virtual Car Car
    {
        get;
        set;
    } = null!;
}

// Car Documents
public class CarDocument
{
    [Key]
    public int CarDocumentId
    {
        get;
        set;
    }

    [Required]
    [MaxLength(50)]
    public string CarChassisNumber
    {
        get;
        set;
    } = string.Empty;

    [Required]
    [MaxLength(500)]
    public string DocumentPath
    {
        get;
        set;
    } = string.Empty; // مسار المستند

    [Required]
    [MaxLength(255)]
    public string OriginalFileName
    {
        get;
        set;
    } = string.Empty; // اسم الملف الأصلي

    [Required]
    public DocumentType DocumentType
    {
        get;    // نوع المستند
        set;
    }

    [MaxLength(200)]
    public string? Description
    {
        get;    // وصف المستند
        set;
    }

    public DateTime UploadDate
    {
        get;
        set;
    } = DateTime.Now;

    // Navigation properties
    [ForeignKey("CarChassisNumber")]
    public virtual Car Car
    {
        get;
        set;
    } = null!;
}

// Customer Documents
public class CustomerDocument
{
    [Key]
    public int CustomerDocumentId
    {
        get;
        set;
    }

    [Required]
    public int CustomerId
    {
        get;
        set;
    }

    [Required]
    [MaxLength(500)]
    public string DocumentPath
    {
        get;
        set;
    } = string.Empty; // مسار المستند

    [Required]
    [MaxLength(255)]
    public string OriginalFileName
    {
        get;
        set;
    } = string.Empty; // اسم الملف الأصلي

    [Required]
    public CustomerDocumentType DocumentType
    {
        get;    // نوع المستند
        set;
    }

    [MaxLength(200)]
    public string? Description
    {
        get;    // وصف المستند
        set;
    }

    public DateTime UploadDate
    {
        get;
        set;
    } = DateTime.Now;

    // Navigation properties
    [ForeignKey("CustomerId")]
    public virtual Customer Customer
    {
        get;
        set;
    } = null!;
}

// Supplier Documents
public class SupplierDocument
{
    [Key]
    public int SupplierDocumentId
    {
        get;
        set;
    }

    [Required]
    public int SupplierId
    {
        get;
        set;
    }

    [Required]
    [MaxLength(500)]
    public string DocumentPath
    {
        get;
        set;
    } = string.Empty; // مسار المستند

    [Required]
    [MaxLength(255)]
    public string OriginalFileName
    {
        get;
        set;
    } = string.Empty; // اسم الملف الأصلي

    [Required]
    public SupplierDocumentType DocumentType
    {
        get;    // نوع المستند
        set;
    }

    [MaxLength(200)]
    public string? Description
    {
        get;    // وصف المستند
        set;
    }

    public DateTime UploadDate
    {
        get;
        set;
    } = DateTime.Now;

    // Navigation properties
    [ForeignKey("SupplierId")]
    public virtual Supplier Supplier
    {
        get;
        set;
    } = null!;
}

// Supplier Payments
public class SupplierPayment
{
    [Key]
    public int SupplierPaymentId
    {
        get;
        set;
    }

    [Required]
    public int SupplierId
    {
        get;
        set;
    }

    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal Amount
    {
        get;    // مبلغ الدفعة
        set;
    }

    [Required]
    public DateTime PaymentDate
    {
        get;
        set;
    } = DateTime.Now; // تاريخ الدفعة

    [MaxLength(200)]
    public string? Notes
    {
        get;    // ملاحظات
        set;
    }

    public DateTime CreatedDate
    {
        get;
        set;
    } = DateTime.Now;

    // Navigation properties
    [ForeignKey("SupplierId")]
    public virtual Supplier Supplier
    {
        get;
        set;
    } = null!;
}

public enum DocumentType
{
    PurchaseInvoice = 1,    // فاتورة الشراء
    OriginCertificate = 2,  // شهادة المنشأ
    Warranty = 3,           // الضمان
    Other = 4               // أخرى
}

public enum CustomerDocumentType
{
    IdCard = 1,             // صورة البطاقة الشخصية
    Passport = 2,           // صورة جواز السفر
    AddressProof = 3,       // إثبات العنوان
    Other = 4               // أخرى
}

public enum SupplierDocumentType
{
    CommercialRegistration = 1, // السجل التجاري
    Agreement = 2,              // اتفاقية
    Contract = 3,               // عقد
    Other = 4                   // أخرى
}
}
