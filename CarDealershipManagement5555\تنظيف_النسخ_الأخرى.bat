@echo off
title تنظيف النسخ الأخرى - Clean Other Copies

echo تنظيف النسخ الأخرى...

REM حذف النسخ الأخرى
if exist "CarDealership_Standalone" (
    echo حذف CarDealership_Standalone...
    rmdir /s /q "CarDealership_Standalone"
)

if exist "CarDealership_Working" (
    echo حذف CarDealership_Working...
    rmdir /s /q "CarDealership_Working"
)

if exist "CarDealership_Updated" (
    echo حذف CarDealership_Updated...
    rmdir /s /q "CarDealership_Updated"
)

if exist "CarDealership_Installer" (
    echo حذف CarDealership_Installer...
    rmdir /s /q "CarDealership_Installer"
)

if exist "CarDealership_Debug_Copy" (
    echo حذف CarDealership_Debug_Copy...
    rmdir /s /q "CarDealership_Debug_Copy"
)

echo تم تنظيف النسخ الأخرى
echo النسخة المتبقية: bin\Debug\net8.0-windows

pause
