using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CarDealershipManagement.Services
{
    /// <summary>
    /// خدمة التقارير المحسنة مع تحسينات جمالية وميزات متقدمة
    /// </summary>
    public static class EnhancedReportService
    {
        #region Report Types and Models
        
        public enum ReportType
        {
            SalesReport,
            InventoryReport,
            CustomerReport,
            FinancialReport,
            InstallmentReport,
            SupplierReport,
            PerformanceReport,
            StatisticsReport
        }

        public class ReportInfo
        {
            public string Title { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Icon { get; set; } = "📊";
            public Color ThemeColor { get; set; } = Color.FromArgb(0, 123, 255);
            public ReportType Type { get; set; }
        }

        public class ReportData
        {
            public DataTable Data { get; set; } = new DataTable();
            public string Title { get; set; } = string.Empty;
            public string Subtitle { get; set; } = string.Empty;
            public Dictionary<string, object> Summary { get; set; } = new();
            public Color ThemeColor { get; set; } = Color.FromArgb(0, 123, 255);
            public DateTime GeneratedAt { get; set; } = DateTime.Now;
        }

        #endregion

        #region Report Definitions

        public static readonly Dictionary<ReportType, ReportInfo> ReportDefinitions = new()
        {
            [ReportType.SalesReport] = new()
            {
                Title = "تقرير المبيعات",
                Description = "تقرير شامل عن المبيعات والإيرادات",
                Icon = "💰",
                ThemeColor = Color.FromArgb(40, 167, 69),
                Type = ReportType.SalesReport
            },
            [ReportType.InventoryReport] = new()
            {
                Title = "تقرير المخزون",
                Description = "حالة المخزون والسيارات المتاحة",
                Icon = "📦",
                ThemeColor = Color.FromArgb(0, 123, 255),
                Type = ReportType.InventoryReport
            },
            [ReportType.CustomerReport] = new()
            {
                Title = "تقرير العملاء",
                Description = "معلومات وإحصائيات العملاء",
                Icon = "👥",
                ThemeColor = Color.FromArgb(108, 117, 125),
                Type = ReportType.CustomerReport
            },
            [ReportType.FinancialReport] = new()
            {
                Title = "التقرير المالي",
                Description = "الوضع المالي والحسابات",
                Icon = "💳",
                ThemeColor = Color.FromArgb(220, 53, 69),
                Type = ReportType.FinancialReport
            },
            [ReportType.InstallmentReport] = new()
            {
                Title = "تقرير الأقساط",
                Description = "حالة الأقساط والمدفوعات",
                Icon = "📅",
                ThemeColor = Color.FromArgb(255, 193, 7),
                Type = ReportType.InstallmentReport
            },
            [ReportType.SupplierReport] = new()
            {
                Title = "تقرير الموردين",
                Description = "معلومات الموردين والمدفوعات",
                Icon = "🏭",
                ThemeColor = Color.FromArgb(23, 162, 184),
                Type = ReportType.SupplierReport
            },
            [ReportType.PerformanceReport] = new()
            {
                Title = "تقرير الأداء",
                Description = "مؤشرات الأداء الرئيسية",
                Icon = "📈",
                ThemeColor = Color.FromArgb(111, 66, 193),
                Type = ReportType.PerformanceReport
            },
            [ReportType.InstallmentReport] = new()
            {
                Title = "تقرير الأقساط",
                Description = "حالة الأقساط والمدفوعات",
                Icon = "📅",
                ThemeColor = Color.FromArgb(255, 193, 7),
                Type = ReportType.InstallmentReport
            },
            [ReportType.StatisticsReport] = new()
            {
                Title = "التقرير الإحصائي",
                Description = "إحصائيات شاملة للنشاط",
                Icon = "📊",
                ThemeColor = Color.FromArgb(253, 126, 20),
                Type = ReportType.StatisticsReport
            }
        };

        #endregion

        #region Data Generation Methods

        /// <summary>
        /// إنشاء تقرير المبيعات المحسن
        /// </summary>
        public static async Task<ReportData> GenerateSalesReportAsync(DateTime fromDate, DateTime toDate, string? paymentMethod = null)
        {
            using var context = DbContextFactory.CreateContext();
            
            var query = context.Sales
                .Include(s => s.Customer)
                .Include(s => s.Car)
                .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate);

            if (!string.IsNullOrEmpty(paymentMethod) && paymentMethod != "الكل")
            {
                if (paymentMethod == "نقدي")
                    query = query.Where(s => s.PaymentMethod == PaymentMethod.Cash);
                else if (paymentMethod == "تقسيط")
                    query = query.Where(s => s.PaymentMethod == PaymentMethod.Installment);
            }

            var sales = await query.OrderByDescending(s => s.SaleDate).ToListAsync();

            var dataTable = new DataTable();
            dataTable.Columns.Add("رقم الفاتورة", typeof(string));
            dataTable.Columns.Add("التاريخ", typeof(string));
            dataTable.Columns.Add("العميل", typeof(string));
            dataTable.Columns.Add("السيارة", typeof(string));
            dataTable.Columns.Add("السعر", typeof(string));
            dataTable.Columns.Add("طريقة الدفع", typeof(string));
            dataTable.Columns.Add("الحالة", typeof(string));

            foreach (var sale in sales)
            {
                dataTable.Rows.Add(
                    sale.SaleId.ToString(),
                    sale.SaleDate.ToString("yyyy/MM/dd"),
                    sale.Customer?.FullName ?? "غير محدد",
                    $"{sale.Car?.Brand} {sale.Car?.Model} - {sale.Car?.Year}",
                    sale.ActualSellPrice.ToString("N0") + " ج.م",
                    sale.PaymentMethod.ToString(),
                    GetSaleStatus(sale)
                );
            }

            var totalAmount = sales.Sum(s => s.ActualSellPrice);
            var avgSale = sales.Any() ? sales.Average(s => s.ActualSellPrice) : 0;
            var cashSales = sales.Count(s => s.PaymentMethod == PaymentMethod.Cash);
            var installmentSales = sales.Count(s => s.PaymentMethod == PaymentMethod.Installment);

            return new ReportData
            {
                Data = dataTable,
                Title = "تقرير المبيعات",
                Subtitle = $"من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}",
                ThemeColor = ReportDefinitions[ReportType.SalesReport].ThemeColor,
                Summary = new Dictionary<string, object>
                {
                    ["إجمالي المبيعات"] = totalAmount.ToString("N0") + " ج.م",
                    ["عدد المبيعات"] = sales.Count.ToString(),
                    ["متوسط البيع"] = avgSale.ToString("N0") + " ج.م",
                    ["المبيعات النقدية"] = cashSales.ToString(),
                    ["المبيعات بالتقسيط"] = installmentSales.ToString()
                }
            };
        }

        /// <summary>
        /// إنشاء تقرير المخزون المحسن
        /// </summary>
        public static async Task<ReportData> GenerateInventoryReportAsync()
        {
            using var context = DbContextFactory.CreateContext();
            
            var cars = await context.Cars
                .OrderBy(c => c.Brand)
                .ThenBy(c => c.Model)
                .ToListAsync();

            var dataTable = new DataTable();
            dataTable.Columns.Add("الرقم", typeof(string));
            dataTable.Columns.Add("الماركة", typeof(string));
            dataTable.Columns.Add("الموديل", typeof(string));
            dataTable.Columns.Add("السنة", typeof(string));
            dataTable.Columns.Add("اللون", typeof(string));
            dataTable.Columns.Add("السعر", typeof(string));
            dataTable.Columns.Add("المورد", typeof(string));
            dataTable.Columns.Add("الحالة", typeof(string));

            foreach (var car in cars)
            {
                dataTable.Rows.Add(
                    car.ChassisNumber,
                    car.Brand ?? "غير محدد",
                    car.Model ?? "غير محدد",
                    car.Year.ToString(),
                    car.Color ?? "غير محدد",
                    car.SuggestedSellPrice.ToString("N0") + " ج.م",
                    "غير محدد",
                    car.IsSold ? "مباع" : "متاح"
                );
            }

            var totalValue = cars.Where(c => !c.IsSold).Sum(c => c.SuggestedSellPrice);
            var availableCars = cars.Count(c => !c.IsSold);
            var soldCars = cars.Count(c => c.IsSold);
            var avgPrice = cars.Any() ? cars.Average(c => c.SuggestedSellPrice) : 0;

            return new ReportData
            {
                Data = dataTable,
                Title = "تقرير المخزون",
                Subtitle = $"تم إنشاؤه في {DateTime.Now:yyyy/MM/dd HH:mm}",
                ThemeColor = ReportDefinitions[ReportType.InventoryReport].ThemeColor,
                Summary = new Dictionary<string, object>
                {
                    ["إجمالي قيمة المخزون"] = totalValue.ToString("N0") + " ج.م",
                    ["السيارات المتاحة"] = availableCars.ToString(),
                    ["السيارات المباعة"] = soldCars.ToString(),
                    ["متوسط السعر"] = avgPrice.ToString("N0") + " ج.م",
                    ["إجمالي السيارات"] = cars.Count.ToString()
                }
            };
        }

        /// <summary>
        /// إنشاء تقرير العملاء المحسن
        /// </summary>
        public static async Task<ReportData> GenerateCustomerReportAsync()
        {
            using var context = DbContextFactory.CreateContext();
            
            var customers = await context.Customers
                .Include(c => c.Sales)
                .OrderBy(c => c.FullName)
                .ToListAsync();

            var dataTable = new DataTable();
            dataTable.Columns.Add("الرقم", typeof(string));
            dataTable.Columns.Add("الاسم", typeof(string));
            dataTable.Columns.Add("الهاتف", typeof(string));
            dataTable.Columns.Add("العنوان", typeof(string));
            dataTable.Columns.Add("عدد المشتريات", typeof(string));
            dataTable.Columns.Add("إجمالي المشتريات", typeof(string));
            dataTable.Columns.Add("آخر شراء", typeof(string));

            foreach (var customer in customers)
            {
                var totalPurchases = customer.Sales?.Sum(s => s.ActualSellPrice) ?? 0;
                var purchaseCount = customer.Sales?.Count ?? 0;
                var lastPurchase = customer.Sales?.OrderByDescending(s => s.SaleDate).FirstOrDefault()?.SaleDate;

                dataTable.Rows.Add(
                    customer.CustomerId.ToString(),
                    customer.FullName ?? "غير محدد",
                    customer.PrimaryPhone ?? "غير محدد",
                    customer.Address ?? "غير محدد",
                    purchaseCount.ToString(),
                    totalPurchases.ToString("N0") + " ج.م",
                    lastPurchase?.ToString("yyyy/MM/dd") ?? "لا يوجد"
                );
            }

            var totalCustomers = customers.Count;
            var activeCustomers = customers.Count(c => c.Sales != null && c.Sales.Any());
            var totalSales = customers.SelectMany(c => c.Sales ?? new List<Sale>()).Sum(s => s.ActualSellPrice);

            return new ReportData
            {
                Data = dataTable,
                Title = "تقرير العملاء",
                Subtitle = $"تم إنشاؤه في {DateTime.Now:yyyy/MM/dd HH:mm}",
                ThemeColor = ReportDefinitions[ReportType.CustomerReport].ThemeColor,
                Summary = new Dictionary<string, object>
                {
                    ["إجمالي العملاء"] = totalCustomers.ToString(),
                    ["العملاء النشطين"] = activeCustomers.ToString(),
                    ["إجمالي مبيعات العملاء"] = totalSales.ToString("N0") + " ج.م",
                    ["متوسط الشراء للعميل"] = activeCustomers > 0 ? (totalSales / activeCustomers).ToString("N0") + " ج.م" : "0 ج.م"
                }
            };
        }

        /// <summary>
        /// إنشاء تقرير الأداء
        /// </summary>
        public static async Task<ReportData> GeneratePerformanceReportAsync(DateTime fromDate, DateTime toDate)
        {
            using var context = DbContextFactory.CreateContext();

            var sales = await context.Sales
                .Include(s => s.Customer)
                .Include(s => s.Car)
                .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate)
                .ToListAsync();

            var dataTable = new DataTable();
            dataTable.Columns.Add("المؤشر", typeof(string));
            dataTable.Columns.Add("القيمة", typeof(string));
            dataTable.Columns.Add("النسبة", typeof(string));
            dataTable.Columns.Add("التقييم", typeof(string));

            // Calculate performance metrics
            var totalSales = sales.Count;
            var totalRevenue = sales.Sum(s => s.ActualSellPrice);
            var avgSaleValue = totalSales > 0 ? totalRevenue / totalSales : 0;
            var cashSales = sales.Count(s => s.PaymentMethod == PaymentMethod.Cash);
            var installmentSales = sales.Count(s => s.PaymentMethod == PaymentMethod.Installment);
            var cashPercentage = totalSales > 0 ? (cashSales * 100.0 / totalSales) : 0;
            var installmentPercentage = totalSales > 0 ? (installmentSales * 100.0 / totalSales) : 0;

            // Add performance data
            dataTable.Rows.Add("إجمالي المبيعات", totalSales.ToString(), "100%", GetPerformanceRating(totalSales, 50));
            dataTable.Rows.Add("إجمالي الإيرادات", totalRevenue.ToString("N0") + " ج.م", "100%", GetPerformanceRating((double)totalRevenue, 1000000));
            dataTable.Rows.Add("متوسط قيمة البيع", avgSaleValue.ToString("N0") + " ج.م", "-", GetPerformanceRating((double)avgSaleValue, 200000));
            dataTable.Rows.Add("المبيعات النقدية", cashSales.ToString(), $"{cashPercentage:F1}%", GetPerformanceRating(cashPercentage, 60));
            dataTable.Rows.Add("المبيعات بالتقسيط", installmentSales.ToString(), $"{installmentPercentage:F1}%", GetPerformanceRating(installmentPercentage, 40));

            return new ReportData
            {
                Data = dataTable,
                Title = "تقرير الأداء",
                Subtitle = $"من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}",
                ThemeColor = ReportDefinitions[ReportType.PerformanceReport].ThemeColor,
                Summary = new Dictionary<string, object>
                {
                    ["إجمالي المبيعات"] = totalSales.ToString(),
                    ["إجمالي الإيرادات"] = totalRevenue.ToString("N0") + " ج.م",
                    ["متوسط البيع"] = avgSaleValue.ToString("N0") + " ج.م",
                    ["نسبة النقدي"] = $"{cashPercentage:F1}%"
                }
            };
        }

        /// <summary>
        /// إنشاء تقرير الأقساط المحسن
        /// </summary>
        public static async Task<ReportData> GenerateInstallmentReportAsync(DateTime fromDate, DateTime toDate, string? statusFilter = null)
        {
            using var context = DbContextFactory.CreateContext();

            var installmentSales = await context.Sales
                .Include(s => s.Customer)
                .Include(s => s.Car)
                .Include(s => s.InstallmentPayments)
                .Where(s => s.PaymentMethod == PaymentMethod.Installment &&
                           s.SaleDate >= fromDate && s.SaleDate <= toDate)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();

            var dataTable = new DataTable();
            dataTable.Columns.Add("رقم البيع", typeof(string));
            dataTable.Columns.Add("العميل", typeof(string));
            dataTable.Columns.Add("السيارة", typeof(string));
            dataTable.Columns.Add("رقم القسط", typeof(string));
            dataTable.Columns.Add("قيمة القسط", typeof(string));
            dataTable.Columns.Add("تاريخ الاستحقاق", typeof(string));
            dataTable.Columns.Add("المدفوع", typeof(string));
            dataTable.Columns.Add("تاريخ الدفع", typeof(string));
            dataTable.Columns.Add("الحالة", typeof(string));
            dataTable.Columns.Add("المتبقي", typeof(string));

            decimal totalInstallmentValue = 0;
            decimal totalPaidAmount = 0;
            int pendingCount = 0;
            int overdueCount = 0;
            int paidCount = 0;

            foreach (var sale in installmentSales)
            {
                var carInfo = $"{sale.Car?.Brand} {sale.Car?.Model} - {sale.Car?.Year}";

                if (sale.InstallmentPayments.Any())
                {
                    // Use actual installment payments
                    foreach (var installment in sale.InstallmentPayments.OrderBy(i => i.InstallmentNumber))
                    {
                        var status = GetInstallmentStatusText(installment);

                        // Apply status filter
                        if (!string.IsNullOrEmpty(statusFilter) && statusFilter != "الكل" && status != statusFilter)
                            continue;

                        var remaining = installment.InstallmentAmount - installment.AmountPaid;

                        dataTable.Rows.Add(
                            sale.SaleId.ToString(),
                            sale.Customer?.FullName ?? "غير محدد",
                            carInfo,
                            installment.InstallmentNumber.ToString(),
                            installment.InstallmentAmount.ToString("N0") + " ج.م",
                            installment.DueDate.ToString("yyyy/MM/dd"),
                            installment.AmountPaid.ToString("N0") + " ج.م",
                            installment.PaidDate?.ToString("yyyy/MM/dd") ?? "-",
                            status,
                            remaining.ToString("N0") + " ج.م"
                        );

                        totalInstallmentValue += installment.InstallmentAmount;
                        totalPaidAmount += installment.AmountPaid;

                        switch (installment.Status)
                        {
                            case InstallmentStatus.Pending:
                                pendingCount++;
                                break;
                            case InstallmentStatus.Overdue:
                                overdueCount++;
                                break;
                            case InstallmentStatus.Paid:
                                paidCount++;
                                break;
                        }
                    }
                }
                else
                {
                    // Generate installment schedule for sales without recorded payments
                    var startDate = sale.FirstInstallmentDate ?? sale.SaleDate.AddDays(30);

                    for (int i = 1; i <= sale.NumberOfInstallments; i++)
                    {
                        var dueDate = CalculateInstallmentDueDate(startDate, i - 1, sale.InstallmentPeriod);
                        var status = dueDate <= DateTime.Now ? "مستحق" : "مجدول";

                        if (dueDate < DateTime.Now.AddDays(-30))
                            status = "متأخر";

                        // Apply status filter
                        if (!string.IsNullOrEmpty(statusFilter) && statusFilter != "الكل" && status != statusFilter)
                            continue;

                        dataTable.Rows.Add(
                            sale.SaleId.ToString(),
                            sale.Customer?.FullName ?? "غير محدد",
                            carInfo,
                            i.ToString(),
                            sale.InstallmentAmount.ToString("N0") + " ج.م",
                            dueDate.ToString("yyyy/MM/dd"),
                            "0 ج.م",
                            "-",
                            status,
                            sale.InstallmentAmount.ToString("N0") + " ج.م"
                        );

                        totalInstallmentValue += sale.InstallmentAmount;

                        if (status == "مستحق") pendingCount++;
                        else if (status == "متأخر") overdueCount++;
                    }
                }
            }

            var collectionRate = totalInstallmentValue > 0 ? (totalPaidAmount / totalInstallmentValue) * 100 : 0;

            return new ReportData
            {
                Data = dataTable,
                Title = "تقرير الأقساط",
                Subtitle = $"من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}",
                ThemeColor = ReportDefinitions[ReportType.InstallmentReport].ThemeColor,
                Summary = new Dictionary<string, object>
                {
                    ["إجمالي قيمة الأقساط"] = totalInstallmentValue.ToString("N0") + " ج.م",
                    ["إجمالي المدفوع"] = totalPaidAmount.ToString("N0") + " ج.م",
                    ["معدل التحصيل"] = $"{collectionRate:F1}%",
                    ["الأقساط المستحقة"] = pendingCount.ToString(),
                    ["الأقساط المتأخرة"] = overdueCount.ToString(),
                    ["الأقساط المدفوعة"] = paidCount.ToString()
                }
            };
        }

        /// <summary>
        /// إنشاء التقرير الإحصائي
        /// </summary>
        public static async Task<ReportData> GenerateStatisticsReportAsync()
        {
            using var context = DbContextFactory.CreateContext();

            var dataTable = new DataTable();
            dataTable.Columns.Add("الفئة", typeof(string));
            dataTable.Columns.Add("العدد", typeof(string));
            dataTable.Columns.Add("النسبة", typeof(string));
            dataTable.Columns.Add("الحالة", typeof(string));

            // Get statistics
            var totalCars = await context.Cars.CountAsync();
            var soldCars = await context.Cars.CountAsync(c => c.IsSold);
            var availableCars = totalCars - soldCars;
            var totalCustomers = await context.Customers.CountAsync();
            var activeCustomers = await context.Customers.CountAsync(c => c.Sales != null && c.Sales.Any());
            var totalSuppliers = 0; // await context.Suppliers.CountAsync();
            var totalSales = await context.Sales.CountAsync();

            var soldPercentage = totalCars > 0 ? (soldCars * 100.0 / totalCars) : 0;
            var availablePercentage = totalCars > 0 ? (availableCars * 100.0 / totalCars) : 0;
            var activeCustomerPercentage = totalCustomers > 0 ? (activeCustomers * 100.0 / totalCustomers) : 0;

            // Add statistics data
            dataTable.Rows.Add("إجمالي السيارات", totalCars.ToString(), "100%", "📊");
            dataTable.Rows.Add("السيارات المباعة", soldCars.ToString(), $"{soldPercentage:F1}%", "✅");
            dataTable.Rows.Add("السيارات المتاحة", availableCars.ToString(), $"{availablePercentage:F1}%", "🚗");
            dataTable.Rows.Add("إجمالي العملاء", totalCustomers.ToString(), "100%", "👥");
            dataTable.Rows.Add("العملاء النشطين", activeCustomers.ToString(), $"{activeCustomerPercentage:F1}%", "🔥");
            dataTable.Rows.Add("الموردين", totalSuppliers.ToString(), "-", "🏭");
            dataTable.Rows.Add("عمليات البيع", totalSales.ToString(), "-", "💰");

            return new ReportData
            {
                Data = dataTable,
                Title = "التقرير الإحصائي",
                Subtitle = $"تم إنشاؤه في {DateTime.Now:yyyy/MM/dd HH:mm}",
                ThemeColor = ReportDefinitions[ReportType.StatisticsReport].ThemeColor,
                Summary = new Dictionary<string, object>
                {
                    ["إجمالي السيارات"] = totalCars.ToString(),
                    ["نسبة المباع"] = $"{soldPercentage:F1}%",
                    ["العملاء النشطين"] = activeCustomers.ToString(),
                    ["إجمالي المبيعات"] = totalSales.ToString()
                }
            };
        }

        #endregion

        #region Helper Methods

        private static string GetSaleStatus(Sale sale)
        {
            if (sale.PaymentMethod == PaymentMethod.Installment)
            {
                // يمكن إضافة منطق للتحقق من حالة الأقساط
                return "تقسيط";
            }
            return "مكتمل";
        }

        /// <summary>
        /// تقييم الأداء بناءً على القيمة والهدف
        /// </summary>
        private static string GetPerformanceRating(double value, double target)
        {
            var percentage = target > 0 ? (value / target) * 100 : 0;

            return percentage switch
            {
                >= 100 => "ممتاز 🌟",
                >= 80 => "جيد جداً ✅",
                >= 60 => "جيد 👍",
                >= 40 => "مقبول ⚠️",
                _ => "يحتاج تحسين ❌"
            };
        }

        /// <summary>
        /// الحصول على نص حالة القسط
        /// </summary>
        private static string GetInstallmentStatusText(InstallmentPayment installment)
        {
            return installment.Status switch
            {
                InstallmentStatus.Pending => installment.DueDate < DateTime.Now ? "متأخر" : "مستحق",
                InstallmentStatus.Paid => "مدفوع",
                InstallmentStatus.Overdue => "متأخر",
                InstallmentStatus.PartiallyPaid => "مدفوع جزئياً",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// حساب تاريخ استحقاق القسط
        /// </summary>
        private static DateTime CalculateInstallmentDueDate(DateTime startDate, int installmentIndex, InstallmentPeriod period)
        {
            return period switch
            {
                InstallmentPeriod.Monthly => startDate.AddMonths(installmentIndex),
                InstallmentPeriod.Quarterly => startDate.AddMonths(installmentIndex * 3),
                InstallmentPeriod.SixMonths => startDate.AddMonths(installmentIndex * 6),
                InstallmentPeriod.Yearly => startDate.AddYears(installmentIndex),
                _ => startDate.AddMonths(installmentIndex)
            };
        }

        /// <summary>
        /// تطبيق تنسيق محسن على DataGridView
        /// </summary>
        public static void ApplyEnhancedStyling(DataGridView dgv, Color themeColor)
        {
            dgv.BackgroundColor = Color.White;
            dgv.BorderStyle = BorderStyle.None;
            dgv.EnableHeadersVisualStyles = false;
            
            // Header styling
            dgv.ColumnHeadersDefaultCellStyle.BackColor = themeColor;
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = 40;
            
            // Cell styling
            dgv.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgv.DefaultCellStyle.ForeColor = Color.FromArgb(33, 37, 41);
            dgv.DefaultCellStyle.BackColor = Color.White;
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(themeColor.R, themeColor.G, themeColor.B, 50);
            dgv.DefaultCellStyle.SelectionForeColor = Color.FromArgb(33, 37, 41);
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Padding = new Padding(5);
            
            // Row styling
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgv.RowTemplate.Height = 35;
            dgv.RowHeadersVisible = false;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            
            // Grid lines
            dgv.GridColor = Color.FromArgb(222, 226, 230);
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
        }

        /// <summary>
        /// إنشاء زر محسن مع تصميم جديد
        /// </summary>
        public static Button CreateEnhancedButton(string text, Point location, Color backColor, string icon = "")
        {
            var button = new Button
            {
                Text = string.IsNullOrEmpty(icon) ? text : $"{icon} {text}",
                Location = location,
                Size = new Size(150, 40),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.BorderColor = backColor;
            
            // Add hover effects
            button.MouseEnter += (s, e) => {
                button.BackColor = ControlPaint.Light(backColor, 0.1f);
            };
            button.MouseLeave += (s, e) => {
                button.BackColor = backColor;
            };
            
            return button;
        }

        #endregion
    }
}
