# إصلاح نهائي لمشكلة كلمة المرور
Add-Type -AssemblyName System.Data.SQLite

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🔧 إصلاح نهائي لكلمة المرور" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$currentDir = $PSScriptRoot
$dbPath = Join-Path $currentDir "CarDealership.db"
$binDbPath = Join-Path $currentDir "bin\Debug\net8.0-windows\CarDealership.db"

# إنشاء قاعدة بيانات جديدة تماماً
Write-Host "🆕 إنشاء قاعدة بيانات جديدة..." -ForegroundColor Yellow

# حذف قواعد البيانات القديمة
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force
    Write-Host "🗑️ تم حذف قاعدة البيانات القديمة" -ForegroundColor Yellow
}

if (Test-Path $binDbPath) {
    Remove-Item $binDbPath -Force
    Write-Host "🗑️ تم حذف قاعدة البيانات القديمة من مجلد bin" -ForegroundColor Yellow
}

try {
    # إنشاء قاعدة بيانات جديدة
    $connectionString = "Data Source=$dbPath;Version=3;"
    $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
    $connection.Open()
    
    Write-Host "🔗 تم إنشاء قاعدة بيانات جديدة" -ForegroundColor Green
    
    # إنشاء جدول المستخدمين
    $createTableQuery = @"
CREATE TABLE Users (
    UserId INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT UNIQUE NOT NULL,
    PasswordHash TEXT NOT NULL,
    FullName TEXT NOT NULL,
    Role TEXT NOT NULL DEFAULT 'Admin',
    IsActive INTEGER NOT NULL DEFAULT 1,
    CreatedDate TEXT NOT NULL,
    LastLoginDate TEXT,
    CanViewSales INTEGER NOT NULL DEFAULT 1,
    CanEditSales INTEGER NOT NULL DEFAULT 1,
    CanDeleteSales INTEGER NOT NULL DEFAULT 1,
    CanViewCustomers INTEGER NOT NULL DEFAULT 1,
    CanEditCustomers INTEGER NOT NULL DEFAULT 1,
    CanDeleteCustomers INTEGER NOT NULL DEFAULT 1,
    CanViewInventory INTEGER NOT NULL DEFAULT 1,
    CanEditInventory INTEGER NOT NULL DEFAULT 1,
    CanDeleteInventory INTEGER NOT NULL DEFAULT 1,
    CanViewReports INTEGER NOT NULL DEFAULT 1,
    CanViewFinancials INTEGER NOT NULL DEFAULT 1,
    CanManageUsers INTEGER NOT NULL DEFAULT 1,
    CanBackupRestore INTEGER NOT NULL DEFAULT 1,
    CanAccessSettings INTEGER NOT NULL DEFAULT 1
);
"@
    
    $command = New-Object System.Data.SQLite.SQLiteCommand($createTableQuery, $connection)
    $command.ExecuteNonQuery() | Out-Null
    
    Write-Host "📋 تم إنشاء جدول المستخدمين" -ForegroundColor Green
    
    # إنشاء مستخدمين متعددين بكلمات مرور مختلفة
    $users = @(
        @{ Username = "admin"; Password = "123"; FullName = "المدير العام" },
        @{ Username = "amrali"; Password = "braa"; FullName = "عمرو علي" },
        @{ Username = "user"; Password = "pass"; FullName = "مستخدم عام" },
        @{ Username = "test"; Password = "test"; FullName = "مستخدم تجريبي" }
    )
    
    foreach ($user in $users) {
        $insertQuery = @"
INSERT INTO Users (
    Username, PasswordHash, FullName, Role, IsActive, CreatedDate,
    CanViewSales, CanEditSales, CanDeleteSales,
    CanViewCustomers, CanEditCustomers, CanDeleteCustomers,
    CanViewInventory, CanEditInventory, CanDeleteInventory,
    CanViewReports, CanViewFinancials, CanManageUsers,
    CanBackupRestore, CanAccessSettings
) VALUES (
    '$($user.Username)', '$($user.Password)', '$($user.FullName)', 'Admin', 1, datetime('now'),
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
);
"@
        
        $command = New-Object System.Data.SQLite.SQLiteCommand($insertQuery, $connection)
        $result = $command.ExecuteNonQuery()
        
        if ($result -gt 0) {
            Write-Host "✅ تم إنشاء المستخدم: $($user.Username)" -ForegroundColor Green
        }
    }
    
    # التحقق من البيانات المحفوظة
    Write-Host ""
    Write-Host "🔍 التحقق من المستخدمين المحفوظين:" -ForegroundColor Cyan
    
    $verifyQuery = "SELECT Username, PasswordHash, FullName FROM Users;"
    $command = New-Object System.Data.SQLite.SQLiteCommand($verifyQuery, $connection)
    $reader = $command.ExecuteReader()
    
    while ($reader.Read()) {
        Write-Host "   المستخدم: $($reader["Username"]) | كلمة المرور: $($reader["PasswordHash"]) | الاسم: $($reader["FullName"])" -ForegroundColor White
    }
    $reader.Close()
    
    $connection.Close()
    
    # نسخ قاعدة البيانات إلى مجلد bin
    $binDir = Split-Path $binDbPath
    if (-not (Test-Path $binDir)) {
        New-Item -ItemType Directory -Path $binDir -Force | Out-Null
    }
    Copy-Item $dbPath $binDbPath -Force
    Write-Host ""
    Write-Host "📋 تم نسخ قاعدة البيانات إلى مجلد bin" -ForegroundColor Green
    
} catch {
    Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
    if ($connection -and $connection.State -eq 'Open') {
        $connection.Close()
    }
}

Write-Host ""
Write-Host "🎉 تم إنشاء قاعدة بيانات جديدة بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "🔑 يمكنك الآن تسجيل الدخول بأي من هذه البيانات:" -ForegroundColor Cyan
Write-Host ""
Write-Host "   الخيار 1:" -ForegroundColor Yellow
Write-Host "   اسم المستخدم: admin" -ForegroundColor White
Write-Host "   كلمة المرور: 123" -ForegroundColor White
Write-Host ""
Write-Host "   الخيار 2:" -ForegroundColor Yellow
Write-Host "   اسم المستخدم: amrali" -ForegroundColor White
Write-Host "   كلمة المرور: braa" -ForegroundColor White
Write-Host ""
Write-Host "   الخيار 3:" -ForegroundColor Yellow
Write-Host "   اسم المستخدم: user" -ForegroundColor White
Write-Host "   كلمة المرور: pass" -ForegroundColor White
Write-Host ""
Write-Host "   الخيار 4:" -ForegroundColor Yellow
Write-Host "   اسم المستخدم: test" -ForegroundColor White
Write-Host "   كلمة المرور: test" -ForegroundColor White
Write-Host ""
Write-Host "💡 نصائح للنجاح:" -ForegroundColor Yellow
Write-Host "   • جرب الخيار الأول (admin / 123) أولاً" -ForegroundColor White
Write-Host "   • اكتب البيانات بأحرف إنجليزية صغيرة" -ForegroundColor White
Write-Host "   • لا تضع مسافات قبل أو بعد النص" -ForegroundColor White
Write-Host "   • تأكد من أن Caps Lock غير مفعل" -ForegroundColor White
Write-Host "   • يمكنك نسخ ولصق البيانات" -ForegroundColor White
Write-Host ""
Write-Host "🚀 الآن شغل البرنامج وجرب تسجيل الدخول!" -ForegroundColor Green
Write-Host ""
Write-Host "📞 للمساعدة أو الدعم الفني، اتصل بالمطور" -ForegroundColor Magenta
Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
