# Database Test and Initialization Script for Car Dealership Management

Write-Host "=== Car Dealership Database Test ===" -ForegroundColor Green

# Test database file existence
$dbPath = "C:\Users\<USER>\Desktop\CarDealershipManagement\CarDealership.db"
if (Test-Path $dbPath) {
    $dbSize = (Get-Item $dbPath).Length
    Write-Host "Database file found: $dbPath" -ForegroundColor Green
    Write-Host "Database size: $dbSize bytes" -ForegroundColor Yellow
} else {
    Write-Host "Database file not found: $dbPath" -ForegroundColor Red
}

# Test SQLite connectivity
Write-Host "`nTesting SQLite connectivity..." -ForegroundColor Cyan

try {
    # Test database tables
    $tables = sqlite3 $dbPath ".tables"
    if ($tables) {
        Write-Host "Found tables:" -ForegroundColor Green
        Write-Host $tables -ForegroundColor Yellow
        
        # Test Users table
        Write-Host "`nTesting Users table..." -ForegroundColor Cyan
        $userCount = sqlite3 $dbPath "SELECT COUNT(*) FROM Users;"
        Write-Host "Users count: $userCount" -ForegroundColor Yellow
        
        if ([int]$userCount -eq 0) {
            Write-Host "No users found in database - this might be the source of the data loading error!" -ForegroundColor Red
            Write-Host "The application expects seed data but the database appears empty." -ForegroundColor Red
        } else {
            Write-Host "Users found - database appears to have data." -ForegroundColor Green
            
            # Show existing users
            Write-Host "`nExisting users:" -ForegroundColor Cyan
            $users = sqlite3 $dbPath "SELECT UserId, Username, FullName, Role FROM Users;"
            Write-Host $users -ForegroundColor Yellow
        }
        
        # Test other important tables
        $carCount = sqlite3 $dbPath "SELECT COUNT(*) FROM Cars;"
        $customerCount = sqlite3 $dbPath "SELECT COUNT(*) FROM Customers;"
        $saleCount = sqlite3 $dbPath "SELECT COUNT(*) FROM Sales;"
        
        Write-Host "`nData summary:" -ForegroundColor Cyan
        Write-Host "Cars: $carCount" -ForegroundColor Yellow
        Write-Host "Customers: $customerCount" -ForegroundColor Yellow
        Write-Host "Sales: $saleCount" -ForegroundColor Yellow
        
    } else {
        Write-Host "No tables found - database might be corrupted or empty!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Error testing database: $($_.Exception.Message)" -ForegroundColor Red
}

# Provide solution suggestions
Write-Host "`n=== Potential Solutions ===" -ForegroundColor Green

if ([int]$userCount -eq 0) {
    Write-Host "1. The database exists but lacks seed data." -ForegroundColor Yellow
    Write-Host "2. Try running the application with database recreation:" -ForegroundColor Yellow
    Write-Host "   - Delete the existing database file" -ForegroundColor Cyan
    Write-Host "   - Run the application again to recreate with seed data" -ForegroundColor Cyan
    
    Write-Host "`nWould you like to recreate the database? (Y/N)" -ForegroundColor Magenta
    $response = Read-Host
    
    if ($response -eq "Y" -or $response -eq "y") {
        Write-Host "Backing up current database..." -ForegroundColor Yellow
        $backupPath = "$dbPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $dbPath $backupPath
        Write-Host "Backup created: $backupPath" -ForegroundColor Green
        
        Write-Host "Removing current database..." -ForegroundColor Yellow
        Remove-Item $dbPath
        
        Write-Host "Database removed. Please run the application again to recreate with seed data." -ForegroundColor Green
    }
} else {
    Write-Host "Database appears to have data. The SQLite loading error might be:" -ForegroundColor Yellow
    Write-Host "1. Entity Framework configuration issue" -ForegroundColor Cyan
    Write-Host "2. Connection string problem" -ForegroundColor Cyan
    Write-Host "3. Migration state mismatch" -ForegroundColor Cyan
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
