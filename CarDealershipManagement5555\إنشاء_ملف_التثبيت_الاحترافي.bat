@echo off
chcp 65001 >nul
title إنشاء ملف التثبيت الاحترافي - Amr Ali Elawamy

echo.
echo ========================================
echo   📦 إنشاء ملف التثبيت الاحترافي
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 سيتم إنشاء ملف تثبيت احترافي يحتوي على:
echo    ✅ البرنامج الكامل مع جميع المكتبات
echo    ✅ قاعدة البيانات والإعدادات
echo    ✅ الأيقونات والموارد
echo    ✅ دليل المستخدم والتوثيق
echo    ✅ نظام التفعيل المتقدم
echo    ✅ اختصارات سطح المكتب وقائمة البدء
echo.

echo 🔍 التحقق من المتطلبات...

REM التحقق من وجود البرنامج
set "SOURCE_DIR=%~dp0CarDealership_Debug_Copy"
if not exist "%SOURCE_DIR%\CarDealershipManagement.exe" (
    echo ❌ البرنامج غير موجود في: %SOURCE_DIR%
    echo يرجى تشغيل "إنشاء_نسخة_من_Debug.bat" أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على البرنامج

REM التحقق من وجود الأيقونة
if not exist "%~dp0app-icon.ico" (
    echo ❌ الأيقونة غير موجودة: app-icon.ico
    echo يرجى تشغيل "إنشاء_أيقونة_بسيطة.ps1" أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على الأيقونة

echo.
echo 📝 إنشاء ملف Inno Setup...

REM إنشاء ملف Inno Setup
(
echo ; ملف التثبيت الاحترافي لبرنامج إدارة معرض السيارات
echo ; المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo ; جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
echo [Setup]
echo AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
echo AppName=برنامج إدارة معرض السيارات
echo AppVersion=1.0.0
echo AppVerName=برنامج إدارة معرض السيارات الإصدار 1.0.0
echo AppPublisher=Amr Ali Elawamy
echo AppPublisherURL=https://www.cardealership.com
echo AppSupportURL=https://www.cardealership.com/support
echo AppUpdatesURL=https://www.cardealership.com/updates
echo AppContact=<EMAIL>
echo AppCopyright=جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo DefaultDirName={autopf}\Car Dealership Management
echo DefaultGroupName=برنامج إدارة معرض السيارات
echo AllowNoIcons=yes
echo LicenseFile=license.txt
echo InfoBeforeFile=readme_install.txt
echo InfoAfterFile=after_install.txt
echo OutputDir=Setup
echo OutputBaseFilename=CarDealershipManagement_Setup_v1.0.0
echo SetupIconFile=app-icon.ico
echo Compression=lzma
echo SolidCompression=yes
echo WizardStyle=modern
echo DisableProgramGroupPage=no
echo PrivilegesRequired=admin
echo ArchitecturesAllowed=x64
echo ArchitecturesInstallIn64BitMode=x64
echo MinVersion=6.1sp1
echo ShowLanguageDialog=auto
echo.
echo [Languages]
echo Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
echo Name: "english"; MessagesFile: "compiler:Default.isl"
echo.
echo [Tasks]
echo Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
echo Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
echo Name: "startmenu"; Description: "إنشاء اختصار في قائمة البدء"; GroupDescription: "اختصارات إضافية"
echo.
echo [Files]
echo Source: "CarDealership_Debug_Copy\CarDealershipManagement.exe"; DestDir: "{app}"; Flags: ignoreversion
echo Source: "CarDealership_Debug_Copy\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
echo Source: "app-icon.ico"; DestDir: "{app}"; Flags: ignoreversion
echo Source: "app-icon.png"; DestDir: "{app}"; Flags: ignoreversion
echo Source: "app-icon.svg"; DestDir: "{app}"; Flags: ignoreversion
echo Source: "license.txt"; DestDir: "{app}"; Flags: ignoreversion
echo Source: "readme_install.txt"; DestDir: "{app}"; Flags: ignoreversion
echo Source: "after_install.txt"; DestDir: "{app}"; Flags: ignoreversion
echo.
echo [Icons]
echo Name: "{group}\برنامج إدارة معرض السيارات"; Filename: "{app}\CarDealershipManagement.exe"; IconFilename: "{app}\app-icon.ico"
echo Name: "{group}\دليل المستخدم"; Filename: "{app}\readme_install.txt"
echo Name: "{group}\معلومات التفعيل"; Filename: "{app}\after_install.txt"
echo Name: "{group}\إلغاء التثبيت"; Filename: "{uninstallexe}"
echo Name: "{autodesktop}\برنامج إدارة معرض السيارات"; Filename: "{app}\CarDealershipManagement.exe"; IconFilename: "{app}\app-icon.ico"; Tasks: desktopicon
echo Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\برنامج إدارة معرض السيارات"; Filename: "{app}\CarDealershipManagement.exe"; IconFilename: "{app}\app-icon.ico"; Tasks: quicklaunchicon
echo.
echo [Run]
echo Filename: "{app}\CarDealershipManagement.exe"; Description: "{cm:LaunchProgram,برنامج إدارة معرض السيارات}"; Flags: nowait postinstall skipifsilent
echo.
echo [UninstallDelete]
echo Type: filesandordirs; Name: "{app}\Data"
echo Type: filesandordirs; Name: "{app}\Logs"
echo Type: filesandordirs; Name: "{app}\Backups"
echo Type: filesandordirs; Name: "{app}\CarFiles"
echo Type: filesandordirs; Name: "{app}\SupplierFiles"
echo.
echo [Code]
echo function GetUninstallString^(^): String;
echo var
echo   sUnInstPath: String;
echo   sUnInstallString: String;
echo begin
echo   sUnInstPath := ExpandConstant^('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#SetupSetting("AppId")}_is1'^);
echo   sUnInstallString := '';
echo   if not RegQueryStringValue^(HKLM, sUnInstPath, 'UninstallString', sUnInstallString^) then
echo     RegQueryStringValue^(HKCU, sUnInstPath, 'UninstallString', sUnInstallString^);
echo   Result := sUnInstallString;
echo end;
echo.
echo function IsUpgrade^(^): Boolean;
echo begin
echo   Result := ^(GetUninstallString^(^) ^<^> '''^);
echo end;
echo.
echo function UnInstallOldVersion^(^): Integer;
echo var
echo   sUnInstallString: String;
echo   iResultCode: Integer;
echo begin
echo   Result := 0;
echo   sUnInstallString := GetUninstallString^(^);
echo   if sUnInstallString ^<^> '' then begin
echo     sUnInstallString := RemoveQuotes^(sUnInstallString^);
echo     if Exec^(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode^) then
echo       Result := 3
echo     else
echo       Result := 2;
echo   end else
echo     Result := 1;
echo end;
echo.
echo procedure CurStepChanged^(CurStep: TSetupStep^);
echo begin
echo   if ^(CurStep=ssInstall^) then
echo   begin
echo     if IsUpgrade^(^) then
echo     begin
echo       UnInstallOldVersion^(^);
echo     end;
echo   end;
echo end;
) > "setup.iss"

echo ✅ تم إنشاء ملف Inno Setup

echo.
echo 📄 إنشاء ملفات التوثيق...

REM إنشاء ملف الترخيص
(
echo اتفاقية ترخيص برنامج إدارة معرض السيارات
echo ===============================================
echo.
echo المطور: Amr Ali Elawamy
echo الهاتف: 01285626623
echo البريد الإلكتروني: <EMAIL>
echo.
echo جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
echo شروط الاستخدام:
echo.
echo 1. هذا البرنامج محمي بحقوق الطبع والنشر
echo 2. يُسمح بالاستخدام الشخصي والتجاري بعد الحصول على ترخيص
echo 3. يُمنع نسخ أو توزيع البرنامج بدون إذن من المطور
echo 4. يُمنع إجراء هندسة عكسية للبرنامج
echo 5. المطور غير مسؤول عن أي أضرار قد تنتج من استخدام البرنامج
echo.
echo للحصول على ترخيص أو للدعم الفني:
echo الهاتف: 01285626623
echo البريد الإلكتروني: <EMAIL>
echo.
echo بالضغط على "موافق" فإنك توافق على جميع الشروط المذكورة أعلاه.
) > "license.txt"

REM إنشاء ملف معلومات ما قبل التثبيت
(
echo مرحباً بك في برنامج إدارة معرض السيارات
echo ==========================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo.
echo 🚗 نظام شامل لإدارة معارض السيارات
echo.
echo الميزات الرئيسية:
echo • إدارة شاملة للمخزون والسيارات
echo • نظام مبيعات متقدم مع الأقساط
echo • إدارة العملاء والموردين
echo • تقارير وإحصائيات تفصيلية
echo • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo • نظام ضمان سلامة البيانات المالية
echo • نسخ احتياطي وأرشفة
echo • نظام تفعيل متقدم
echo.
echo متطلبات النظام:
echo • Windows 7 SP1 أو أحدث ^(64-bit^)
echo • .NET 8.0 Runtime ^(سيتم تثبيته تلقائياً^)
echo • 100 ميجابايت مساحة فارغة على القرص الصلب
echo • 2 جيجابايت ذاكرة وصول عشوائي
echo.
echo سيقوم المثبت بإنشاء:
echo • مجلد البرنامج في Program Files
echo • اختصارات في قائمة البدء وسطح المكتب
echo • قاعدة بيانات فارغة جاهزة للاستخدام
echo • ملفات التوثيق والمساعدة
echo.
echo للمتابعة اضغط "التالي"
) > "readme_install.txt"

REM إنشاء ملف معلومات ما بعد التثبيت
(
echo تم تثبيت برنامج إدارة معرض السيارات بنجاح!
echo ==========================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo.
echo 🎉 تم التثبيت بنجاح!
echo.
echo 🔑 بيانات الدخول الافتراضية:
echo.
echo المطور ^(جميع الصلاحيات^):
echo   اسم المستخدم: amrali
echo   كلمة المرور: braa
echo.
echo المدير ^(صلاحيات إدارية^):
echo   اسم المستخدم: admin
echo   كلمة المرور: 123
echo.
echo مندوب المبيعات ^(صلاحيات أساسية^):
echo   اسم المستخدم: user
echo   كلمة المرور: pass
echo.
echo 🆕 نظام التفعيل:
echo • اختر "نسخة تجريبية" للبدء فوراً ^(30 يوم مجاني^)
echo • أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo.
echo 🚀 للبدء:
echo 1. اضغط على أيقونة البرنامج من سطح المكتب
echo 2. أو ابحث عن "برنامج إدارة معرض السيارات" في قائمة البدء
echo 3. سجل الدخول باستخدام البيانات أعلاه
echo 4. اختر نوع التفعيل المناسب
echo.
echo 💡 نصائح مهمة:
echo • قم بتغيير كلمات المرور بعد أول تسجيل دخول
echo • أنشئ نسخة احتياطية من البيانات بانتظام
echo • راجع دليل المستخدم للاستفادة القصوى من البرنامج
echo.
echo 📞 للدعم الفني:
echo الهاتف: 01285626623
echo البريد الإلكتروني: <EMAIL>
echo.
echo 🏆 شكراً لاختيارك برنامج إدارة معرض السيارات!
echo جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
) > "after_install.txt"

echo ✅ تم إنشاء ملفات التوثيق

echo.
echo 📁 إنشاء مجلد Setup...

if not exist "Setup" mkdir "Setup"

echo ✅ تم إنشاء مجلد Setup

echo.
echo 🔧 البحث عن Inno Setup...

REM البحث عن Inno Setup في المواقع الشائعة
set "INNO_PATH="
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
) else if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
) else if exist "C:\Program Files\Inno Setup 5\ISCC.exe" (
    set "INNO_PATH=C:\Program Files\Inno Setup 5\ISCC.exe"
)

if "%INNO_PATH%"=="" (
    echo ❌ لم يتم العثور على Inno Setup
    echo.
    echo 📥 يرجى تحميل وتثبيت Inno Setup من:
    echo    https://jrsoftware.org/isdl.php
    echo.
    echo 💡 بعد التثبيت، شغل هذا الملف مرة أخرى
    echo.
    echo 📋 أو يمكنك استخدام ملف setup.iss يدوياً:
    echo    1. افتح Inno Setup Compiler
    echo    2. افتح ملف setup.iss
    echo    3. اضغط Build ^> Compile
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Inno Setup: %INNO_PATH%

echo.
echo 🏗️ بناء ملف التثبيت...

"%INNO_PATH%" "setup.iss"

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء ملف التثبيت بنجاح!
    echo.
    
    if exist "Setup\CarDealershipManagement_Setup_v1.0.0.exe" (
        echo 📦 ملف التثبيت جاهز:
        echo    📁 المكان: Setup\CarDealershipManagement_Setup_v1.0.0.exe
        
        REM عرض حجم الملف
        for %%A in ("Setup\CarDealershipManagement_Setup_v1.0.0.exe") do (
            set "FILE_SIZE=%%~zA"
            set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
            echo    📏 الحجم: !FILE_SIZE_MB! ميجابايت
        )
        
        echo.
        echo 🎯 ملف التثبيت يحتوي على:
        echo    ✅ البرنامج الكامل مع جميع المكتبات
        echo    ✅ قاعدة البيانات والإعدادات
        echo    ✅ الأيقونات والموارد
        echo    ✅ دليل المستخدم والتوثيق
        echo    ✅ نظام التفعيل المتقدم
        echo    ✅ اختصارات سطح المكتب وقائمة البدء
        echo.
        
        echo 💡 يمكنك الآن:
        echo    1. توزيع ملف التثبيت على العملاء
        echo    2. رفعه على موقعك الإلكتروني
        echo    3. إرساله عبر البريد الإلكتروني
        echo    4. نسخه على فلاشة USB
        echo.
        
        echo هل تريد تشغيل ملف التثبيت لاختباره؟ ^(Y/N^)
        set /p "TEST_SETUP="
        if /i "%TEST_SETUP%"=="Y" (
            echo 🚀 تشغيل ملف التثبيت...
            start "" "Setup\CarDealershipManagement_Setup_v1.0.0.exe"
        )
        
        echo.
        echo هل تريد فتح مجلد Setup؟ ^(Y/N^)
        set /p "OPEN_SETUP="
        if /i "%OPEN_SETUP%"=="Y" (
            start "" "Setup"
        )
        
    ) else (
        echo ❌ لم يتم العثور على ملف التثبيت
    )
) else (
    echo ❌ فشل في إنشاء ملف التثبيت
    echo يرجى مراجعة الأخطاء والمحاولة مرة أخرى
)

echo.
echo 📊 ملخص العملية:
echo    ✅ تم إنشاء ملف Inno Setup
echo    ✅ تم إنشاء ملفات التوثيق
echo    ✅ تم إنشاء مجلد Setup
if exist "Setup\CarDealershipManagement_Setup_v1.0.0.exe" (
    echo    ✅ تم إنشاء ملف التثبيت بنجاح
) else (
    echo    ❌ لم يتم إنشاء ملف التثبيت
)

echo.
echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
