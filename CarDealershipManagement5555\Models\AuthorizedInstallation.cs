using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CarDealershipManagement.Models
{
public class AuthorizedInstallation
{
    [Key]
    public int InstallationId
    {
        get;
        set;
    }

    [Required]
    [StringLength(255)]
    public string DeviceId
    {
        get;
        set;
    } = string.Empty;

    [Required]
    [StringLength(100)]
    public string ActivationCode
    {
        get;
        set;
    } = string.Empty;

    [Required]
    public DateTime ActivationDate
    {
        get;
        set;
    } = DateTime.UtcNow;

    public DateTime? ExpirationDate
    {
        get;
        set;
    }

    [Required]
    public bool IsActive
    {
        get;
        set;
    } = true;

    [StringLength(500)]
    public string? DeviceInfo
    {
        get;
        set;
    }

    [StringLength(100)]
    public string? MachineName
    {
        get;
        set;
    }

    // Audit fields
    [Required]
    public DateTime CreatedDate
    {
        get;
        set;
    } = DateTime.UtcNow;

    [Required]
    [StringLength(100)]
    public string CreatedBy
    {
        get;
        set;
    } = string.Empty;

    public DateTime? ModifiedDate
    {
        get;
        set;
    }

    [StringLength(100)]
    public string? UpdatedBy
    {
        get;
        set;
    }

    // Additional properties for activation management
    [StringLength(100)]
    public string? ActivatedBy
    {
        get;
        set;
    }

    [StringLength(500)]
    public string? ActivationReason
    {
        get;
        set;
    }

    public DateTime? DeactivationDate
    {
        get;
        set;
    }

    [StringLength(100)]
    public string? DeactivatedBy
    {
        get;
        set;
    }

    [StringLength(500)]
    public string? DeactivationReason
    {
        get;
        set;
    }

    [StringLength(1000)]
    public string? Notes
    {
        get;
        set;
    }
}
}
