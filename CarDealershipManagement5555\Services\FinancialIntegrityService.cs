using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CarDealershipManagement.Services
{
    /// <summary>
    /// خدمة ضمان سلامة البيانات المالية
    /// تمنع حذف السيارات إلا بعد تسجيلها في سجل المبيعات
    /// </summary>
    public static class FinancialIntegrityService
    {
        /// <summary>
        /// التحقق من إمكانية حذف السيارة مع ضمان سلامة البيانات المالية
        /// </summary>
        public static async Task<CarDeletionValidationResult> ValidateCarDeletionAsync(string chassisNumber)
        {
            try
            {
                using var context = DbContextFactory.CreateContext();
                
                var car = await context.Cars
                    .Include(c => c.Sale)
                    .ThenInclude(s => s.InstallmentPayments)
                    .FirstOrDefaultAsync(c => c.ChassisNumber == chassisNumber);

                if (car == null)
                {
                    return new CarDeletionValidationResult
                    {
                        CanDelete = false,
                        Reason = "السيارة غير موجودة",
                        RequiredAction = CarDeletionAction.None
                    };
                }

                // إذا كانت السيارة غير مباعة، يمكن حذفها مباشرة
                if (!car.IsSold || car.Sale == null)
                {
                    return new CarDeletionValidationResult
                    {
                        CanDelete = true,
                        Reason = "السيارة غير مباعة - يمكن حذفها مباشرة",
                        RequiredAction = CarDeletionAction.DirectDeletion,
                        CarInfo = GetCarInfo(car)
                    };
                }

                // السيارة مباعة - التحقق من حالة المبيعات
                var sale = car.Sale;
                var saleInfo = await GetSaleDetailsAsync(context, sale);

                // التحقق من حالة الدفع
                if (sale.PaymentMethod == PaymentMethod.Cash)
                {
                    // بيع نقدي - يجب تسجيل العملية في سجل المبيعات قبل الحذف
                    return new CarDeletionValidationResult
                    {
                        CanDelete = true,
                        Reason = "بيع نقدي مكتمل - يجب تسجيل العملية في سجل المبيعات قبل الحذف",
                        RequiredAction = CarDeletionAction.RequiresSaleRecording,
                        CarInfo = GetCarInfo(car),
                        SaleInfo = saleInfo,
                        WarningMessage = "⚠️ تحذير: سيتم حذف السيارة نهائياً بعد تسجيل العملية في سجل المبيعات"
                    };
                }
                else
                {
                    // بيع بالتقسيط - التحقق من حالة الأقساط
                    var installmentStatus = await GetInstallmentStatusAsync(context, sale);
                    
                    if (installmentStatus.IsFullyPaid)
                    {
                        return new CarDeletionValidationResult
                        {
                            CanDelete = true,
                            Reason = "بيع بالتقسيط مكتمل الدفع - يجب تسجيل العملية في سجل المبيعات قبل الحذف",
                            RequiredAction = CarDeletionAction.RequiresSaleRecording,
                            CarInfo = GetCarInfo(car),
                            SaleInfo = saleInfo,
                            InstallmentInfo = installmentStatus,
                            WarningMessage = "⚠️ تحذير: سيتم حذف السيارة وجميع الأقساط نهائياً بعد تسجيل العملية في سجل المبيعات"
                        };
                    }
                    else
                    {
                        return new CarDeletionValidationResult
                        {
                            CanDelete = false,
                            Reason = "لا يمكن حذف السيارة - يوجد أقساط غير مدفوعة",
                            RequiredAction = CarDeletionAction.CompletePayments,
                            CarInfo = GetCarInfo(car),
                            SaleInfo = saleInfo,
                            InstallmentInfo = installmentStatus,
                            WarningMessage = $"❌ يجب استكمال دفع الأقساط أولاً. المتبقي: {installmentStatus.RemainingAmount:N0} ج.م"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new CarDeletionValidationResult
                {
                    CanDelete = false,
                    Reason = $"خطأ في التحقق من صحة البيانات: {ex.Message}",
                    RequiredAction = CarDeletionAction.None
                };
            }
        }

        /// <summary>
        /// تسجيل عملية البيع في سجل المبيعات المؤرشف قبل الحذف
        /// </summary>
        public static async Task<bool> ArchiveSaleBeforeDeletionAsync(string chassisNumber, string archivedBy)
        {
            try
            {
                using var context = DbContextFactory.CreateContext();
                
                var sale = await context.Sales
                    .Include(s => s.Car)
                    .Include(s => s.Customer)
                    .Include(s => s.InstallmentPayments)
                    .FirstOrDefaultAsync(s => s.CarChassisNumber == chassisNumber);

                if (sale == null) return false;

                // تحديث حالة البيع إلى مؤرشف
                sale.IsArchived = true;
                sale.ArchivedDate = DateTime.Now;
                sale.IsCompleted = true;
                sale.CompletedDate = DateTime.Now;
                sale.OperationStatus = SaleOperationStatus.Archived;
                sale.ModifiedDate = DateTime.Now;

                // إضافة معلومات الأرشفة
                if (!string.IsNullOrEmpty(archivedBy))
                {
                    sale.ApprovedBy = archivedBy;
                    sale.ApprovedDate = DateTime.Now;
                    sale.IsApproved = true;
                }

                await context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// حذف السيارة مع ضمان سلامة البيانات المالية
        /// </summary>
        public static async Task<CarDeletionResult> SafeDeleteCarAsync(string chassisNumber, string deletedBy)
        {
            try
            {
                // التحقق من صحة العملية أولاً
                var validation = await ValidateCarDeletionAsync(chassisNumber);
                
                if (!validation.CanDelete)
                {
                    return new CarDeletionResult
                    {
                        Success = false,
                        Message = validation.Reason,
                        ValidationResult = validation
                    };
                }

                using var context = DbContextFactory.CreateContext();
                
                var car = await context.Cars
                    .Include(c => c.Sale)
                    .ThenInclude(s => s.InstallmentPayments)
                    .FirstOrDefaultAsync(c => c.ChassisNumber == chassisNumber);

                if (car == null)
                {
                    return new CarDeletionResult
                    {
                        Success = false,
                        Message = "السيارة غير موجودة"
                    };
                }

                // إذا كانت السيارة مباعة، أرشف البيع أولاً
                if (car.IsSold && car.Sale != null && validation.RequiredAction == CarDeletionAction.RequiresSaleRecording)
                {
                    var archiveSuccess = await ArchiveSaleBeforeDeletionAsync(chassisNumber, deletedBy);
                    if (!archiveSuccess)
                    {
                        return new CarDeletionResult
                        {
                            Success = false,
                            Message = "فشل في أرشفة بيانات البيع"
                        };
                    }
                }

                // حذف ملفات السيارة
                await DeleteCarFilesAsync(chassisNumber);

                // حذف السيارة (سيتم حذف البيع والأقساط تلقائياً بسبب Cascade Delete)
                context.Cars.Remove(car);
                await context.SaveChangesAsync();

                return new CarDeletionResult
                {
                    Success = true,
                    Message = "تم حذف السيارة بنجاح مع ضمان سلامة البيانات المالية",
                    ValidationResult = validation
                };
            }
            catch (Exception ex)
            {
                return new CarDeletionResult
                {
                    Success = false,
                    Message = $"خطأ في حذف السيارة: {ex.Message}"
                };
            }
        }

        #region Helper Methods

        private static CarInfo GetCarInfo(Car car)
        {
            return new CarInfo
            {
                ChassisNumber = car.ChassisNumber,
                Brand = car.Brand,
                Model = car.Model,
                Year = car.Year,
                PurchasePrice = car.PurchasePrice,
                SellPrice = car.SuggestedSellPrice,
                IsSold = car.IsSold
            };
        }

        private static async Task<SaleInfo> GetSaleDetailsAsync(CarDealershipContext context, Sale sale)
        {
            var customer = await context.Customers.FindAsync(sale.CustomerId);
            
            return new SaleInfo
            {
                SaleId = sale.SaleId,
                SaleDate = sale.SaleDate,
                ActualSellPrice = sale.ActualSellPrice,
                PaymentMethod = sale.PaymentMethod,
                CustomerName = customer?.FullName ?? "غير محدد",
                TotalPaid = sale.TotalPaid,
                RemainingAmount = sale.RemainingAmount,
                PaymentStatus = sale.PaymentStatus
            };
        }

        private static async Task<InstallmentInfo> GetInstallmentStatusAsync(CarDealershipContext context, Sale sale)
        {
            var installments = await context.InstallmentPayments
                .Where(ip => ip.SaleId == sale.SaleId)
                .ToListAsync();

            var totalInstallments = installments.Count;
            var paidInstallments = installments.Count(i => i.Status == InstallmentStatus.Paid);
            var totalAmount = installments.Sum(i => i.InstallmentAmount);
            var paidAmount = installments.Sum(i => i.AmountPaid);
            var remainingAmount = totalAmount - paidAmount;

            return new InstallmentInfo
            {
                TotalInstallments = totalInstallments,
                PaidInstallments = paidInstallments,
                PendingInstallments = totalInstallments - paidInstallments,
                TotalAmount = totalAmount,
                PaidAmount = paidAmount,
                RemainingAmount = remainingAmount,
                IsFullyPaid = remainingAmount <= 0
            };
        }

        private static async Task DeleteCarFilesAsync(string chassisNumber)
        {
            try
            {
                var carFilesDir = Path.Combine("CarFiles", chassisNumber);
                if (Directory.Exists(carFilesDir))
                {
                    Directory.Delete(carFilesDir, true);
                }
            }
            catch
            {
                // تجاهل أخطاء حذف الملفات
            }
        }

        #endregion
    }

    #region Data Models

    public class CarDeletionValidationResult
    {
        public bool CanDelete { get; set; }
        public string Reason { get; set; } = string.Empty;
        public CarDeletionAction RequiredAction { get; set; }
        public string? WarningMessage { get; set; }
        public CarInfo? CarInfo { get; set; }
        public SaleInfo? SaleInfo { get; set; }
        public InstallmentInfo? InstallmentInfo { get; set; }
    }

    public class CarDeletionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public CarDeletionValidationResult? ValidationResult { get; set; }
    }

    public class CarInfo
    {
        public string ChassisNumber { get; set; } = string.Empty;
        public string Brand { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public int Year { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal SellPrice { get; set; }
        public bool IsSold { get; set; }
    }

    public class SaleInfo
    {
        public int SaleId { get; set; }
        public DateTime SaleDate { get; set; }
        public decimal ActualSellPrice { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalPaid { get; set; }
        public decimal RemainingAmount { get; set; }
        public PaymentStatus PaymentStatus { get; set; }
    }

    public class InstallmentInfo
    {
        public int TotalInstallments { get; set; }
        public int PaidInstallments { get; set; }
        public int PendingInstallments { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public bool IsFullyPaid { get; set; }
    }

    public enum CarDeletionAction
    {
        None,                    // لا يمكن الحذف
        DirectDeletion,          // حذف مباشر (سيارة غير مباعة)
        RequiresSaleRecording,   // يتطلب تسجيل البيع في السجل
        CompletePayments         // يتطلب استكمال الدفعات أولاً
    }

    #endregion
}
