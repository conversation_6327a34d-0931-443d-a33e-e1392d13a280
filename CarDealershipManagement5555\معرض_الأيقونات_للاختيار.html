<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 معرض الأيقونات - اختر التصميم المفضل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2em;
            margin: 10px 0;
            opacity: 0.9;
        }
        
        .icons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .icon-option {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            cursor: pointer;
            position: relative;
        }
        
        .icon-option:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            border-color: #FFD700;
        }
        
        .icon-option.selected {
            border-color: #00ff00;
            background: rgba(0, 255, 0, 0.1);
        }
        
        .icon-option img {
            width: 128px;
            height: 128px;
            margin-bottom: 20px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
            border-radius: 10px;
        }
        
        .icon-option h3 {
            color: #FFD700;
            margin: 15px 0;
            font-size: 1.3em;
        }
        
        .icon-option p {
            opacity: 0.8;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .option-number {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #FFD700;
            color: black;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .current-badge {
            background: linear-gradient(45deg, #00ff00, #32cd32);
            color: black;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
            margin-top: 10px;
            display: inline-block;
        }
        
        .selection-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 40px 0;
        }
        
        .selection-info h2 {
            color: #FFD700;
            margin-bottom: 20px;
        }
        
        .apply-button {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: black;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
            margin: 10px;
        }
        
        .apply-button:hover {
            transform: scale(1.05);
        }
        
        .developer-info {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-top: 40px;
            border: 2px solid #FFD700;
        }
        
        .features-list {
            text-align: right;
            margin: 20px 0;
        }
        
        .features-list li {
            margin: 5px 0;
            opacity: 0.9;
        }
    </style>
    <script>
        let selectedIcon = null;
        
        function selectIcon(optionNumber, iconName) {
            // إزالة التحديد السابق
            document.querySelectorAll('.icon-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // تحديد الأيقونة الجديدة
            document.getElementById('option-' + optionNumber).classList.add('selected');
            selectedIcon = iconName;
            
            // تحديث معلومات التحديد
            document.getElementById('selected-info').innerHTML = 
                `تم اختيار: <strong>${iconName}</strong><br>
                <button class="apply-button" onclick="applyIcon()">تطبيق هذه الأيقونة</button>`;
        }
        
        function applyIcon() {
            if (selectedIcon) {
                alert('سيتم تطبيق الأيقونة: ' + selectedIcon + '\n\nيرجى الانتظار...');
                // هنا يمكن إضافة كود لتطبيق الأيقونة فعلياً
            } else {
                alert('يرجى اختيار أيقونة أولاً');
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 معرض الأيقونات</h1>
            <p>اختر التصميم المفضل لبرنامج إدارة معرض السيارات</p>
        </div>
        
        <div class="icons-grid">
            <!-- الأيقونة الحالية -->
            <div class="icon-option" id="option-current" onclick="selectIcon('current', 'الأيقونة الاحترافية الحالية')">
                <div class="option-number">✓</div>
                <img src="professional-car-icon.png" alt="الأيقونة الحالية">
                <h3>🔷 الأيقونة الحالية</h3>
                <p>التصميم الاحترافي المطبق حالياً</p>
                <ul class="features-list">
                    <li>✅ خلفية زرقاء متدرجة</li>
                    <li>✅ حدود ذهبية أنيقة</li>
                    <li>✅ سيارة مفصلة</li>
                    <li>✅ تاج التميز</li>
                </ul>
                <div class="current-badge">مطبق حالياً</div>
            </div>
            
            <!-- الخيار 1: كلاسيكية زرقاء -->
            <div class="icon-option" id="option-1" onclick="selectIcon(1, 'الأيقونة الكلاسيكية الزرقاء')">
                <div class="option-number">1</div>
                <img src="icon_option_1_classic_blue.png" alt="كلاسيكية زرقاء">
                <h3>🔵 كلاسيكية زرقاء</h3>
                <p>تصميم كلاسيكي بسيط وأنيق</p>
                <ul class="features-list">
                    <li>• خلفية زرقاء صافية</li>
                    <li>• حدود بيضاء نظيفة</li>
                    <li>• سيارة بسيطة</li>
                    <li>• نص واضح</li>
                </ul>
            </div>
            
            <!-- الخيار 2: حديثة خضراء -->
            <div class="icon-option" id="option-2" onclick="selectIcon(2, 'الأيقونة الحديثة الخضراء')">
                <div class="option-number">2</div>
                <img src="icon_option_2_modern_green.png" alt="حديثة خضراء">
                <h3>🟢 حديثة خضراء</h3>
                <p>تصميم حديث بألوان طبيعية</p>
                <ul class="features-list">
                    <li>• خلفية خضراء متدرجة</li>
                    <li>• سيارة مع تفاصيل</li>
                    <li>• عجلات واقعية</li>
                    <li>• مظهر عصري</li>
                </ul>
            </div>
            
            <!-- الخيار 3: أنيقة رمادية -->
            <div class="icon-option" id="option-3" onclick="selectIcon(3, 'الأيقونة الأنيقة الرمادية')">
                <div class="option-number">3</div>
                <img src="icon_option_3_elegant_gray.png" alt="أنيقة رمادية">
                <h3>⚫ أنيقة رمادية</h3>
                <p>تصميم أنيق وفاخر</p>
                <ul class="features-list">
                    <li>• خلفية رمادية متدرجة</li>
                    <li>• حدود ذهبية فاخرة</li>
                    <li>• سيارة فضية</li>
                    <li>• مظهر راقي</li>
                </ul>
            </div>
            
            <!-- الخيار 4: نارية حمراء -->
            <div class="icon-option" id="option-4" onclick="selectIcon(4, 'الأيقونة النارية الحمراء')">
                <div class="option-number">4</div>
                <img src="icon_option_4_fire_red.png" alt="نارية حمراء">
                <h3>🔴 نارية حمراء</h3>
                <p>تصميم ناري وقوي</p>
                <ul class="features-list">
                    <li>• خلفية حمراء نارية</li>
                    <li>• سيارة رياضية</li>
                    <li>• مصابيح LED زرقاء</li>
                    <li>• طاقة وحيوية</li>
                </ul>
            </div>
            
            <!-- الخيار 5: فاخرة بنفسجية -->
            <div class="icon-option" id="option-5" onclick="selectIcon(5, 'الأيقونة الفاخرة البنفسجية')">
                <div class="option-number">5</div>
                <img src="icon_option_5_luxury_purple.png" alt="فاخرة بنفسجية">
                <h3>🟣 فاخرة بنفسجية</h3>
                <p>تصميم فاخر وملكي</p>
                <ul class="features-list">
                    <li>• خلفية بنفسجية فاخرة</li>
                    <li>• حدود ذهبية مزدوجة</li>
                    <li>• سيارة بلاتينية</li>
                    <li>• تاج ملكي</li>
                </ul>
            </div>
            
            <!-- الخيار 6: عصرية برتقالية -->
            <div class="icon-option" id="option-6" onclick="selectIcon(6, 'الأيقونة العصرية البرتقالية')">
                <div class="option-number">6</div>
                <img src="icon_option_6_modern_orange.png" alt="عصرية برتقالية">
                <h3>🟠 عصرية برتقالية</h3>
                <p>تصميم عصري ومشرق</p>
                <ul class="features-list">
                    <li>• خلفية برتقالية مشرقة</li>
                    <li>• تأثير لمعان</li>
                    <li>• سيارة حديثة</li>
                    <li>• مظهر مبهج</li>
                </ul>
            </div>
        </div>
        
        <div class="selection-info">
            <h2>🎯 اختيارك</h2>
            <div id="selected-info">
                <p>انقر على أي أيقونة لاختيارها</p>
            </div>
        </div>
        
        <div class="developer-info">
            <h3 style="color: #FFD700;">👨‍💻 معلومات المطور</h3>
            <p><strong>المطور:</strong> Amr Ali Elawamy</p>
            <p><strong>الهاتف:</strong> 01285626623</p>
            <p><strong>البريد:</strong> <EMAIL></p>
            <p><strong>الحقوق:</strong> جميع الحقوق محفوظة © 2024</p>
        </div>
    </div>
</body>
</html>
