-- إصلاح نهائي لجدول العملاء - حذف الحقول القديمة نهائياً
-- المطور: <PERSON><PERSON> <PERSON> - 01285626623 - <EMAIL>

-- 1. التحقق من وجود الجدول
SELECT name FROM sqlite_master WHERE type='table' AND name='Customers';

-- 2. عرض البنية الحالية
PRAGMA table_info(Customers);

-- 3. إنشاء جدول العملاء الجديد المبسط (بدون الحقول القديمة)
DROP TABLE IF EXISTS Customers_Final;
CREATE TABLE Customers_Final (
    CustomerId INTEGER PRIMARY KEY AUTOINCREMENT,
    FullName TEXT NOT NULL,
    IdNumber TEXT NOT NULL UNIQUE,
    Address TEXT NOT NULL,
    PrimaryPhone TEXT NOT NULL,
    SecondaryPhone TEXT,
    Email TEXT,
    IsActive INTEGER DEFAULT 1,
    IsDeleted INTEGER DEFAULT 0,
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT,
    DeletedDate TEXT
);

-- 4. نقل البيانات الموجودة (مع دمج العنوان إذا كانت الحقول القديمة موجودة)
INSERT INTO Customers_Final (
    CustomerId, FullName, IdNumber, Address, PrimaryPhone,
    SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate
)
SELECT 
    CustomerId,
    FullName,
    IdNumber,
    CASE 
        -- إذا كان العنوان موجود بالفعل، استخدمه
        WHEN EXISTS(SELECT 1 FROM pragma_table_info('Customers') WHERE name='Address') THEN
            COALESCE(Address, 'غير محدد')
        -- إذا كانت الحقول القديمة موجودة، ادمجها
        ELSE
            CASE 
                WHEN EXISTS(SELECT 1 FROM pragma_table_info('Customers') WHERE name='Country') THEN
                    TRIM(
                        COALESCE(Country, '') || 
                        CASE WHEN Country IS NOT NULL AND Country != '' AND (City IS NOT NULL OR Area IS NOT NULL OR Street IS NOT NULL) THEN ', ' ELSE '' END ||
                        COALESCE(City, '') || 
                        CASE WHEN City IS NOT NULL AND City != '' AND (Area IS NOT NULL OR Street IS NOT NULL) THEN ', ' ELSE '' END ||
                        COALESCE(Area, '') || 
                        CASE WHEN Area IS NOT NULL AND Area != '' AND Street IS NOT NULL AND Street != '' THEN ', ' ELSE '' END ||
                        COALESCE(Street, ''),
                        ', '
                    )
                ELSE 'غير محدد'
            END
    END AS Address,
    PrimaryPhone,
    SecondaryPhone,
    Email,
    IsActive,
    IsDeleted,
    CreatedDate,
    ModifiedDate,
    DeletedDate
FROM Customers;

-- 5. حذف الجدول القديم وإعادة تسمية الجديد
DROP TABLE Customers;
ALTER TABLE Customers_Final RENAME TO Customers;

-- 6. تنظيف العناوين
UPDATE Customers 
SET Address = TRIM(Address)
WHERE Address IS NOT NULL;

UPDATE Customers 
SET Address = 'غير محدد'
WHERE Address IS NULL OR Address = '' OR Address = ', , ,' OR Address = ', ,' OR Address = ',';

-- 7. إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_customers_fullname ON Customers(FullName);
CREATE INDEX IF NOT EXISTS idx_customers_idnumber ON Customers(IdNumber);
CREATE INDEX IF NOT EXISTS idx_customers_active ON Customers(IsActive);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON Customers(PrimaryPhone);

-- 8. التحقق من النتيجة النهائية
PRAGMA table_info(Customers);
SELECT COUNT(*) as 'عدد العملاء' FROM Customers;
SELECT 'تم الإصلاح بنجاح - جدول العملاء مبسط' as 'النتيجة';
