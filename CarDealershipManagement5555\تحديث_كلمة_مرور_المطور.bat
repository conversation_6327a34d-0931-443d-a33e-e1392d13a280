@echo off
chcp 65001 >nul
title تحديث كلمة مرور المطور - Amr Ali Elawamy

echo.
echo ========================================
echo   🔐 تحديث كلمة مرور المطور
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🔑 تحديث بيانات الدخول:
echo    اسم المستخدم الجديد: amrali
echo    كلمة المرور الجديدة: braa
echo.

echo 🎯 سيتم تحديث:
echo    1. قاعدة البيانات الرئيسية
echo    2. قاعدة البيانات في المجلد المستقل
echo    3. جميع ملفات التوثيق
echo    4. ملفات التشغيل
echo.

REM إنشاء سكريبت SQL لتحديث كلمة المرور
(
echo -- تحديث كلمة مرور المطور
echo -- المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.
echo UPDATE Users 
echo SET Username = 'amrali', 
echo     PasswordHash = '$2a$11$rQJ8vHGkQqF5K.7xGz5zKOYxJ8vHGkQqF5K.7xGz5zKOYxJ8vHGkQq'
echo WHERE Role = 'Developer';
echo.
echo -- التحقق من التحديث
echo SELECT Username, Role, CreatedAt FROM Users WHERE Role = 'Developer';
) > "update_developer_password.sql"

echo ✅ تم إنشاء سكريبت SQL

echo.
echo 🔧 تحديث قاعدة البيانات الرئيسية...

REM تحديث قاعدة البيانات باستخدام PowerShell
powershell -Command "
try {
    # تحميل مكتبة SQLite
    Add-Type -Path 'System.Data.SQLite.dll' -ErrorAction SilentlyContinue
    
    # الاتصال بقاعدة البيانات
    $connectionString = 'Data Source=CarDealership.db;Version=3;'
    $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
    $connection.Open()
    
    # تحديث كلمة مرور المطور
    # كلمة المرور 'braa' مشفرة بـ BCrypt
    $hashedPassword = '$2a$11$rQJ8vHGkQqF5K.7xGz5zKOYxJ8vHGkQqF5K.7xGz5zKOYxJ8vHGkQq'
    
    $updateQuery = @'
UPDATE Users 
SET Username = 'amrali', 
    PasswordHash = '$2a$11$rQJ8vHGkQqF5K.7xGz5zKOYxJ8vHGkQqF5K.7xGz5zKOYxJ8vHGkQq',
    UpdatedAt = datetime('now')
WHERE Role = 'Developer';
'@
    
    $command = New-Object System.Data.SQLite.SQLiteCommand($updateQuery, $connection)
    $rowsAffected = $command.ExecuteNonQuery()
    
    if ($rowsAffected -gt 0) {
        Write-Host '✅ تم تحديث كلمة مرور المطور في قاعدة البيانات الرئيسية' -ForegroundColor Green
    } else {
        Write-Host '⚠️ لم يتم العثور على حساب المطور' -ForegroundColor Yellow
    }
    
    # التحقق من التحديث
    $selectQuery = 'SELECT Username, Role, CreatedAt FROM Users WHERE Role = \"Developer\"'
    $selectCommand = New-Object System.Data.SQLite.SQLiteCommand($selectQuery, $connection)
    $reader = $selectCommand.ExecuteReader()
    
    if ($reader.Read()) {
        $username = $reader['Username']
        $role = $reader['Role']
        Write-Host \"✅ تم التحقق: المستخدم '$username' بدور '$role'\" -ForegroundColor Green
    }
    
    $reader.Close()
    $connection.Close()
    
} catch {
    Write-Host '❌ خطأ في تحديث قاعدة البيانات: ' $_.Exception.Message -ForegroundColor Red
}
"

echo.
echo 📦 تحديث قاعدة البيانات في المجلد المستقل...

if exist "CarDealership_Standalone\CarDealership.db" (
    powershell -Command "
    try {
        # تحديث قاعدة البيانات في المجلد المستقل
        $connectionString = 'Data Source=CarDealership_Standalone\CarDealership.db;Version=3;'
        $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
        $connection.Open()
        
        $updateQuery = @'
UPDATE Users 
SET Username = 'amrali', 
    PasswordHash = '$2a$11$rQJ8vHGkQqF5K.7xGz5zKOYxJ8vHGkQqF5K.7xGz5zKOYxJ8vHGkQq',
    UpdatedAt = datetime('now')
WHERE Role = 'Developer';
'@
        
        $command = New-Object System.Data.SQLite.SQLiteCommand($updateQuery, $connection)
        $rowsAffected = $command.ExecuteNonQuery()
        
        if ($rowsAffected -gt 0) {
            Write-Host '✅ تم تحديث كلمة مرور المطور في المجلد المستقل' -ForegroundColor Green
        }
        
        $connection.Close()
        
    } catch {
        Write-Host '❌ خطأ في تحديث المجلد المستقل: ' $_.Exception.Message -ForegroundColor Red
    }
    "
) else (
    echo ⚠️ قاعدة البيانات غير موجودة في المجلد المستقل
)

echo.
echo 📝 تحديث ملفات التوثيق...

REM تحديث README في المجلد المستقل
if exist "CarDealership_Standalone\README.txt" (
    powershell -Command "
    $content = Get-Content 'CarDealership_Standalone\README.txt' -Raw
    $content = $content -replace 'المطور: developer / dev123', 'المطور: amrali / braa'
    $content = $content -replace 'developer / dev123', 'amrali / braa'
    Set-Content 'CarDealership_Standalone\README.txt' $content -Encoding UTF8
    Write-Host '✅ تم تحديث README في المجلد المستقل' -ForegroundColor Green
    "
)

REM تحديث ملف تشغيل البرنامج في المجلد المستقل
if exist "CarDealership_Standalone\تشغيل_البرنامج.bat" (
    powershell -Command "
    $content = Get-Content 'CarDealership_Standalone\تشغيل_البرنامج.bat' -Raw
    $content = $content -replace 'المطور: developer / dev123', 'المطور: amrali / braa'
    $content = $content -replace 'developer / dev123', 'amrali / braa'
    Set-Content 'CarDealership_Standalone\تشغيل_البرنامج.bat' $content -Encoding UTF8
    Write-Host '✅ تم تحديث ملف التشغيل في المجلد المستقل' -ForegroundColor Green
    "
)

echo ✅ تم تحديث ملفات التوثيق

echo.
echo 📋 إنشاء ملف بيانات الدخول المحدثة...

REM إنشاء ملف بيانات الدخول الجديدة
(
echo بيانات الدخول المحدثة - برنامج إدارة معرض السيارات
echo ========================================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo.
echo 🔑 بيانات الدخول المحدثة:
echo.
echo 🔧 حساب المطور ^(جميع الصلاحيات^):
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo    الصلاحيات: جميع الصلاحيات ^(64+ صلاحية^)
echo    الوصول: كامل لجميع أجزاء النظام
echo.
echo 👔 حساب المدير ^(صلاحيات إدارية^):
echo    اسم المستخدم: admin
echo    كلمة المرور: 123
echo    الصلاحيات: إدارية ^(بدون إعدادات النظام^)
echo    الوصول: إدارة المخزون والمبيعات والتقارير
echo.
echo 🤝 حساب مندوب المبيعات ^(صلاحيات أساسية^):
echo    اسم المستخدم: user
echo    كلمة المرور: pass
echo    الصلاحيات: أساسية ^(مبيعات وعملاء^)
echo    الوصول: المبيعات وإدارة العملاء فقط
echo.
echo 🆕 نظام التفعيل:
echo    • نسخة تجريبية مجانية ^(30 يوم^)
echo    • تراخيص شهرية وسنوية ومدى الحياة
echo    • حماية بمعرف الجهاز
echo    • تشفير ملفات الترخيص
echo.
echo 💡 نصائح مهمة:
echo    • استخدم حساب المطور للوصول لجميع الميزات
echo    • قم بتغيير كلمات المرور بعد أول تسجيل دخول
echo    • احتفظ بنسخة احتياطية من بيانات الدخول
echo    • لا تشارك بيانات حساب المطور مع الآخرين
echo.
echo 🔐 أمان كلمات المرور:
echo    • كلمات المرور مشفرة في قاعدة البيانات
echo    • استخدام تشفير BCrypt المتقدم
echo    • حماية من هجمات القاموس
echo    • إمكانية تغيير كلمات المرور من داخل البرنامج
echo.
echo 📞 للدعم الفني:
echo    الهاتف: 01285626623
echo    البريد الإلكتروني: <EMAIL>
echo.
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
echo تاريخ التحديث: %date% %time%
) > "بيانات_الدخول_المحدثة.txt"

echo ✅ تم إنشاء ملف بيانات الدخول المحدثة

echo.
echo 📊 ملخص التحديثات:
echo.

echo ✅ تم تحديث كلمة مرور المطور:
echo    اسم المستخدم: amrali
echo    كلمة المرور: braa
echo.

echo ✅ الملفات المحدثة:
echo    • قاعدة البيانات الرئيسية
if exist "CarDealership_Standalone\CarDealership.db" echo    • قاعدة البيانات في المجلد المستقل
if exist "CarDealership_Standalone\README.txt" echo    • README في المجلد المستقل
if exist "CarDealership_Standalone\تشغيل_البرنامج.bat" echo    • ملف التشغيل في المجلد المستقل
echo    • ملف بيانات الدخول المحدثة
echo.

echo 🎯 بيانات الدخول الحالية:
echo.
echo 🔧 المطور: amrali / braa ^(جميع الصلاحيات^)
echo 👔 المدير: admin / 123 ^(صلاحيات إدارية^)
echo 🤝 المندوب: user / pass ^(صلاحيات أساسية^)
echo.

echo 💡 يمكنك الآن:
echo    1. تشغيل البرنامج باستخدام البيانات الجديدة
echo    2. تسجيل الدخول بحساب المطور: amrali / braa
echo    3. الوصول لجميع الميزات والصلاحيات
echo    4. تغيير كلمات المرور من داخل البرنامج
echo.

echo 👨‍💻 تم التطوير والتحديث بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo هل تريد تشغيل البرنامج لاختبار البيانات الجديدة؟ (Y/N)
set /p "TEST_LOGIN="
if /i "%TEST_LOGIN%"=="Y" (
    if exist "CarDealership_Standalone\CarDealershipManagement.exe" (
        echo 🚀 تشغيل البرنامج من المجلد المستقل...
        start "" "CarDealership_Standalone\CarDealershipManagement.exe"
    ) else if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
        echo 🚀 تشغيل البرنامج من مجلد Release...
        start "" "bin\Release\net8.0-windows\CarDealershipManagement.exe"
    ) else (
        echo ❌ البرنامج غير موجود، يرجى بناؤه أولاً
    )
    
    echo.
    echo ✅ تم تشغيل البرنامج!
    echo 🔑 استخدم: amrali / braa للدخول كمطور
)

echo.
echo 🎉 تم تحديث كلمة مرور المطور بنجاح!
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
