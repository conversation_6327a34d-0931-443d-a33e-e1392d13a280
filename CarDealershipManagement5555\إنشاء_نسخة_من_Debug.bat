@echo off
chcp 65001 >nul
title إنشاء نسخة من البرنامج - Debug Version

echo.
echo ========================================
echo   📦 إنشاء نسخة من البرنامج
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 سيتم إنشاء نسخة من البرنامج الموجود في:
echo    📁 C:\Users\<USER>\Desktop\CarDealershipManagement_updated\CarDealershipManagement5555\bin\Debug\net8.0-windows
echo.

set "SOURCE_DIR=C:\Users\<USER>\Desktop\CarDealershipManagement_updated\CarDealershipManagement5555\bin\Debug\net8.0-windows"
set "DEST_DIR=%~dp0CarDealership_Debug_Copy"

echo 🔍 التحقق من وجود المجلد المصدر...

if not exist "%SOURCE_DIR%" (
    echo ❌ المجلد المصدر غير موجود: %SOURCE_DIR%
    echo.
    echo 🔧 يرجى التأكد من:
    echo    1. بناء البرنامج في وضع Debug
    echo    2. صحة المسار المحدد
    echo.
    pause
    exit /b 1
)

if not exist "%SOURCE_DIR%\CarDealershipManagement.exe" (
    echo ❌ الملف التنفيذي غير موجود في المجلد المصدر
    echo.
    echo 🔧 يرجى بناء البرنامج أولاً باستخدام:
    echo    dotnet build --configuration Debug
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على المجلد المصدر والملف التنفيذي

echo.
echo 🧹 تنظيف المجلد الهدف إذا كان موجوداً...

if exist "%DEST_DIR%" (
    rmdir /s /q "%DEST_DIR%"
    echo ✅ تم حذف المجلد السابق
)

echo.
echo 📁 إنشاء هيكل المجلد الجديد...

mkdir "%DEST_DIR%"
mkdir "%DEST_DIR%\Data"
mkdir "%DEST_DIR%\Backups"
mkdir "%DEST_DIR%\Reports"
mkdir "%DEST_DIR%\Documentation"
mkdir "%DEST_DIR%\CarFiles"
mkdir "%DEST_DIR%\SupplierFiles"
mkdir "%DEST_DIR%\Templates"

echo ✅ تم إنشاء هيكل المجلدات

echo.
echo 📋 نسخ ملفات البرنامج...

REM نسخ جميع ملفات البرنامج
robocopy "%SOURCE_DIR%" "%DEST_DIR%" /E /XD runtimes /NFL /NDL /NJH /NJS

if %ERRORLEVEL% LEQ 1 (
    echo ✅ تم نسخ ملفات البرنامج بنجاح
) else (
    echo ⚠️ تحذير: قد تكون هناك مشاكل في النسخ
)

REM نسخ مجلد runtimes إذا كان موجوداً
if exist "%SOURCE_DIR%\runtimes" (
    echo نسخ مجلد runtimes...
    robocopy "%SOURCE_DIR%\runtimes" "%DEST_DIR%\runtimes" /E /NFL /NDL /NJH /NJS
    echo ✅ تم نسخ مجلد runtimes
)

echo.
echo 📋 إنشاء ملفات إضافية...

REM إنشاء ملف README
(
echo برنامج إدارة معرض السيارات - نسخة Debug
echo ===============================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo.
echo 🚗 نظام شامل لإدارة معارض السيارات
echo.
echo 🚀 طريقة التشغيل:
echo    1. اضغط مرتين على CarDealershipManagement.exe
echo    2. اختر "نسخة تجريبية" للبدء فوراً ^(30 يوم^)
echo    3. أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo.
echo 🔑 بيانات الدخول:
echo    المطور: amrali / braa ^(جميع الصلاحيات^)
echo    المدير: admin / 123 ^(صلاحيات إدارية^)
echo    المندوب: user / pass ^(صلاحيات أساسية^)
echo.
echo 🎯 الميزات الرئيسية:
echo    • إدارة شاملة للمخزون والسيارات
echo    • نظام مبيعات متقدم مع الأقساط
echo    • إدارة العملاء والموردين
echo    • تقارير وإحصائيات تفصيلية
echo    • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo    • نظام ضمان سلامة البيانات المالية
echo    • نسخ احتياطي وأرشفة
echo.
echo 🆕 نظام التفعيل المتقدم:
echo    • نسخة تجريبية مجانية ^(30 يوم^)
echo    • تراخيص شهرية وسنوية ومدى الحياة
echo    • حماية بمعرف الجهاز
echo    • تشفير ملفات الترخيص
echo.
echo 💡 نصائح مهمة:
echo    • قم بإنشاء نسخة احتياطية من مجلد Data بانتظام
echo    • لا تحذف أي ملفات من المجلد
echo    • استخدم كلمات مرور قوية
echo    • راجع التقارير دورياً لمتابعة الأداء
echo.
echo 📞 للدعم الفني: 01285626623
echo 📧 البريد الإلكتروني: <EMAIL>
echo 🌐 الموقع: www.cardealership.com
echo.
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.
echo تاريخ الإنشاء: %date% %time%
) > "%DEST_DIR%\README.txt"

REM إنشاء ملف تشغيل سريع
(
echo @echo off
echo chcp 65001 ^>nul
echo title برنامج إدارة معرض السيارات - Amr Ali Elawamy
echo.
echo echo ========================================
echo echo    🚗 برنامج إدارة معرض السيارات
echo echo ========================================
echo echo.
echo echo 👨‍💻 المطور: Amr Ali Elawamy
echo echo 📞 الهاتف: 01285626623
echo echo 📧 البريد: <EMAIL>
echo echo.
echo echo 🚀 جاري تشغيل البرنامج...
echo echo.
echo echo 🔑 بيانات الدخول:
echo echo    المطور: amrali / braa
echo echo    المدير: admin / 123  
echo echo    المندوب: user / pass
echo echo.
echo echo 💡 يمكنك أيضاً اختيار "نسخة تجريبية" للبدء فوراً
echo echo.
echo start "" "CarDealershipManagement.exe"
echo echo ✅ تم تشغيل البرنامج!
echo echo.
echo echo اضغط أي مفتاح للخروج...
echo pause ^>nul
) > "%DEST_DIR%\تشغيل_البرنامج.bat"

REM إنشاء ملف النسخ الاحتياطي
(
echo @echo off
echo chcp 65001 ^>nul
echo title إنشاء نسخة احتياطية
echo.
echo echo ========================================
echo echo    💾 إنشاء نسخة احتياطية
echo echo ========================================
echo echo.
echo.
echo set "BACKUP_DIR=Backups\Backup_%%date:~-4,4%%%%date:~-10,2%%%%date:~-7,2%%_%%time:~0,2%%%%time:~3,2%%%%time:~6,2%%"
echo set "BACKUP_DIR=%%BACKUP_DIR: =0%%"
echo.
echo echo 📁 إنشاء مجلد النسخة الاحتياطية...
echo mkdir "%%BACKUP_DIR%%"
echo.
echo echo 💾 نسخ قاعدة البيانات...
echo if exist "Data\CarDealership.db" copy "Data\CarDealership.db" "%%BACKUP_DIR%%\" ^>nul
echo if exist "CarDealership.db" copy "CarDealership.db" "%%BACKUP_DIR%%\" ^>nul
echo.
echo echo 📁 نسخ ملفات السيارات...
echo if exist "CarFiles" xcopy "CarFiles" "%%BACKUP_DIR%%\CarFiles\" /E /I /Q ^>nul
echo.
echo echo 📁 نسخ ملفات الموردين...
echo if exist "SupplierFiles" xcopy "SupplierFiles" "%%BACKUP_DIR%%\SupplierFiles\" /E /I /Q ^>nul
echo.
echo echo ✅ تم إنشاء النسخة الاحتياطية بنجاح!
echo echo 📂 المكان: %%BACKUP_DIR%%
echo echo.
echo echo اضغط أي مفتاح للخروج...
echo pause ^>nul
) > "%DEST_DIR%\إنشاء_نسخة_احتياطية.bat"

REM نسخ الأيقونات إذا كانت موجودة
if exist "%~dp0app-icon.ico" (
    copy "%~dp0app-icon.ico" "%DEST_DIR%\" >nul 2>&1
    echo ✅ تم نسخ أيقونة البرنامج
)

if exist "%~dp0app-icon.png" (
    copy "%~dp0app-icon.png" "%DEST_DIR%\" >nul 2>&1
)

if exist "%~dp0app-icon.svg" (
    copy "%~dp0app-icon.svg" "%DEST_DIR%\" >nul 2>&1
)

REM نسخ قاعدة البيانات إذا كانت موجودة
if exist "%~dp0CarDealership.db" (
    copy "%~dp0CarDealership.db" "%DEST_DIR%\Data\" >nul 2>&1
    echo ✅ تم نسخ قاعدة البيانات
)

echo ✅ تم إنشاء الملفات الإضافية

echo.
echo 📊 عرض معلومات النسخة المنشأة...
echo.

if exist "%DEST_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم إنشاء النسخة بنجاح!
    echo.
    echo 📁 مكان النسخة: %DEST_DIR%
    echo.
    
    REM عرض حجم الملف التنفيذي
    for %%A in ("%DEST_DIR%\CarDealershipManagement.exe") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📏 حجم الملف التنفيذي: !FILE_SIZE_MB! ميجابايت
    )
    
    echo.
    echo 📋 محتويات النسخة:
    echo    ✅ CarDealershipManagement.exe - الملف التنفيذي الرئيسي
    echo    ✅ تشغيل_البرنامج.bat - تشغيل سريع
    echo    ✅ إنشاء_نسخة_احتياطية.bat - أداة النسخ الاحتياطي
    echo    ✅ README.txt - دليل سريع
    echo    ✅ Data\ - مجلد قواعد البيانات
    echo    ✅ Documentation\ - أدلة المستخدم
    echo    ✅ Backups\ - مجلد النسخ الاحتياطية
    echo    ✅ Reports\ - مجلد التقارير
    echo    ✅ CarFiles\ - ملفات السيارات
    echo    ✅ SupplierFiles\ - ملفات الموردين
    echo    ✅ جميع المكتبات المطلوبة ^(DLL files^)
    echo.
    
    echo 🎯 النسخة جاهزة للاستخدام والتوزيع!
    echo.
    echo 💡 يمكنك الآن:
    echo    1. نسخ المجلد إلى أي مكان
    echo    2. ضغطه في ملف ZIP للتوزيع
    echo    3. نسخه إلى فلاشة USB
    echo    4. رفعه على الإنترنت للتحميل
    echo    5. تشغيله مباشرة من المجلد
    echo.
    
    echo 🚀 لتشغيل البرنامج من النسخة الجديدة:
    echo    1. انتقل إلى: %DEST_DIR%
    echo    2. اضغط مرتين على "تشغيل_البرنامج.bat"
    echo    3. أو اضغط مرتين على "CarDealershipManagement.exe"
    echo.
    
) else (
    echo ❌ فشل في إنشاء النسخة
    echo يرجى التحقق من صحة المسار المصدر
)

echo.
echo 🎉 انتهت العملية!
echo.
echo 📂 النسخة الجديدة: %DEST_DIR%
echo 📁 المصدر: %SOURCE_DIR%
echo.

echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo هل تريد فتح المجلد الجديد؟ (Y/N)
set /p "OPEN_FOLDER="
if /i "%OPEN_FOLDER%"=="Y" (
    start "" "%DEST_DIR%"
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
