@echo off
chcp 65001 >nul
title تطبيق الإصلاحات الفورية - Immediate Fixes

echo.
echo ========================================
echo    🔧 تطبيق الإصلاحات الفورية
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🔍 المشكلة المكتشفة:
echo - الإصلاحات موجودة في الكود لكن البرنامج المشغل قديم
echo - نحتاج لتطبيق الإصلاحات على قاعدة البيانات مباشرة
echo.

echo 💾 إنشاء نسخة احتياطية...
if exist "CarDealership.db" (
    set "BACKUP_NAME=CarDealership_BeforeImmediateFix_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.db"
    set "BACKUP_NAME=%BACKUP_NAME: =0%"
    copy "CarDealership.db" "%BACKUP_NAME%" >nul
    echo ✅ تم إنشاء نسخة احتياطية: %BACKUP_NAME%
) else (
    echo ⚠️ ملف قاعدة البيانات غير موجود
)

echo.
echo 🔧 تطبيق الإصلاحات على قاعدة البيانات...

REM إنشاء ملف SQL للإصلاحات الفورية
echo -- إصلاحات فورية لقاعدة البيانات > إصلاحات_فورية.sql
echo -- المطور: Amr Ali Elawamy - 01285626623 - <EMAIL> >> إصلاحات_فورية.sql
echo. >> إصلاحات_فورية.sql

echo -- إضافة حقول إيميل وموقع المعرض إذا لم تكن موجودة >> إصلاحات_فورية.sql
echo ALTER TABLE SystemSettings ADD COLUMN CompanyEmail TEXT DEFAULT ''; >> إصلاحات_فورية.sql
echo ALTER TABLE SystemSettings ADD COLUMN CompanyWebsite TEXT DEFAULT ''; >> إصلاحات_فورية.sql
echo. >> إصلاحات_فورية.sql

echo -- إضافة صلاحية إدارة مندوبي المبيعات >> إصلاحات_فورية.sql
echo ALTER TABLE UserPermissions ADD COLUMN CanManageSalesReps INTEGER DEFAULT 0; >> إصلاحات_فورية.sql
echo. >> إصلاحات_فورية.sql

echo -- تحديث صلاحيات المطور >> إصلاحات_فورية.sql
echo UPDATE UserPermissions SET CanManageSalesReps = 1 WHERE UserId = 1; >> إصلاحات_فورية.sql
echo. >> إصلاحات_فورية.sql

echo -- تبسيط جدول العملاء >> إصلاحات_فورية.sql
echo -- إنشاء جدول العملاء الجديد المبسط >> إصلاحات_فورية.sql
echo CREATE TABLE IF NOT EXISTS Customers_New ( >> إصلاحات_فورية.sql
echo     CustomerId INTEGER PRIMARY KEY AUTOINCREMENT, >> إصلاحات_فورية.sql
echo     FullName TEXT NOT NULL, >> إصلاحات_فورية.sql
echo     IdNumber TEXT NOT NULL, >> إصلاحات_فورية.sql
echo     Address TEXT NOT NULL, >> إصلاحات_فورية.sql
echo     PrimaryPhone TEXT NOT NULL, >> إصلاحات_فورية.sql
echo     SecondaryPhone TEXT, >> إصلاحات_فورية.sql
echo     Email TEXT, >> إصلاحات_فورية.sql
echo     IsActive INTEGER DEFAULT 1, >> إصلاحات_فورية.sql
echo     IsDeleted INTEGER DEFAULT 0, >> إصلاحات_فورية.sql
echo     CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP, >> إصلاحات_فورية.sql
echo     ModifiedDate TEXT, >> إصلاحات_فورية.sql
echo     DeletedDate TEXT >> إصلاحات_فورية.sql
echo ); >> إصلاحات_فورية.sql
echo. >> إصلاحات_فورية.sql

echo -- نقل البيانات الموجودة >> إصلاحات_فورية.sql
echo INSERT OR IGNORE INTO Customers_New ( >> إصلاحات_فورية.sql
echo     CustomerId, FullName, IdNumber, Address, PrimaryPhone, >> إصلاحات_فورية.sql
echo     SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate >> إصلاحات_فورية.sql
echo ) >> إصلاحات_فورية.sql
echo SELECT >> إصلاحات_فورية.sql
echo     CustomerId, >> إصلاحات_فورية.sql
echo     FullName, >> إصلاحات_فورية.sql
echo     IdNumber, >> إصلاحات_فورية.sql
echo     COALESCE(Country, '') ^|^| ', ' ^|^| COALESCE(City, '') ^|^| ', ' ^|^| COALESCE(Area, '') ^|^| ', ' ^|^| COALESCE(Street, '') AS Address, >> إصلاحات_فورية.sql
echo     PrimaryPhone, >> إصلاحات_فورية.sql
echo     SecondaryPhone, >> إصلاحات_فورية.sql
echo     Email, >> إصلاحات_فورية.sql
echo     IsActive, >> إصلاحات_فورية.sql
echo     IsDeleted, >> إصلاحات_فورية.sql
echo     CreatedDate, >> إصلاحات_فورية.sql
echo     ModifiedDate, >> إصلاحات_فورية.sql
echo     DeletedDate >> إصلاحات_فورية.sql
echo FROM Customers WHERE EXISTS (SELECT 1 FROM Customers LIMIT 1); >> إصلاحات_فورية.sql
echo. >> إصلاحات_فورية.sql

echo -- حذف الجدول القديم وإعادة تسمية الجديد >> إصلاحات_فورية.sql
echo DROP TABLE IF EXISTS Customers_Old; >> إصلاحات_فورية.sql
echo ALTER TABLE Customers RENAME TO Customers_Old; >> إصلاحات_فورية.sql
echo ALTER TABLE Customers_New RENAME TO Customers; >> إصلاحات_فورية.sql
echo. >> إصلاحات_فورية.sql

echo -- تنظيف العناوين >> إصلاحات_فورية.sql
echo UPDATE Customers >> إصلاحات_فورية.sql
echo SET Address = TRIM(REPLACE(REPLACE(REPLACE(Address, ', , ', ', '), '  ', ' '), ', ,', ',')) >> إصلاحات_فورية.sql
echo WHERE Address IS NOT NULL; >> إصلاحات_فورية.sql
echo. >> إصلاحات_فورية.sql
echo UPDATE Customers >> إصلاحات_فورية.sql
echo SET Address = TRIM(Address, ', ') >> إصلاحات_فورية.sql
echo WHERE Address IS NOT NULL; >> إصلاحات_فورية.sql
echo. >> إصلاحات_فورية.sql
echo UPDATE Customers >> إصلاحات_فورية.sql
echo SET Address = 'غير محدد' >> إصلاحات_فورية.sql
echo WHERE Address IS NULL OR Address = '' OR Address = ', , , '; >> إصلاحات_فورية.sql

if exist "CarDealership.db" (
    echo تطبيق الإصلاحات...
    sqlite3 CarDealership.db < إصلاحات_فورية.sql
    
    if %errorlevel% equ 0 (
        echo ✅ تم تطبيق الإصلاحات بنجاح
    ) else (
        echo ⚠️ حدثت مشاكل في تطبيق بعض الإصلاحات
    )
) else (
    echo ⚠️ لا يمكن تطبيق الإصلاحات - ملف قاعدة البيانات غير موجود
)

echo.
echo 🚀 تشغيل البرنامج...
start "" "bin\Debug\net8.0-windows\CarDealershipManagement.exe"

echo.
echo ✅ تم تطبيق الإصلاحات الفورية
echo.

echo 📝 ما تم إصلاحه:
echo ✅ إضافة حقول إيميل وموقع المعرض
echo ✅ إضافة صلاحية إدارة مندوبي المبيعات
echo ✅ تبسيط نموذج العملاء (حذف الحقول غير المطلوبة)
echo ✅ دمج العنوان في حقل واحد
echo.

echo 🔐 بيانات الدخول:
echo 👤 اسم المستخدم: amrali
echo 🔑 كلمة المرور: braa
echo.

echo 📍 مواقع التحديثات:
echo 📧 إيميل المعرض: الإدارة → الإعدادات → معلومات الشركة
echo 👥 العملاء المبسط: إدارة العملاء → إضافة عميل
echo 👨‍💼 صلاحية المندوبين: إدارة المستخدمين → صلاحيات المدير
echo.

echo 📞 للدعم: 01285626623 - <EMAIL>
echo.

REM تنظيف الملفات المؤقتة
del إصلاحات_فورية.sql >nul 2>&1

pause
