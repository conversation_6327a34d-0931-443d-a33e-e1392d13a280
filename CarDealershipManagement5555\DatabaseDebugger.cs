using System;
using System.Linq;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement
{
public class DatabaseDebugger
{
    public static void CheckDatabase()
    {
        try
        {
            var options = new DbContextOptionsBuilder<CarDealershipContext>()
            .UseSqlite("Data Source=CarDealership.db")
            .Options;

            using var context = new CarDealershipContext(options);

            Console.WriteLine("=== Database Debug Information ===");
            Console.WriteLine();

            // Ensure database is created
            bool created = context.Database.EnsureCreated();
            Console.WriteLine($"Database created: {created}");

            // Check if database file exists
            bool dbExists = File.Exists("CarDealership.db");
            Console.WriteLine($"Database file exists: {dbExists}");

            if(dbExists)
            {
                var fileInfo = new FileInfo("CarDealership.db");
                Console.WriteLine($"Database file size: {fileInfo.Length} bytes");
                Console.WriteLine($"Last modified: {fileInfo.LastWriteTime}");
            }

            Console.WriteLine();

            // Check users table
            var users = context.Users.Include(u => u.Permissions).ToList();
            Console.WriteLine($"Total users in database: {users.Count}");
            Console.WriteLine();

            if(users.Any())
            {
                Console.WriteLine("Users found:");
                foreach(var user in users)
                {
                    Console.WriteLine($"- ID: {user.UserId}");
                    Console.WriteLine($"  Username: '{user.Username}'");
                    Console.WriteLine($"  Full Name: '{user.FullName}'");
                    Console.WriteLine($"  Role: {user.Role}");
                    Console.WriteLine($"  Is Active: {user.IsActive}");
                    Console.WriteLine($"  Password Hash: {user.PasswordHash?.Substring(0, Math.Min(20, user.PasswordHash.Length))}...");
                    Console.WriteLine($"  Has Permissions: {user.Permissions != null}");
                    Console.WriteLine();
                }
            }
            else
            {
                Console.WriteLine("No users found! Creating default user...");
                CreateDefaultUser(context);
            }

            // Test login for default credentials
            Console.WriteLine("=== Testing Login Credentials ===");
            TestLogin(context, "amrali", "braa");

        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    private static void CreateDefaultUser(CarDealershipContext context)
    {
        try
        {
            var hashedPassword = BCrypt.Net.BCrypt.HashPassword("braa");

            var defaultUser = new User
            {
                Username = "amrali",
                PasswordHash = hashedPassword,
                FullName = "عمرو علي",
                Role = UserRole.Developer,
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            context.Users.Add(defaultUser);
            context.SaveChanges();

            // Create permissions for the user
            var permissions = new UserPermissions
            {
                UserId = defaultUser.UserId,
                CanAddCar = true,
                CanEditCar = true,
                CanDeleteCar = true,
                CanViewInventory = true,
                CanSell = true,
                CanEditSale = true,
                CanDeleteSale = true,
                CanViewSales = true,
                CanAddCustomer = true,
                CanEditCustomer = true,
                CanDeleteCustomer = true,
                CanViewCustomers = true,
                CanViewCustomerReport = true,
                CanManageSuppliers = true,
                CanViewAccounts = true,
                CanViewGeneralReports = true,
                CanManageUsers = true,
                CanManageSettings = true,
                CanAddManager = true,
                CanManageManagerPassword = true,
                CanActivateSubscription = true,
                CanResetSystem = true,
                CanRestoreDefaults = true,
                CanFullActivityManagement = true,
                CanCopyDatabase = true,
                CanArchiveSystem = true,
                CanAddSalesRep = true,
                CanManageSalesRepPassword = true,
                CanPrintReports = true,
                CanPrintStatements = true,
                CanActivateInstallation = true,
                CreatedDate = DateTime.Now
            };

            context.UserPermissions.Add(permissions);
            context.SaveChanges();

            Console.WriteLine("Default user created successfully!");
            Console.WriteLine($"Username: amrali");
            Console.WriteLine($"Password: braa");
            Console.WriteLine($"Password Hash: {hashedPassword}");
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error creating default user: {ex.Message}");
        }
    }

    private static void TestLogin(CarDealershipContext context, string username, string password)
    {
        try
        {
            Console.WriteLine($"Testing login with username: '{username}' and password: '{password}'");

            var user = context.Users
                       .Include(u => u.Permissions)
                       .FirstOrDefault(u => u.Username == username && u.IsActive);

            if(user == null)
            {
                Console.WriteLine($"❌ User '{username}' not found or not active!");
                return;
            }

            Console.WriteLine($"✅ User found: {user.FullName}");
            Console.WriteLine($"Stored password hash: {user.PasswordHash}");

            bool passwordValid = BCrypt.Net.BCrypt.Verify(password, user.PasswordHash);
            Console.WriteLine($"Password verification result: {(passwordValid ? "✅ VALID" : "❌ INVALID")}");

            if(passwordValid)
            {
                Console.WriteLine("🎉 Login should work with these credentials!");
            }
            else
            {
                Console.WriteLine("⚠️ Password verification failed. There might be an issue with the stored hash.");

                // Try to update the password
                Console.WriteLine("Attempting to reset password...");
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(password);
                context.SaveChanges();
                Console.WriteLine("Password reset completed. Try logging in again.");
            }
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error testing login: {ex.Message}");
        }
    }
}
}
