-- تحديث نموذج العملاء - إزالة الحقول غير المطلوبة وإضافة حقل العنوان
-- المطور: Amr <PERSON> - 01285626623 - <EMAIL>

-- إن<PERSON>اء نسخة احتياطية من جدول العملاء
CREATE TABLE IF NOT EXISTS Customers_Backup AS SELECT * FROM Customers;

-- إ<PERSON><PERSON><PERSON><PERSON> جدول العملاء الجديد
CREATE TABLE IF NOT EXISTS Customers_New (
    CustomerId INTEGER PRIMARY KEY AUTOINCREMENT,
    FullName TEXT NOT NULL,
    IdNumber TEXT NOT NULL,
    Address TEXT NOT NULL,
    PrimaryPhone TEXT NOT NULL,
    SecondaryPhone TEXT,
    Email TEXT,
    IsActive INTEGER DEFAULT 1,
    IsDeleted INTEGER DEFAULT 0,
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT,
    DeletedDate TEXT
);

-- نقل البيانات من الجدول القديم إلى الجديد
INSERT INTO Customers_New (
    CustomerId, FullName, IdNumber, Address, PrimaryPhone, 
    SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate
)
SELECT 
    CustomerId, 
    FullName, 
    IdNumber, 
    COALESCE(Country, '') || ', ' || COALESCE(City, '') || ', ' || COALESCE(Area, '') || ', ' || COALESCE(Street, '') AS Address,
    PrimaryPhone, 
    SecondaryPhone, 
    Email, 
    IsActive, 
    IsDeleted, 
    CreatedDate, 
    ModifiedDate, 
    DeletedDate
FROM Customers;

-- حذف الجدول القديم
DROP TABLE Customers;

-- إعادة تسمية الجدول الجديد
ALTER TABLE Customers_New RENAME TO Customers;

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_customers_fullname ON Customers(FullName);
CREATE INDEX IF NOT EXISTS idx_customers_idnumber ON Customers(IdNumber);
CREATE INDEX IF NOT EXISTS idx_customers_active ON Customers(IsActive);
CREATE INDEX IF NOT EXISTS idx_customers_deleted ON Customers(IsDeleted);

-- تحديث البيانات لتنظيف العناوين
UPDATE Customers 
SET Address = TRIM(REPLACE(REPLACE(REPLACE(Address, ', , ', ', '), '  ', ' '), ', ,', ','))
WHERE Address IS NOT NULL;

-- إزالة الفواصل الزائدة من بداية ونهاية العناوين
UPDATE Customers 
SET Address = TRIM(Address, ', ')
WHERE Address IS NOT NULL;

-- تحديث العناوين الفارغة
UPDATE Customers 
SET Address = 'غير محدد'
WHERE Address IS NULL OR Address = '' OR Address = ', , , ';

PRAGMA foreign_keys=off;
PRAGMA foreign_keys=on;
