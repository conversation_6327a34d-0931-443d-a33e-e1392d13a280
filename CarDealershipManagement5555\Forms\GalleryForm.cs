using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;

namespace CarDealershipManagement.Forms
{
public partial class GalleryForm : Form
{
    private readonly CarDealershipContext _context;
    private string _selectedCarChassisNumber;
    private bool _isLoading = false;

    public GalleryForm(CarDealershipContext context)
    {
        _context = context;
        InitializeComponent();
        LoadCars();
        LoadGallerySettings();
    }

    private void InitializeComponent()
    {
        this.components = new Container();
        this.SuspendLayout();

        // Form settings
        this.Text = "إدارة المعرض - Gallery Management";
        this.Size = new Size(1000, 700);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
        this.RightToLeft = RightToLeft.Yes;
        this.RightToLeftLayout = true;

        // Car selection panel
        var carPanel = new Panel
        {
            Dock = DockStyle.Top,
            Height = 60,
            BackColor = Color.LightGray
        };

        var carLabel = new Label
        {
            Text = "اختر السيارة:",
            Location = new Point(10, 20),
            Size = new Size(80, 23),
            Font = new Font("Segoe UI", 9F, FontStyle.Bold)
        };

        cmbCars = new ComboBox
        {
            Location = new Point(100, 17),
            Size = new Size(200, 23),
            DropDownStyle = ComboBoxStyle.DropDownList,
            Font = new Font("Segoe UI", 9F)
        };
        cmbCars.SelectedIndexChanged += CmbCars_SelectedIndexChanged;

        btnRefreshCars = new Button
        {
            Text = "تحديث",
            Location = new Point(310, 16),
            Size = new Size(75, 25),
            UseVisualStyleBackColor = true
        };
        btnRefreshCars.Click += BtnRefreshCars_Click;

        // Gallery settings panel
        var settingsPanel = new Panel
        {
            Location = new Point(400, 10),
            Size = new Size(300, 40),
            BackColor = Color.Transparent
        };

        chkGalleryEnabled = new CheckBox
        {
            Text = "تفعيل المعرض",
            Location = new Point(0, 10),
            Size = new Size(100, 20),
            Font = new Font("Segoe UI", 9F)
        };
        chkGalleryEnabled.CheckedChanged += ChkGalleryEnabled_CheckedChanged;

        chkShowImages = new CheckBox
        {
            Text = "إظهار الصور",
            Location = new Point(110, 10),
            Size = new Size(100, 20),
            Font = new Font("Segoe UI", 9F)
        };
        chkShowImages.CheckedChanged += ChkShowImages_CheckedChanged;

        settingsPanel.Controls.AddRange(new Control[] { chkGalleryEnabled, chkShowImages });

        carPanel.Controls.AddRange(new Control[] { carLabel, cmbCars, btnRefreshCars, settingsPanel });

        // Images panel
        var imagesPanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.White,
            Padding = new Padding(10)
        };

        // Upload button
        btnUploadImage = new Button
        {
            Text = "إضافة صورة",
            Location = new Point(10, 10),
            Size = new Size(100, 30),
            BackColor = Color.Green,
            ForeColor = Color.White,
            UseVisualStyleBackColor = false
        };
        btnUploadImage.Click += BtnUploadImage_Click;

        btnDeleteImage = new Button
        {
            Text = "حذف الصورة",
            Location = new Point(120, 10),
            Size = new Size(100, 30),
            BackColor = Color.Red,
            ForeColor = Color.White,
            UseVisualStyleBackColor = false,
            Enabled = false
        };
        btnDeleteImage.Click += BtnDeleteImage_Click;

        // Images flow layout
        flpImages = new FlowLayoutPanel
        {
            Location = new Point(10, 50),
            Size = new Size(imagesPanel.Width - 20, imagesPanel.Height - 60),
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
            AutoScroll = true,
            FlowDirection = FlowDirection.LeftToRight,
            BackColor = Color.WhiteSmoke
        };

        imagesPanel.Controls.AddRange(new Control[] { btnUploadImage, btnDeleteImage, flpImages });

        // Status strip
        statusStrip = new StatusStrip();
        lblStatus = new ToolStripStatusLabel("جاهز");
        statusStrip.Items.Add(lblStatus);

        this.Controls.AddRange(new Control[] { carPanel, imagesPanel, statusStrip });
        this.ResumeLayout(false);
        this.PerformLayout();
    }

    private ComboBox cmbCars;
    private Button btnRefreshCars;
    private CheckBox chkGalleryEnabled;
    private CheckBox chkShowImages;
    private Button btnUploadImage;
    private Button btnDeleteImage;
    private FlowLayoutPanel flpImages;
    private StatusStrip statusStrip;
    private ToolStripStatusLabel lblStatus;
    private IContainer components;
    private PictureBox selectedPictureBox;

    private async void LoadCars()
    {
        try
        {
            _isLoading = true;
            cmbCars.DataSource = null;

            var cars = await _context.Cars
                       .OrderBy(c => c.Brand)
                       .ThenBy(c => c.Model)
                       .Select(c => new
            {
                c.ChassisNumber, DisplayText = $"{c.Brand} {c.Model} - {c.Year}"
            })
            .ToListAsync();

            cmbCars.DataSource = cars;
            cmbCars.DisplayMember = "DisplayText";
            cmbCars.ValueMember = "ChassisNumber";

            if(cars.Any())
            {
                cmbCars.SelectedIndex = 0;
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل السيارات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async void LoadGallerySettings()
    {
        try
        {
            var settings = await _context.SystemSettings.FirstOrDefaultAsync();
            if(settings != null)
            {
                chkGalleryEnabled.Checked = settings.GalleryDataEnabled;
                chkShowImages.Checked = settings.ShowCarImages;
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل إعدادات المعرض: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void CmbCars_SelectedIndexChanged(object sender, EventArgs e)
    {
        if(_isLoading || cmbCars.SelectedValue == null)
        {
            return;
        }

        _selectedCarChassisNumber = cmbCars.SelectedValue?.ToString();
        await LoadCarImages();
    }

    private async Task LoadCarImages()
    {
        if(string.IsNullOrEmpty(_selectedCarChassisNumber))
        {
            return;
        }

        try
        {
            flpImages.Controls.Clear();
            selectedPictureBox = null;
            btnDeleteImage.Enabled = false;

            var images = await _context.CarImages
                         .Where(ci => ci.CarChassisNumber == _selectedCarChassisNumber)
                         .OrderBy(ci => ci.UploadDate)
                         .ToListAsync();

            foreach(var image in images)
            {
                var pictureBox = CreateImageControl(image);
                flpImages.Controls.Add(pictureBox);
            }

            lblStatus.Text = $"تم تحميل {images.Count} صورة";
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الصور: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            lblStatus.Text = "خطأ في تحميل الصور";
        }
    }

    private PictureBox CreateImageControl(CarImage carImage)
    {
        var pictureBox = new PictureBox
        {
            Size = new Size(150, 120),
            Margin = new Padding(5),
            SizeMode = PictureBoxSizeMode.StretchImage,
            BorderStyle = BorderStyle.FixedSingle,
            Tag = carImage,
            Cursor = Cursors.Hand
        };

        try
        {
            if(File.Exists(carImage.ImagePath))
            {
                pictureBox.Image = Image.FromFile(carImage.ImagePath);
            }
            else
            {
                // Create placeholder image
                var bmp = new Bitmap(150, 120);
                using(var g = Graphics.FromImage(bmp))
                {
                    g.Clear(Color.LightGray);
                    g.DrawString("الصورة غير موجودة", this.Font, Brushes.Black, 10, 50);
                }
                pictureBox.Image = bmp;
            }
        }
        catch
        {
            // Create error placeholder
            var bmp = new Bitmap(150, 120);
            using(var g = Graphics.FromImage(bmp))
            {
                g.Clear(Color.LightCoral);
                g.DrawString("خطأ في الصورة", this.Font, Brushes.White, 20, 50);
            }
            pictureBox.Image = bmp;
        }

        pictureBox.Click += PictureBox_Click;
        return pictureBox;
    }

    private void PictureBox_Click(object sender, EventArgs e)
    {
        // Deselect previous selection
        if(selectedPictureBox != null)
        {
            selectedPictureBox.BackColor = Color.Transparent;
        }

        // Select new picture
        selectedPictureBox = sender as PictureBox;
        if(selectedPictureBox != null)
        {
            selectedPictureBox.BackColor = Color.LightBlue;
            btnDeleteImage.Enabled = true;
        }
    }

    private async void BtnUploadImage_Click(object sender, EventArgs e)
    {
        if(string.IsNullOrEmpty(_selectedCarChassisNumber))
        {
            MessageBox.Show("يرجى اختيار سيارة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            var settings = await _context.SystemSettings.FirstOrDefaultAsync();
            var maxImages = settings?.MaxImagesPerCar ?? 10;

            var currentImageCount = await _context.CarImages
                                    .CountAsync(ci => ci.CarChassisNumber == _selectedCarChassisNumber);

            if(currentImageCount >= maxImages)
            {
                MessageBox.Show($"تم الوصول للحد الأقصى من الصور ({maxImages}) لهذه السيارة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using(var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
                openFileDialog.Multiselect = true;

                if(openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    var imagesDir = Path.Combine(Application.StartupPath, "Images");
                    if(!Directory.Exists(imagesDir))
                    {
                        Directory.CreateDirectory(imagesDir);
                    }

                    var carImagesDir = Path.Combine(imagesDir, $"Car_{_selectedCarChassisNumber}");
                    if(!Directory.Exists(carImagesDir))
                    {
                        Directory.CreateDirectory(carImagesDir);
                    }

                    foreach(var filePath in openFileDialog.FileNames)
                    {
                        if(currentImageCount >= maxImages)
                        {
                            MessageBox.Show($"تم الوصول للحد الأقصى من الصور ({maxImages})", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            break;
                        }

                        // Validate file exists and is accessible
                        if(!File.Exists(filePath))
                        {
                            MessageBox.Show($"الملف غير موجود: {Path.GetFileName(filePath)}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            continue;
                        }

                        // Validate file size (limit to 10MB)
                        var fileInfo = new FileInfo(filePath);
                        if(fileInfo.Length > 10 * 1024 * 1024)
                        {
                            MessageBox.Show($"حجم الملف كبير جداً (أكثر من 10 ميجابايت): {Path.GetFileName(filePath)}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            continue;
                        }

                        // Validate image format by trying to load it
                        try
                        {
                            using(var testImage = Image.FromFile(filePath))
                            {
                                // Image is valid, proceed
                            }
                        }
                        catch
                        {
                            MessageBox.Show($"تنسيق الصورة غير صحيح: {Path.GetFileName(filePath)}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            continue;
                        }

                        var fileName = $"{Guid.NewGuid()}{Path.GetExtension(filePath)}";
                        var destinationPath = Path.Combine(carImagesDir, fileName);

                        try
                        {
                            File.Copy(filePath, destinationPath, true);
                        }
                        catch(Exception copyEx)
                        {
                            MessageBox.Show($"خطأ في نسخ الملف {Path.GetFileName(filePath)}: {copyEx.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            continue;
                        }

                        var carImage = new CarImage
                        {
                            CarChassisNumber = _selectedCarChassisNumber,
                            ImagePath = destinationPath,
                            Description = Path.GetFileNameWithoutExtension(Path.GetFileName(filePath)),
                            OriginalFileName = Path.GetFileName(filePath),
                            UploadDate = DateTime.Now
                        };

                        _context.CarImages.Add(carImage);
                        currentImageCount++;
                    }

                    await _context.SaveChangesAsync();
                    await LoadCarImages();

                    lblStatus.Text = "تم رفع الصور بنجاح";
                }
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في رفع الصورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            lblStatus.Text = "خطأ في رفع الصورة";
        }
    }

    private async void BtnDeleteImage_Click(object sender, EventArgs e)
    {
        if(selectedPictureBox?.Tag is not CarImage carImage)
        {
            MessageBox.Show("يرجى اختيار صورة للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var result = MessageBox.Show("هل أنت متأكد من حذف هذه الصورة؟", "تأكيد الحذف",
                                     MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        if(result == DialogResult.Yes)
        {
            try
            {
                // Delete from database
                _context.CarImages.Remove(carImage);
                await _context.SaveChangesAsync();

                // Delete file if exists
                if(File.Exists(carImage.ImagePath))
                {
                    File.Delete(carImage.ImagePath);
                }

                await LoadCarImages();
                lblStatus.Text = "تم حذف الصورة بنجاح";
            }
            catch(Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الصورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في حذف الصورة";
            }
        }
    }

    private async void BtnRefreshCars_Click(object sender, EventArgs e)
    {
        LoadCars();
    }

    private async void ChkGalleryEnabled_CheckedChanged(object sender, EventArgs e)
    {
        await UpdateGallerySetting("GalleryDataEnabled", chkGalleryEnabled.Checked);
    }

    private async void ChkShowImages_CheckedChanged(object sender, EventArgs e)
    {
        await UpdateGallerySetting("ShowCarImages", chkShowImages.Checked);
    }

    private async Task UpdateGallerySetting(string settingName, bool value)
    {
        try
        {
            var settings = await _context.SystemSettings.FirstOrDefaultAsync();
            if(settings != null)
            {
                switch(settingName)
                {
                case "GalleryDataEnabled":
                    settings.GalleryDataEnabled = value;
                    break;
                case "ShowCarImages":
                    settings.ShowCarImages = value;
                    break;
                }

                settings.ModifiedDate = DateTime.Now;
                await _context.SaveChangesAsync();

                lblStatus.Text = "تم حفظ الإعدادات";
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    protected override void Dispose(bool disposing)
    {
        if(disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }
}
}
