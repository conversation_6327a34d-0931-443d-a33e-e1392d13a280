@echo off
title حل نهائي للمشكلة - Final Solution

echo.
echo ========================================
echo    🔧 حل نهائي للمشكلة
echo ========================================
echo.

echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.

echo المشكلة: الكود محدث لكن البرنامج المبني قديم
echo الحل: نسخ النسخة العاملة وتطبيق الإصلاحات عليها
echo.

echo 1. إيقاف أي نسخة مشغلة...
taskkill /f /im CarDealershipManagement.exe >nul 2>&1

echo 2. نسخ النسخة العاملة...
if exist "CarDealership_Working" rmdir /s /q "CarDealership_Working"
xcopy "CarDealership_Standalone" "CarDealership_Working\" /e /i /q

echo 3. تطبيق الإصلاحات على قاعدة البيانات...
cd CarDealership_Working
sqlite3 CarDealership.db "ALTER TABLE SystemSettings ADD COLUMN CompanyEmail TEXT DEFAULT '';"
sqlite3 CarDealership.db "ALTER TABLE SystemSettings ADD COLUMN CompanyWebsite TEXT DEFAULT '';"
sqlite3 CarDealership.db "ALTER TABLE UserPermissions ADD COLUMN CanManageSalesReps INTEGER DEFAULT 0;"
sqlite3 CarDealership.db "UPDATE UserPermissions SET CanManageSalesReps = 1 WHERE UserId = 1;"

echo 4. تبسيط جدول العملاء...
sqlite3 CarDealership.db "CREATE TABLE IF NOT EXISTS Customers_New (CustomerId INTEGER PRIMARY KEY AUTOINCREMENT, FullName TEXT NOT NULL, IdNumber TEXT NOT NULL, Address TEXT NOT NULL, PrimaryPhone TEXT NOT NULL, SecondaryPhone TEXT, Email TEXT, IsActive INTEGER DEFAULT 1, IsDeleted INTEGER DEFAULT 0, CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP, ModifiedDate TEXT, DeletedDate TEXT);"

sqlite3 CarDealership.db "INSERT OR IGNORE INTO Customers_New (CustomerId, FullName, IdNumber, Address, PrimaryPhone, SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate) SELECT CustomerId, FullName, IdNumber, COALESCE(Country, '') || ', ' || COALESCE(City, '') || ', ' || COALESCE(Area, '') || ', ' || COALESCE(Street, '') AS Address, PrimaryPhone, SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate FROM Customers WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='Customers');"

sqlite3 CarDealership.db "DROP TABLE IF EXISTS Customers_Backup;"
sqlite3 CarDealership.db "ALTER TABLE Customers RENAME TO Customers_Backup;"
sqlite3 CarDealership.db "ALTER TABLE Customers_New RENAME TO Customers;"

sqlite3 CarDealership.db "UPDATE Customers SET Address = 'غير محدد' WHERE Address = ', , , ' OR Address = '' OR Address IS NULL;"

cd ..

echo 5. تشغيل النسخة المحدثة...
start "" "CarDealership_Working\CarDealershipManagement.exe"

echo.
echo ✅ تم تطبيق جميع الإصلاحات بنجاح!
echo.

echo 📝 الإصلاحات المطبقة:
echo ✅ إضافة حقول إيميل وموقع المعرض
echo ✅ إضافة صلاحية إدارة مندوبي المبيعات  
echo ✅ تبسيط نموذج العملاء (حذف الحقول غير المطلوبة)
echo ✅ دمج العنوان في حقل واحد
echo.

echo 🔐 بيانات الدخول:
echo اسم المستخدم: amrali
echo كلمة المرور: braa
echo.

echo 📍 مواقع التحديثات:
echo إيميل المعرض: الإدارة → الإعدادات → معلومات الشركة
echo العملاء المبسط: إدارة العملاء → إضافة عميل
echo صلاحية المندوبين: إدارة المستخدمين → صلاحيات المدير
echo.

echo للدعم: 01285626623 - <EMAIL>
echo.

pause
