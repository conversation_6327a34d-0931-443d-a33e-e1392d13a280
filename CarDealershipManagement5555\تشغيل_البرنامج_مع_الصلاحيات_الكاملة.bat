@echo off
chcp 65001 >nul
title برنامج إدارة معرض السيارات - الصلاحيات الكاملة

echo.
echo ========================================
echo    🚗 برنامج إدارة معرض السيارات 🚗
echo ========================================
echo.

echo 🔧 بناء البرنامج مع التحسينات الجديدة...
echo.

REM بناء البرنامج
dotnet build --configuration Release >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء البرنامج بنجاح
) else (
    echo ⚠️ تحذير: قد تكون هناك مشاكل في البناء، سيتم المتابعة...
)

echo.
echo 🚀 تشغيل البرنامج...
echo.

cd /d "%~dp0\bin\Release\net8.0-windows"

if not exist "CarDealershipManagement.exe" (
    cd /d "%~dp0\bin\Debug\net8.0-windows"
)

if exist "CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج
    echo.
    echo 🔑 بيانات الدخول المتاحة:
    echo.
    echo 🔧 حساب المطور (صلاحيات كاملة):
    echo    اسم المستخدم: developer
    echo    كلمة المرور: dev123
    echo    الصلاحيات: جميع الصلاحيات متاحة
    echo.
    echo 👔 حسابات المديرين:
    echo    admin / 123 (مدير عام)
    echo    amrali / braa (عمرو علي - مدير)
    echo.
    echo 🤝 حسابات المندوبين:
    echo    user / pass (مستخدم عام)
    echo    test / test (مستخدم تجريبي)
    echo.
    echo 🆕 التحسينات الجديدة في نظام الصلاحيات:
    echo.
    echo ✅ قائمة صلاحيات كاملة ومنظمة:
    echo    📦 صلاحيات المخزون (8 صلاحيات)
    echo       • عرض المخزون، إضافة سيارة، تعديل سيارة، حذف سيارة
    echo       • عرض تفاصيل السيارة، إدارة ملفات السيارة
    echo       • طباعة المخزون، تصدير المخزون
    echo.
    echo    💰 صلاحيات المبيعات (8 صلاحيات)
    echo       • عرض المبيعات، إضافة بيع، تعديل البيع، حذف البيع
    echo       • معالجة المدفوعات، إدارة الأقساط
    echo       • طباعة تقارير المبيعات، تصدير بيانات المبيعات
    echo.
    echo    👥 صلاحيات العملاء (8 صلاحيات)
    echo       • عرض العملاء، إضافة عميل، تعديل العميل، حذف العميل
    echo       • عرض تاريخ العميل، إدارة ملفات العميل
    echo       • طباعة تقارير العملاء، تصدير بيانات العملاء
    echo.
    echo    🏭 صلاحيات الموردين (8 صلاحيات)
    echo       • عرض الموردين، إضافة مورد، تعديل المورد، حذف المورد
    echo       • عرض تاريخ المورد، إدارة ملفات المورد
    echo       • طباعة تقارير الموردين، تصدير بيانات الموردين
    echo.
    echo    📊 صلاحيات التقارير (8 صلاحيات)
    echo       • عرض التقارير، إنشاء التقارير، طباعة التقارير، تصدير التقارير
    echo       • عرض البيانات المالية، عرض الإحصائيات
    echo       • جدولة التقارير، تخصيص التقارير
    echo.
    echo    ⚙️ صلاحيات الإدارة (8 صلاحيات)
    echo       • إدارة المستخدمين، إضافة مستخدم، تعديل مستخدم، حذف مستخدم
    echo       • إعادة تعيين كلمات المرور، إدارة الصلاحيات
    echo       • عرض نشاط المستخدمين، إدارة الأدوار
    echo.
    echo    🔧 صلاحيات النظام (8 صلاحيات)
    echo       • الوصول للإعدادات، النسخ الاحتياطي، استعادة قاعدة البيانات
    echo       • أرشفة البيانات، تصدير البيانات، استيراد البيانات
    echo       • إدارة الاشتراك، عرض سجلات النظام
    echo.
    echo    🖨️ صلاحيات الطباعة (8 صلاحيات)
    echo       • طباعة المخزون، طباعة المبيعات، طباعة العملاء، طباعة الموردين
    echo       • طباعة التقارير، طباعة الإحصائيات
    echo       • طباعة الفواتير، طباعة الكشوفات
    echo.
    echo 🔧 صلاحيات المطور الخاصة (8 صلاحيات):
    echo    • إضافة مديرين، إدارة كلمات مرور المديرين
    echo    • تفعيل الاشتراكات، تفعيل التثبيت
    echo    • إعادة تعيين النظام، استعادة الافتراضيات
    echo    • الوصول لأدوات المطور، تعديل نواة النظام
    echo.
    echo 👔 صلاحيات المدير الخاصة (5 صلاحيات):
    echo    • الإدارة الكاملة للنشاط، نسخ قاعدة البيانات
    echo    • أرشفة النظام، إضافة مندوب مبيعات
    echo    • إدارة كلمة مرور مندوب المبيعات
    echo.
    echo 📋 كيفية الوصول لإدارة المستخدمين:
    echo    1. سجل الدخول بحساب مطور أو مدير
    echo    2. اضغط على قائمة "الإدارة" في شريط القوائم
    echo    3. اختر "إدارة المستخدمين"
    echo    4. اضغط "إضافة مستخدم" لإنشاء مندوب مبيعات جديد
    echo    5. في تبويب "الصلاحيات" ستجد جميع الصلاحيات منظمة بالفئات
    echo.
    echo 🎯 مميزات نظام الصلاحيات الجديد:
    echo    ✅ تنظيم الصلاحيات في فئات واضحة
    echo    ✅ عرض الصلاحيات في عمودين لسهولة القراءة
    echo    ✅ أيقونات وألوان مميزة لكل فئة
    echo    ✅ تلميحات توضح وصف كل صلاحية
    echo    ✅ عرض الصلاحيات الافتراضية لكل دور
    echo    ✅ تحديث تلقائي للصلاحيات عند اختيار الدور
    echo    ✅ حفظ وتحميل ديناميكي لجميع الصلاحيات
    echo.
    echo 🚀 جاري فتح البرنامج...
    echo.
    
    REM تشغيل البرنامج
    start "" "CarDealershipManagement.exe"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    echo.
    echo 💡 نصائح للاستفادة القصوى:
    echo    • استخدم حساب المطور للوصول لجميع الميزات
    echo    • جرب إنشاء مستخدمين بأدوار مختلفة
    echo    • اختبر الصلاحيات المختلفة لكل دور
    echo    • استخدم التلميحات لفهم وصف كل صلاحية
    echo.
    echo 🔒 الميزات الأخرى المتاحة:
    echo    • نظام ضمان سلامة البيانات المالية عند حذف السيارات
    echo    • تبويب الأقساط المحسن مع إحصائيات متقدمة
    echo    • طباعة وتصدير محسن للتقارير
    echo.
    
) else (
    echo ❌ خطأ: لم يتم العثور على ملف البرنامج
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. تأكد من بناء المشروع في Visual Studio
    echo    2. تحقق من مسار الملف
    echo    3. أعد تشغيل Visual Studio كمسؤول
    echo.
)

cd /d "%~dp0"

echo 📞 للمساعدة أو الدعم الفني، اتصل بالمطور
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
