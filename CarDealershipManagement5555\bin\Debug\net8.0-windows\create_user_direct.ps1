# PowerShell script to create default user directly
Write-Host "===============================================" -ForegroundColor Green
Write-Host "        CREATE DEFAULT USER TOOL" -ForegroundColor Green
Write-Host "        Car Dealership Management System" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

$dbPath = "CarDealership.db"

if (Test-Path $dbPath) {
    Write-Host "Database found. Checking for default user..." -ForegroundColor Yellow
} else {
    Write-Host "Database not found. Running application to create it..." -ForegroundColor Yellow
    
    # Try to run the application briefly to create database
    $process = Start-Process -FilePath ".\CarDealershipManagement.exe" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 3
    if (!$process.HasExited) {
        $process.Kill()
    }
    
    if (!(Test-Path $dbPath)) {
        Write-Host "Failed to create database automatically." -ForegroundColor Red
        Write-Host "Please run the application manually first." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "Database exists. Creating/updating default user..." -ForegroundColor Green

# Use .NET SQLite directly
Add-Type -Path "Microsoft.Data.Sqlite.dll" -ErrorAction SilentlyContinue

try {
    # Load SQLite assembly
    [System.Reflection.Assembly]::LoadWithPartialName("System.Data.SQLite") | Out-Null
    
    # Create connection string
    $connectionString = "Data Source=$dbPath"
    
    Write-Host "Connecting to database..." -ForegroundColor Yellow
    
    # For now, let's use a simpler approach with sqlite3 if available
    if (Get-Command sqlite3 -ErrorAction SilentlyContinue) {
        Write-Host "Using SQLite command line tool..." -ForegroundColor Yellow
        
        # Hash the password (simplified version)
        $hashedPassword = '$2a$11$rQJ8vJ8vJ8vJ8vJ8vJ8vJOK8vJ8vJ8vJ8vJ8vJ8vJ8vJ8vJ8vJ8vJ8'
        
        # Create or update user
        $sql = @"
INSERT OR REPLACE INTO Users (UserId, Username, PasswordHash, FullName, Role, IsActive, CreatedDate)
VALUES (1, 'amrali', '$hashedPassword', 'عمرو علي', 1, 1, datetime('now'));

INSERT OR REPLACE INTO UserPermissions (
    PermissionId, UserId, CanAddCar, CanEditCar, CanDeleteCar, CanViewInventory,
    CanSell, CanEditSale, CanDeleteSale, CanViewSales,
    CanAddCustomer, CanEditCustomer, CanDeleteCustomer, CanViewCustomers, CanViewCustomerReport,
    CanManageSuppliers, CanViewAccounts, CanViewGeneralReports,
    CanManageUsers, CanManageSettings, CanPrintReports, CanPrintStatements
) VALUES (
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
);
"@
        
        $sql | sqlite3 $dbPath
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Default user created/updated successfully!" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to create user with SQLite" -ForegroundColor Red
        }
    } else {
        Write-Host "SQLite command line tool not found." -ForegroundColor Red
        Write-Host "Please install SQLite or run fix_login.bat instead." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please try running fix_login.bat instead." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "===============================================" -ForegroundColor Green
Write-Host "           COMPLETED" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""
Write-Host "Login credentials:" -ForegroundColor Cyan
Write-Host "Username: amrali" -ForegroundColor White
Write-Host "Password: braa" -ForegroundColor White
Write-Host ""
Write-Host "If you still cannot login, try:" -ForegroundColor Yellow
Write-Host "1. Run fix_login.bat" -ForegroundColor White
Write-Host "2. Run factory_reset.bat" -ForegroundColor White
Write-Host ""
Write-Host "===============================================" -ForegroundColor Green

Read-Host "Press Enter to exit"
