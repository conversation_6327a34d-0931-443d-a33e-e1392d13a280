@echo off
chcp 65001 >nul
title تشغيل البرنامج من المجلد المستقل

echo.
echo ========================================
echo   🚀 تشغيل البرنامج من المجلد المستقل
echo ========================================
echo.

set "STANDALONE_DIR=%~dp0CarDealership_Standalone"

if exist "%STANDALONE_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج في المجلد المستقل
    echo.
    echo 📁 المكان: %STANDALONE_DIR%
    echo.
    
    REM عرض حجم الملف
    for %%A in ("%STANDALONE_DIR%\CarDealershipManagement.exe") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📏 حجم الملف: !FILE_SIZE_MB! ميجابايت
    )
    
    echo.
    echo 🔑 بيانات الدخول:
    echo    المطور: developer / dev123 (جميع الصلاحيات)
    echo    المدير: admin / 123 (صلاحيات إدارية)
    echo    المندوب: user / pass (صلاحيات أساسية)
    echo.
    echo 🆕 نظام التفعيل:
    echo    • نسخة تجريبية مجانية (30 يوم)
    echo    • تراخيص شهرية وسنوية ومدى الحياة
    echo    • حماية بمعرف الجهاز
    echo.
    echo 🚀 جاري تشغيل البرنامج...
    echo.
    
    cd /d "%STANDALONE_DIR%"
    start "" "CarDealershipManagement.exe"
    
    echo ✅ تم تشغيل البرنامج من المجلد المستقل!
    echo.
    echo 💡 المجلد المستقل يحتوي على:
    echo    • الملف التنفيذي وجميع المكتبات المطلوبة
    echo    • قاعدة البيانات
    echo    • ملفات التوثيق والأدلة
    echo    • ملف تشغيل سريع
    echo.
    echo 🎯 يمكنك الآن:
    echo    1. نسخ المجلد إلى أي مكان
    echo    2. ضغطه في ملف ZIP للتوزيع
    echo    3. نسخه إلى فلاشة USB
    echo    4. رفعه على الإنترنت للتحميل
    echo.
    
) else (
    echo ❌ لم يتم العثور على البرنامج في المجلد المستقل
    echo.
    echo 🔧 يرجى تشغيل أحد الملفات التالية أولاً:
    echo    • نسخ_المجلد_المستقل.bat
    echo    • إنشاء_مجلد_مستقل_للتوزيع.bat
    echo.
)

cd /d "%~dp0"

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
