using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CarDealershipManagement.Models
{
public class SupplierPaymentSchedule
{
    [Key]
    public int ScheduleId
    {
        get;
        set;
    }

    [ForeignKey("Supplier")]
    public int SupplierId
    {
        get;
        set;
    }

    [Column(TypeName = "DATE")]
    public DateTime DueDate
    {
        get;
        set;
    }

    [Column(TypeName = "DECIMAL(10, 2)")]
    public decimal Amount
    {
        get;
        set;
    }

    [StringLength(20)]
    public string PaymentStatus
    {
        get;
        set;
    } = "Pending";

    [StringLength(500)]
    public string Description
    {
        get;
        set;
    } = string.Empty;

    // Navigation property
    public virtual Supplier Supplier
    {
        get;
        set;
    } = null!;
}
}
