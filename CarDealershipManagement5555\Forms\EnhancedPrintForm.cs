using System;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Windows.Forms;
using CarDealershipManagement.Services;

namespace CarDealershipManagement.Forms
{
    /// <summary>
    /// نموذج طباعة محسن مع تصميم احترافي
    /// </summary>
    public partial class EnhancedPrintForm : Form
    {
        private readonly DataTable dataTable;
        private readonly string reportTitle;
        private readonly string reportSubtitle;
        private readonly Color themeColor;
        private readonly Dictionary<string, object> summary;
        
        private PrintDocument printDocument;
        private PrintPreviewControl printPreviewControl;
        private Button btnPrint;
        private Button btnPageSetup;
        private Button btnClose;
        private Label lblPageInfo;
        private ComboBox cmbZoom;
        
        private int currentRow = 0;
        private int pageNumber = 1;
        private int totalPages = 1;

        public EnhancedPrintForm(EnhancedReportService.ReportData reportData)
        {
            this.dataTable = reportData.Data;
            this.reportTitle = reportData.Title;
            this.reportSubtitle = reportData.Subtitle;
            this.themeColor = reportData.ThemeColor;
            this.summary = reportData.Summary;
            
            InitializeComponent();
            SetupPrintDocument();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.Text = "معاينة الطباعة - " + reportTitle;
            this.StartPosition = FormStartPosition.CenterParent;
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(248, 249, 250);

            // Create toolbar
            CreateToolbar();
            
            // Create print preview
            CreatePrintPreview();
            
            // Create status bar
            CreateStatusBar();
        }

        private void CreateToolbar()
        {
            var toolbar = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Print button
            btnPrint = new Button
            {
                Text = "🖨️ طباعة",
                Location = new Point(10, 10),
                Size = new Size(100, 40),
                BackColor = themeColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.Click += BtnPrint_Click;

            // Page setup button
            btnPageSetup = new Button
            {
                Text = "⚙️ إعداد الصفحة",
                Location = new Point(120, 10),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnPageSetup.FlatAppearance.BorderSize = 0;
            btnPageSetup.Click += BtnPageSetup_Click;

            // Zoom combo
            var lblZoom = new Label
            {
                Text = "التكبير:",
                Location = new Point(260, 20),
                Size = new Size(50, 20),
                Font = new Font("Segoe UI", 9F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            cmbZoom = new ComboBox
            {
                Location = new Point(320, 17),
                Size = new Size(80, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F)
            };
            cmbZoom.Items.AddRange(new object[] { "50%", "75%", "100%", "125%", "150%", "200%" });
            cmbZoom.SelectedIndex = 2; // 100%
            cmbZoom.SelectedIndexChanged += CmbZoom_SelectedIndexChanged;

            // Close button
            btnClose = new Button
            {
                Text = "❌ إغلاق",
                Location = new Point(toolbar.Width - 110, 10),
                Size = new Size(90, 40),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += (s, e) => this.Close();

            toolbar.Controls.AddRange(new Control[] { btnPrint, btnPageSetup, lblZoom, cmbZoom, btnClose });
            this.Controls.Add(toolbar);
        }

        private void CreatePrintPreview()
        {
            printPreviewControl = new PrintPreviewControl
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250),
                UseAntiAlias = true
            };

            this.Controls.Add(printPreviewControl);
        }

        private void CreateStatusBar()
        {
            var statusBar = new Panel
            {
                Height = 30,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(233, 236, 239),
                BorderStyle = BorderStyle.FixedSingle
            };

            lblPageInfo = new Label
            {
                Text = "الصفحة 1 من 1",
                Location = new Point(10, 5),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 9F),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblGeneratedAt = new Label
            {
                Text = $"تم الإنشاء في: {DateTime.Now:yyyy/MM/dd HH:mm:ss}",
                Location = new Point(statusBar.Width - 200, 5),
                Size = new Size(190, 20),
                Font = new Font("Segoe UI", 9F),
                TextAlign = ContentAlignment.MiddleRight,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            statusBar.Controls.AddRange(new Control[] { lblPageInfo, lblGeneratedAt });
            this.Controls.Add(statusBar);
        }

        private void SetupPrintDocument()
        {
            printDocument = new PrintDocument();
            printDocument.PrintPage += PrintDocument_PrintPage;
            printDocument.BeginPrint += (s, e) => { currentRow = 0; pageNumber = 1; };
            
            printPreviewControl.Document = printDocument;
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

            // Fonts
            Font titleFont = new Font("Segoe UI", 18, FontStyle.Bold);
            Font subtitleFont = new Font("Segoe UI", 12, FontStyle.Regular);
            Font headerFont = new Font("Segoe UI", 10, FontStyle.Bold);
            Font bodyFont = new Font("Segoe UI", 9);
            Font summaryFont = new Font("Segoe UI", 10, FontStyle.Bold);

            float yPos = e.MarginBounds.Top;
            float leftMargin = e.MarginBounds.Left;
            float rightMargin = e.MarginBounds.Right;
            float bottomMargin = e.MarginBounds.Bottom;
            float pageWidth = e.MarginBounds.Width;

            // Draw header with theme color
            using (var headerBrush = new SolidBrush(themeColor))
            {
                g.FillRectangle(headerBrush, leftMargin, yPos, pageWidth, 60);
            }

            // Draw title
            using (var titleBrush = new SolidBrush(Color.White))
            {
                var titleSize = g.MeasureString(reportTitle, titleFont);
                g.DrawString(reportTitle, titleFont, titleBrush, 
                    leftMargin + (pageWidth - titleSize.Width) / 2, yPos + 10);
            }

            // Draw subtitle
            if (!string.IsNullOrEmpty(reportSubtitle))
            {
                using (var subtitleBrush = new SolidBrush(Color.White))
                {
                    var subtitleSize = g.MeasureString(reportSubtitle, subtitleFont);
                    g.DrawString(reportSubtitle, subtitleFont, subtitleBrush,
                        leftMargin + (pageWidth - subtitleSize.Width) / 2, yPos + 35);
                }
            }

            yPos += 80;

            // Draw summary if it's the first page
            if (pageNumber == 1 && summary.Any())
            {
                yPos = DrawSummarySection(g, yPos, leftMargin, pageWidth, summaryFont);
                yPos += 20;
            }

            // Calculate column widths
            float[] columnWidths = CalculateColumnWidths(g, headerFont, pageWidth);

            // Draw table header
            yPos = DrawTableHeader(g, yPos, leftMargin, columnWidths, headerFont);

            // Draw table rows
            yPos = DrawTableRows(g, yPos, leftMargin, columnWidths, bodyFont, bottomMargin, e);

            // Draw footer
            DrawFooter(g, e.MarginBounds, bodyFont);

            // Update page info
            this.Invoke((MethodInvoker)delegate {
                lblPageInfo.Text = $"الصفحة {pageNumber} من {totalPages}";
            });
        }

        private float DrawSummarySection(Graphics g, float yPos, float leftMargin, float pageWidth, Font summaryFont)
        {
            var summaryHeight = 80;
            var summaryRect = new RectangleF(leftMargin, yPos, pageWidth, summaryHeight);
            
            // Draw summary background
            using (var summaryBrush = new SolidBrush(Color.FromArgb(248, 249, 250)))
            {
                g.FillRectangle(summaryBrush, summaryRect);
            }
            
            // Draw summary border
            using (var borderPen = new Pen(Color.FromArgb(222, 226, 230), 2))
            {
                g.DrawRectangle(borderPen, Rectangle.Round(summaryRect));
            }

            // Draw summary title
            g.DrawString("ملخص التقرير", summaryFont, Brushes.Black, leftMargin + 10, yPos + 10);

            // Draw summary items
            float summaryY = yPos + 35;
            float itemWidth = pageWidth / Math.Min(summary.Count, 4);
            int itemIndex = 0;

            foreach (var item in summary.Take(4))
            {
                float itemX = leftMargin + (itemIndex * itemWidth) + 10;
                
                g.DrawString(item.Key + ":", new Font("Segoe UI", 9), Brushes.Gray, itemX, summaryY);
                g.DrawString(item.Value.ToString(), summaryFont, Brushes.Black, itemX, summaryY + 20);
                
                itemIndex++;
            }

            return yPos + summaryHeight;
        }

        private float[] CalculateColumnWidths(Graphics g, Font headerFont, float pageWidth)
        {
            float[] columnWidths = new float[dataTable.Columns.Count];
            float totalWidth = 0;

            // Calculate minimum width for each column
            for (int i = 0; i < dataTable.Columns.Count; i++)
            {
                float headerWidth = g.MeasureString(dataTable.Columns[i].ColumnName, headerFont).Width + 20;
                float maxDataWidth = 0;

                // Check first 10 rows for data width
                for (int j = 0; j < Math.Min(10, dataTable.Rows.Count); j++)
                {
                    string cellValue = dataTable.Rows[j][i]?.ToString() ?? "";
                    float dataWidth = g.MeasureString(cellValue, new Font("Segoe UI", 9)).Width + 20;
                    maxDataWidth = Math.Max(maxDataWidth, dataWidth);
                }

                columnWidths[i] = Math.Max(headerWidth, maxDataWidth);
                totalWidth += columnWidths[i];
            }

            // Adjust widths to fit page
            if (totalWidth > pageWidth)
            {
                float scaleFactor = pageWidth / totalWidth;
                for (int i = 0; i < columnWidths.Length; i++)
                {
                    columnWidths[i] *= scaleFactor;
                }
            }

            return columnWidths;
        }

        private float DrawTableHeader(Graphics g, float yPos, float leftMargin, float[] columnWidths, Font headerFont)
        {
            float headerHeight = 35;
            float xPos = leftMargin;

            for (int i = 0; i < dataTable.Columns.Count; i++)
            {
                var headerRect = new RectangleF(xPos, yPos, columnWidths[i], headerHeight);
                
                // Draw header background
                using (var headerBrush = new SolidBrush(Color.FromArgb(233, 236, 239)))
                {
                    g.FillRectangle(headerBrush, headerRect);
                }
                
                // Draw header border
                g.DrawRectangle(Pens.Gray, Rectangle.Round(headerRect));
                
                // Draw header text
                var headerFormat = new StringFormat 
                { 
                    Alignment = StringAlignment.Center, 
                    LineAlignment = StringAlignment.Center 
                };
                g.DrawString(dataTable.Columns[i].ColumnName, headerFont, Brushes.Black, headerRect, headerFormat);
                
                xPos += columnWidths[i];
            }

            return yPos + headerHeight;
        }

        private float DrawTableRows(Graphics g, float yPos, float leftMargin, float[] columnWidths, Font bodyFont, float bottomMargin, PrintPageEventArgs e)
        {
            float rowHeight = 30;

            while (currentRow < dataTable.Rows.Count)
            {
                if (yPos + rowHeight > bottomMargin - 50) // Leave space for footer
                {
                    e.HasMorePages = true;
                    pageNumber++;
                    return yPos;
                }

                DataRow row = dataTable.Rows[currentRow];
                float xPos = leftMargin;

                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    var cellRect = new RectangleF(xPos, yPos, columnWidths[i], rowHeight);
                    
                    // Alternate row colors
                    if (currentRow % 2 == 0)
                    {
                        g.FillRectangle(Brushes.White, cellRect);
                    }
                    else
                    {
                        using (var altBrush = new SolidBrush(Color.FromArgb(248, 249, 250)))
                        {
                            g.FillRectangle(altBrush, cellRect);
                        }
                    }
                    
                    // Draw cell border
                    using (var borderPen = new Pen(Color.FromArgb(222, 226, 230)))
                    {
                        g.DrawRectangle(borderPen, Rectangle.Round(cellRect));
                    }
                    
                    // Draw cell text
                    string cellValue = row[i]?.ToString() ?? "";
                    var cellFormat = new StringFormat 
                    { 
                        Alignment = StringAlignment.Center, 
                        LineAlignment = StringAlignment.Center,
                        Trimming = StringTrimming.EllipsisCharacter
                    };
                    g.DrawString(cellValue, bodyFont, Brushes.Black, cellRect, cellFormat);
                    
                    xPos += columnWidths[i];
                }

                yPos += rowHeight;
                currentRow++;
            }

            e.HasMorePages = false;
            return yPos;
        }

        private void DrawFooter(Graphics g, Rectangle marginBounds, Font bodyFont)
        {
            float footerY = marginBounds.Bottom - 30;
            
            // Draw page number
            string pageText = $"الصفحة {pageNumber}";
            var pageSize = g.MeasureString(pageText, bodyFont);
            g.DrawString(pageText, bodyFont, Brushes.Gray, 
                marginBounds.Right - pageSize.Width, footerY);
            
            // Draw generation time
            string timeText = $"تم الإنشاء في: {DateTime.Now:yyyy/MM/dd HH:mm}";
            g.DrawString(timeText, bodyFont, Brushes.Gray, marginBounds.Left, footerY);
        }

        #region Event Handlers

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            var printDialog = new PrintDialog { Document = printDocument };
            if (printDialog.ShowDialog() == DialogResult.OK)
            {
                printDocument.Print();
            }
        }

        private void BtnPageSetup_Click(object sender, EventArgs e)
        {
            var pageSetupDialog = new PageSetupDialog { Document = printDocument };
            pageSetupDialog.ShowDialog();
            printPreviewControl.InvalidatePreview();
        }

        private void CmbZoom_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbZoom.SelectedItem != null)
            {
                string zoomText = cmbZoom.SelectedItem.ToString().Replace("%", "");
                if (double.TryParse(zoomText, out double zoomValue))
                {
                    printPreviewControl.Zoom = zoomValue / 100.0;
                }
            }
        }

        #endregion
    }
}
