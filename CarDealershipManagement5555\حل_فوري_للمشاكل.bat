@echo off
title حل فوري للمشاكل - Immediate Problem Fix

echo.
echo ========================================
echo    🔧 حل فوري للمشاكل
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🔍 المشكلة:
echo - الإصلاحات موجودة في الكود لكن البرنامج المشغل قديم
echo - نحتاج لتطبيق الإصلاحات مباشرة على قاعدة البيانات
echo.

echo 💾 إنشاء نسخة احتياطية...
if exist "CarDealership_Standalone\CarDealership.db" (
    copy "CarDealership_Standalone\CarDealership.db" "CarDealership_Standalone\CarDealership_Backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%.db" >nul
    echo ✅ تم إنشاء نسخة احتياطية
)

echo.
echo 🔧 تطبيق الإصلاحات على قاعدة البيانات...

REM إنشاء ملف SQL للإصلاحات
echo -- إصلاحات فورية > temp_fix.sql
echo ALTER TABLE SystemSettings ADD COLUMN CompanyEmail TEXT DEFAULT ''; >> temp_fix.sql
echo ALTER TABLE SystemSettings ADD COLUMN CompanyWebsite TEXT DEFAULT ''; >> temp_fix.sql
echo ALTER TABLE UserPermissions ADD COLUMN CanManageSalesReps INTEGER DEFAULT 0; >> temp_fix.sql
echo UPDATE UserPermissions SET CanManageSalesReps = 1 WHERE UserId = 1; >> temp_fix.sql

REM تطبيق الإصلاحات
if exist "CarDealership_Standalone\CarDealership.db" (
    echo تطبيق الإصلاحات...
    cd CarDealership_Standalone
    sqlite3 CarDealership.db < ..\temp_fix.sql
    cd ..
    echo ✅ تم تطبيق الإصلاحات
)

echo.
echo 🚀 تشغيل البرنامج المحدث...
start "" "CarDealership_Standalone\CarDealershipManagement.exe"

echo.
echo ✅ تم تطبيق الحل الفوري
echo.

echo 📝 ما تم إصلاحه:
echo ✅ إضافة حقول إيميل وموقع المعرض
echo ✅ إضافة صلاحية إدارة مندوبي المبيعات
echo.

echo 🔐 بيانات الدخول:
echo 👤 اسم المستخدم: amrali
echo 🔑 كلمة المرور: braa
echo.

echo 📍 مواقع التحديثات:
echo 📧 إيميل المعرض: الإدارة → الإعدادات → معلومات الشركة
echo 👨‍💼 صلاحية المندوبين: إدارة المستخدمين → صلاحيات المدير
echo.

echo 📞 للدعم: 01285626623 - <EMAIL>
echo.

REM تنظيف
del temp_fix.sql >nul 2>&1

pause
