using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CarDealershipManagement.Models
{
public class Sale
{
    [Key]
    public int SaleId
    {
        get;
        set;
    }

    [Required]
    [MaxLength(50)]
    public string CarChassisNumber
    {
        get;
        set;
    } = string.Empty; // ربط بالسيارة

    [Required]
    public int CustomerId
    {
        get;    // ربط بالعميل
        set;
    }

    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal ActualSellPrice
    {
        get;    // سعر البيع الفعلي
        set;
    }

    [Required]
    public PaymentMethod PaymentMethod
    {
        get;    // طريقة الدفع
        set;
    }

    [Required]
    public DateTime SaleDate
    {
        get;
        set;
    } = DateTime.Now; // تاريخ البيع

    [Column(TypeName = "decimal(18,2)")]
    public decimal DownPayment
    {
        get;
        set;
    } = 0; // الدفعة المقدمة (للتقسيط)

    public int NumberOfInstallments
    {
        get;
        set;
    } = 0; // عدد الأقساط

    [Column(TypeName = "decimal(18,2)")]
    public decimal InstallmentAmount
    {
        get;
        set;
    } = 0; // قيمة القسط

    public InstallmentPeriod InstallmentPeriod
    {
        get;
        set;
    } = InstallmentPeriod.Monthly; // المدة بين الأقساط

    public DateTime? FirstInstallmentDate
    {
        get;    // تاريخ أول قسط
        set;
    }

    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalPaid
    {
        get;
        set;
    } = 0; // إجمالي المدفوع

    [Column(TypeName = "decimal(18,2)")]
    public decimal RemainingAmount
    {
        get;
        set;
    } = 0; // المبلغ المتبقي

    public PaymentStatus PaymentStatus
    {
        get;
        set;
    } = PaymentStatus.Pending; // حالة الدفع

    // Operation status and archival fields
    public SaleOperationStatus OperationStatus
    {
        get;
        set;
    } = SaleOperationStatus.Active; // حالة العملية
    public bool IsCompleted
    {
        get;
        set;
    } = false; // العملية مكتملة
    public bool IsArchived
    {
        get;
        set;
    } = false; // مؤرشفة
    public DateTime? CompletedDate
    {
        get;    // تاريخ اكتمال العملية
        set;
    }
    public DateTime? ArchivedDate
    {
        get;    // تاريخ الأرشفة
        set;
    }
    public bool IsApproved
    {
        get;
        set;
    } = false; // عملية معتمدة
    public DateTime? ApprovedDate
    {
        get;    // تاريخ الاعتماد
        set;
    }
    public string? ApprovedBy
    {
        get;    // معتمد من قبل
        set;
    }

    public DateTime CreatedDate
    {
        get;
        set;
    } = DateTime.Now;
    public DateTime? ModifiedDate
    {
        get;
        set;
    }

    // Navigation properties
    [ForeignKey("CarChassisNumber")]
    public virtual Car Car
    {
        get;
        set;
    } = null!;

    [ForeignKey("CustomerId")]
    public virtual Customer Customer
    {
        get;
        set;
    } = null!;

    public virtual ICollection<InstallmentPayment> InstallmentPayments
    {
        get;
        set;
    } = new List<InstallmentPayment>();
}

public enum PaymentMethod
{
    Cash = 1,           // نقدي
    Installment = 2     // تقسيط
}

public enum InstallmentPeriod
{
    Monthly = 1,        // شهري
    Quarterly = 2,      // ربع سنوي
    SixMonths = 3,      // نصف سنوي
    Yearly = 4          // سنوي
}

public enum PaymentStatus
{
    Pending = 1,        // مستحقة
    PartiallyPaid = 2,  // مدفوعة جزئياً
    FullyPaid = 3       // مدفوعة بالكامل
}

public enum SaleOperationStatus
{
    Active = 1,         // نشطة
    Pending = 2,        // معلقة
    Completed = 3,      // مكتملة
    Cancelled = 4,      // ملغاة
    Archived = 5        // مؤرشفة
}
}
