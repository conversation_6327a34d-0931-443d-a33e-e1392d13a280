using System;
using System.Drawing.Printing;
using CarDealershipManagement.Services;

namespace CarDealershipManagement.Services
{
/// <summary>
/// Example utility class showing how to use the enhanced printing service with paper size customization
/// </summary>
public static class PrintingExamples
{
    // Common paper sizes in hundredths of an inch (as required by .NET printing)
    public static class PaperSizes
    {
        public const float A4_WIDTH = 827f;     // 8.27 inches
        public const float A4_HEIGHT = 1169f;   // 11.69 inches

        public const float A5_WIDTH = 583f;     // 5.83 inches
        public const float A5_HEIGHT = 827f;    // 8.27 inches

        public const float LETTER_WIDTH = 850f;  // 8.5 inches
        public const float LETTER_HEIGHT = 1100f; // 11 inches

        public const float LEGAL_WIDTH = 850f;   // 8.5 inches
        public const float LEGAL_HEIGHT = 1400f; // 14 inches

        public const float THERMAL_WIDTH = 300f; // 3 inches (typical receipt printer)
        public const float THERMAL_HEIGHT = 600f; // 6 inches
    }

    /// <summary>
    /// Print customer statement with A4 paper size
    /// </summary>
    /// <param name="customerId">Customer ID to print statement for</param>
    public static void PrintCustomerStatementA4(int customerId)
    {
        PrintingService.PrintCustomerStatement(customerId);
    }

    /// <summary>
    /// Print customer statement with custom paper size
    /// </summary>
    /// <param name="customerId">Customer ID to print statement for</param>
    /// <param name="widthInches">Paper width in inches</param>
    /// <param name="heightInches">Paper height in inches</param>
    public static void PrintCustomerStatementCustom(int customerId, float widthInches, float heightInches)
    {
        // Note: Custom paper sizes are not currently supported by the PrintingService
        // Using default printing for now
        PrintingService.PrintCustomerStatement(customerId);
    }

    /// <summary>
    /// Print car details with A4 paper size
    /// </summary>
    /// <param name="chassisNumber">Chassis number of the car</param>
    public static void PrintCarDetailsA4(string chassisNumber)
    {
        // Note: PrintCarDetails method is not available in the current PrintingService
        // This is a placeholder for when the method is implemented
        throw new NotImplementedException("PrintCarDetails method is not yet implemented in PrintingService");
    }

    /// <summary>
    /// Print car details with thermal receipt paper size
    /// </summary>
    /// <param name="chassisNumber">Chassis number of the car</param>
    public static void PrintCarDetailsThermal(string chassisNumber)
    {
        // Note: PrintCarDetails method is not available in the current PrintingService
        // This is a placeholder for when the method is implemented
        throw new NotImplementedException("PrintCarDetails method is not yet implemented in PrintingService");
    }

    /// <summary>
    /// Print sale invoice with Letter paper size
    /// </summary>
    /// <param name="saleId">Sale ID to print invoice for</param>
    public static void PrintSaleInvoiceLetter(int saleId)
    {
        // Note: PrintSaleInvoice method is not available in the current PrintingService
        // This is a placeholder for when the method is implemented
        throw new NotImplementedException("PrintSaleInvoice method is not yet implemented in PrintingService");
    }

    /// <summary>
    /// Print supplier statement with A5 paper size
    /// </summary>
    /// <param name="supplierId">Supplier ID to print statement for</param>
    public static void PrintSupplierStatementA5(int supplierId)
    {
        PrintingService.PrintSupplierStatement(supplierId);
    }

    /// <summary>
    /// Print sales report with Legal paper size
    /// </summary>
    /// <param name="fromDate">Start date for the report</param>
    /// <param name="toDate">End date for the report</param>
    public static void PrintSalesReportLegal(DateTime fromDate, DateTime toDate)
    {
        PrintingService.PrintSalesReport(fromDate, toDate);
    }

    /// <summary>
    /// Print financial report with A4 paper size
    /// </summary>
    /// <param name="fromDate">Start date for the report</param>
    /// <param name="toDate">End date for the report</param>
    public static void PrintFinancialReportA4(DateTime fromDate, DateTime toDate)
    {
        PrintingService.PrintFinancialReport(fromDate, toDate);
    }

    /// <summary>
    /// Print inventory report with A4 paper size
    /// </summary>
    public static void PrintInventoryReportA4()
    {
        PrintingService.PrintInventoryReport();
    }

    /// <summary>
    /// Helper method to convert millimeters to hundredths of an inch
    /// </summary>
    /// <param name="millimeters">Size in millimeters</param>
    /// <returns>Size in hundredths of an inch</returns>
    public static float MillimetersToHundredthsOfInch(float millimeters)
    {
        return millimeters / 0.254f; // 1 inch = 25.4 mm, so 0.01 inch = 0.254 mm
    }

    /// <summary>
    /// Helper method to convert centimeters to hundredths of an inch
    /// </summary>
    /// <param name="centimeters">Size in centimeters</param>
    /// <returns>Size in hundredths of an inch</returns>
    public static float CentimetersToHundredthsOfInch(float centimeters)
    {
        return centimeters / 0.0254f; // 1 inch = 2.54 cm, so 0.01 inch = 0.0254 cm
    }

    /// <summary>
    /// Example of printing with custom paper size in millimeters
    /// </summary>
    /// <param name="customerId">Customer ID</param>
    /// <param name="widthMM">Paper width in millimeters</param>
    /// <param name="heightMM">Paper height in millimeters</param>
    public static void PrintCustomerStatementCustomMM(int customerId, float widthMM, float heightMM)
    {
        // Note: Custom paper sizes are not currently supported by the PrintingService
        // Using default printing for now
        PrintingService.PrintCustomerStatement(customerId);
    }

    /// <summary>
    /// Example of printing with custom paper size in centimeters
    /// </summary>
    /// <param name="chassisNumber">Chassis number</param>
    /// <param name="widthCM">Paper width in centimeters</param>
    /// <param name="heightCM">Paper height in centimeters</param>
    public static void PrintCarDetailsCustomCM(string chassisNumber, float widthCM, float heightCM)
    {
        // Note: PrintCarDetails method is not available in the current PrintingService
        // This is a placeholder for when the method is implemented
        throw new NotImplementedException("PrintCarDetails method is not yet implemented in PrintingService");
    }
}
}
