using System.Windows.Forms;
using System.Data;
using System.Drawing;
using System.Linq;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Helpers;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace CarDealershipManagement.Forms
{
public partial class ReportsForm : Form
{
    private TabControl tabControl = null!;
    private TabPage tabSalesReports = null!;
    private TabPage tabInventoryReports = null!;
    private TabPage tabCustomerReports = null!;
    private TabPage tabFinancialReports = null!;
    private TabPage tabInstallmentReports = null!;
    private TabPage tabSupplierReports = null!;
    private TabPage tabPerformanceReports = null!;
    private TabPage tabStatisticsReports = null!;

    // Enhanced UI Components
    private Panel pnlHeader = null!;
    private Label lblTitle = null!;
    private Label lblSubtitle = null!;
    private Panel pnlQuickStats = null!;

    // Sales Reports Controls
    private DataGridView dgvSalesReport = null!;
    private DateTimePicker dtpSalesFromDate = null!;
    private DateTimePicker dtpSalesToDate = null!;
    private ComboBox cmbSalesPaymentMethod = null!;
    private Button btnGenerateSalesReport = null!;
    private Button btnPrintSalesReport = null!;
    private Button btnPrintSalesSelected = null!;
    private Button btnPrintSalesSummary = null!;
    private Label lblSalesTotalAmount = null!;
    private Label lblSalesCount = null!;

    // Additional Print Buttons for other reports
    private Button btnPrintInventorySelected = null!;
    private Button btnPrintCustomerSelected = null!;
    private Button btnPrintFinancialSelected = null!;
    private Button btnPrintInstallmentSelected = null!;
    private Button btnPrintSupplierSelected = null!;

    #region Enhanced UI Creation Methods

    /// <summary>
    /// إنشاء رأس الصفحة المحسن
    /// </summary>
    private void CreateEnhancedHeader()
    {
        pnlHeader = new Panel
        {
            Height = 120,
            Dock = DockStyle.Top,
            BackColor = Color.FromArgb(52, 58, 64)
        };

        // Title
        lblTitle = new Label
        {
            Text = "📊 مركز التقارير المتقدم",
            Location = new Point(20, 20),
            Size = new Size(400, 35),
            Font = new Font("Segoe UI", 18F, FontStyle.Bold),
            ForeColor = Color.White,
            BackColor = Color.Transparent
        };

        // Subtitle
        lblSubtitle = new Label
        {
            Text = "تقارير شاملة ومتقدمة لإدارة أعمالك",
            Location = new Point(20, 55),
            Size = new Size(400, 25),
            Font = new Font("Segoe UI", 12F),
            ForeColor = Color.FromArgb(173, 181, 189),
            BackColor = Color.Transparent
        };

        // Quick stats panel
        CreateQuickStatsPanel();

        pnlHeader.Controls.AddRange(new Control[] { lblTitle, lblSubtitle, pnlQuickStats });
        this.Controls.Add(pnlHeader);
    }

    /// <summary>
    /// إنشاء لوحة الإحصائيات السريعة
    /// </summary>
    private async void CreateQuickStatsPanel()
    {
        pnlQuickStats = new Panel
        {
            Location = new Point(this.Width - 400, 10),
            Size = new Size(380, 100),
            BackColor = Color.Transparent,
            Anchor = AnchorStyles.Top | AnchorStyles.Right
        };

        // Load quick stats
        await LoadQuickStats();
    }

    /// <summary>
    /// تحميل الإحصائيات السريعة
    /// </summary>
    private async Task LoadQuickStats()
    {
        try
        {
            using var context = DbContextFactory.CreateContext();

            var totalSales = await context.Sales.CountAsync();
            var totalRevenue = await context.Sales.SumAsync(s => s.ActualSellPrice);
            var availableCars = await context.Cars.CountAsync(c => !c.IsSold);
            var totalCustomers = await context.Customers.CountAsync();

            var stats = new[]
            {
                new { Label = "إجمالي المبيعات", Value = totalSales.ToString(), Color = Color.FromArgb(40, 167, 69) },
                new { Label = "الإيرادات", Value = totalRevenue.ToString("N0") + " ج.م", Color = Color.FromArgb(0, 123, 255) },
                new { Label = "السيارات المتاحة", Value = availableCars.ToString(), Color = Color.FromArgb(255, 193, 7) },
                new { Label = "العملاء", Value = totalCustomers.ToString(), Color = Color.FromArgb(108, 117, 125) }
            };

            int x = 0;
            foreach (var stat in stats)
            {
                var statCard = CreateStatCard(stat.Label, stat.Value, stat.Color);
                statCard.Location = new Point(x, 0);
                pnlQuickStats.Controls.Add(statCard);
                x += 95;
            }
        }
        catch (Exception ex)
        {
            // Handle error silently for now
            Console.WriteLine($"Error loading quick stats: {ex.Message}");
        }
    }

    /// <summary>
    /// إنشاء بطاقة إحصائية
    /// </summary>
    private Panel CreateStatCard(string label, string value, Color color)
    {
        var card = new Panel
        {
            Size = new Size(90, 80),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblValue = new Label
        {
            Text = value,
            Location = new Point(5, 15),
            Size = new Size(80, 25),
            Font = new Font("Segoe UI", 12F, FontStyle.Bold),
            ForeColor = color,
            TextAlign = ContentAlignment.MiddleCenter
        };

        var lblLabel = new Label
        {
            Text = label,
            Location = new Point(5, 45),
            Size = new Size(80, 30),
            Font = new Font("Segoe UI", 8F),
            ForeColor = Color.FromArgb(108, 117, 125),
            TextAlign = ContentAlignment.MiddleCenter
        };

        card.Controls.AddRange(new Control[] { lblValue, lblLabel });
        return card;
    }

    #endregion

    #region Enhanced Styling Methods (Improved)

    /// <summary>
    /// تطبيق تنسيق محسن على DataGridView
    /// </summary>
    private void StyleDataGridView(DataGridView dgv, Color? themeColor = null)
    {
        var theme = themeColor ?? Color.FromArgb(0, 123, 255);
        EnhancedReportService.ApplyEnhancedStyling(dgv, theme);
    }

    #endregion

    /// <summary>
    /// إنشاء زر محسن مع تأثيرات بصرية
    /// </summary>
    private Button CreateStyledButton(string text, Point location, Color backColor, Color? foreColor = null, string icon = "")
    {
        // حساب عرض الزر بناءً على طول النص
        int baseWidth = 120;
        int textLength = text.Length;
        int calculatedWidth = baseWidth + (textLength * 6);
        int buttonWidth = Math.Max(calculatedWidth, 140);

        var displayText = string.IsNullOrEmpty(icon) ? text : $"{icon} {text}";

        var button = new Button
        {
            Text = displayText,
            Location = location,
            Size = new Size(buttonWidth, 40),
            BackColor = backColor,
            ForeColor = foreColor ?? Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9F, FontStyle.Bold),
            UseVisualStyleBackColor = false,
            Cursor = Cursors.Hand,
            TextAlign = ContentAlignment.MiddleCenter
        };

        button.FlatAppearance.BorderSize = 0;
        button.FlatAppearance.BorderColor = backColor;

        // Add hover effects
        button.MouseEnter += (s, e) => {
            button.BackColor = ControlPaint.Light(backColor, 0.1f);
        };
        button.MouseLeave += (s, e) => {
            button.BackColor = backColor;
        };

        return button;
    }

    private void StyleButton(Button btn, Color? backColor = null)
    {
        btn.FlatStyle = FlatStyle.Flat;
        btn.FlatAppearance.BorderSize = 0;
        btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
        btn.ForeColor = Color.White;
        btn.BackColor = backColor ?? Color.FromArgb(0, 123, 255);
        btn.Height = 35;
        btn.Width = 110;
        btn.Cursor = Cursors.Hand;
    }

    private void AddResponsiveLayoutHelper(DataGridView dgv)
    {
        dgv.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
        dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
        dgv.ScrollBars = ScrollBars.Both;
    }


    // Inventory Reports Controls
    private DataGridView dgvInventoryReport = null!;
    private ComboBox cmbInventoryCondition = null!;
    private ComboBox cmbInventoryBrand = null!;
    private Button btnGenerateInventoryReport = null!;
    private Button btnPrintInventoryReport = null!;
    private Label lblTotalCarsCount = null!;
    private Label lblTotalInventoryValue = null!;

    // Customer Reports Controls
    private DataGridView dgvCustomerReport = null!;
    private TextBox txtCustomerSearch = null!;
    private Button btnGenerateCustomerReport = null!;
    private Button btnPrintCustomerReport = null!;
    private Label lblTotalCustomersCount = null!;

    // Financial Reports Controls
    private DataGridView dgvFinancialReport = null!;
    private DateTimePicker dtpFinancialFromDate = null!;
    private DateTimePicker dtpFinancialToDate = null!;
    private Button btnGenerateFinancialReport = null!;
    private Button btnPrintFinancialReport = null!;
    private Label lblTotalProfit = null!;
    private Label lblTotalRevenue = null!;

    // Installment Reports Controls
    private DataGridView dgvInstallmentReport = null!;
    private ComboBox cmbInstallmentStatus = null!;
    private DateTimePicker dtpInstallmentFromDate = null!;
    private DateTimePicker dtpInstallmentToDate = null!;
    private Button btnGenerateInstallmentReport = null!;
    private Button btnPrintInstallmentReport = null!;
    private Label lblTotalInstallmentAmount = null!;
    private Label lblPendingInstallments = null!;

    // Supplier Reports Controls
    private DataGridView dgvSupplierReport = null!;
    private TextBox txtSupplierSearch = null!;
    private Button btnGenerateSupplierReport = null!;
    private Button btnPrintSupplierReport = null!;
    private Label lblTotalSuppliersCount = null!;
    private Label lblTotalSupplierDebt = null!;

    public ReportsForm()
    {
        InitializeComponent();
        LoadInitialData();
    }

    private void InitializeComponent()
    {
        this.Text = "📊 مركز التقارير المتقدم - Car Dealership Reports";
        this.WindowState = FormWindowState.Maximized;
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(248, 249, 250);
        this.Font = new Font("Segoe UI", 10F);
        this.Icon = SystemIcons.Application;
        this.MaximizeBox = true;
        this.MinimizeBox = true;
        this.FormBorderStyle = FormBorderStyle.Sizable;
        this.MinimumSize = new Size(1400, 800);

        // تطبيق إعدادات النافذة المرنة للشاشة الكاملة
        ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this,
            Screen.PrimaryScreen.WorkingArea.Width,
            Screen.PrimaryScreen.WorkingArea.Height);

        // Create enhanced header
        CreateEnhancedHeader();

        // Header Panel - matching InventoryForm style
        var headerPanel = new Panel
        {
            Size = new Size(1200, 80),
            Location = new Point(0, 0),
            BackColor = Color.FromArgb(248, 249, 250),
            Dock = DockStyle.Top,
            Padding = new Padding(10)
        };

        // Add logo placeholder
        var pbLogo = new PictureBox
        {
            Location = new Point(20, 10),
            Size = new Size(60, 60),
            SizeMode = PictureBoxSizeMode.Zoom,
            BorderStyle = BorderStyle.FixedSingle,
            BackColor = Color.LightGray
        };

        // Title Label - matching InventoryForm style
        var headerLabel = new Label
        {
            Text = "نظام التقارير",
            Location = new Point(100, 25),
            Size = new Size(200, 30),
            Font = new Font("Segoe UI", 14F, FontStyle.Bold),
            ForeColor = Color.FromArgb(0, 102, 204),
            BackColor = Color.Transparent
        };

        headerPanel.Controls.AddRange(new Control[] { pbLogo, headerLabel });

        // Tab Control
        tabControl = new TabControl
        {
            Size = new Size(1180, 680),
            Location = new Point(10, 90),
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
        };

        // Create tabs
        CreateSalesReportsTab();
        CreateInventoryReportsTab();
        CreateCustomerReportsTab();
        CreateFinancialReportsTab();
        CreateInstallmentReportsTab();
        CreateSupplierReportsTab();
        CreatePerformanceReportsTab();
        CreateStatisticsReportsTab();

        // Add controls to form
        this.Controls.AddRange(new Control[] { headerPanel, tabControl });
    }

    private void CreateSalesReportsTab()
    {
        tabSalesReports = new TabPage("تقارير المبيعات")
        {
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Filter Panel
        var filterPanel = new Panel
        {
            Size = new Size(1150, 120),
            Location = new Point(10, 10),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblFromDate = new Label { Text = "من تاريخ:", Location = new Point(20, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        dtpSalesFromDate = new DateTimePicker { Location = new Point(100, 18), Size = new Size(150, 23), Format = DateTimePickerFormat.Short };
        dtpSalesFromDate.Value = DateTime.Now.AddMonths(-1);

        var lblToDate = new Label { Text = "إلى تاريخ:", Location = new Point(270, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        dtpSalesToDate = new DateTimePicker { Location = new Point(340, 18), Size = new Size(150, 23), Format = DateTimePickerFormat.Short };

        var lblPaymentMethod = new Label { Text = "طريقة الدفع:", Location = new Point(510, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        cmbSalesPaymentMethod = new ComboBox { Location = new Point(590, 18), Size = new Size(120, 23), DropDownStyle = ComboBoxStyle.DropDownList };
        cmbSalesPaymentMethod.Items.AddRange(new[] { "الكل", "نقدي", "تقسيط" });
        cmbSalesPaymentMethod.SelectedIndex = 0;

        // Summary labels
        lblSalesTotalAmount = new Label { Text = "إجمالي المبيعات: 0", Location = new Point(20, 50), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(40, 167, 69) };
        lblSalesCount = new Label { Text = "عدد المبيعات: 0", Location = new Point(200, 50), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(0, 123, 255) };

        // Enhanced buttons with icons and improved styling
        btnGenerateSalesReport = CreateStyledButton("إنشاء التقرير", new Point(20, 80),
            EnhancedReportService.ReportDefinitions[EnhancedReportService.ReportType.SalesReport].ThemeColor, null, "📊");
        btnGenerateSalesReport.Click += BtnGenerateSalesReport_Click;

        btnPrintSalesReport = CreateStyledButton("طباعة محسنة", new Point(180, 80), Color.FromArgb(111, 66, 193), null, "🖨️");
        btnPrintSalesReport.Click += (sender, e) => BtnEnhancedPrint_Click(EnhancedReportService.ReportType.SalesReport);

        btnPrintSalesSelected = CreateStyledButton("طباعة المحدد", new Point(340, 80), Color.FromArgb(220, 53, 69), null, "📄");
        btnPrintSalesSelected.Click += (sender, e) => BtnPrintSelectedRows_Click(dgvSalesReport, "تقرير المبيعات المحددة");

        btnPrintSalesSummary = CreateStyledButton("ملخص المبيعات", new Point(520, 80), Color.FromArgb(40, 167, 69), null, "📈");
        btnPrintSalesSummary.Click += (sender, e) => BtnPrintSummary_Click("ملخص المبيعات", lblSalesTotalAmount.Text, lblSalesCount.Text);

        // Add export button
        var btnExportSales = CreateStyledButton("تصدير", new Point(700, 80), Color.FromArgb(23, 162, 184), null, "📤");
        btnExportSales.Click += (sender, e) => BtnExportReport_Click(EnhancedReportService.ReportType.SalesReport);

        filterPanel.Controls.AddRange(new Control[]
        {
            lblFromDate, dtpSalesFromDate, lblToDate, dtpSalesToDate, lblPaymentMethod, cmbSalesPaymentMethod,
            lblSalesTotalAmount, lblSalesCount,
            btnGenerateSalesReport, btnPrintSalesReport, btnPrintSalesSelected, btnPrintSalesSummary, btnExportSales
        });

        // DataGridView - نفس تنسيق InventoryForm
        dgvSalesReport = new DataGridView
        {
            Location = new Point(10, 140),
            Size = new Size(1150, 500),
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.Fixed3D,
            Font = new Font("Segoe UI", 9F)
        };
        StyleDataGridView(dgvSalesReport);
        SetupSalesReportDataGridView();

        tabSalesReports.Controls.AddRange(new Control[] { filterPanel, dgvSalesReport });
        tabControl.TabPages.Add(tabSalesReports);
    }

    private void CreateInventoryReportsTab()
    {
        tabInventoryReports = new TabPage("تقارير المخزون")
        {
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Filter Panel
        var filterPanel = new Panel
        {
            Size = new Size(1150, 120),
            Location = new Point(10, 10),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblCondition = new Label { Text = "الحالة:", Location = new Point(20, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        cmbInventoryCondition = new ComboBox { Location = new Point(80, 18), Size = new Size(120, 23), DropDownStyle = ComboBoxStyle.DropDownList };
        cmbInventoryCondition.Items.AddRange(new[] { "الكل", "جديدة", "مستعملة" });
        cmbInventoryCondition.SelectedIndex = 0;

        var lblBrand = new Label { Text = "الماركة:", Location = new Point(220, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        cmbInventoryBrand = new ComboBox { Location = new Point(280, 18), Size = new Size(150, 23), DropDownStyle = ComboBoxStyle.DropDownList };

        // Summary labels
        lblTotalCarsCount = new Label { Text = "إجمالي السيارات: 0", Location = new Point(20, 50), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(0, 123, 255) };
        lblTotalInventoryValue = new Label { Text = "قيمة المخزون: 0", Location = new Point(200, 50), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(40, 167, 69) };

        // Buttons row - محسن للشاشة الكاملة
        btnGenerateInventoryReport = CreateStyledButton("📦 إنشاء التقرير", new Point(20, 80), Color.FromArgb(0, 123, 255));
        btnGenerateInventoryReport.Click += BtnGenerateInventoryReport_Click;

        btnPrintInventoryReport = CreateStyledButton("🖨️ طباعة", new Point(180, 80), Color.FromArgb(75, 0, 130));
        btnPrintInventoryReport.Click += (sender, e) => BtnPrintReport_Click(dgvInventoryReport, "تقرير المخزون");

        // إضافة أزرار إضافية للطباعة المتخصصة
        btnPrintInventorySelected = CreateStyledButton("📄 طباعة المحدد", new Point(340, 80), Color.FromArgb(220, 53, 69));
        btnPrintInventorySelected.Click += (sender, e) => BtnPrintSelectedRows_Click(dgvInventoryReport, "تقرير المخزون المحدد");

        var btnInventorySummary = CreateStyledButton("📊 ملخص المخزون", new Point(520, 80), Color.FromArgb(40, 167, 69));
        btnInventorySummary.Click += (sender, e) => BtnPrintSummary_Click("ملخص المخزون", lblTotalCarsCount.Text, lblTotalInventoryValue.Text);

        filterPanel.Controls.AddRange(new Control[]
        {
            lblCondition, cmbInventoryCondition, lblBrand, cmbInventoryBrand,
            lblTotalCarsCount, lblTotalInventoryValue,
            btnGenerateInventoryReport, btnPrintInventoryReport, btnPrintInventorySelected, btnInventorySummary
        });

        // DataGridView - نفس تنسيق InventoryForm
        dgvInventoryReport = new DataGridView
        {
            Location = new Point(10, 140),
            Size = new Size(1150, 500),
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.Fixed3D,
            Font = new Font("Segoe UI", 9F)
        };
        StyleDataGridView(dgvInventoryReport);
        SetupInventoryReportDataGridView();

        tabInventoryReports.Controls.AddRange(new Control[] { filterPanel, dgvInventoryReport });
        tabControl.TabPages.Add(tabInventoryReports);
    }

    private void CreateCustomerReportsTab()
    {
        tabCustomerReports = new TabPage("تقارير العملاء")
        {
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Filter Panel
        var filterPanel = new Panel
        {
            Size = new Size(1150, 120),
            Location = new Point(10, 10),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblSearch = new Label { Text = "البحث:", Location = new Point(20, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        txtCustomerSearch = new TextBox { Location = new Point(80, 18), Size = new Size(250, 23), PlaceholderText = "اسم العميل أو رقم الهوية" };

        // Summary labels
        var lblTotalCustomersCount = new Label { Text = "إجمالي العملاء: 0", Location = new Point(20, 60), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(0, 123, 255) };

        // Buttons row - تحسين المسافات والتناسق
        btnGenerateCustomerReport = CreateStyledButton("إنشاء التقرير", new Point(20, 90), Color.FromArgb(0, 123, 255));
        btnGenerateCustomerReport.Click += BtnGenerateCustomerReport_Click;

        btnPrintCustomerReport = CreateStyledButton("🖨️ طباعة", new Point(150, 90), Color.FromArgb(75, 0, 130));
        btnPrintCustomerReport.Click += (sender, e) => BtnPrintReport_Click(dgvCustomerReport, "تقرير العملاء");

        // إضافة أزرار إضافية للطباعة المتخصصة
        btnPrintCustomerSelected = CreateStyledButton("طباعة المحدد", new Point(280, 90), Color.FromArgb(220, 53, 69));
        btnPrintCustomerSelected.Click += (sender, e) => BtnPrintSelectedRows_Click(dgvCustomerReport, "تقرير العملاء المحددين");

        var btnCustomerSummary = CreateStyledButton("ملخص العملاء", new Point(420, 90), Color.FromArgb(40, 167, 69));
        btnCustomerSummary.Click += (sender, e) => BtnPrintSummary_Click("ملخص العملاء", lblTotalCustomersCount.Text, "عدد العملاء النشطين");

        filterPanel.Controls.AddRange(new Control[]
        {
            lblSearch, txtCustomerSearch, lblTotalCustomersCount,
            btnGenerateCustomerReport, btnPrintCustomerReport, btnPrintCustomerSelected, btnCustomerSummary
        });

        // DataGridView
        dgvCustomerReport = new DataGridView
        {
            Location = new Point(10, 140),
            Size = new Size(1150, 500),
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
        };
        StyleDataGridView(dgvCustomerReport);

        SetupCustomerReportDataGridView();

        tabCustomerReports.Controls.AddRange(new Control[] { filterPanel, dgvCustomerReport });
        tabControl.TabPages.Add(tabCustomerReports);
    }

    private void CreateFinancialReportsTab()
    {
        tabFinancialReports = new TabPage("التقارير المالية")
        {
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Filter Panel
        var filterPanel = new Panel
        {
            Size = new Size(1150, 120),
            Location = new Point(10, 10),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblFromDate = new Label { Text = "من تاريخ:", Location = new Point(20, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        dtpFinancialFromDate = new DateTimePicker { Location = new Point(100, 18), Size = new Size(150, 23), Format = DateTimePickerFormat.Short };
        dtpFinancialFromDate.Value = DateTime.Now.AddMonths(-1);

        var lblToDate = new Label { Text = "إلى تاريخ:", Location = new Point(270, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        dtpFinancialToDate = new DateTimePicker { Location = new Point(340, 18), Size = new Size(150, 23), Format = DateTimePickerFormat.Short };

        // Summary labels
        lblTotalRevenue = new Label { Text = "إجمالي الإيرادات: 0", Location = new Point(20, 60), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(40, 167, 69) };
        lblTotalProfit = new Label { Text = "إجمالي الربح: 0", Location = new Point(200, 60), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(0, 123, 255) };

        // Buttons row - تحسين المسافات والتناسق
        btnGenerateFinancialReport = CreateStyledButton("إنشاء التقرير", new Point(20, 90), Color.FromArgb(0, 123, 255));
        btnGenerateFinancialReport.Click += BtnGenerateFinancialReport_Click;

        btnPrintFinancialReport = CreateStyledButton("🖨️ طباعة", new Point(150, 90), Color.FromArgb(75, 0, 130));
        btnPrintFinancialReport.Click += (sender, e) => BtnPrintReport_Click(dgvFinancialReport, "التقرير المالي");

        // إضافة أزرار إضافية للطباعة المتخصصة
        btnPrintFinancialSelected = CreateStyledButton("طباعة المحدد", new Point(280, 90), Color.FromArgb(220, 53, 69));
        btnPrintFinancialSelected.Click += (sender, e) => BtnPrintSelectedRows_Click(dgvFinancialReport, "التقرير المالي المحدد");

        var btnFinancialSummary = CreateStyledButton("ملخص مالي", new Point(420, 90), Color.FromArgb(40, 167, 69));
        btnFinancialSummary.Click += (sender, e) => BtnPrintSummary_Click("الملخص المالي", lblTotalRevenue.Text, lblTotalProfit.Text);

        filterPanel.Controls.AddRange(new Control[]
        {
            lblFromDate, dtpFinancialFromDate, lblToDate, dtpFinancialToDate,
            lblTotalRevenue, lblTotalProfit,
            btnGenerateFinancialReport, btnPrintFinancialReport, btnPrintFinancialSelected, btnFinancialSummary
        });

        // DataGridView
        dgvFinancialReport = new DataGridView
        {
            Location = new Point(10, 140),
            Size = new Size(1150, 500),
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
        };
        StyleDataGridView(dgvFinancialReport);

        SetupFinancialReportDataGridView();

        tabFinancialReports.Controls.AddRange(new Control[] { filterPanel, dgvFinancialReport });
        tabControl.TabPages.Add(tabFinancialReports);
    }

    private void CreateInstallmentReportsTab()
    {
        tabInstallmentReports = new TabPage("📅 تقارير الأقساط")
        {
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Filter Panel
        var filterPanel = new Panel
        {
            Size = new Size(1150, 120),
            Location = new Point(10, 10),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblStatus = new Label { Text = "الحالة:", Location = new Point(20, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        cmbInstallmentStatus = new ComboBox { Location = new Point(80, 18), Size = new Size(120, 23), DropDownStyle = ComboBoxStyle.DropDownList };
        cmbInstallmentStatus.Items.AddRange(new[] { "الكل", "مستحق", "مدفوع", "متأخر", "مدفوع جزئياً", "مجدول" });
        cmbInstallmentStatus.SelectedIndex = 0;

        var lblFromDate = new Label { Text = "من تاريخ:", Location = new Point(220, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        dtpInstallmentFromDate = new DateTimePicker { Location = new Point(300, 18), Size = new Size(150, 23), Format = DateTimePickerFormat.Short };
        dtpInstallmentFromDate.Value = DateTime.Now.AddMonths(-1);

        var lblToDate = new Label { Text = "إلى تاريخ:", Location = new Point(470, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        dtpInstallmentToDate = new DateTimePicker { Location = new Point(540, 18), Size = new Size(150, 23), Format = DateTimePickerFormat.Short };

        // Summary labels
        lblTotalInstallmentAmount = new Label { Text = "إجمالي الأقساط: 0", Location = new Point(20, 60), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(40, 167, 69) };
        lblPendingInstallments = new Label { Text = "الأقساط المستحقة: 0", Location = new Point(200, 60), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(220, 53, 69) };

        // Buttons row - تحسين المسافات والتناسق
        btnGenerateInstallmentReport = CreateStyledButton("إنشاء التقرير", new Point(20, 90), Color.FromArgb(0, 123, 255));
        btnGenerateInstallmentReport.Click += BtnGenerateInstallmentReport_Click;

        btnPrintInstallmentReport = CreateStyledButton("طباعة محسنة", new Point(150, 90), Color.FromArgb(111, 66, 193), null, "🖨️");
        btnPrintInstallmentReport.Click += (sender, e) => BtnEnhancedPrint_Click(EnhancedReportService.ReportType.InstallmentReport);

        // إضافة أزرار إضافية للطباعة المتخصصة
        btnPrintInstallmentSelected = CreateStyledButton("طباعة المحدد", new Point(280, 90), Color.FromArgb(220, 53, 69), null, "📄");
        btnPrintInstallmentSelected.Click += (sender, e) => BtnPrintSelectedRows_Click(dgvInstallmentReport, "تقرير التقسيط المحدد");

        var btnInstallmentSummary = CreateStyledButton("ملخص التقسيط", new Point(420, 90), Color.FromArgb(40, 167, 69), null, "📈");
        btnInstallmentSummary.Click += (sender, e) => ShowInstallmentSummaryDialog();

        // Add export button
        var btnExportInstallments = CreateStyledButton("تصدير", new Point(560, 90), Color.FromArgb(23, 162, 184), null, "📤");
        btnExportInstallments.Click += (sender, e) => BtnExportReport_Click(EnhancedReportService.ReportType.InstallmentReport);

        filterPanel.Controls.AddRange(new Control[]
        {
            lblStatus, cmbInstallmentStatus, lblFromDate, dtpInstallmentFromDate, lblToDate, dtpInstallmentToDate,
            lblTotalInstallmentAmount, lblPendingInstallments,
            btnGenerateInstallmentReport, btnPrintInstallmentReport, btnPrintInstallmentSelected, btnInstallmentSummary, btnExportInstallments
        });

        // DataGridView
        dgvInstallmentReport = new DataGridView
        {
            Location = new Point(10, 140),
            Size = new Size(1150, 500),
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
        };
        StyleDataGridView(dgvInstallmentReport);

        SetupInstallmentReportDataGridView();

        tabInstallmentReports.Controls.AddRange(new Control[] { filterPanel, dgvInstallmentReport });
        tabControl.TabPages.Add(tabInstallmentReports);
    }

    private void CreateSupplierReportsTab()
    {
        tabSupplierReports = new TabPage("تقارير الموردين")
        {
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Filter Panel
        var filterPanel = new Panel
        {
            Size = new Size(1150, 120),
            Location = new Point(10, 10),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblSearch = new Label { Text = "البحث:", Location = new Point(20, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        txtSupplierSearch = new TextBox { Location = new Point(80, 18), Size = new Size(250, 23), PlaceholderText = "اسم المورد أو رقم الهاتف" };

        // Summary labels
        lblTotalSuppliersCount = new Label { Text = "إجمالي الموردين: 0", Location = new Point(20, 60), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(0, 123, 255) };
        lblTotalSupplierDebt = new Label { Text = "إجمالي المستحقات: 0", Location = new Point(200, 60), Font = new Font("Segoe UI", 9, FontStyle.Bold), ForeColor = Color.FromArgb(220, 53, 69) };

        // Buttons row - تحسين المسافات والتناسق
        btnGenerateSupplierReport = CreateStyledButton("إنشاء التقرير", new Point(20, 90), Color.FromArgb(0, 123, 255));
        btnGenerateSupplierReport.Click += (sender, e) => BtnGenerateSupplierReport_Click(sender, e, txtSupplierSearch);

        btnPrintSupplierReport = CreateStyledButton("🖨️ طباعة", new Point(150, 90), Color.FromArgb(75, 0, 130));
        btnPrintSupplierReport.Click += (sender, e) => BtnPrintReport_Click(dgvSupplierReport, "تقرير الموردين");

        // إضافة أزرار إضافية للطباعة المتخصصة
        btnPrintSupplierSelected = CreateStyledButton("طباعة المحدد", new Point(280, 90), Color.FromArgb(220, 53, 69));
        btnPrintSupplierSelected.Click += (sender, e) => BtnPrintSelectedRows_Click(dgvSupplierReport, "تقرير الموردين المحددين");

        var btnSupplierSummary = CreateStyledButton("ملخص الموردين", new Point(420, 90), Color.FromArgb(40, 167, 69));
        btnSupplierSummary.Click += (sender, e) => BtnPrintSummary_Click("ملخص الموردين", lblTotalSuppliersCount.Text, lblTotalSupplierDebt.Text);

        filterPanel.Controls.AddRange(new Control[]
        {
            lblSearch, txtSupplierSearch,
            lblTotalSuppliersCount, lblTotalSupplierDebt,
            btnGenerateSupplierReport, btnPrintSupplierReport, btnPrintSupplierSelected, btnSupplierSummary
        });

        // DataGridView
        dgvSupplierReport = new DataGridView
        {
            Location = new Point(10, 140),
            Size = new Size(1150, 500),
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
        };
        StyleDataGridView(dgvSupplierReport);

        SetupSupplierReportDataGridView(dgvSupplierReport);

        tabSupplierReports.Controls.AddRange(new Control[] { filterPanel, dgvSupplierReport });
        tabControl.TabPages.Add(tabSupplierReports);
    }

    private void SetupSalesReportDataGridView()
    {
        dgvSalesReport.Columns.Clear();
        dgvSalesReport.Columns.Add("SaleId", "رقم البيع");
        dgvSalesReport.Columns.Add("SaleDate", "تاريخ البيع");
        dgvSalesReport.Columns.Add("CustomerName", "اسم العميل");
        dgvSalesReport.Columns.Add("CarInfo", "معلومات السيارة");
        dgvSalesReport.Columns.Add("PaymentMethod", "طريقة الدفع");
        dgvSalesReport.Columns.Add("SellPrice", "سعر البيع");
        dgvSalesReport.Columns.Add("PaidAmount", "المبلغ المدفوع");
        dgvSalesReport.Columns.Add("RemainingAmount", "المبلغ المتبقي");
        dgvSalesReport.Columns.Add("PaymentStatus", "حالة الدفع");
    }

    private void SetupInventoryReportDataGridView()
    {
        dgvInventoryReport.Columns.Clear();
        dgvInventoryReport.Columns.Add("ChassisNumber", "رقم الشاسيه");
        dgvInventoryReport.Columns.Add("Brand", "الماركة");
        dgvInventoryReport.Columns.Add("Model", "الموديل");
        dgvInventoryReport.Columns.Add("Year", "السنة");
        dgvInventoryReport.Columns.Add("Color", "اللون");
        dgvInventoryReport.Columns.Add("Condition", "الحالة");
        dgvInventoryReport.Columns.Add("PurchasePrice", "سعر الشراء");
        dgvInventoryReport.Columns.Add("CashPrice", "سعر البيع نقداً");
        dgvInventoryReport.Columns.Add("InstallmentPrice", "سعر البيع تقسيط");
        dgvInventoryReport.Columns.Add("Status", "الحالة");
    }

    private void SetupCustomerReportDataGridView()
    {
        dgvCustomerReport.Columns.Clear();
        dgvCustomerReport.Columns.Add("CustomerId", "رقم العميل");
        dgvCustomerReport.Columns.Add("FullName", "الاسم الكامل");
        dgvCustomerReport.Columns.Add("IdNumber", "رقم الهوية");
        dgvCustomerReport.Columns.Add("Phone", "الهاتف");
        dgvCustomerReport.Columns.Add("Email", "البريد الإلكتروني");
        dgvCustomerReport.Columns.Add("PurchaseCount", "عدد المشتريات");
        dgvCustomerReport.Columns.Add("TotalPurchaseAmount", "إجمالي المشتريات");
        dgvCustomerReport.Columns.Add("LastPurchaseDate", "تاريخ آخر شراء");
    }

    private void SetupFinancialReportDataGridView()
    {
        dgvFinancialReport.Columns.Clear();
        dgvFinancialReport.Columns.Add("Date", "التاريخ");
        dgvFinancialReport.Columns.Add("Description", "الوصف");
        dgvFinancialReport.Columns.Add("Revenue", "الإيرادات");
        dgvFinancialReport.Columns.Add("Cost", "التكلفة");
        dgvFinancialReport.Columns.Add("Profit", "الربح");
        dgvFinancialReport.Columns.Add("PaymentMethod", "طريقة الدفع");
    }

    private void SetupInstallmentReportDataGridView()
    {
        dgvInstallmentReport.Columns.Clear();
        dgvInstallmentReport.Columns.Add("SaleId", "رقم البيع");
        dgvInstallmentReport.Columns.Add("CustomerName", "اسم العميل");
        dgvInstallmentReport.Columns.Add("CarInfo", "معلومات السيارة");
        dgvInstallmentReport.Columns.Add("InstallmentNumber", "رقم القسط");
        dgvInstallmentReport.Columns.Add("DueDate", "تاريخ الاستحقاق");
        dgvInstallmentReport.Columns.Add("PaidDate", "تاريخ الدفع");
        dgvInstallmentReport.Columns.Add("InstallmentAmount", "قيمة القسط");
        dgvInstallmentReport.Columns.Add("Status", "الحالة");
        dgvInstallmentReport.Columns.Add("ReceivedByUser", "تم التحصيل بواسطة");
    }

    private void SetupSupplierReportDataGridView(DataGridView dgvSupplierReport)
    {
        dgvSupplierReport.Columns.Clear();
        dgvSupplierReport.Columns.Add("SupplierId", "رقم المورد");
        dgvSupplierReport.Columns.Add("SupplierName", "اسم المورد");
        dgvSupplierReport.Columns.Add("ResponsiblePerson", "الشخص المسؤول");
        dgvSupplierReport.Columns.Add("Phone", "الهاتف");
        dgvSupplierReport.Columns.Add("Email", "البريد الإلكتروني");
        dgvSupplierReport.Columns.Add("TotalOwed", "إجمالي المستحقات");
        dgvSupplierReport.Columns.Add("TotalPaid", "إجمالي المدفوعات");
        dgvSupplierReport.Columns.Add("Balance", "الرصيد");
    }

    private async void LoadInitialData()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            // Load brands for inventory filter
            var brands = await context.Cars.Select(c => c.Brand).Distinct().ToListAsync();
            cmbInventoryBrand.Items.Add("الكل");
            cmbInventoryBrand.Items.AddRange(brands.ToArray());
            cmbInventoryBrand.SelectedIndex = 0;
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    // Event handlers - placeholder implementations
    private async void BtnGenerateSalesReport_Click(object? sender, EventArgs e)
    {
        try
        {
            btnGenerateSalesReport.Enabled = false;
            btnGenerateSalesReport.Text = "جاري الإنشاء...";

            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            // Get filter values
            var fromDate = dtpSalesFromDate.Value.Date;
            var toDate = dtpSalesToDate.Value.Date.AddDays(1).AddTicks(-1); // End of day
            var paymentMethodFilter = cmbSalesPaymentMethod.SelectedItem?.ToString();

            // Build query
            var query = context.Sales
                        .Include(s => s.Customer)
                        .Include(s => s.Car)
                        .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate);

            // Apply payment method filter
            if(paymentMethodFilter != "الكل")
            {
                var paymentMethod = paymentMethodFilter == "نقدي" ? PaymentMethod.Cash : PaymentMethod.Installment;
                query = query.Where(s => s.PaymentMethod == paymentMethod);
            }

            var salesData = await query.OrderByDescending(s => s.SaleDate).ToListAsync();

            // Clear existing data
            dgvSalesReport.Rows.Clear();

            // Populate DataGridView
            decimal totalAmount = 0;
            int salesCount = 0;

            foreach(var sale in salesData)
            {
                var carInfo = $"{sale.Car.Brand} {sale.Car.Model} ({sale.Car.Year})";
                var paymentMethodText = sale.PaymentMethod == PaymentMethod.Cash ? "نقدي" : "تقسيط";
                var paymentStatus = GetPaymentStatus(sale);

                dgvSalesReport.Rows.Add(
                    sale.SaleId,
                    sale.SaleDate.ToString("yyyy-MM-dd"),
                    sale.Customer.FullName,
                    carInfo,
                    paymentMethodText,
                    sale.ActualSellPrice.ToString("N0"),
                    sale.TotalPaid.ToString("N0"),
                    (sale.ActualSellPrice - sale.TotalPaid).ToString("N0"),
                    paymentStatus
                );

                totalAmount += sale.ActualSellPrice;
                salesCount++;
            }

            // Update summary labels
            lblSalesTotalAmount.Text = $"إجمالي المبيعات: {totalAmount:N0}";
            lblSalesCount.Text = $"عدد المبيعات: {salesCount}";

            MessageBox.Show($"تم إنشاء تقرير المبيعات بنجاح\nعدد المبيعات: {salesCount}\nإجمالي المبيعات: {totalAmount:N0}",
                            "تم بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير المبيعات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            btnGenerateSalesReport.Enabled = true;
            btnGenerateSalesReport.Text = "إنشاء التقرير";
        }
    }

    private async void BtnGenerateInventoryReport_Click(object? sender, EventArgs e)
    {
        try
        {
            btnGenerateInventoryReport.Enabled = false;
            btnGenerateInventoryReport.Text = "جاري الإنشاء...";

            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            // Get filter values
            var conditionFilter = cmbInventoryCondition.SelectedItem?.ToString();
            var brandFilter = cmbInventoryBrand.SelectedItem?.ToString();

            // Build query
            var query = context.Cars.AsQueryable();

            // Apply condition filter
            if(conditionFilter != "الكل")
            {
                var condition = conditionFilter == "مستعملة" ? CarCondition.Used : CarCondition.New;
                query = query.Where(c => c.Condition == condition);
            }

            // Apply brand filter
            if(brandFilter != "الكل")
            {
                query = query.Where(c => c.Brand == brandFilter);
            }

            var inventoryData = await query.OrderBy(c => c.Brand).ThenBy(c => c.Model).ToListAsync();

            // Clear existing data
            dgvInventoryReport.Rows.Clear();

            // Populate DataGridView
            decimal totalInventoryValue = 0;
            int totalCount = 0;

            foreach(var car in inventoryData)
            {
                var conditionText = car.Condition == CarCondition.New ? "جديد" : "مستعمل";
                var statusText = !car.IsSold ? "متوفر" : "مباع";

                // Only count available cars in inventory value
                if(!car.IsSold)
                {
                    totalInventoryValue += car.PurchasePrice;
                }

                dgvInventoryReport.Rows.Add(
                    car.ChassisNumber,
                    car.Brand,
                    car.Model,
                    car.Year.ToString(),
                    car.Color,
                    conditionText,
                    car.PurchasePrice.ToString("N0"),
                    car.SuggestedSellPrice.ToString("N0"),
                    car.InstallmentSellPrice.ToString("N0"),
                    statusText
                );

                totalCount++;
            }

            // Update summary labels
            lblTotalInventoryValue.Text = $"قيمة المخزون: {totalInventoryValue:N0}";
            lblTotalCarsCount.Text = $"إجمالي السيارات: {totalCount}";

            MessageBox.Show($"تم إنشاء تقرير المخزون بنجاح\nعدد السيارات: {totalCount}\nإجمالي قيمة المخزون: {totalInventoryValue:N0}",
                            "تم بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير المخزون: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            btnGenerateInventoryReport.Enabled = true;
            btnGenerateInventoryReport.Text = "إنشاء التقرير";
        }
    }

    private async void BtnGenerateCustomerReport_Click(object? sender, EventArgs e)
    {
        try
        {
            btnGenerateCustomerReport.Enabled = false;
            btnGenerateCustomerReport.Text = "جاري الإنشاء...";

            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            // Get search term
            var searchTerm = txtCustomerSearch.Text.Trim();

            // Build query
            var query = context.Customers.AsQueryable();

            // Apply search filter
            if(!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(c =>
                                    c.FullName.Contains(searchTerm) ||
                                    c.IdNumber.Contains(searchTerm) ||
                                    c.PrimaryPhone.Contains(searchTerm) ||
                                    c.Email.Contains(searchTerm));
            }

            var customersData = await query.OrderBy(c => c.FullName).ToListAsync();

            // Clear existing data
            dgvCustomerReport.Rows.Clear();

            // Populate DataGridView with customer purchase statistics
            int totalCustomers = 0;

            foreach(var customer in customersData)
            {
                // Get customer purchase statistics
                var customerSales = await context.Sales
                                    .Where(s => s.CustomerId == customer.CustomerId)
                                    .ToListAsync();

                var purchaseCount = customerSales.Count;
                var totalPurchaseAmount = customerSales.Sum(s => s.ActualSellPrice);
                var lastPurchaseDate = customerSales.OrderByDescending(s => s.SaleDate)
                                       .FirstOrDefault()?.SaleDate;

                dgvCustomerReport.Rows.Add(
                    customer.CustomerId,
                    customer.FullName,
                    customer.IdNumber,
                    customer.PrimaryPhone,
                    customer.Email ?? "",
                    purchaseCount,
                    totalPurchaseAmount.ToString("N0"),
                    lastPurchaseDate?.ToString("yyyy-MM-dd") ?? "لا يوجد"
                );

                totalCustomers++;
            }

            // Update summary labels
            lblTotalCustomersCount.Text = $"إجمالي العملاء: {totalCustomers}";

            MessageBox.Show($"تم إنشاء تقرير العملاء بنجاح\nعدد العملاء: {totalCustomers}",
                            "تم بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير العملاء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            btnGenerateCustomerReport.Enabled = true;
            btnGenerateCustomerReport.Text = "إنشاء التقرير";
        }
    }

    private async void BtnGenerateFinancialReport_Click(object? sender, EventArgs e)
    {
        try
        {
            btnGenerateFinancialReport.Enabled = false;
            btnGenerateFinancialReport.Text = "جاري الإنشاء...";

            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            // Get filter values
            var fromDate = dtpFinancialFromDate.Value.Date;
            var toDate = dtpFinancialToDate.Value.Date.AddDays(1).AddTicks(-1); // End of day

            // Get sales data with car information
            var salesData = await context.Sales
                            .Include(s => s.Car)
                            .Include(s => s.Customer)
                            .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate)
                            .OrderByDescending(s => s.SaleDate)
                            .ToListAsync();

            // Clear existing data
            dgvFinancialReport.Rows.Clear();

            // Populate DataGridView
            decimal totalRevenue = 0;
            decimal totalCost = 0;
            decimal totalProfit = 0;

            foreach(var sale in salesData)
            {
                var revenue = sale.ActualSellPrice;
                var cost = sale.Car.PurchasePrice;
                var profit = revenue - cost;
                var paymentMethodText = sale.PaymentMethod == PaymentMethod.Cash ? "نقدي" : "تقسيط";
                var description = $"بيع {sale.Car.Brand} {sale.Car.Model} - {sale.Customer.FullName}";

                dgvFinancialReport.Rows.Add(
                    sale.SaleDate.ToString("yyyy-MM-dd"),
                    description,
                    revenue.ToString("N0"),
                    cost.ToString("N0"),
                    profit.ToString("N0"),
                    paymentMethodText
                );

                totalRevenue += revenue;
                totalCost += cost;
                totalProfit += profit;
            }

            // Update summary labels
            lblTotalRevenue.Text = $"إجمالي الإيرادات: {totalRevenue:N0}";
            lblTotalProfit.Text = $"إجمالي الربح: {totalProfit:N0}";

            MessageBox.Show($"تم إنشاء التقرير المالي بنجاح\nإجمالي الإيرادات: {totalRevenue:N0}\nإجمالي الربح: {totalProfit:N0}",
                            "تم بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء التقرير المالي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            btnGenerateFinancialReport.Enabled = true;
            btnGenerateFinancialReport.Text = "إنشاء التقرير";
        }
    }

    private async void BtnGenerateInstallmentReport_Click(object? sender, EventArgs e)
    {
        try
        {
            btnGenerateInstallmentReport.Enabled = false;
            btnGenerateInstallmentReport.Text = "⏳ جاري التحميل...";

            var statusFilter = cmbInstallmentStatus.SelectedItem?.ToString();
            if (statusFilter == "الكل") statusFilter = null;

            var reportData = await EnhancedReportService.GenerateInstallmentReportAsync(
                dtpInstallmentFromDate.Value, dtpInstallmentToDate.Value, statusFilter);

            // Apply enhanced styling
            dgvInstallmentReport.DataSource = reportData.Data;
            EnhancedReportService.ApplyEnhancedStyling(dgvInstallmentReport, reportData.ThemeColor);

            // Update summary labels
            if (reportData.Summary.ContainsKey("إجمالي قيمة الأقساط"))
                lblTotalInstallmentAmount.Text = $"إجمالي الأقساط: {reportData.Summary["إجمالي قيمة الأقساط"]}";

            if (reportData.Summary.ContainsKey("الأقساط المستحقة"))
                lblPendingInstallments.Text = $"الأقساط المستحقة: {reportData.Summary["الأقساط المستحقة"]}";

            // Show success message
            var successMsg = $"✅ تم إنشاء تقرير الأقساط بنجاح!\n\n" +
                           $"📊 إجمالي الأقساط: {reportData.Summary["إجمالي قيمة الأقساط"]}\n" +
                           $"💰 إجمالي المدفوع: {reportData.Summary["إجمالي المدفوع"]}\n" +
                           $"📈 معدل التحصيل: {reportData.Summary["معدل التحصيل"]}";

            MessageBox.Show(successMsg, "نجح الإنشاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير الأقساط: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            btnGenerateInstallmentReport.Enabled = true;
            btnGenerateInstallmentReport.Text = "📊 إنشاء التقرير";
        }
    }

    private async void BtnGenerateSupplierReport_Click(object? sender, EventArgs e, TextBox txtSupplierSearch)
    {
        try
        {
            var btnGenerateSupplierReport = sender as Button;
            if(btnGenerateSupplierReport == null)
            {
                return;
            }

            btnGenerateSupplierReport.Enabled = false;
            btnGenerateSupplierReport.Text = "جاري الإنشاء...";

            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            // Get search term
            var searchTerm = txtSupplierSearch.Text.Trim();

            // Build query
            var query = context.Suppliers
                        .Include(s => s.Payments)
                        .AsQueryable();

            // Apply search filter
            if(!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(s =>
                                    s.SupplierName.Contains(searchTerm) ||
                                    s.Phone.Contains(searchTerm) ||
                                    s.Email.Contains(searchTerm) ||
                                    s.ResponsiblePerson.Contains(searchTerm));
            }

            var suppliersData = await query.OrderBy(s => s.SupplierName).ToListAsync();

            // Find the supplier DataGridView in the tab
            var supplierTab = tabSupplierReports;
            var dgvSupplierReport = supplierTab.Controls.OfType<DataGridView>().FirstOrDefault();
            if(dgvSupplierReport == null)
            {
                return;
            }

            // Clear existing data
            dgvSupplierReport.Rows.Clear();

            // Populate DataGridView
            int totalSuppliers = 0;
            decimal totalAmountOwed = 0;

            foreach(var supplier in suppliersData)
            {
                // Calculate supplier financial data
                var totalOwed = supplier.TotalOwed;
                var totalPaid = supplier.Payments.Sum(p => p.Amount);
                var balance = totalOwed - totalPaid;

                dgvSupplierReport.Rows.Add(
                    supplier.SupplierId,
                    supplier.SupplierName,
                    supplier.ResponsiblePerson ?? "",
                    supplier.Phone,
                    supplier.Email ?? "",
                    totalOwed.ToString("N0"),
                    totalPaid.ToString("N0"),
                    balance.ToString("N0")
                );

                totalSuppliers++;
                totalAmountOwed += balance;
            }

            // Update summary labels
            var filterPanel = supplierTab.Controls.OfType<Panel>().FirstOrDefault();
            if(filterPanel != null)
            {
                var lblTotalSuppliersCount = filterPanel.Controls.OfType<Label>().FirstOrDefault(l => l.Text.Contains("إجمالي الموردين"));
                var lblTotalSupplierDebt = filterPanel.Controls.OfType<Label>().FirstOrDefault(l => l.Text.Contains("إجمالي المستحقات"));

                if(lblTotalSuppliersCount != null)
                {
                    lblTotalSuppliersCount.Text = $"إجمالي الموردين: {totalSuppliers}";
                }
                if(lblTotalSupplierDebt != null)
                {
                    lblTotalSupplierDebt.Text = $"إجمالي المستحقات: {totalAmountOwed:N0}";
                }
            }

            MessageBox.Show($"تم إنشاء تقرير الموردين بنجاح\nعدد الموردين: {totalSuppliers}\nإجمالي المستحقات: {totalAmountOwed:N0}",
                            "تم بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير الموردين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            var btnGenerateSupplierReport = sender as Button;
            if(btnGenerateSupplierReport != null)
            {
                btnGenerateSupplierReport.Enabled = true;
                btnGenerateSupplierReport.Text = "إنشاء التقرير";
            }
        }
    }

    private void OpenPrintReportForm(DataGridView dgv)
    {
        if(dgv.Rows.Count == 0)
        {
            MessageBox.Show("الرجاء إنشاء التقرير أولاً قبل محاولة الطباعة.", "لا توجد بيانات للطباعة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        // إنشاء DataGridView مؤقت ليحمل البيانات المراد طباعتها
        var dgvToPrint = new DataGridView();

        // نسخ الأعمدة من المصدر الأصلي
        foreach(DataGridViewColumn col in dgv.Columns)
        {
            dgvToPrint.Columns.Add(col.Clone() as DataGridViewColumn);
        }

        // التحقق إذا كان هناك صفوف محددة
        if(dgv.SelectedRows.Count > 0)
        {
            // إذا كان هناك تحديد، أضف الصفوف المحددة فقط
            foreach(DataGridViewRow row in dgv.SelectedRows)
            {
                int newIndex = dgvToPrint.Rows.Add();
                for(int i = 0; i < row.Cells.Count; i++)
                {
                    dgvToPrint.Rows[newIndex].Cells[i].Value = row.Cells[i].Value;
                }
            }
        }
        else
        {
            // إذا لم يكن هناك تحديد، انسخ كل الصفوف
            foreach(DataGridViewRow row in dgv.Rows)
            {
                if(row.IsNewRow)
                {
                    continue;    // تجاهل الصف الجديد الفارغ
                }
                int newIndex = dgvToPrint.Rows.Add();
                for(int i = 0; i < row.Cells.Count; i++)
                {
                    dgvToPrint.Rows[newIndex].Cells[i].Value = row.Cells[i].Value;
                }
            }
        }

        using(var printForm = new PrintReportForm(dgvToPrint))
        {
            printForm.Text = dgv.FindForm().Text;
            printForm.ShowDialog();
        }
    }



    private void BtnPrintSelectedReport_Click(DataGridView dgv)
    {
        if(dgv.SelectedRows.Count == 0)
        {
            MessageBox.Show("الرجاء تحديد الصفوف المراد طباعتها.", "لا توجد بيانات للطباعة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        var selectedRows = dgv.SelectedRows.Cast<DataGridViewRow>().ToList();
        PrintSelectedRows(selectedRows);
    }

    private void BtnPrintSummaryReport_Click(DataGridView dgv)
    {
        // Here you can print only the summary labels or any summary part required
        MessageBox.Show("طباعة الملخص قيد التطوير!");
    }

    private void PrintSelectedRows(List<DataGridViewRow> selectedRows)
    {
        if(selectedRows.Count == 0)
        {
            return;
        }

        // Get the parent DataGridView to copy column structure
        var parentDgv = selectedRows[0].DataGridView;
        if(parentDgv == null)
        {
            return;
        }

        // Create temporary DataGridView for printing
        var dgvToPrint = new DataGridView();
        dgvToPrint.AllowUserToAddRows = false;
        dgvToPrint.AllowUserToDeleteRows = false;
        dgvToPrint.ReadOnly = true;
        dgvToPrint.AutoGenerateColumns = false;

        // Copy column structure
        foreach(DataGridViewColumn column in parentDgv.Columns)
        {
            dgvToPrint.Columns.Add(column.Name, column.HeaderText);
        }

        // Copy selected rows data
        foreach(var row in selectedRows)
        {
            int newIndex = dgvToPrint.Rows.Add();
            for(int i = 0; i < row.Cells.Count; i++)
            {
                dgvToPrint.Rows[newIndex].Cells[i].Value = row.Cells[i].Value;
            }
        }

        // Open print form
        using(var printForm = new PrintReportForm(dgvToPrint))
        {
            printForm.Text = parentDgv.FindForm()?.Text ?? "تقرير محدد";
            printForm.ShowDialog();
        }
    }

    private void BtnPrintReport_Click(DataGridView dgv, string reportName = null)
    {
        if(dgv.Rows.Count == 0)
        {
            MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            // إنشاء نموذج طباعة محسن مع اللوجو وبيانات المعرض
            var printForm = new EnhancedReportPrintForm(dgv, reportName ?? "تقرير");
            printForm.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        // نسخ الصفوف المحددة أو كل الصفوف

    }

    private void BtnPrintByClient_Click(DataGridView dgv)
    {
        if(dgv.Rows.Count == 0)
        {
            MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        using(var printOptionForm = new ClientPrintOptionForm())
        {
            if(printOptionForm.ShowDialog() == DialogResult.OK)
            {
                var dataToPrint = new DataGridView();
                dataToPrint.AllowUserToAddRows = false;
                dataToPrint.AllowUserToDeleteRows = false;
                dataToPrint.ReadOnly = true;
                dataToPrint.AutoGenerateColumns = false;

                // Clone the columns from original DataGridView
                foreach(DataGridViewColumn column in dgv.Columns)
                {
                    dataToPrint.Columns.Add(column.Name, column.HeaderText);
                }

                // Filter based on selection
                switch(printOptionForm.SelectedOption)
                {
                case ClientPrintOptionForm.PrintOption.AllClients:
                    // Copy all rows
                    foreach(DataGridViewRow row in dgv.Rows)
                    {
                        if(!row.IsNewRow)
                        {
                            int newIndex = dataToPrint.Rows.Add();
                            for(int i = 0; i < row.Cells.Count; i++)
                            {
                                dataToPrint.Rows[newIndex].Cells[i].Value = row.Cells[i].Value;
                            }
                        }
                    }
                    break;

                case ClientPrintOptionForm.PrintOption.SpecificClient:
                    if(printOptionForm.SelectedClient != null)
                    {
                        foreach(DataGridViewRow row in dgv.Rows)
                        {
                            if(!row.IsNewRow && row.Cells.Count > 0)
                            {
                                // Check if this row belongs to the selected client
                                var customerName = row.Cells["CustomerName"]?.Value?.ToString();
                                if(customerName != null && customerName.Equals(printOptionForm.SelectedClient.FullName, StringComparison.OrdinalIgnoreCase))
                                {
                                    int newIndex = dataToPrint.Rows.Add();
                                    for(int i = 0; i < row.Cells.Count; i++)
                                    {
                                        dataToPrint.Rows[newIndex].Cells[i].Value = row.Cells[i].Value;
                                    }
                                }
                            }
                        }
                    }
                    break;

                case ClientPrintOptionForm.PrintOption.MultipleClients:
                    foreach(DataGridViewRow row in dgv.Rows)
                    {
                        if(!row.IsNewRow && row.Cells.Count > 0)
                        {
                            var customerName = row.Cells["CustomerName"]?.Value?.ToString();
                            if(customerName != null && printOptionForm.SelectedClients.Any(c => c.FullName.Equals(customerName, StringComparison.OrdinalIgnoreCase)))
                            {
                                int newIndex = dataToPrint.Rows.Add();
                                for(int i = 0; i < row.Cells.Count; i++)
                                {
                                    dataToPrint.Rows[newIndex].Cells[i].Value = row.Cells[i].Value;
                                }
                            }
                        }
                    }
                    break;
                }

                if(dataToPrint.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات تطابق معايير الاختيار", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using(var printForm = new PrintReportForm(dataToPrint))
                {
                    string title = "تقرير العملاء المحددين";
                    switch(printOptionForm.SelectedOption)
                    {
                    case ClientPrintOptionForm.PrintOption.SpecificClient:
                        title = $"تقرير العميل - {printOptionForm.SelectedClient?.FullName}";
                        break;
                    case ClientPrintOptionForm.PrintOption.MultipleClients:
                        title = $"تقرير العملاء المحددين ({printOptionForm.SelectedClients.Count} عميل)";
                        break;
                    }
                    printForm.Text = title;
                    printForm.ShowDialog();
                }
            }
        }
    }

    private string GetPaymentStatus(Sale sale)
    {
        if(sale.PaymentMethod == PaymentMethod.Cash)
        {
            return sale.TotalPaid >= sale.ActualSellPrice ? "مدفوع بالكامل" : "مدفوع جزئياً";
        }
        else // Installment
        {
            var remainingAmount = sale.ActualSellPrice - sale.TotalPaid;
            if(remainingAmount <= 0)
            {
                return "مدفوع بالكامل";
            }
            else if(sale.TotalPaid > 0)
            {
                return "مدفوع جزئياً";
            }
            else
            {
                return "لم يتم الدفع";
            }
        }
    }

    // Method to print selected rows from DataGridView
    private void BtnPrintSelectedRows_Click(DataGridView dgv, string reportTitle)
    {
        if(dgv.SelectedRows.Count == 0)
        {
            MessageBox.Show("الرجاء تحديد الصفوف المراد طباعتها.", "لا توجد صفوف محددة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            // Create temporary DataGridView for printing selected rows
            var dgvToPrint = new DataGridView()
            {
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                AutoGenerateColumns = false
            };

            // Copy column structure
            foreach(DataGridViewColumn column in dgv.Columns)
            {
                dgvToPrint.Columns.Add(column.Name, column.HeaderText);
            }

            // Copy selected rows data
            foreach(DataGridViewRow row in dgv.SelectedRows)
            {
                if(!row.IsNewRow)
                {
                    int newIndex = dgvToPrint.Rows.Add();
                    for(int i = 0; i < row.Cells.Count; i++)
                    {
                        dgvToPrint.Rows[newIndex].Cells[i].Value = row.Cells[i].Value;
                    }
                }
            }

            // Open print form with selected data
            using(var printForm = new PrintReportForm(dgvToPrint, reportTitle))
            {
                printForm.Text = reportTitle;
                printForm.ShowDialog();
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الصفوف المحددة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    // Method to print summary information
    private void BtnPrintSummary_Click(string summaryTitle, string summaryData1, string summaryData2)
    {
        try
        {
            // Create a simple summary report
            var summaryText = new System.Text.StringBuilder();
            summaryText.AppendLine($"===== {summaryTitle} =====");
            summaryText.AppendLine();
            summaryText.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm}");
            summaryText.AppendLine();
            summaryText.AppendLine(summaryData1);
            summaryText.AppendLine(summaryData2);
            summaryText.AppendLine();
            summaryText.AppendLine("============================");

            // Create a simple form to display and print the summary
            var summaryForm = new Form()
            {
                Text = summaryTitle,
                Size = new Size(500, 400),
                StartPosition = FormStartPosition.CenterParent,
                BackColor = Color.White
            };

            var textBox = new TextBox()
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                Text = summaryText.ToString(),
                Font = new Font("Segoe UI", 11),
                ReadOnly = true,
                BackColor = Color.White
            };

            var buttonPanel = new Panel()
            {
                Dock = DockStyle.Bottom,
                Height = 50,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            var btnPrint = new Button()
            {
                Text = "طباعة",
                Size = new Size(80, 30),
                Location = new Point(10, 10),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnPrint.FlatAppearance.BorderSize = 0;

            var btnClose = new Button()
            {
                Text = "إغلاق",
                Size = new Size(80, 30),
                Location = new Point(100, 10),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnClose.FlatAppearance.BorderSize = 0;

            btnPrint.Click += (s, e) =>
            {
                try
                {
                    var printDocument = new System.Drawing.Printing.PrintDocument();
                    printDocument.PrintPage += (sender, printArgs) =>
                    {
                        var font = new Font("Arial", 12);
                        printArgs.Graphics.DrawString(summaryText.ToString(), font, Brushes.Black, 50, 50);
                    };

                    var printDialog = new PrintDialog()
                    {
                        Document = printDocument
                    };

                    if(printDialog.ShowDialog() == DialogResult.OK)
                    {
                        printDocument.Print();
                    }
                }
                catch(Exception printEx)
                {
                    MessageBox.Show($"خطأ في الطباعة: {printEx.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            btnClose.Click += (s, e) => summaryForm.Close();

            buttonPanel.Controls.AddRange(new Control[] { btnPrint, btnClose });
            summaryForm.Controls.AddRange(new Control[] { textBox, buttonPanel });

            summaryForm.ShowDialog();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء تقرير الملخص: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #region Enhanced Report Methods

    /// <summary>
    /// طباعة محسنة للتقارير
    /// </summary>
    private async void BtnEnhancedPrint_Click(EnhancedReportService.ReportType reportType)
    {
        try
        {
            EnhancedReportService.ReportData reportData = null;

            switch (reportType)
            {
                case EnhancedReportService.ReportType.SalesReport:
                    var paymentMethod = cmbSalesPaymentMethod.SelectedItem?.ToString();
                    if (paymentMethod == "الكل") paymentMethod = null;
                    reportData = await EnhancedReportService.GenerateSalesReportAsync(
                        dtpSalesFromDate.Value, dtpSalesToDate.Value, paymentMethod);
                    break;

                case EnhancedReportService.ReportType.InventoryReport:
                    reportData = await EnhancedReportService.GenerateInventoryReportAsync();
                    break;

                case EnhancedReportService.ReportType.CustomerReport:
                    reportData = await EnhancedReportService.GenerateCustomerReportAsync();
                    break;

                case EnhancedReportService.ReportType.PerformanceReport:
                    // Use default date range if not available
                    var perfFromDate = DateTime.Now.AddMonths(-1);
                    var perfToDate = DateTime.Now;
                    reportData = await EnhancedReportService.GeneratePerformanceReportAsync(perfFromDate, perfToDate);
                    break;

                case EnhancedReportService.ReportType.InstallmentReport:
                    var instStatusFilter = cmbInstallmentStatus?.SelectedItem?.ToString();
                    if (instStatusFilter == "الكل") instStatusFilter = null;
                    reportData = await EnhancedReportService.GenerateInstallmentReportAsync(
                        dtpInstallmentFromDate?.Value ?? DateTime.Now.AddMonths(-3),
                        dtpInstallmentToDate?.Value ?? DateTime.Now,
                        instStatusFilter);
                    break;

                case EnhancedReportService.ReportType.StatisticsReport:
                    reportData = await EnhancedReportService.GenerateStatisticsReportAsync();
                    break;

                default:
                    MessageBox.Show("نوع التقرير غير مدعوم حالياً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
            }

            if (reportData != null)
            {
                var printForm = new EnhancedPrintForm(reportData);
                printForm.ShowDialog();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء التقرير المحسن: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// تصدير التقارير المحسن
    /// </summary>
    private async void BtnExportReport_Click(EnhancedReportService.ReportType reportType)
    {
        try
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "HTML Files (*.html)|*.html|CSV Files (*.csv)|*.csv|XML Files (*.xml)|*.xml|JSON Files (*.json)|*.json|Text Files (*.txt)|*.txt",
                DefaultExt = "html",
                FileName = $"{EnhancedReportService.ReportDefinitions[reportType].Title}_{DateTime.Now:yyyyMMdd}"
            };

            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                // Generate report data
                EnhancedReportService.ReportData reportData = null;

                switch (reportType)
                {
                    case EnhancedReportService.ReportType.SalesReport:
                        var paymentMethod = cmbSalesPaymentMethod.SelectedItem?.ToString();
                        if (paymentMethod == "الكل") paymentMethod = null;
                        reportData = await EnhancedReportService.GenerateSalesReportAsync(
                            dtpSalesFromDate.Value, dtpSalesToDate.Value, paymentMethod);
                        break;

                    case EnhancedReportService.ReportType.InventoryReport:
                        reportData = await EnhancedReportService.GenerateInventoryReportAsync();
                        break;

                    case EnhancedReportService.ReportType.CustomerReport:
                        reportData = await EnhancedReportService.GenerateCustomerReportAsync();
                        break;

                    case EnhancedReportService.ReportType.PerformanceReport:
                        reportData = await EnhancedReportService.GeneratePerformanceReportAsync(
                            DateTime.Now.AddMonths(-1), DateTime.Now);
                        break;

                    case EnhancedReportService.ReportType.InstallmentReport:
                        var instStatusFilter = cmbInstallmentStatus?.SelectedItem?.ToString();
                        if (instStatusFilter == "الكل") instStatusFilter = null;
                        reportData = await EnhancedReportService.GenerateInstallmentReportAsync(
                            dtpInstallmentFromDate?.Value ?? DateTime.Now.AddMonths(-3),
                            dtpInstallmentToDate?.Value ?? DateTime.Now,
                            instStatusFilter);
                        break;

                    case EnhancedReportService.ReportType.StatisticsReport:
                        reportData = await EnhancedReportService.GenerateStatisticsReportAsync();
                        break;

                    default:
                        MessageBox.Show("نوع التقرير غير مدعوم للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                }

                if (reportData != null)
                {
                    // Determine export format based on file extension
                    var extension = Path.GetExtension(saveDialog.FileName).ToLower();
                    var format = extension switch
                    {
                        ".csv" => ReportExportService.ExportFormat.CSV,
                        ".xml" => ReportExportService.ExportFormat.XML,
                        ".json" => ReportExportService.ExportFormat.JSON,
                        ".txt" => ReportExportService.ExportFormat.TXT,
                        _ => ReportExportService.ExportFormat.HTML
                    };

                    // Export the report
                    var success = await ReportExportService.ExportReportAsync(reportData, saveDialog.FileName, format);

                    if (success)
                    {
                        var result = MessageBox.Show(
                            $"✅ تم تصدير التقرير بنجاح!\n\nالملف: {saveDialog.FileName}\n\nهل تريد فتح الملف؟",
                            "نجح التصدير",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Information);

                        if (result == DialogResult.Yes)
                        {
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = saveDialog.FileName,
                                UseShellExecute = true
                            });
                        }
                    }
                    else
                    {
                        MessageBox.Show("❌ فشل في تصدير التقرير", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }



    /// <summary>
    /// إنشاء تبويب تقارير الأداء
    /// </summary>
    private void CreatePerformanceReportsTab()
    {
        tabPerformanceReports = new TabPage("📈 تقارير الأداء")
        {
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Filter Panel
        var filterPanel = new Panel
        {
            Size = new Size(1150, 100),
            Location = new Point(10, 10),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblFromDate = new Label { Text = "من تاريخ:", Location = new Point(20, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        var dtpFromDate = new DateTimePicker { Location = new Point(100, 18), Size = new Size(150, 23), Format = DateTimePickerFormat.Short };
        dtpFromDate.Value = DateTime.Now.AddMonths(-1);

        var lblToDate = new Label { Text = "إلى تاريخ:", Location = new Point(270, 20), Font = new Font("Segoe UI", 9, FontStyle.Bold) };
        var dtpToDate = new DateTimePicker { Location = new Point(340, 18), Size = new Size(150, 23), Format = DateTimePickerFormat.Short };

        var btnGenerate = CreateStyledButton("إنشاء تقرير الأداء", new Point(20, 60),
            EnhancedReportService.ReportDefinitions[EnhancedReportService.ReportType.PerformanceReport].ThemeColor, null, "📈");

        var btnPrint = CreateStyledButton("طباعة محسنة", new Point(200, 60), Color.FromArgb(111, 66, 193), null, "🖨️");

        filterPanel.Controls.AddRange(new Control[] { lblFromDate, dtpFromDate, lblToDate, dtpToDate, btnGenerate, btnPrint });

        // DataGridView
        var dgvPerformance = new DataGridView
        {
            Location = new Point(10, 120),
            Size = new Size(1150, 500),
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
        };

        btnGenerate.Click += async (s, e) => {
            try
            {
                btnGenerate.Enabled = false;
                btnGenerate.Text = "⏳ جاري التحميل...";

                var reportData = await EnhancedReportService.GeneratePerformanceReportAsync(dtpFromDate.Value, dtpToDate.Value);
                dgvPerformance.DataSource = reportData.Data;
                EnhancedReportService.ApplyEnhancedStyling(dgvPerformance, reportData.ThemeColor);

                MessageBox.Show("✅ تم إنشاء تقرير الأداء بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnGenerate.Enabled = true;
                btnGenerate.Text = "📈 إنشاء تقرير الأداء";
            }
        };

        btnPrint.Click += (s, e) => BtnEnhancedPrint_Click(EnhancedReportService.ReportType.PerformanceReport);

        tabPerformanceReports.Controls.AddRange(new Control[] { filterPanel, dgvPerformance });
        tabControl.TabPages.Add(tabPerformanceReports);
    }

    /// <summary>
    /// إنشاء تبويب التقارير الإحصائية
    /// </summary>
    private void CreateStatisticsReportsTab()
    {
        tabStatisticsReports = new TabPage("📊 التقارير الإحصائية")
        {
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Control Panel
        var controlPanel = new Panel
        {
            Size = new Size(1150, 80),
            Location = new Point(10, 10),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var btnGenerate = CreateStyledButton("إنشاء التقرير الإحصائي", new Point(20, 20),
            EnhancedReportService.ReportDefinitions[EnhancedReportService.ReportType.StatisticsReport].ThemeColor, null, "📊");

        var btnPrint = CreateStyledButton("طباعة محسنة", new Point(200, 20), Color.FromArgb(111, 66, 193), null, "🖨️");

        var btnRefresh = CreateStyledButton("تحديث", new Point(380, 20), Color.FromArgb(40, 167, 69), null, "🔄");

        controlPanel.Controls.AddRange(new Control[] { btnGenerate, btnPrint, btnRefresh });

        // DataGridView
        var dgvStatistics = new DataGridView
        {
            Location = new Point(10, 100),
            Size = new Size(1150, 520),
            Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
        };

        btnGenerate.Click += async (s, e) => {
            try
            {
                btnGenerate.Enabled = false;
                btnGenerate.Text = "⏳ جاري التحميل...";

                var reportData = await EnhancedReportService.GenerateStatisticsReportAsync();
                dgvStatistics.DataSource = reportData.Data;
                EnhancedReportService.ApplyEnhancedStyling(dgvStatistics, reportData.ThemeColor);

                MessageBox.Show("✅ تم إنشاء التقرير الإحصائي بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnGenerate.Enabled = true;
                btnGenerate.Text = "📊 إنشاء التقرير الإحصائي";
            }
        };

        btnPrint.Click += (s, e) => BtnEnhancedPrint_Click(EnhancedReportService.ReportType.StatisticsReport);
        btnRefresh.Click += (s, e) => btnGenerate.PerformClick();

        tabStatisticsReports.Controls.AddRange(new Control[] { controlPanel, dgvStatistics });
        tabControl.TabPages.Add(tabStatisticsReports);
    }

    /// <summary>
    /// عرض ملخص الأقساط في نافذة منفصلة
    /// </summary>
    private async void ShowInstallmentSummaryDialog()
    {
        try
        {
            var reportData = await EnhancedReportService.GenerateInstallmentReportAsync(
                dtpInstallmentFromDate?.Value ?? DateTime.Now.AddMonths(-3),
                dtpInstallmentToDate?.Value ?? DateTime.Now);

            var summaryText = "📊 ملخص تقرير الأقساط\n";
            summaryText += "=" + new string('=', 40) + "\n\n";

            foreach (var item in reportData.Summary)
            {
                summaryText += $"• {item.Key}: {item.Value}\n";
            }

            summaryText += "\n" + new string('=', 40) + "\n";
            summaryText += $"تم الإنشاء في: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";

            MessageBox.Show(summaryText, "ملخص الأقساط", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء ملخص الأقساط: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion
}
}
