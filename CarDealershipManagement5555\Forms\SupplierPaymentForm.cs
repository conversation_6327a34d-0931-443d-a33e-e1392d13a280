using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class SupplierPaymentForm : Form
    {
        private int supplierId;
        private NumericUpDown numAmount = null!;
        private TextBox txtNotes = null!;
        private Button btnSave = null!;
        private Button btnCancel = null!;
        private Label lblSupplierInfo = null!;
        private Label lblStatus = null!;

        public SupplierPaymentForm(int supplierId)
        {
            this.supplierId = supplierId;
            InitializeComponent();
            LoadSupplierInfo();
        }

        private void InitializeComponent()
        {
            this.Text = "دفع للمورد - Supplier Payment";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(20, 260),
                Size = new Size(450, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Supplier Info Label
            lblSupplierInfo = new Label
            {
                Location = new Point(20, 20),
                Size = new Size(450, 23),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204)
            };

            // Amount Input
            var lblAmount = new Label
            {
                Text = "المبلغ:",
                Location = new Point(20, 70),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            numAmount = new NumericUpDown
            {
                Location = new Point(150, 68),
                Size = new Size(150, 25),
                DecimalPlaces = 2,
                Maximum = 1000000,
                Font = new Font("Segoe UI", 9F)
            };

            // Notes Input
            var lblNotes = new Label
            {
                Text = "ملاحظات:",
                Location = new Point(20, 110),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            txtNotes = new TextBox
            {
                Location = new Point(150, 108),
                Size = new Size(300, 80),
                Multiline = true,
                Font = new Font("Segoe UI", 9F)
            };

            // Save Button
            btnSave = new Button
            {
                Text = "حفظ الدفعة",
                Location = new Point(250, 290),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;

            // Cancel Button
            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(360, 290),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] { lblSupplierInfo, lblAmount, numAmount, lblNotes, txtNotes, btnSave, btnCancel, lblStatus });
        }

        private async void LoadSupplierInfo()
        {
            lblStatus.Text = ""; // Clear previous status messages
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var supplier = await context.Suppliers.FindAsync(supplierId);
                if (supplier != null)
                {
                    lblSupplierInfo.Text = $"المورد: {supplier.SupplierName} - المتبقي: {supplier.Balance:C}";
                }
                else
                {
                    lblStatus.Text = "لم يتم العثور على بيانات المورد.";
                }
            }, "تحميل بيانات المورد");
        }

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages

            if (numAmount.Value <= 0)
            {
                lblStatus.Text = "يرجى إدخال مبلغ صحيح.";
                numAmount.Focus();
                return;
            }

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var supplier = await context.Suppliers.FindAsync(supplierId);
                if (supplier != null)
                {
                    var payment = new SupplierPayment
                    {
                        SupplierId = supplierId,
                        Amount = numAmount.Value,
                        PaymentDate = DateTime.Now,
                        Notes = string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text
                    };

                    context.SupplierPayments.Add(payment);

                    // Update supplier totals
                    supplier.TotalPaid += numAmount.Value;
                    supplier.Balance = supplier.TotalOwed - supplier.TotalPaid;

                    if (supplier.Balance > 0)
                    {
                        supplier.AccountStatus = SupplierAccountStatus.Creditor;
                    }
                    else if (supplier.Balance < 0)
                    {
                        supplier.AccountStatus = SupplierAccountStatus.Debtor;
                    }
                    else
                    {
                        supplier.AccountStatus = SupplierAccountStatus.Neutral;
                    }

                    await context.SaveChangesAsync();
                    MessageBox.Show("تم حفظ الدفعة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    lblStatus.Text = "المورد غير موجود.";
                }
            }, "حفظ الدفعة");
        }
    }
}


