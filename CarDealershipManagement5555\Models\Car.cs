using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CarDealershipManagement.Models
{
    public class Car
    {
        [Key]
        [Required(ErrorMessage = "رقم الشاسيه مطلوب.")]
        [StringLength(50, ErrorMessage = "رقم الشاسيه لا يمكن أن يتجاوز 50 حرفًا.")]
        public string ChassisNumber
        {
            get;
            set;
        } = string.Empty; // رقم الشاسيه - Primary Key

        [Required(ErrorMessage = "نوع السيارة مطلوب.")]
        [StringLength(50, ErrorMessage = "نوع السيارة لا يمكن أن يتجاوز 50 حرفًا.")]
        public string Type
        {
            get;
            set;
        } = string.Empty; // نوع السيارة (سيدان، دفع رباعي، إلخ)

        [Required(ErrorMessage = "ماركة السيارة مطلوبة.")]
        [StringLength(50, ErrorMessage = "ماركة السيارة لا يمكن أن تتجاوز 50 حرفًا.")]
        public string Brand
        {
            get;
            set;
        } = string.Empty; // الماركة (تويوتا، مرسيدس، إلخ)

        [Required(ErrorMessage = "موديل السيارة مطلوب.")]
        [StringLength(50, ErrorMessage = "موديل السيارة لا يمكن أن يتجاوز 50 حرفًا.")]
        public string Model
        {
            get;
            set;
        } = string.Empty; // الموديل

        [Required(ErrorMessage = "سنة الصنع مطلوبة.")]
        [Range(1900, 2100, ErrorMessage = "سنة الصنع يجب أن تكون بين 1900 و 2100.")]
        public int Year
        {
            get;    // سنة الصنع
            set;
        }

        [Required(ErrorMessage = "لون السيارة مطلوب.")]
        [StringLength(30, ErrorMessage = "لون السيارة لا يمكن أن يتجاوز 30 حرفًا.")]
        public string Color
        {
            get;
            set;
        } = string.Empty; // اللون

        [StringLength(50, ErrorMessage = "رقم لوحة السيارة لا يمكن أن يتجاوز 50 حرفًا.")]
        public string? PlateNumber
        {
            get;    // رقم لوحة السيارة (اختياري)
            set;
        }

        [Required(ErrorMessage = "عداد الكيلومترات مطلوب.")]
        [Range(0, int.MaxValue, ErrorMessage = "عداد الكيلومترات يجب أن يكون قيمة موجبة.")]
        public int Mileage
        {
            get;    // عداد الكيلومترات
            set;
        }

        [Required(ErrorMessage = "سعر الشراء مطلوب.")]
        [Column(TypeName = "decimal(18,2)")]
        [Range(0.01, double.MaxValue, ErrorMessage = "سعر الشراء يجب أن يكون أكبر من صفر.")]
        public decimal PurchasePrice
        {
            get;    // سعر الشراء
            set;
        }

        [Required(ErrorMessage = "سعر البيع المقترح مطلوب.")]
        [Column(TypeName = "decimal(18,2)")]
        [Range(0.01, double.MaxValue, ErrorMessage = "سعر البيع المقترح يجب أن يكون أكبر من صفر.")]
        public decimal SuggestedSellPrice
        {
            get;    // سعر البيع المقترح (نقداً)
            set;
        }

        [Required(ErrorMessage = "سعر البيع بالتقسيط مطلوب.")]
        [Column(TypeName = "decimal(18,2)")]
        [Range(0.01, double.MaxValue, ErrorMessage = "سعر البيع بالتقسيط يجب أن يكون أكبر من صفر.")]
        public decimal InstallmentSellPrice
        {
            get;    // سعر البيع بالتقسيط
            set;
        }

        [Required(ErrorMessage = "حالة السيارة مطلوبة.")]
        public CarCondition Condition
        {
            get;    // حالة السيارة (جديدة/مستعملة)
            set;
        }

        [Required(ErrorMessage = "تاريخ الشراء مطلوب.")]
        public DateTime PurchaseDate
        {
            get;    // تاريخ الشراء
            set;
        }

        public bool IsSold
        {
            get;
            set;
        } = false; // هل تم بيعها

        [StringLength(20, ErrorMessage = "حالة السيارة لا يمكن أن تتجاوز 20 حرفًا.")]
        public string Status
        {
            get;
            set;
        } = "Available"; // حالة السيارة (Available, Sold, Reserved, etc.)

        public DateTime CreatedDate
        {
            get;
            set;
        } = DateTime.Now;
        public DateTime? ModifiedDate
        {
            get;
            set;
        }

        // Navigation properties
        public virtual ICollection<CarImage> Images
        {
            get;
            set;
        } = new List<CarImage>();
        public virtual ICollection<CarDocument> Documents
        {
            get;
            set;
        } = new List<CarDocument>();
        public virtual Sale? Sale
        {
            get;    // البيع المرتبط بهذه السيارة
            set;
        }
    }

    public enum CarCondition
    {
        New = 1,      // جديدة
        Used = 2      // مستعملة
    }
}


