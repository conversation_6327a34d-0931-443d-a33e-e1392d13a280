@echo off
chcp 65001 >nul
title إعادة تعيين كلمة المرور

echo.
echo ========================================
echo    🔑 إعادة تعيين كلمة المرور
echo ========================================
echo.

echo 🔧 إعادة تعيين بيانات الدخول...
echo.

REM تشغيل أداة إنشاء المستخدم الجديد
powershell -ExecutionPolicy Bypass -File "إنشاء_مستخدم_جديد.ps1" -NonInteractive

echo.
echo ✅ تم إعادة تعيين بيانات الدخول!
echo.
echo 🔑 البيانات الجديدة:
echo    اسم المستخدم: admin
echo    كلمة المرور: 123456
echo.
echo 🚀 يمكنك الآن:
echo    1. تشغيل البرنامج
echo    2. استخدام البيانات الجديدة لتسجيل الدخول
echo    3. الانتقال إلى تبويب "📅 تقارير الأقساط"
echo.
echo 📋 خطوات الوصول لتبويب الأقساط المحسن:
echo    1. سجل الدخول باستخدام البيانات الجديدة
echo    2. اختر "التقارير" من القائمة الرئيسية
echo    3. انتقل إلى تبويب "📅 تقارير الأقساط"
echo.
echo 🎯 الميزات الجديدة المنتظرة:
echo    • 📊 إنشاء التقرير - تقرير شامل مع فلترة
echo    • 🖨️ طباعة محسنة - طباعة احترافية
echo    • 📤 تصدير - 5 صيغ مختلفة
echo    • 📈 ملخص الأقساط - إحصائيات سريعة
echo.
echo 📞 للمساعدة أو الدعم الفني، اتصل بالمطور
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
