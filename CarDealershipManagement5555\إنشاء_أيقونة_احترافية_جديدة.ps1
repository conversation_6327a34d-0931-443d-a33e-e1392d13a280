# إنشاء أيقونة احترافية جديدة لبرنامج إدارة معرض السيارات
# المطور: Amr <PERSON> Elawamy - 01285626623 - <EMAIL>

Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🎨 إنشاء أيقونة احترافية جديدة" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "👨‍💻 المطور: Amr Ali Elawamy" -ForegroundColor Green
Write-Host "📞 الهاتف: 01285626623" -ForegroundColor Green
Write-Host "📧 البريد: <EMAIL>" -ForegroundColor Green
Write-Host ""

# إنشاء أيقونة احترافية متقدمة
function New-ProfessionalIcon {
    param(
        [string]$OutputPath,
        [int]$Size = 256
    )
    
    Write-Host "🎨 إنشاء أيقونة احترافية بحجم ${Size}x${Size}..." -ForegroundColor Yellow
    
    # إنشاء bitmap
    $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    $graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias
    
    # خلفية متدرجة احترافية (أزرق إلى أزرق داكن)
    $centerPoint = New-Object System.Drawing.PointF($Size/2, $Size/2)
    $gradientBrush = New-Object System.Drawing.Drawing2D.PathGradientBrush(@($centerPoint))
    $gradientBrush.CenterColor = [System.Drawing.Color]::FromArgb(255, 70, 130, 200)  # أزرق فاتح
    $gradientBrush.SurroundColors = @([System.Drawing.Color]::FromArgb(255, 25, 60, 120))  # أزرق داكن
    
    # رسم الخلفية الدائرية
    $rect = New-Object System.Drawing.Rectangle(10, 10, $Size-20, $Size-20)
    $graphics.FillEllipse($gradientBrush, $rect)
    
    # إضافة حدود ذهبية
    $borderPen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(255, 255, 215, 0), 4)
    $graphics.DrawEllipse($borderPen, $rect)
    
    # رسم السيارة في المنتصف
    $carColor = [System.Drawing.Color]::White
    $carBrush = New-Object System.Drawing.SolidBrush($carColor)
    $carPen = New-Object System.Drawing.Pen($carColor, 2)
    
    # جسم السيارة الرئيسي
    $carBody = New-Object System.Drawing.Rectangle($Size/4, $Size/2-20, $Size/2, 25)
    $graphics.FillRectangle($carBrush, $carBody)
    
    # مقدمة السيارة
    $carFront = New-Object System.Drawing.Rectangle($Size/4-15, $Size/2-15, 15, 15)
    $graphics.FillRectangle($carBrush, $carFront)
    
    # نوافذ السيارة
    $windowColor = [System.Drawing.Color]::FromArgb(200, 173, 216, 230)
    $windowBrush = New-Object System.Drawing.SolidBrush($windowColor)
    $frontWindow = New-Object System.Drawing.Rectangle($Size/4+5, $Size/2-18, 15, 12)
    $rearWindow = New-Object System.Drawing.Rectangle($Size/4+25, $Size/2-18, 15, 12)
    $graphics.FillRectangle($windowBrush, $frontWindow)
    $graphics.FillRectangle($windowBrush, $rearWindow)
    
    # عجلات السيارة
    $wheelColor = [System.Drawing.Color]::FromArgb(255, 64, 64, 64)
    $wheelBrush = New-Object System.Drawing.SolidBrush($wheelColor)
    $wheel1 = New-Object System.Drawing.Rectangle($Size/4+5, $Size/2+20, 12, 12)
    $wheel2 = New-Object System.Drawing.Rectangle($Size/4+30, $Size/2+20, 12, 12)
    $graphics.FillEllipse($wheelBrush, $wheel1)
    $graphics.FillEllipse($wheelBrush, $wheel2)
    
    # مصابيح السيارة
    $lightColor = [System.Drawing.Color]::FromArgb(255, 255, 255, 0)
    $lightBrush = New-Object System.Drawing.SolidBrush($lightColor)
    $light1 = New-Object System.Drawing.Rectangle($Size/4-12, $Size/2-10, 4, 4)
    $light2 = New-Object System.Drawing.Rectangle($Size/4-12, $Size/2-5, 4, 4)
    $graphics.FillEllipse($lightBrush, $light1)
    $graphics.FillEllipse($lightBrush, $light2)
    
    # أيقونة الإدارة (ترس صغير في الأعلى)
    $gearColor = [System.Drawing.Color]::FromArgb(255, 255, 215, 0)
    $gearBrush = New-Object System.Drawing.SolidBrush($gearColor)
    $gearRect = New-Object System.Drawing.Rectangle($Size-50, 30, 20, 20)
    $graphics.FillEllipse($gearBrush, $gearRect)
    
    # أيقونة المبيعات (رمز $ في الأسفل)
    $dollarColor = [System.Drawing.Color]::FromArgb(255, 0, 255, 0)
    $dollarBrush = New-Object System.Drawing.SolidBrush($dollarColor)
    $dollarFont = New-Object System.Drawing.Font("Arial", $Size/16, [System.Drawing.FontStyle]::Bold)
    $dollarRect = New-Object System.Drawing.RectangleF(30, $Size-60, 30, 30)
    $graphics.DrawString("$", $dollarFont, $dollarBrush, $dollarRect)
    
    # تاج ذهبي في الأعلى (رمز التميز)
    $crownColor = [System.Drawing.Color]::FromArgb(255, 255, 215, 0)
    $crownBrush = New-Object System.Drawing.SolidBrush($crownColor)
    $crownPoints = @(
        [System.Drawing.Point]::new($Size/2-15, 40),
        [System.Drawing.Point]::new($Size/2-10, 25),
        [System.Drawing.Point]::new($Size/2-5, 35),
        [System.Drawing.Point]::new($Size/2, 20),
        [System.Drawing.Point]::new($Size/2+5, 35),
        [System.Drawing.Point]::new($Size/2+10, 25),
        [System.Drawing.Point]::new($Size/2+15, 40)
    )
    $graphics.FillPolygon($crownBrush, $crownPoints)
    
    # نص "CAR" في الأعلى
    $titleFont = New-Object System.Drawing.Font("Arial", $Size/20, [System.Drawing.FontStyle]::Bold)
    $titleBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $titleRect = New-Object System.Drawing.RectangleF($Size/2-25, 60, 50, 20)
    $format = New-Object System.Drawing.StringFormat
    $format.Alignment = [System.Drawing.StringAlignment]::Center
    $graphics.DrawString("CAR", $titleFont, $titleBrush, $titleRect, $format)
    
    # نص "MGMT" في الأسفل
    $subtitleRect = New-Object System.Drawing.RectangleF($Size/2-30, 75, 60, 20)
    $graphics.DrawString("MGMT", $titleFont, $titleBrush, $subtitleRect, $format)
    
    # نص المطور في الأسفل
    $devFont = New-Object System.Drawing.Font("Arial", $Size/32, [System.Drawing.FontStyle]::Regular)
    $devBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(200, 255, 255, 255))
    $devRect = New-Object System.Drawing.RectangleF(20, $Size-40, $Size-40, 15)
    $graphics.DrawString("by Amr Ali", $devFont, $devBrush, $devRect, $format)
    
    # رقم الهاتف
    $phoneRect = New-Object System.Drawing.RectangleF(20, $Size-25, $Size-40, 15)
    $graphics.DrawString("01285626623", $devFont, $devBrush, $phoneRect, $format)
    
    # تنظيف الموارد
    $graphics.Dispose()
    $gradientBrush.Dispose()
    $borderPen.Dispose()
    $carBrush.Dispose()
    $carPen.Dispose()
    $windowBrush.Dispose()
    $wheelBrush.Dispose()
    $lightBrush.Dispose()
    $gearBrush.Dispose()
    $dollarBrush.Dispose()
    $crownBrush.Dispose()
    $titleBrush.Dispose()
    $devBrush.Dispose()
    $titleFont.Dispose()
    $devFont.Dispose()
    $dollarFont.Dispose()
    $format.Dispose()
    
    return $bitmap
}

# إنشاء ملف ICO متعدد الأحجام
function New-MultiSizeIcon {
    param([string]$OutputPath)
    
    Write-Host "📦 إنشاء ملف ICO متعدد الأحجام..." -ForegroundColor Yellow
    
    # إنشاء أحجام مختلفة
    $sizes = @(16, 24, 32, 48, 64, 128, 256)
    $bitmaps = @()
    
    foreach ($size in $sizes) {
        Write-Host "   📏 إنشاء حجم ${size}x${size}..." -ForegroundColor Gray
        $bitmap = New-ProfessionalIcon -OutputPath $OutputPath -Size $size
        $bitmaps += $bitmap
    }
    
    # حفظ كملف ICO
    try {
        # استخدام أكبر حجم كأساس
        $mainBitmap = $bitmaps[-1]
        $icon = [System.Drawing.Icon]::FromHandle($mainBitmap.GetHicon())
        
        # حفظ الملف
        $fileStream = [System.IO.File]::Create($OutputPath)
        $icon.Save($fileStream)
        $fileStream.Close()
        
        Write-Host "✅ تم حفظ ملف ICO: $OutputPath" -ForegroundColor Green
        
        # تنظيف الموارد
        $icon.Dispose()
        foreach ($bitmap in $bitmaps) {
            $bitmap.Dispose()
        }
        
        return $true
    }
    catch {
        Write-Host "❌ خطأ في حفظ ملف ICO: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# إنشاء ملف PNG
function New-PngIcon {
    param([string]$OutputPath, [int]$Size = 256)
    
    Write-Host "🖼️ إنشاء ملف PNG بحجم ${Size}x${Size}..." -ForegroundColor Yellow
    
    $bitmap = New-ProfessionalIcon -OutputPath $OutputPath -Size $Size
    
    try {
        $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        Write-Host "✅ تم حفظ ملف PNG: $OutputPath" -ForegroundColor Green
        $bitmap.Dispose()
        return $true
    }
    catch {
        Write-Host "❌ خطأ في حفظ ملف PNG: $($_.Exception.Message)" -ForegroundColor Red
        $bitmap.Dispose()
        return $false
    }
}

# إنشاء ملف SVG
function New-SvgIcon {
    param([string]$OutputPath)
    
    Write-Host "🎨 إنشاء ملف SVG..." -ForegroundColor Yellow
    
    $svgContent = @"
<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4682C8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#193C78;stop-opacity:1" />
    </radialGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- خلفية دائرية متدرجة -->
  <circle cx="128" cy="128" r="118" fill="url(#bgGradient)" stroke="#FFD700" stroke-width="4" filter="url(#shadow)"/>
  
  <!-- تاج ذهبي -->
  <polygon points="113,40 118,25 123,35 128,20 133,35 138,25 143,40" fill="#FFD700" stroke="#FFA500" stroke-width="1"/>
  
  <!-- نص CAR -->
  <text x="128" y="75" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white" text-anchor="middle">CAR</text>
  
  <!-- نص MGMT -->
  <text x="128" y="95" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white" text-anchor="middle">MGMT</text>
  
  <!-- جسم السيارة -->
  <rect x="64" y="118" width="128" height="25" fill="white" rx="3"/>
  
  <!-- مقدمة السيارة -->
  <rect x="49" y="123" width="15" height="15" fill="white" rx="2"/>
  
  <!-- نوافذ السيارة -->
  <rect x="69" y="110" width="30" height="12" fill="#ADD8E6" rx="2"/>
  <rect x="109" y="110" width="30" height="12" fill="#ADD8E6" rx="2"/>
  <rect x="149" y="110" width="30" height="12" fill="#ADD8E6" rx="2"/>
  
  <!-- عجلات السيارة -->
  <circle cx="85" cy="155" r="8" fill="#404040"/>
  <circle cx="85" cy="155" r="5" fill="#606060"/>
  <circle cx="135" cy="155" r="8" fill="#404040"/>
  <circle cx="135" cy="155" r="5" fill="#606060"/>
  <circle cx="170" cy="155" r="8" fill="#404040"/>
  <circle cx="170" cy="155" r="5" fill="#606060"/>
  
  <!-- مصابيح السيارة -->
  <circle cx="52" cy="126" r="3" fill="#FFFF00"/>
  <circle cx="52" cy="133" r="3" fill="#FFFF00"/>
  
  <!-- أيقونة الإدارة (ترس) -->
  <circle cx="206" cy="50" r="12" fill="#FFD700"/>
  <circle cx="206" cy="50" r="8" fill="#FFA500"/>
  <circle cx="206" cy="50" r="4" fill="#FFD700"/>
  
  <!-- رمز المبيعات ($) -->
  <text x="50" y="210" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#00FF00">$</text>
  
  <!-- معلومات المطور -->
  <text x="128" y="220" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.8)" text-anchor="middle">by Amr Ali Elawamy</text>
  <text x="128" y="235" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.8)" text-anchor="middle">01285626623</text>
  
  <!-- تأثيرات إضافية -->
  <circle cx="128" cy="128" r="118" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
</svg>
"@

    try {
        Set-Content -Path $OutputPath -Value $svgContent -Encoding UTF8
        Write-Host "✅ تم حفظ ملف SVG: $OutputPath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ خطأ في حفظ ملف SVG: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# تشغيل العملية
Write-Host "🚀 بدء إنشاء الأيقونات الاحترافية..." -ForegroundColor Cyan
Write-Host ""

# إنشاء الأيقونات
$success = 0

# إنشاء ملف ICO
if (New-MultiSizeIcon -OutputPath "professional-car-icon.ico") {
    $success++
}

# إنشاء ملف PNG
if (New-PngIcon -OutputPath "professional-car-icon.png" -Size 256) {
    $success++
}

# إنشاء ملف SVG
if (New-SvgIcon -OutputPath "professional-car-icon.svg") {
    $success++
}

Write-Host ""
Write-Host "📊 ملخص العملية:" -ForegroundColor Cyan
Write-Host "   ✅ تم إنشاء $success من 3 ملفات أيقونة" -ForegroundColor Green

if ($success -eq 3) {
    Write-Host ""
    Write-Host "🎉 تم إنشاء جميع الأيقونات بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📁 الملفات المنشأة:" -ForegroundColor White
    Write-Host "   🔹 professional-car-icon.ico - للاستخدام في Windows" -ForegroundColor Gray
    Write-Host "   🔹 professional-car-icon.png - للعرض والويب" -ForegroundColor Gray
    Write-Host "   🔹 professional-car-icon.svg - قابل للتكبير بدون فقدان الجودة" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🎨 مميزات الأيقونة الجديدة:" -ForegroundColor White
    Write-Host "   ✅ تصميم احترافي متدرج" -ForegroundColor Gray
    Write-Host "   ✅ سيارة مفصلة مع نوافذ وعجلات" -ForegroundColor Gray
    Write-Host "   ✅ رموز الإدارة والمبيعات" -ForegroundColor Gray
    Write-Host "   ✅ تاج ذهبي يرمز للتميز" -ForegroundColor Gray
    Write-Host "   ✅ معلومات المطور واضحة" -ForegroundColor Gray
    Write-Host "   ✅ ألوان احترافية متناسقة" -ForegroundColor Gray
    Write-Host ""
} else {
    Write-Host ""
    Write-Host "⚠️ تم إنشاء بعض الأيقونات فقط" -ForegroundColor Yellow
}

Write-Host "👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy" -ForegroundColor Green
Write-Host "📞 للتواصل: 01285626623" -ForegroundColor Green
Write-Host "📧 البريد: <EMAIL>" -ForegroundColor Green
Write-Host "🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy" -ForegroundColor Green

Write-Host ""
Read-Host "اضغط Enter للمتابعة"
