using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
public partial class SupplierSelectionForm : Form
{
    private TextBox txtSearch = null!;
    private DataGridView dgvSuppliers = null!;
    private Button btnSelect = null!;
    private Button btnCancel = null!;
    private Label lblInfo = null!;

    public int? SelectedSupplierId
    {
        get;
        private set;
    }

    public SupplierSelectionForm()
    {
        InitializeComponent();
        LoadSuppliers();
    }

    private void InitializeComponent()
    {
        this.Text = "اختيار المورد - Select Supplier";
        this.Size = new Size(800, 600);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(248, 249, 250);
        this.Icon = SystemIcons.Application;
        this.MaximizeBox = false;
        this.MinimizeBox = false;
        this.FormBorderStyle = FormBorderStyle.FixedDialog;

        // Header Panel
        var headerPanel = new Panel
        {
            Size = new Size(800, 60),
            Location = new Point(0, 0),
            BackColor = Color.FromArgb(52, 58, 64),
            Dock = DockStyle.Top
        };

        var headerLabel = new Label
        {
            Text = "اختيار المورد للطباعة",
            Font = new Font("Segoe UI", 16, FontStyle.Bold),
            ForeColor = Color.White,
            Location = new Point(20, 10),
            AutoSize = true
        };

        var headerSubLabel = new Label
        {
            Text = "اختر المورد الذي تريد طباعة كشف حسابه",
            Font = new Font("Segoe UI", 10),
            ForeColor = Color.FromArgb(206, 212, 218),
            Location = new Point(20, 35),
            AutoSize = true
        };

        headerPanel.Controls.AddRange(new Control[] { headerLabel, headerSubLabel });

        // Search Panel
        var searchPanel = new Panel
        {
            Size = new Size(760, 50),
            Location = new Point(20, 70),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblSearch = new Label
        {
            Text = "البحث:",
            Location = new Point(10, 15),
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            AutoSize = true
        };

        txtSearch = new TextBox
        {
            Location = new Point(70, 12),
            Size = new Size(300, 23),
            Font = new Font("Segoe UI", 10),
            PlaceholderText = "اسم المورد أو رقم الهاتف أو البريد الإلكتروني..."
        };
        txtSearch.TextChanged += TxtSearch_TextChanged;

        var btnSearch = new Button
        {
            Text = "بحث",
            Location = new Point(380, 11),
            Size = new Size(70, 25),
            BackColor = Color.FromArgb(0, 123, 255),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };
        btnSearch.Click += (s, e) => FilterSuppliers();

        var btnClearSearch = new Button
        {
            Text = "مسح",
            Location = new Point(460, 11),
            Size = new Size(70, 25),
            BackColor = Color.FromArgb(108, 117, 125),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };
        btnClearSearch.Click += (s, e) => ClearSearch();

        lblInfo = new Label
        {
            Text = "إجمالي الموردين: 0",
            Location = new Point(550, 15),
            Font = new Font("Segoe UI", 9, FontStyle.Bold),
            ForeColor = Color.FromArgb(40, 167, 69),
            AutoSize = true
        };

        searchPanel.Controls.AddRange(new Control[] { lblSearch, txtSearch, btnSearch, btnClearSearch, lblInfo });

        // DataGridView
        dgvSuppliers = new DataGridView
        {
            Location = new Point(20, 130),
            Size = new Size(760, 380),
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.Fixed3D,
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            Font = new Font("Segoe UI", 9)
        };

        SetupDataGridView();
        dgvSuppliers.DoubleClick += DgvSuppliers_DoubleClick;

        // Button Panel
        var buttonPanel = new Panel
        {
            Size = new Size(760, 50),
            Location = new Point(20, 520),
            BackColor = Color.FromArgb(248, 249, 250)
        };

        btnSelect = new Button
        {
            Text = "اختيار المورد",
            Location = new Point(550, 10),
            Size = new Size(100, 30),
            BackColor = Color.FromArgb(40, 167, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            Enabled = false
        };
        btnSelect.Click += BtnSelect_Click;

        btnCancel = new Button
        {
            Text = "إلغاء",
            Location = new Point(660, 10),
            Size = new Size(80, 30),
            BackColor = Color.FromArgb(220, 53, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 10, FontStyle.Bold)
        };
        btnCancel.Click += BtnCancel_Click;

        buttonPanel.Controls.AddRange(new Control[] { btnSelect, btnCancel });

        // Add controls to form
        this.Controls.AddRange(new Control[] { headerPanel, searchPanel, dgvSuppliers, buttonPanel });

        // Enable selection change event
        dgvSuppliers.SelectionChanged += DgvSuppliers_SelectionChanged;
    }

    private void SetupDataGridView()
    {
        dgvSuppliers.Columns.Clear();
        dgvSuppliers.Columns.Add("SupplierId", "رقم المورد");
        dgvSuppliers.Columns.Add("SupplierName", "اسم المورد");
        dgvSuppliers.Columns.Add("ResponsiblePerson", "الشخص المسؤول");
        dgvSuppliers.Columns.Add("Phone", "الهاتف");
        dgvSuppliers.Columns.Add("Email", "البريد الإلكتروني");
        dgvSuppliers.Columns.Add("Address", "العنوان");
        dgvSuppliers.Columns.Add("TotalOwed", "إجمالي المستحقات");
        dgvSuppliers.Columns.Add("TotalPaid", "إجمالي المدفوعات");
        dgvSuppliers.Columns.Add("Balance", "الرصيد");

        // Set column widths
        dgvSuppliers.Columns["SupplierId"].Width = 80;
        dgvSuppliers.Columns["SupplierName"].Width = 150;
        dgvSuppliers.Columns["ResponsiblePerson"].Width = 120;
        dgvSuppliers.Columns["Phone"].Width = 120;
        dgvSuppliers.Columns["Email"].Width = 150;
        dgvSuppliers.Columns["Address"].Width = 150;
        dgvSuppliers.Columns["TotalOwed"].Width = 100;
        dgvSuppliers.Columns["TotalPaid"].Width = 100;
        dgvSuppliers.Columns["Balance"].Width = 100;

        // Set column headers alignment
        foreach(DataGridViewColumn column in dgvSuppliers.Columns)
        {
            column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
            column.HeaderCell.Style.Font = new Font("Segoe UI", 9, FontStyle.Bold);
        }
    }

    private async void LoadSuppliers()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var suppliers = await context.Suppliers
                            .Include(s => s.Payments)
                            .OrderBy(s => s.SupplierName)
                            .ToListAsync();

            dgvSuppliers.Rows.Clear();

            foreach(var supplier in suppliers)
            {
                var totalOwed = supplier.TotalOwed;
                var totalPaid = supplier.Payments.Sum(p => p.Amount);
                var balance = totalOwed - totalPaid;

                dgvSuppliers.Rows.Add(
                    supplier.SupplierId,
                    supplier.SupplierName,
                    supplier.ResponsiblePerson ?? "",
                    supplier.Phone,
                    supplier.Email ?? "",
                    supplier.Address,
                    totalOwed.ToString("N0"),
                    totalPaid.ToString("N0"),
                    balance.ToString("N0")
                );
            }

            lblInfo.Text = $"إجمالي الموردين: {suppliers.Count}";
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات الموردين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void TxtSearch_TextChanged(object sender, EventArgs e)
    {
        if(txtSearch.Text.Length >= 2 || string.IsNullOrEmpty(txtSearch.Text))
        {
            FilterSuppliers();
        }
    }

    private async void FilterSuppliers()
    {
        try
        {
            string searchTerm = txtSearch.Text.Trim();

            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var query = context.Suppliers.Include(s => s.Payments).AsQueryable();

            if(!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(s =>
                                    s.SupplierName.Contains(searchTerm) ||
                                    s.Phone.Contains(searchTerm) ||
                                    (s.Email != null && s.Email.Contains(searchTerm)) ||
                                    (s.ResponsiblePerson != null && s.ResponsiblePerson.Contains(searchTerm)) ||
                                    s.Address.Contains(searchTerm)
                                   );
            }

            var suppliers = await query.OrderBy(s => s.SupplierName).ToListAsync();

            dgvSuppliers.Rows.Clear();

            foreach(var supplier in suppliers)
            {
                var totalOwed = supplier.TotalOwed;
                var totalPaid = supplier.Payments.Sum(p => p.Amount);
                var balance = totalOwed - totalPaid;

                dgvSuppliers.Rows.Add(
                    supplier.SupplierId,
                    supplier.SupplierName,
                    supplier.ResponsiblePerson ?? "",
                    supplier.Phone,
                    supplier.Email ?? "",
                    supplier.Address,
                    totalOwed.ToString("N0"),
                    totalPaid.ToString("N0"),
                    balance.ToString("N0")
                );
            }

            lblInfo.Text = $"إجمالي الموردين: {suppliers.Count}";
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ClearSearch()
    {
        txtSearch.Clear();
        LoadSuppliers();
    }

    private void DgvSuppliers_SelectionChanged(object sender, EventArgs e)
    {
        btnSelect.Enabled = dgvSuppliers.SelectedRows.Count > 0;
    }

    private void DgvSuppliers_DoubleClick(object sender, EventArgs e)
    {
        if(dgvSuppliers.SelectedRows.Count > 0)
        {
            SelectSupplier();
        }
    }

    private void BtnSelect_Click(object sender, EventArgs e)
    {
        SelectSupplier();
    }

    private void SelectSupplier()
    {
        if(dgvSuppliers.SelectedRows.Count == 0)
        {
            MessageBox.Show("يرجى اختيار مورد أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            SelectedSupplierId = Convert.ToInt32(dgvSuppliers.SelectedRows[0].Cells["SupplierId"].Value);
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في اختيار المورد: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void BtnCancel_Click(object sender, EventArgs e)
    {
        this.DialogResult = DialogResult.Cancel;
        this.Close();
    }
}
}
