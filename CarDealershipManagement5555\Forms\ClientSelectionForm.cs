using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
public partial class ClientSelectionForm : Form
{
    private TextBox txtSearch = null!;
    private DataGridView dgvClients = null!;
    private Button btnSelect = null!;
    private Button btnCancel = null!;
    private Label lblInfo = null!;

    public Customer? SelectedClient
    {
        get;
        private set;
    }

    public ClientSelectionForm()
    {
        InitializeComponent();
        LoadClients();
    }

    private void InitializeComponent()
    {
        this.Text = "اختيار العميل - Select Client";
        this.Size = new Size(800, 600);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(248, 249, 250);
        this.Icon = SystemIcons.Application;
        this.MaximizeBox = false;
        this.MinimizeBox = false;
        this.FormBorderStyle = FormBorderStyle.FixedDialog;

        // Header Panel
        var headerPanel = new Panel
        {
            Size = new Size(800, 60),
            Location = new Point(0, 0),
            BackColor = Color.FromArgb(52, 58, 64),
            Dock = DockStyle.Top
        };

        var headerLabel = new Label
        {
            Text = "اختيار العميل للطباعة",
            Font = new Font("Segoe UI", 16, FontStyle.Bold),
            ForeColor = Color.White,
            Location = new Point(20, 10),
            AutoSize = true
        };

        var headerSubLabel = new Label
        {
            Text = "اختر العميل الذي تريد طباعة تقريره",
            Font = new Font("Segoe UI", 10),
            ForeColor = Color.FromArgb(206, 212, 218),
            Location = new Point(20, 35),
            AutoSize = true
        };

        headerPanel.Controls.AddRange(new Control[] { headerLabel, headerSubLabel });

        // Search Panel
        var searchPanel = new Panel
        {
            Size = new Size(760, 50),
            Location = new Point(20, 70),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblSearch = new Label
        {
            Text = "البحث:",
            Location = new Point(10, 15),
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            AutoSize = true
        };

        txtSearch = new TextBox
        {
            Location = new Point(70, 12),
            Size = new Size(300, 23),
            Font = new Font("Segoe UI", 10),
            PlaceholderText = "اسم العميل أو رقم الهوية أو الهاتف..."
        };
        txtSearch.TextChanged += TxtSearch_TextChanged;

        var btnSearch = new Button
        {
            Text = "بحث",
            Location = new Point(380, 11),
            Size = new Size(70, 25),
            BackColor = Color.FromArgb(0, 123, 255),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };
        btnSearch.Click += (s, e) => FilterClients();

        var btnClearSearch = new Button
        {
            Text = "مسح",
            Location = new Point(460, 11),
            Size = new Size(70, 25),
            BackColor = Color.FromArgb(108, 117, 125),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };
        btnClearSearch.Click += (s, e) => ClearSearch();

        lblInfo = new Label
        {
            Text = "إجمالي العملاء: 0",
            Location = new Point(550, 15),
            Font = new Font("Segoe UI", 9, FontStyle.Bold),
            ForeColor = Color.FromArgb(40, 167, 69),
            AutoSize = true
        };

        searchPanel.Controls.AddRange(new Control[] { lblSearch, txtSearch, btnSearch, btnClearSearch, lblInfo });

        // DataGridView
        dgvClients = new DataGridView
        {
            Location = new Point(20, 130),
            Size = new Size(760, 380),
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.Fixed3D,
            AllowUserToAddRows = false,
            AllowUserToDeleteRows = false,
            ReadOnly = true,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            Font = new Font("Segoe UI", 9)
        };

        SetupDataGridView();
        dgvClients.DoubleClick += DgvClients_DoubleClick;

        // Button Panel
        var buttonPanel = new Panel
        {
            Size = new Size(760, 50),
            Location = new Point(20, 520),
            BackColor = Color.FromArgb(248, 249, 250)
        };

        btnSelect = new Button
        {
            Text = "اختيار العميل",
            Location = new Point(550, 10),
            Size = new Size(100, 30),
            BackColor = Color.FromArgb(40, 167, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            Enabled = false
        };
        btnSelect.Click += BtnSelect_Click;

        btnCancel = new Button
        {
            Text = "إلغاء",
            Location = new Point(660, 10),
            Size = new Size(80, 30),
            BackColor = Color.FromArgb(220, 53, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 10, FontStyle.Bold)
        };
        btnCancel.Click += BtnCancel_Click;

        buttonPanel.Controls.AddRange(new Control[] { btnSelect, btnCancel });

        // Add controls to form
        this.Controls.AddRange(new Control[] { headerPanel, searchPanel, dgvClients, buttonPanel });

        // Enable selection change event
        dgvClients.SelectionChanged += DgvClients_SelectionChanged;
    }

    private void SetupDataGridView()
    {
        dgvClients.Columns.Clear();
        dgvClients.Columns.Add("CustomerId", "رقم العميل");
        dgvClients.Columns.Add("FullName", "الاسم الكامل");
        dgvClients.Columns.Add("IdNumber", "رقم الهوية");
        dgvClients.Columns.Add("PrimaryPhone", "الهاتف الأساسي");
        dgvClients.Columns.Add("Email", "البريد الإلكتروني");
        dgvClients.Columns.Add("City", "المدينة");
        dgvClients.Columns.Add("SalesCount", "عدد المشتريات");
        dgvClients.Columns.Add("TotalPurchases", "إجمالي المشتريات");
        dgvClients.Columns.Add("LastPurchase", "آخر شراء");

        // Set column widths
        dgvClients.Columns["CustomerId"].Width = 80;
        dgvClients.Columns["FullName"].Width = 150;
        dgvClients.Columns["IdNumber"].Width = 120;
        dgvClients.Columns["PrimaryPhone"].Width = 120;
        dgvClients.Columns["Email"].Width = 150;
        dgvClients.Columns["City"].Width = 100;
        dgvClients.Columns["SalesCount"].Width = 80;
        dgvClients.Columns["TotalPurchases"].Width = 120;
        dgvClients.Columns["LastPurchase"].Width = 100;

        // Set column headers alignment
        foreach(DataGridViewColumn column in dgvClients.Columns)
        {
            column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
            column.HeaderCell.Style.Font = new Font("Segoe UI", 9, FontStyle.Bold);
        }
    }

    private async void LoadClients()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var customers = await context.Customers
                            .Include(c => c.Sales)
                            .OrderBy(c => c.FullName)
                            .ToListAsync();

            dgvClients.Rows.Clear();

            foreach(var customer in customers)
            {
                var salesCount = customer.Sales.Count;
                var totalPurchases = customer.Sales.Sum(s => s.ActualSellPrice);
                var lastPurchase = customer.Sales.OrderByDescending(s => s.SaleDate).FirstOrDefault()?.SaleDate;

                dgvClients.Rows.Add(
                    customer.CustomerId,
                    customer.FullName,
                    customer.IdNumber,
                    customer.PrimaryPhone,
                    customer.Email ?? "",
                    customer.Address,
                    salesCount,
                    totalPurchases.ToString("N0"),
                    lastPurchase?.ToString("yyyy-MM-dd") ?? "لا يوجد"
                );
            }

            lblInfo.Text = $"إجمالي العملاء: {customers.Count}";
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات العملاء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void TxtSearch_TextChanged(object sender, EventArgs e)
    {
        if(txtSearch.Text.Length >= 2 || string.IsNullOrEmpty(txtSearch.Text))
        {
            FilterClients();
        }
    }

    private async void FilterClients()
    {
        try
        {
            string searchTerm = txtSearch.Text.Trim();

            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var query = context.Customers.Include(c => c.Sales).AsQueryable();

            if(!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(c =>
                                    c.FullName.Contains(searchTerm) ||
                                    c.IdNumber.Contains(searchTerm) ||
                                    c.PrimaryPhone.Contains(searchTerm) ||
                                    c.SecondaryPhone.Contains(searchTerm) ||
                                    (c.Email != null && c.Email.Contains(searchTerm)) ||
                                    c.Address.Contains(searchTerm)
                                   );
            }

            var customers = await query.OrderBy(c => c.FullName).ToListAsync();

            dgvClients.Rows.Clear();

            foreach(var customer in customers)
            {
                var salesCount = customer.Sales.Count;
                var totalPurchases = customer.Sales.Sum(s => s.ActualSellPrice);
                var lastPurchase = customer.Sales.OrderByDescending(s => s.SaleDate).FirstOrDefault()?.SaleDate;

                dgvClients.Rows.Add(
                    customer.CustomerId,
                    customer.FullName,
                    customer.IdNumber,
                    customer.PrimaryPhone,
                    customer.Email ?? "",
                    customer.Address,
                    salesCount,
                    totalPurchases.ToString("N0"),
                    lastPurchase?.ToString("yyyy-MM-dd") ?? "لا يوجد"
                );
            }

            lblInfo.Text = $"إجمالي العملاء: {customers.Count}";
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ClearSearch()
    {
        txtSearch.Clear();
        LoadClients();
    }

    private void DgvClients_SelectionChanged(object sender, EventArgs e)
    {
        btnSelect.Enabled = dgvClients.SelectedRows.Count > 0;
    }

    private void DgvClients_DoubleClick(object sender, EventArgs e)
    {
        if(dgvClients.SelectedRows.Count > 0)
        {
            SelectClient();
        }
    }

    private async void BtnSelect_Click(object sender, EventArgs e)
    {
        SelectClient();
    }

    private async void SelectClient()
    {
        if(dgvClients.SelectedRows.Count == 0)
        {
            MessageBox.Show("يرجى اختيار عميل أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            int customerId = Convert.ToInt32(dgvClients.SelectedRows[0].Cells["CustomerId"].Value);

            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            SelectedClient = await context.Customers
                             .Include(c => c.Sales)
                             .ThenInclude(s => s.Car)
                             .FirstOrDefaultAsync(c => c.CustomerId == customerId);

            if(SelectedClient != null)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("خطأ في تحديد العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في اختيار العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void BtnCancel_Click(object sender, EventArgs e)
    {
        this.DialogResult = DialogResult.Cancel;
        this.Close();
    }
}
}
