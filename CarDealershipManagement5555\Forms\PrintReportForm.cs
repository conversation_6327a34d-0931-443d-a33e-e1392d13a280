using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using CarDealershipManagement.Models;

namespace CarDealershipManagement.Forms
{
public partial class PrintReportForm : Form
{
    private DataGridView dgvToPrint;
    private int currentRow = 0;
    private int pageNumber = 1;
    private string reportName;

    public PrintReportForm(DataGridView dgv, string reportName = null)
    {
        InitializeComponent();
        this.dgvToPrint = dgv;
        this.reportName = reportName ?? this.Text ?? "تقرير";
        this.printPreviewControl.Document = this.printDocument;
    }

    private void btnPrint_Click(object sender, EventArgs e)
    {
        PrintDialog printDialog = new PrintDialog();
        printDialog.Document = this.printDocument;
        if(printDialog.ShowDialog() == DialogResult.OK)
        {
            this.printDocument.Print();
        }
    }

    private void printDocument_PrintPage(object sender, PrintPageEventArgs e)
    {
        Graphics g = e.Graphics;
        Font titleFont = new Font("Arial", 20, FontStyle.Bold);
        Font headerFont = new Font("Arial", 11, FontStyle.Bold);
        Font bodyFont = new Font("Arial", 10);
        float yPos = 0;
        float leftMargin = e.MarginBounds.Left;
        float topMargin = e.MarginBounds.Top;
        float bottomMargin = e.MarginBounds.Bottom;
        float tableWidth = e.MarginBounds.Width;

        // --- 1. حساب عرض الأعمدة بذكاء ---
        float[] columnWidths = CalculateColumnWidths(g, headerFont, bodyFont, tableWidth);

        // --- 2. رسم عنوان التقرير ---
        string reportTitle = this.reportName ?? this.Text ?? "تقرير";
        yPos = topMargin;
        g.DrawString(reportTitle, titleFont, Brushes.Black, leftMargin + (tableWidth - g.MeasureString(reportTitle, titleFont).Width) / 2, yPos);
        yPos += titleFont.Height + 20;

        // --- 3. رسم رأس الجدول ---
        float xPos = leftMargin;
        float headerHeight = 0;
        for(int i = 0; i < dgvToPrint.Columns.Count; i++)
        {
            float currentHeaderHeight = g.MeasureString(dgvToPrint.Columns[i].HeaderText, headerFont, (int)columnWidths[i]).Height + 10;
            if(currentHeaderHeight > headerHeight)
            {
                headerHeight = currentHeaderHeight;
            }
        }

        for(int i = 0; i < dgvToPrint.Columns.Count; i++)
        {
            g.FillRectangle(Brushes.LightGray, xPos, yPos, columnWidths[i], headerHeight);
            g.DrawRectangle(Pens.Black, xPos, yPos, columnWidths[i], headerHeight);
            StringFormat headerFormat = new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center, FormatFlags = StringFormatFlags.DirectionRightToLeft };
            g.DrawString(dgvToPrint.Columns[i].HeaderText, headerFont, Brushes.Black, new RectangleF(xPos, yPos, columnWidths[i], headerHeight), headerFormat);
            xPos += columnWidths[i];
        }
        yPos += headerHeight;

        // --- 4. رسم صفوف الجدول ---
        while(currentRow < dgvToPrint.Rows.Count)
        {
            DataGridViewRow row = dgvToPrint.Rows[currentRow];
            float rowHeight = CalculateRowHeight(g, row, bodyFont, columnWidths);

            if(yPos + rowHeight > bottomMargin)
            {
                e.HasMorePages = true;
                pageNumber++;
                return;
            }

            xPos = leftMargin;
            for(int i = 0; i < dgvToPrint.Columns.Count; i++)
            {
                string cellText = row.Cells[i].Value?.ToString() ?? "";
                RectangleF cellBounds = new RectangleF(xPos, yPos, columnWidths[i], rowHeight);
                g.DrawRectangle(Pens.Black, cellBounds.X, cellBounds.Y, cellBounds.Width, cellBounds.Height);

                StringFormat format = new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center, FormatFlags = StringFormatFlags.DirectionRightToLeft };
                g.DrawString(cellText, bodyFont, Brushes.Black, cellBounds, format);
                xPos += columnWidths[i];
            }
            yPos += rowHeight;
            currentRow++;
        }

        // --- 5. رسم رقم الصفحة ---
        string pageNumText = "صفحة " + pageNumber;
        g.DrawString(pageNumText, bodyFont, Brushes.Black, leftMargin + (tableWidth - g.MeasureString(pageNumText, bodyFont).Width) / 2, bottomMargin + 20);

        // --- إعادة تعيين للمرة القادمة ---
        e.HasMorePages = false;
        currentRow = 0;
        pageNumber = 1;
    }

    private float[] CalculateColumnWidths(Graphics g, Font headerFont, Font bodyFont, float tableWidth)
    {
        float[] widths = new float[dgvToPrint.Columns.Count];
        float totalWidth = 0;

        for(int i = 0; i < dgvToPrint.Columns.Count; i++)
        {
            // ابدأ بعرض العنوان
            float maxWidth = g.MeasureString(dgvToPrint.Columns[i].HeaderText, headerFont).Width + 10;

            // تحقق من عرض البيانات في الصفوف (عينة كافية)
            foreach(DataGridViewRow row in dgvToPrint.Rows)
            {
                string cellValue = row.Cells[i].Value?.ToString() ?? "";
                float cellWidth = g.MeasureString(cellValue, bodyFont).Width + 10;
                if(cellWidth > maxWidth)
                {
                    maxWidth = cellWidth;
                }
            }
            widths[i] = maxWidth;
            totalWidth += maxWidth;
        }

        // تعديل العرض ليتناسب مع عرض الصفحة
        float ratio = tableWidth / totalWidth;
        for(int i = 0; i < widths.Length; i++)
        {
            widths[i] *= ratio;
        }

        return widths;
    }

    private float CalculateRowHeight(Graphics g, DataGridViewRow row, Font font, float[] columnWidths)
    {
        float maxHeight = font.Height + 10; // الحد الأدنى للارتفاع
        for(int i = 0; i < row.Cells.Count; i++)
        {
            string cellValue = row.Cells[i].Value?.ToString() ?? "";
            // حساب الارتفاع المطلوب للنص مع التفافه
            float requiredHeight = g.MeasureString(cellValue, font, (int)(columnWidths[i] - 10)).Height + 10;
            if(requiredHeight > maxHeight)
            {
                maxHeight = requiredHeight;
            }
        }
        return maxHeight;
    }

    private void btnPageSetup_Click(object sender, EventArgs e)
    {
        PageSetupDialog pageSetupDialog = new PageSetupDialog();
        pageSetupDialog.Document = this.printDocument;
        if(pageSetupDialog.ShowDialog() == DialogResult.OK)
        {
            // تحديث معاينة الطباعة بعد تغيير إعدادات الصفحة
            this.printPreviewControl.InvalidatePreview();
        }
    }

    private void btnZoomIn_Click(object sender, EventArgs e)
    {
        if(this.printPreviewControl.Zoom < 2.0)
        {
            this.printPreviewControl.Zoom += 0.25;
        }
    }

    private void btnZoomOut_Click(object sender, EventArgs e)
    {
        if(this.printPreviewControl.Zoom > 0.25)
        {
            this.printPreviewControl.Zoom -= 0.25;
        }
    }

    private void btnClose_Click(object sender, EventArgs e)
    {
        this.Close();
    }
}
}
