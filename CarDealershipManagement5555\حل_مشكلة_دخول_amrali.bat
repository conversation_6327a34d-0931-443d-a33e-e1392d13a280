@echo off
title حل مشكلة دخول amrali

echo.
echo ========================================
echo    حل مشكلة دخول المستخدم amrali
echo ========================================
echo.

echo المطور: Amr Ali Elawamy
echo الهاتف: 01285626623
echo البريد: <EMAIL>
echo.

echo جاري حل مشكلة تسجيل الدخول...
echo.

REM حذف قاعدة البيانات القديمة إذا كانت موجودة
if exist "CarDealership.db" (
    echo حذف قاعدة البيانات القديمة...
    del "CarDealership.db"
    echo تم حذف قاعدة البيانات القديمة
)

echo.
echo تشغيل البرنامج لإنشاء قاعدة بيانات جديدة...

REM تشغيل البرنامج لإنشاء قاعدة البيانات
start /wait /min "CarDealership" "bin\Debug\net8.0-windows\CarDealershipManagement.exe"

echo.
echo تم إنشاء قاعدة بيانات جديدة
echo.

echo بيانات تسجيل الدخول:
echo اسم المستخدم: amrali
echo كلمة المرور: braa
echo.

echo او يمكنك استخدام:
echo اسم المستخدم: admin
echo كلمة المرور: admin
echo.

echo تشغيل البرنامج للاختبار...
start "" "bin\Debug\net8.0-windows\CarDealershipManagement.exe"

echo.
echo تم تشغيل البرنامج
echo استخدم البيانات المذكورة أعلاه لتسجيل الدخول
echo.

echo المطور: Amr Ali Elawamy - 01285626623
echo.
pause
