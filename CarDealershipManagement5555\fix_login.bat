@echo off
title Fix Login Password
color 0A

echo.
echo ===============================================
echo           Fix Login Password Tool
echo ===============================================
echo.
echo This tool will fix the login password issue
echo.

echo Checking database file...
if exist "CarDealership.db" (
    echo ✅ Database file found
    echo.
    echo Deleting current database to reset password...
    del "CarDealership.db" >nul 2>&1
    echo ✅ Database deleted successfully
) else (
    echo ❌ Database file not found
)

echo.
echo ===============================================
echo              SOLUTION APPLIED
echo ===============================================
echo.
echo The database has been reset.
echo When you run the program next time:
echo.
echo 1. A new database will be created automatically
echo 2. Default user will be created with correct password
echo 3. Login credentials will be:
echo    Username: amrali
echo    Password: braa
echo.
echo ===============================================
echo.
echo Press any key to continue...
pause >nul

exit /b 0
