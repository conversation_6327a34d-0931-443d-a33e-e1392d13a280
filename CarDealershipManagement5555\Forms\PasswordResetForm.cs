using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class PasswordResetForm : Form
    {
        private Label lblUserInfo;
        private TextBox txtNewPassword;
        private TextBox txtConfirmPassword;
        private Button btnSave;
        private Button btnCancel;
        private CheckBox chkShowPassword;
        private Label lblStatus = null!;

        private int userId;
        private string username;

        public PasswordResetForm(int userId, string username)
        {
            this.userId = userId;
            this.username = username;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "إعادة تعيين كلمة المرور - Reset Password";
            this.Size = new Size(500, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(30, 215),
                Size = new Size(400, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Title
            var titleLabel = new Label
            {
                Text = "إعادة تعيين كلمة المرور",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64),
                Location = new Point(30, 20),
                Size = new Size(300, 25)
            };

            // User info
            lblUserInfo = new Label
            {
                Text = $"إعادة تعيين كلمة المرور للمستخدم: {username}",
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(30, 55),
                Size = new Size(400, 25)
            };

            // New password label and textbox
            var lblNewPassword = new Label
            {
                Text = "كلمة المرور الجديدة:",
                Location = new Point(30, 90),
                Size = new Size(120, 20)
            };

            txtNewPassword = new TextBox
            {
                Location = new Point(160, 88),
                Size = new Size(250, 23),
                UseSystemPasswordChar = true
            };

            // Confirm password label and textbox
            var lblConfirmPassword = new Label
            {
                Text = "تأكيد كلمة المرور:",
                Location = new Point(30, 125),
                Size = new Size(120, 20)
            };

            txtConfirmPassword = new TextBox
            {
                Location = new Point(160, 123),
                Size = new Size(250, 23),
                UseSystemPasswordChar = true
            };

            // Show password checkbox
            chkShowPassword = new CheckBox
            {
                Text = "إظهار كلمة المرور",
                Location = new Point(160, 155),
                Size = new Size(150, 20)
            };
            chkShowPassword.CheckedChanged += ChkShowPassword_CheckedChanged;

            // Buttons
            btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(160, 180),
                Size = new Size(75, 30),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(250, 180),
                Size = new Size(75, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel
            };
            btnCancel.FlatAppearance.BorderSize = 0;

            // Add controls to form
            this.Controls.AddRange(new Control[]
            {
                titleLabel, lblUserInfo, lblNewPassword, txtNewPassword,
                lblConfirmPassword, txtConfirmPassword, chkShowPassword,
                btnSave, btnCancel, lblStatus
            });

            this.CancelButton = btnCancel;
        }

        private void ChkShowPassword_CheckedChanged(object? sender, EventArgs e)
        {
            txtNewPassword.UseSystemPasswordChar = !chkShowPassword.Checked;
            txtConfirmPassword.UseSystemPasswordChar = !chkShowPassword.Checked;
        }

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = "";

            if (string.IsNullOrWhiteSpace(txtNewPassword.Text))
            {
                lblStatus.Text = "يرجى إدخال كلمة المرور الجديدة.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            if (txtNewPassword.Text.Length < 6)
            {
                lblStatus.Text = "يجب أن تكون كلمة المرور 6 أحرف على الأقل.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            if (txtNewPassword.Text != txtConfirmPassword.Text)
            {
                lblStatus.Text = "كلمتا المرور غير متطابقتين.";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            try
            {
                using var context = DbContextFactory.CreateContext();
                var user = await context.Users.FindAsync(userId);
                if (user != null)
                {
                    user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(txtNewPassword.Text);
                    user.ModifiedDate = DateTime.Now;
                    await context.SaveChangesAsync();

                    lblStatus.Text = "تم تحديث كلمة المرور بنجاح.";
                    lblStatus.ForeColor = Color.Green;

                    await Task.Delay(1500);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    lblStatus.Text = "لم يتم العثور على المستخدم.";
                    lblStatus.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, true, "حدث خطأ أثناء تحديث كلمة المرور.");
                lblStatus.Text = "حدث خطأ أثناء تحديث كلمة المرور.";
                lblStatus.ForeColor = Color.Red;
            }
        }
    }
}
