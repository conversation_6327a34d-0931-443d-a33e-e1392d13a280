# تشغيل برنامج إدارة معرض السيارات
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🚗 برنامج إدارة معرض السيارات 🚗" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$programPath = Join-Path $PSScriptRoot "bin\Debug\net8.0-windows\CarDealershipManagement.exe"

if (Test-Path $programPath) {
    Write-Host "✅ تم العثور على البرنامج" -ForegroundColor Green
    Write-Host ""
    Write-Host "🔑 بيانات الدخول:" -ForegroundColor Yellow
    Write-Host "   اسم المستخدم: amrali" -ForegroundColor White
    Write-Host "   كلمة المرور: braa" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 جاري فتح البرنامج..." -ForegroundColor Green
    Write-Host ""
    
    # تشغيل البرنامج
    Start-Process -FilePath $programPath -WorkingDirectory (Split-Path $programPath)
    
    Write-Host "✅ تم تشغيل البرنامج بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 للوصول إلى تبويب الأقساط المحسن:" -ForegroundColor Cyan
    Write-Host "   1. سجل الدخول باستخدام البيانات أعلاه" -ForegroundColor White
    Write-Host "   2. اختر 'التقارير' من القائمة الرئيسية" -ForegroundColor White
    Write-Host "   3. انتقل إلى تبويب '📅 تقارير الأقساط'" -ForegroundColor White
    Write-Host ""
    Write-Host "🎯 الميزات الجديدة:" -ForegroundColor Cyan
    Write-Host "   • 📊 إنشاء التقرير - تقرير شامل مع فلترة" -ForegroundColor White
    Write-Host "   • 🖨️ طباعة محسنة - طباعة احترافية" -ForegroundColor White
    Write-Host "   • 📤 تصدير - 5 صيغ مختلفة" -ForegroundColor White
    Write-Host "   • 📈 ملخص الأقساط - إحصائيات سريعة" -ForegroundColor White
    Write-Host ""
    
} else {
    Write-Host "❌ خطأ: لم يتم العثور على ملف البرنامج" -ForegroundColor Red
    Write-Host "📁 المسار المتوقع: $programPath" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔧 الحلول المقترحة:" -ForegroundColor Cyan
    Write-Host "   1. تأكد من بناء المشروع أولاً" -ForegroundColor White
    Write-Host "   2. تحقق من وجود الملف في المسار الصحيح" -ForegroundColor White
    Write-Host "   3. أعد تشغيل Visual Studio كمسؤول" -ForegroundColor White
    Write-Host ""
}

Write-Host "📞 للمساعدة أو الدعم الفني، اتصل بالمطور" -ForegroundColor Magenta
Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
