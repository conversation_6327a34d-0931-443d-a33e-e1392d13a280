using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
public partial class InstallmentPaymentForm : Form
{
    private DataGridView dgvInstallments;
    private NumericUpDown numPaymentAmount;
    private Button btnPayInstallment;
    private Button btnCancel;
    private Label lblSaleInfo;
    private Label lblCustomerInfo;
    private Label lblTotalDue;
    private Label lblTotalPaid;
    private Label lblRemainingAmount;

    private int saleId;

    public InstallmentPaymentForm(int saleId)
    {
        this.saleId = saleId;
        InitializeComponent();
        LoadData();
    }

    private void InitializeComponent()
    {
        this.Text = "دفع الأقساط - Installment Payment";
        this.Size = new Size(900, 600);
        this.StartPosition = FormStartPosition.CenterScreen;

        // Sale information labels
        lblSaleInfo = new Label { Location = new Point(20, 20), Size = new Size(400, 23), Font = new Font("Arial", 10, FontStyle.Bold) };
        lblCustomerInfo = new Label { Location = new Point(20, 50), Size = new Size(400, 23), Font = new Font("Arial", 10) };
        lblTotalDue = new Label { Location = new Point(450, 20), Size = new Size(200, 23), Font = new Font("Arial", 10) };
        lblTotalPaid = new Label { Location = new Point(450, 50), Size = new Size(200, 23), Font = new Font("Arial", 10) };
        lblRemainingAmount = new Label { Location = new Point(450, 80), Size = new Size(200, 23), Font = new Font("Arial", 10, FontStyle.Bold) };

        // Installments DataGridView
        dgvInstallments = new DataGridView
        {
            Location = new Point(20, 120),
            Size = new Size(850, 350),
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            ReadOnly = true,
            AllowUserToAddRows = false
        };

        // Payment controls
        var lblPaymentAmount = new Label { Text = "مبلغ الدفع:", Location = new Point(20, 490), Size = new Size(80, 23) };
        numPaymentAmount = new NumericUpDown { Location = new Point(110, 488), Size = new Size(150, 23), DecimalPlaces = 2, Maximum = 1000000 };

        btnPayInstallment = new Button { Text = "دفع القسط", Location = new Point(280, 485), Size = new Size(100, 30) };
        btnPayInstallment.Click += BtnPayInstallment_Click;

        btnCancel = new Button { Text = "إغلاق", Location = new Point(390, 485), Size = new Size(100, 30) };
        btnCancel.Click += (s, e) => this.Close();

        this.Controls.AddRange(new Control[]
        {
            lblSaleInfo, lblCustomerInfo, lblTotalDue, lblTotalPaid, lblRemainingAmount,
            dgvInstallments, lblPaymentAmount, numPaymentAmount, btnPayInstallment, btnCancel
        });
    }

    private async void LoadData()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var sale = await context.Sales
                       .Include(s => s.Car)
                       .Include(s => s.Customer)
                       .Include(s => s.InstallmentPayments)
                       .FirstOrDefaultAsync(s => s.SaleId == saleId);

            if(sale != null)
            {
                // Update sale information labels
                lblSaleInfo.Text = $"عملية البيع: {sale.Car.Brand} {sale.Car.Model} - {sale.CarChassisNumber}";
                lblCustomerInfo.Text = $"العميل: {sale.Customer.FullName} - {sale.Customer.IdNumber}";
                lblTotalDue.Text = $"إجمالي المبلغ: {sale.ActualSellPrice:C}";
                lblTotalPaid.Text = $"المدفوع: {sale.TotalPaid:C}";
                lblRemainingAmount.Text = $"المتبقي: {sale.RemainingAmount:C}";

                // Load installments
                var installments = sale.InstallmentPayments.OrderBy(i => i.InstallmentNumber).ToList();

                dgvInstallments.DataSource = installments.Select(i => new
                {
                    رقم_القسط = i.InstallmentNumber,
                    قيمة_القسط = i.InstallmentAmount.ToString("C"),
                    تاريخ_الاستحقاق = i.DueDate.ToString("yyyy-MM-dd"),
                    المدفوع = i.AmountPaid.ToString("C"),
                    تاريخ_الدفع = i.PaidDate?.ToString("yyyy-MM-dd") ?? "غير مدفوع",
                    الحالة = i.Status switch
                {
                    InstallmentStatus.Pending => "مستحق",
                    InstallmentStatus.Paid => "مدفوع",
                    InstallmentStatus.Overdue => "متأخر",
                    InstallmentStatus.PartiallyPaid => "مدفوع جزئياً",
                    _ => "غير محدد"
                },
                ملاحظات = i.Notes ?? "",
                InstallmentPaymentId = i.InstallmentPaymentId
            }).ToList();

                // Set selected installment payment amount
                if(dgvInstallments.Rows.Count > 0)
                {
                    dgvInstallments.SelectionChanged += (s, e) =>
                    {
                        if(dgvInstallments.SelectedRows.Count > 0)
                        {
                            var selectedRow = dgvInstallments.SelectedRows[0];
                            var installmentId = Convert.ToInt32(selectedRow.Cells["InstallmentPaymentId"].Value);
                            var installment = installments.FirstOrDefault(i => i.InstallmentPaymentId == installmentId);
                            if(installment != null && installment.Status != InstallmentStatus.Paid)
                            {
                                numPaymentAmount.Value = installment.InstallmentAmount - installment.AmountPaid;
                            }
                        }
                    };
                }
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void BtnPayInstallment_Click(object? sender, EventArgs e)
    {
        if(dgvInstallments.SelectedRows.Count == 0)
        {
            MessageBox.Show("يرجى اختيار قسط للدفع", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        if(numPaymentAmount.Value <= 0)
        {
            MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var selectedRow = dgvInstallments.SelectedRows[0];
            var installmentId = Convert.ToInt32(selectedRow.Cells["InstallmentPaymentId"].Value);

            var installment = await context.InstallmentPayments.FindAsync(installmentId);
            var sale = await context.Sales.FindAsync(saleId);

            if(installment != null && sale != null)
            {
                var paymentAmount = numPaymentAmount.Value;
                var remainingInstallmentAmount = installment.InstallmentAmount - installment.AmountPaid;

                if(paymentAmount > remainingInstallmentAmount)
                {
                    var result = MessageBox.Show($"المبلغ المدخل ({paymentAmount:C}) أكبر من المتبقي ({remainingInstallmentAmount:C}). هل تريد دفع المتبقي فقط؟",
                                                 "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if(result == DialogResult.Yes)
                    {
                        paymentAmount = remainingInstallmentAmount;
                    }
                    else
                    {
                        return;
                    }
                }

                // Update installment
                installment.AmountPaid += paymentAmount;
                installment.PaidDate = DateTime.Now;

                if(installment.AmountPaid >= installment.InstallmentAmount)
                {
                    installment.Status = InstallmentStatus.Paid;
                }
                else
                {
                    installment.Status = InstallmentStatus.PartiallyPaid;
                }

                // Update sale totals
                sale.TotalPaid += paymentAmount;
                sale.RemainingAmount = sale.ActualSellPrice - sale.TotalPaid;

                if(sale.RemainingAmount <= 0)
                {
                    sale.PaymentStatus = PaymentStatus.FullyPaid;
                    await context.SaveChangesAsync();

                    // Ask if user wants to delete car and customer after full payment
                    var deleteResult = MessageBox.Show(
                                           "تم دفع كامل المبلغ بنجاح! هل تريد حذف السيارة والعميل من النظام؟\n" +
                                           "ملاحظة: سيتم حذف السيارة والعميل وبيانات البيع نهائياً من قاعدة البيانات",
                                           "تم إنهاء الدفع - حذف البيانات",
                                           MessageBoxButtons.YesNo,
                                           MessageBoxIcon.Question);

                    if(deleteResult == DialogResult.Yes)
                    {
                        // Get car and customer for deletion
                        var carToDelete = await context.Cars.FindAsync(sale.CarChassisNumber);
                        var customerToDelete = await context.Customers.FindAsync(sale.CustomerId);

                        // Remove sale first due to foreign key constraints
                        context.Sales.Remove(sale);

                        if(carToDelete != null)
                        {
                            // Remove car (related images and documents will be cascade deleted)
                            context.Cars.Remove(carToDelete);
                        }

                        if(customerToDelete != null)
                        {
                            // Remove customer (related documents will be cascade deleted)
                            context.Customers.Remove(customerToDelete);
                        }

                        await context.SaveChangesAsync();
                        MessageBox.Show("تم إنهاء الدفع بنجاح وحذف جميع البيانات (السيارة، العميل، وعملية البيع)", "تم بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // Close the form as the sale is completed and deleted
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                        return;
                    }
                    else
                    {
                        MessageBox.Show("تم إنهاء جميع الأقساط بنجاح!", "تم إنهاء الدفع", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // Close the form as all installments are paid
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                        return;
                    }
                }
                else
                {
                    sale.PaymentStatus = PaymentStatus.PartiallyPaid;
                    await context.SaveChangesAsync();
                    MessageBox.Show($"تم دفع قسط بقيمة {paymentAmount:C} بنجاح.\nالمتبقي من إجمالي المبلغ: {sale.RemainingAmount:C}", "تم دفع القسط", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                LoadData(); // Refresh data
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في دفع القسط: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
}
