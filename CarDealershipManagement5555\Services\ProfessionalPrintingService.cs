using System.Drawing.Printing;
using System.Text;
using CarDealershipManagement.Models;
using CarDealershipManagement.Data;
using CarDealershipManagement.Forms;
using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Windows.Forms;

namespace CarDealershipManagement.Services
{
public static class ProfessionalPrintingService
{
    private static List<string> printLines = new List<string>();
    private static int currentLineIndex = 0;
    private static Font regularFont = new Font("Arial", 10);
    private static Font boldFont = new Font("Arial", 10, FontStyle.Bold);
    private static Font titleFont = new Font("Arial", 16, FontStyle.Bold);
    private static Font headerFont = new Font("Arial", 12, FontStyle.Bold);
    private static Font arabicFont = new Font("Arial Unicode MS", 10);
    private static Font arabicBoldFont = new Font("Arial Unicode MS", 10, FontStyle.Bold);
    private static Font arabicTitleFont = new Font("Arial Unicode MS", 16, FontStyle.Bold);

    // Company information
    private static string companyName = "معرض السيارات الحديث";
    private static string companyNameEn = "Modern Car Dealership";
    private static string companyAddress = "شارع الملك فهد، الرياض، المملكة العربية السعودية";
    private static string companyPhone = "0112345678";
    private static string companyEmail = "<EMAIL>";

    #region Client-Specific Report Printing

    /// <summary>
    /// Shows a client selection dialog and prints the selected client's statement
    /// </summary>
    public static void PrintClientSpecificStatement()
    {
        using var clientSelectionForm = new ClientSelectionForm();
        if(clientSelectionForm.ShowDialog() == DialogResult.OK)
        {
            var selectedClient = clientSelectionForm.SelectedClient;
            if(selectedClient != null)
            {
                PrintCustomerStatement(selectedClient.CustomerId);
            }
        }
    }

    /// <summary>
    /// Shows a client selection dialog and prints client-specific sales report
    /// </summary>
    public static void PrintClientSpecificSalesReport()
    {
        using var clientSelectionForm = new ClientSelectionForm();
        if(clientSelectionForm.ShowDialog() == DialogResult.OK)
        {
            var selectedClient = clientSelectionForm.SelectedClient;
            if(selectedClient != null)
            {
                using var dateRangeForm = new DateRangeSelectionForm();
                if(dateRangeForm.ShowDialog() == DialogResult.OK)
                {
                    PrintClientSalesReport(selectedClient.CustomerId, dateRangeForm.FromDate, dateRangeForm.ToDate);
                }
            }
        }
    }

    /// <summary>
    /// Shows a client selection dialog and prints client-specific installment report
    /// </summary>
    public static void PrintClientSpecificInstallmentReport()
    {
        using var clientSelectionForm = new ClientSelectionForm();
        if(clientSelectionForm.ShowDialog() == DialogResult.OK)
        {
            var selectedClient = clientSelectionForm.SelectedClient;
            if(selectedClient != null)
            {
                PrintClientInstallmentReport(selectedClient.CustomerId);
            }
        }
    }

    #endregion

    #region Enhanced Customer Statement Printing

    public static void PrintCustomerStatement(int customerId)
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var customer = context.Customers
                           .Include(c => c.Sales)
                           .ThenInclude(s => s.Car)
                           .Include(c => c.Sales)
                           .ThenInclude(s => s.InstallmentPayments)
                           .FirstOrDefault(c => c.CustomerId == customerId);

            if(customer == null)
            {
                MessageBox.Show("العميل غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            GenerateEnhancedCustomerStatementContent(customer);
            ShowPrintPreviewDialog();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة كشف حساب العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public static void PrintClientSalesReport(int customerId, DateTime fromDate, DateTime toDate)
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var customer = context.Customers
                           .Include(c => c.Sales)
                           .ThenInclude(s => s.Car)
                           .Include(c => c.Sales)
                           .ThenInclude(s => s.InstallmentPayments)
                           .FirstOrDefault(c => c.CustomerId == customerId);

            if(customer == null)
            {
                MessageBox.Show("العميل غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            var clientSales = customer.Sales
                              .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate)
                              .ToList();

            GenerateClientSalesReportContent(customer, clientSales, fromDate, toDate);
            ShowPrintPreviewDialog();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة تقرير مبيعات العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public static void PrintClientInstallmentReport(int customerId)
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var customer = context.Customers
                           .Include(c => c.Sales)
                           .ThenInclude(s => s.Car)
                           .Include(c => c.Sales)
                           .ThenInclude(s => s.InstallmentPayments)
                           .FirstOrDefault(c => c.CustomerId == customerId);

            if(customer == null)
            {
                MessageBox.Show("العميل غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            var installmentSales = customer.Sales
                                   .Where(s => s.PaymentMethod == PaymentMethod.Installment)
                                   .ToList();

            GenerateClientInstallmentReportContent(customer, installmentSales);
            ShowPrintPreviewDialog();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة تقرير أقساط العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    #region Enhanced Report Generation

    private static void GenerateEnhancedCustomerStatementContent(Customer customer)
    {
        printLines.Clear();

        // Header with company logo area
        AddReportHeader("كشف حساب العميل");
        AddSeparator();

        // Company information
        AddCompanyInfo();
        AddSeparator();

        // Customer information section
        AddSectionHeader("بيانات العميل");
        AddCustomerInfo(customer);
        AddSeparator();

        // Sales summary
        AddSectionHeader("ملخص المشتريات");
        AddSalesSummary(customer);
        AddSeparator();

        // Detailed sales information
        AddSectionHeader("تفاصيل المشتريات");
        AddDetailedSalesInfo(customer);
        AddSeparator();

        // Payment schedule (if applicable)
        if(customer.Sales.Any(s => s.PaymentMethod == PaymentMethod.Installment))
        {
            AddSectionHeader("جدول الأقساط");
            AddPaymentSchedule(customer);
            AddSeparator();
        }

        // Footer
        AddReportFooter();
    }

    private static void GenerateClientSalesReportContent(Customer customer, List<Sale> sales, DateTime fromDate, DateTime toDate)
    {
        printLines.Clear();

        // Header
        AddReportHeader("تقرير مبيعات العميل");
        AddSeparator();

        // Company information
        AddCompanyInfo();
        AddSeparator();

        // Report period
        printLines.Add($"الفترة: من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");
        printLines.Add($"Period: From {fromDate:yyyy-MM-dd} To {toDate:yyyy-MM-dd}");
        printLines.Add("");

        // Customer information
        AddSectionHeader("بيانات العميل");
        AddCustomerInfo(customer);
        AddSeparator();

        // Sales details
        AddSectionHeader("تفاصيل المبيعات");
        AddFilteredSalesDetails(sales);
        AddSeparator();

        // Sales summary
        AddSectionHeader("ملخص المبيعات");
        AddFilteredSalesSummary(sales);

        // Footer
        AddReportFooter();
    }

    private static void GenerateClientInstallmentReportContent(Customer customer, List<Sale> installmentSales)
    {
        printLines.Clear();

        // Header
        AddReportHeader("تقرير أقساط العميل");
        AddSeparator();

        // Company information
        AddCompanyInfo();
        AddSeparator();

        // Customer information
        AddSectionHeader("بيانات العميل");
        AddCustomerInfo(customer);
        AddSeparator();

        // Installment summary
        AddSectionHeader("ملخص الأقساط");
        AddInstallmentSummary(installmentSales);
        AddSeparator();

        // Detailed installment information
        AddSectionHeader("تفاصيل الأقساط");
        AddDetailedInstallmentInfo(installmentSales);
        AddSeparator();

        // Footer
        AddReportFooter();
    }

    #endregion

    #region Report Building Helpers

    private static void AddReportHeader(string arabicTitle)
    {
        printLines.Add("═".PadRight(80, '═'));
        printLines.Add("");
        printLines.Add(companyName.PadLeft(60));
        printLines.Add("");
        printLines.Add(arabicTitle.PadLeft(55));
        printLines.Add("");
        printLines.Add("═".PadRight(80, '═'));
        printLines.Add("");
    }

    private static void AddCompanyInfo()
    {
        printLines.Add("معلومات الشركة / Company Information:");
        printLines.Add("─".PadRight(40, '─'));
        printLines.Add($"العنوان: {companyAddress}");
        printLines.Add($"الهاتف: {companyPhone}");
        printLines.Add($"البريد الإلكتروني: {companyEmail}");
        printLines.Add($"تاريخ الطباعة: {DateTime.Now:yyyy-MM-dd HH:mm}");
        printLines.Add($"Print Date: {DateTime.Now:yyyy-MM-dd HH:mm}");
        printLines.Add("");
    }

    private static void AddArabicCompanyInfo()
    {
        printLines.Add("معلومات الشركة:");
        printLines.Add("─".PadRight(40, '─'));
        printLines.Add($"العنوان: {companyAddress}");
        printLines.Add($"الهاتف: {companyPhone}");
        printLines.Add($"البريد الإلكتروني: {companyEmail}");
        printLines.Add($"تاريخ الطباعة: {DateTime.Now:yyyy-MM-dd HH:mm}");
        printLines.Add("");
    }

    private static void AddSectionHeader(string arabicTitle)
    {
        printLines.Add(arabicTitle);
        printLines.Add("─".PadRight(50, '─'));
    }

    private static void AddCustomerInfo(Customer customer)
    {
        printLines.Add($"الاسم الكامل: {customer.FullName}");
        printLines.Add($"Full Name: {customer.FullName}");
        printLines.Add($"رقم الهوية: {customer.IdNumber}");
        printLines.Add($"ID Number: {customer.IdNumber}");
        printLines.Add($"الهاتف الأساسي: {customer.PrimaryPhone}");
        printLines.Add($"Primary Phone: {customer.PrimaryPhone}");

        if(!string.IsNullOrEmpty(customer.SecondaryPhone))
        {
            printLines.Add($"الهاتف الثانوي: {customer.SecondaryPhone}");
            printLines.Add($"Secondary Phone: {customer.SecondaryPhone}");
        }

        if(!string.IsNullOrEmpty(customer.Email))
        {
            printLines.Add($"البريد الإلكتروني: {customer.Email}");
            printLines.Add($"Email: {customer.Email}");
        }

        printLines.Add($"العنوان: {customer.Street}, {customer.Area}, {customer.City}, {customer.Country}");
        printLines.Add($"Address: {customer.Street}, {customer.Area}, {customer.City}, {customer.Country}");
        printLines.Add("");
    }

    private static void AddSalesSummary(Customer customer)
    {
        var totalSales = customer.Sales.Count;
        var totalAmount = customer.Sales.Sum(s => s.ActualSellPrice);
        var totalPaid = customer.Sales.Sum(s => s.TotalPaid);
        var totalRemaining = totalAmount - totalPaid;
        var cashSales = customer.Sales.Count(s => s.PaymentMethod == PaymentMethod.Cash);
        var installmentSales = customer.Sales.Count(s => s.PaymentMethod == PaymentMethod.Installment);

        printLines.Add($"إجمالي المبيعات: {totalSales} | Total Sales: {totalSales}");
        printLines.Add($"المبيعات النقدية: {cashSales} | Cash Sales: {cashSales}");
        printLines.Add($"المبيعات بالتقسيط: {installmentSales} | Installment Sales: {installmentSales}");
        printLines.Add($"إجمالي المبلغ: {totalAmount:C} | Total Amount: {totalAmount:C}");
        printLines.Add($"إجمالي المدفوع: {totalPaid:C} | Total Paid: {totalPaid:C}");
        printLines.Add($"إجمالي المتبقي: {totalRemaining:C} | Total Remaining: {totalRemaining:C}");
        printLines.Add("");
    }

    private static void AddDetailedSalesInfo(Customer customer)
    {
        foreach(var sale in customer.Sales.OrderByDescending(s => s.SaleDate))
        {
            printLines.Add($"رقم البيع: {sale.SaleId} | Sale ID: {sale.SaleId}");
            printLines.Add($"تاريخ البيع: {sale.SaleDate:yyyy-MM-dd} | Sale Date: {sale.SaleDate:yyyy-MM-dd}");
            printLines.Add($"السيارة: {sale.Car.Brand} {sale.Car.Model} ({sale.Car.Year})");
            printLines.Add($"Car: {sale.Car.Brand} {sale.Car.Model} ({sale.Car.Year})");
            printLines.Add($"رقم الشاسيه: {sale.Car.ChassisNumber}");
            printLines.Add($"Chassis Number: {sale.Car.ChassisNumber}");
            printLines.Add($"المبلغ الإجمالي: {sale.ActualSellPrice:C} | Total Amount: {sale.ActualSellPrice:C}");
            printLines.Add($"المبلغ المدفوع: {sale.TotalPaid:C} | Paid Amount: {sale.TotalPaid:C}");
            printLines.Add($"المبلغ المتبقي: {(sale.ActualSellPrice - sale.TotalPaid):C} | Remaining: {(sale.ActualSellPrice - sale.TotalPaid):C}");
            printLines.Add($"طريقة الدفع: {(sale.PaymentMethod == PaymentMethod.Cash ? "نقدي" : "تقسيط")} | Payment Method: {(sale.PaymentMethod == PaymentMethod.Cash ? "Cash" : "Installment")}");

            if(sale.PaymentMethod == PaymentMethod.Installment)
            {
                printLines.Add($"عدد الأقساط: {sale.NumberOfInstallments} | Number of Installments: {sale.NumberOfInstallments}");
                printLines.Add($"قيمة القسط: {sale.InstallmentAmount:C} | Installment Amount: {sale.InstallmentAmount:C}");
                if(sale.FirstInstallmentDate.HasValue)
                {
                    printLines.Add($"تاريخ أول قسط: {sale.FirstInstallmentDate:yyyy-MM-dd} | First Installment Date: {sale.FirstInstallmentDate:yyyy-MM-dd}");
                }
            }

            printLines.Add("".PadRight(50, '·'));
        }
        printLines.Add("");
    }

    private static void AddPaymentSchedule(Customer customer)
    {
        var installmentSales = customer.Sales.Where(s => s.PaymentMethod == PaymentMethod.Installment).ToList();

        foreach(var sale in installmentSales)
        {
            printLines.Add($"السيارة: {sale.Car.Brand} {sale.Car.Model} - رقم البيع: {sale.SaleId}");
            printLines.Add($"Car: {sale.Car.Brand} {sale.Car.Model} - Sale ID: {sale.SaleId}");
            printLines.Add("جدولة الأقساط / Payment Schedule:");

            if(sale.InstallmentPayments != null && sale.InstallmentPayments.Any())
            {
                foreach(var payment in sale.InstallmentPayments.OrderBy(p => p.DueDate))
                {
                    var status = payment.Status == InstallmentStatus.Paid ? "مدفوع/Paid" :
                                 payment.Status == InstallmentStatus.Overdue ? "متأخر/Overdue" : "معلق/Pending";

                    printLines.Add($"  القسط {payment.InstallmentNumber}: {payment.InstallmentAmount:C} - {status}");
                    printLines.Add($"  تاريخ الاستحقاق: {payment.DueDate:yyyy-MM-dd} | Due Date: {payment.DueDate:yyyy-MM-dd}");

                    if(payment.PaidDate.HasValue)
                    {
                        printLines.Add($"  تاريخ الدفع: {payment.PaidDate:yyyy-MM-dd} | Paid Date: {payment.PaidDate:yyyy-MM-dd}");
                    }
                    printLines.Add("");
                }
            }
            else
            {
                // Generate theoretical payment schedule
                if(sale.FirstInstallmentDate.HasValue)
                {
                    for(int i = 1; i <= sale.NumberOfInstallments; i++)
                    {
                        var dueDate = sale.FirstInstallmentDate.Value.AddMonths(i - 1);
                        printLines.Add($"  القسط {i}: {sale.InstallmentAmount:C} - تاريخ الاستحقاق: {dueDate:yyyy-MM-dd}");
                        printLines.Add($"  Installment {i}: {sale.InstallmentAmount:C} - Due Date: {dueDate:yyyy-MM-dd}");
                    }
                }
            }

            printLines.Add("".PadRight(50, '·'));
        }
        printLines.Add("");
    }

    private static void AddFilteredSalesDetails(List<Sale> sales)
    {
        if(!sales.Any())
        {
            printLines.Add("لا توجد مبيعات في الفترة المحددة");
            printLines.Add("No sales found in the specified period");
            printLines.Add("");
            return;
        }

        foreach(var sale in sales.OrderByDescending(s => s.SaleDate))
        {
            printLines.Add($"تاريخ البيع: {sale.SaleDate:yyyy-MM-dd} | Sale Date: {sale.SaleDate:yyyy-MM-dd}");
            printLines.Add($"السيارة: {sale.Car.Brand} {sale.Car.Model} ({sale.Car.Year})");
            printLines.Add($"المبلغ: {sale.ActualSellPrice:C} | Amount: {sale.ActualSellPrice:C}");
            printLines.Add($"طريقة الدفع: {(sale.PaymentMethod == PaymentMethod.Cash ? "نقدي" : "تقسيط")} | Payment: {(sale.PaymentMethod == PaymentMethod.Cash ? "Cash" : "Installment")}");
            printLines.Add("".PadRight(40, '·'));
        }
        printLines.Add("");
    }

    private static void AddFilteredSalesSummary(List<Sale> sales)
    {
        var totalAmount = sales.Sum(s => s.ActualSellPrice);
        var totalPaid = sales.Sum(s => s.TotalPaid);
        var cashSales = sales.Count(s => s.PaymentMethod == PaymentMethod.Cash);
        var installmentSales = sales.Count(s => s.PaymentMethod == PaymentMethod.Installment);

        printLines.Add($"عدد المبيعات: {sales.Count} | Total Sales: {sales.Count}");
        printLines.Add($"المبيعات النقدية: {cashSales} | Cash Sales: {cashSales}");
        printLines.Add($"المبيعات بالتقسيط: {installmentSales} | Installment Sales: {installmentSales}");
        printLines.Add($"إجمالي المبلغ: {totalAmount:C} | Total Amount: {totalAmount:C}");
        printLines.Add($"إجمالي المدفوع: {totalPaid:C} | Total Paid: {totalPaid:C}");
        printLines.Add("");
    }

    private static void AddInstallmentSummary(List<Sale> installmentSales)
    {
        var totalAmount = installmentSales.Sum(s => s.ActualSellPrice);
        var totalPaid = installmentSales.Sum(s => s.TotalPaid);
        var totalRemaining = totalAmount - totalPaid;
        var totalInstallments = installmentSales.Sum(s => s.NumberOfInstallments);

        printLines.Add($"عدد المبيعات بالتقسيط: {installmentSales.Count} | Installment Sales: {installmentSales.Count}");
        printLines.Add($"إجمالي المبلغ: {totalAmount:C} | Total Amount: {totalAmount:C}");
        printLines.Add($"إجمالي المدفوع: {totalPaid:C} | Total Paid: {totalPaid:C}");
        printLines.Add($"إجمالي المتبقي: {totalRemaining:C} | Total Remaining: {totalRemaining:C}");
        printLines.Add($"إجمالي عدد الأقساط: {totalInstallments} | Total Installments: {totalInstallments}");
        printLines.Add("");
    }

    private static void AddDetailedInstallmentInfo(List<Sale> installmentSales)
    {
        foreach(var sale in installmentSales.OrderByDescending(s => s.SaleDate))
        {
            printLines.Add($"رقم البيع: {sale.SaleId} | Sale ID: {sale.SaleId}");
            printLines.Add($"تاريخ البيع: {sale.SaleDate:yyyy-MM-dd} | Sale Date: {sale.SaleDate:yyyy-MM-dd}");
            printLines.Add($"السيارة: {sale.Car.Brand} {sale.Car.Model} ({sale.Car.Year})");
            printLines.Add($"المبلغ الإجمالي: {sale.ActualSellPrice:C} | Total Amount: {sale.ActualSellPrice:C}");
            printLines.Add($"الدفعة الأولى: {sale.DownPayment:C} | Down Payment: {sale.DownPayment:C}");
            printLines.Add($"عدد الأقساط: {sale.NumberOfInstallments} | Number of Installments: {sale.NumberOfInstallments}");
            printLines.Add($"قيمة القسط: {sale.InstallmentAmount:C} | Installment Amount: {sale.InstallmentAmount:C}");
            printLines.Add($"المبلغ المدفوع: {sale.TotalPaid:C} | Total Paid: {sale.TotalPaid:C}");
            printLines.Add($"المبلغ المتبقي: {(sale.ActualSellPrice - sale.TotalPaid):C} | Remaining: {(sale.ActualSellPrice - sale.TotalPaid):C}");

            if(sale.FirstInstallmentDate.HasValue)
            {
                printLines.Add($"تاريخ أول قسط: {sale.FirstInstallmentDate:yyyy-MM-dd} | First Installment: {sale.FirstInstallmentDate:yyyy-MM-dd}");
            }

            printLines.Add("".PadRight(50, '·'));
        }
        printLines.Add("");
    }

    private static void AddSeparator()
    {
        printLines.Add("");
    }

    private static void AddReportFooter()
    {
        printLines.Add("");
        printLines.Add("═".PadRight(80, '═'));
        printLines.Add("شكراً لتعاملكم معنا".PadLeft(50));
        printLines.Add("Thank you for your business".PadLeft(50));
        printLines.Add("");
        printLines.Add("هذا التقرير تم إنشاؤه آلياً من نظام إدارة معرض السيارات".PadLeft(65));
        printLines.Add("This report was generated automatically from the Car Dealership Management System".PadLeft(75));
        printLines.Add("");
        printLines.Add("═".PadRight(80, '═'));
    }

    #endregion

    #region Print Dialog and Preview

    private static void ShowPrintPreviewDialog()
    {
        try
        {
            using var printPreviewDialog = new PrintPreviewDialog();
            using var printDocument = new PrintDocument();

            printDocument.PrintPage += PrintDocument_PrintPage;
            printPreviewDialog.Document = printDocument;
            printPreviewDialog.Width = 800;
            printPreviewDialog.Height = 600;
            printPreviewDialog.StartPosition = FormStartPosition.CenterScreen;

            if(printPreviewDialog.ShowDialog() == DialogResult.OK)
            {
                ShowPrintDialog(printDocument);
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void ShowPrintDialog(PrintDocument printDocument)
    {
        try
        {
            using var printDialog = new PrintDialog();
            printDialog.Document = printDocument;
            printDialog.UseEXDialog = true;
            printDialog.AllowCurrentPage = true;
            printDialog.AllowSomePages = true;
            printDialog.AllowSelection = false;

            if(printDialog.ShowDialog() == DialogResult.OK)
            {
                currentLineIndex = 0;
                printDocument.Print();
                MessageBox.Show("تم طباعة التقرير بنجاح", "تم بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
    {
        try
        {
            float yPos = 0;
            int count = 0;
            float leftMargin = e.MarginBounds.Left;
            float topMargin = e.MarginBounds.Top;
            string line = "";

            // Calculate the number of lines per page
            float linesPerPage = e.MarginBounds.Height / regularFont.GetHeight(e.Graphics!);

            // Print each line of the document
            while(count < linesPerPage && currentLineIndex < printLines.Count)
            {
                line = printLines[currentLineIndex];
                yPos = topMargin + (count * regularFont.GetHeight(e.Graphics!));

                // Choose appropriate font based on content
                Font currentFont = GetFontForLine(line);
                Brush brush = Brushes.Black;

                // Special formatting for different line types
                if(line.Contains("═") && line.Length > 20)
                {
                    currentFont = boldFont;
                    brush = Brushes.DarkBlue;
                }
                else if(line.Contains("─") && line.Length > 10)
                {
                    currentFont = boldFont;
                    brush = Brushes.Gray;
                }
                else if(line.Contains("تقرير") || line.Contains("كشف حساب") || line.Contains("Report") || line.Contains("Statement"))
                {
                    currentFont = titleFont;
                    brush = Brushes.DarkBlue;
                }
                else if(line.Contains(companyName) || line.Contains(companyNameEn))
                {
                    currentFont = headerFont;
                    brush = Brushes.DarkGreen;
                }

                // Draw the line
                e.Graphics.DrawString(line, currentFont, brush, leftMargin, yPos);

                count++;
                currentLineIndex++;
            }

            // If more lines exist, print another page
            if(currentLineIndex < printLines.Count)
            {
                e.HasMorePages = true;
            }
            else
            {
                e.HasMorePages = false;
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الصفحة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            e.HasMorePages = false;
        }
    }

    private static Font GetFontForLine(string line)
    {
        // Check if line contains Arabic characters
        bool hasArabic = line.Any(c => c >= 0x0600 && c <= 0x06FF);

        if(hasArabic)
        {
            if(line.Contains("تقرير") || line.Contains("كشف حساب"))
            {
                return arabicTitleFont;
            }
            else if(line.Contains("═") || line.Contains("─"))
            {
                return arabicBoldFont;
            }
            else
            {
                return arabicFont;
            }
        }
        else
        {
            if(line.Contains("Report") || line.Contains("Statement"))
            {
                return titleFont;
            }
            else if(line.Contains("═") || line.Contains("─"))
            {
                return boldFont;
            }
            else
            {
                return regularFont;
            }
        }
    }

    #endregion

    #region Legacy Methods (Enhanced)

    public static void PrintSupplierStatement(int supplierId)
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var supplier = context.Suppliers
                           .Include(s => s.Payments)
                           .FirstOrDefault(s => s.SupplierId == supplierId);

            if(supplier == null)
            {
                MessageBox.Show("المورد غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            GenerateEnhancedSupplierStatementContent(supplier);
            ShowPrintPreviewDialog();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة كشف حساب المورد: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void GenerateEnhancedSupplierStatementContent(Supplier supplier)
    {
        printLines.Clear();

        // Header
        AddReportHeader("كشف حساب المورد");
        AddSeparator();

        // Company information
        AddCompanyInfo();
        AddSeparator();

        // Supplier information
        AddSectionHeader("بيانات المورد");
        printLines.Add($"اسم المورد: {supplier.SupplierName}");
        printLines.Add($"Supplier Name: {supplier.SupplierName}");
        printLines.Add($"الشخص المسؤول: {supplier.ResponsiblePerson ?? "غير محدد"}");
        printLines.Add($"Responsible Person: {supplier.ResponsiblePerson ?? "Not specified"}");
        printLines.Add($"الهاتف: {supplier.Phone}");
        printLines.Add($"Phone: {supplier.Phone}");
        printLines.Add($"البريد الإلكتروني: {supplier.Email ?? "غير محدد"}");
        printLines.Add($"Email: {supplier.Email ?? "Not specified"}");
        printLines.Add($"العنوان: {supplier.Address}");
        printLines.Add($"Address: {supplier.Address}");
        AddSeparator();

        // Financial summary
        AddSectionHeader("الملخص المالي");
        var totalOwed = supplier.TotalOwed;
        var totalPaid = supplier.Payments.Sum(p => p.Amount);
        var balance = totalOwed - totalPaid;

        printLines.Add($"إجمالي المستحقات: {totalOwed:C} | Total Owed: {totalOwed:C}");
        printLines.Add($"إجمالي المدفوعات: {totalPaid:C} | Total Paid: {totalPaid:C}");
        printLines.Add($"الرصيد: {balance:C} | Balance: {balance:C}");
        AddSeparator();

        // Payment details
        AddSectionHeader("تفاصيل المدفوعات");

        if(supplier.Payments.Any())
        {
            foreach(var payment in supplier.Payments.OrderByDescending(p => p.PaymentDate))
            {
                printLines.Add($"تاريخ الدفع: {payment.PaymentDate:yyyy-MM-dd} | Payment Date: {payment.PaymentDate:yyyy-MM-dd}");
                printLines.Add($"المبلغ: {payment.Amount:C} | Amount: {payment.Amount:C}");
                printLines.Add($"الملاحظات: {payment.Notes ?? "لا توجد"} | Notes: {payment.Notes ?? "None"}");
                printLines.Add("".PadRight(40, '·'));
            }
        }
        else
        {
            printLines.Add("لا توجد مدفوعات مسجلة");
            printLines.Add("No payments recorded");
        }

        AddSeparator();
        AddReportFooter();
    }

    public static void PrintSalesReport(DateTime fromDate, DateTime toDate)
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var sales = context.Sales
                        .Include(s => s.Car)
                        .Include(s => s.Customer)
                        .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate)
                        .OrderByDescending(s => s.SaleDate)
                        .ToList();

            GenerateEnhancedSalesReportContent(sales, fromDate, toDate);
            ShowPrintPreviewDialog();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة تقرير المبيعات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public static void PrintInventoryReport(List<Car> cars)
    {
        try
        {
            GenerateEnhancedInventoryReportContent(cars);
            ShowPrintPreviewDialog();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة تقرير المخزون: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void GenerateEnhancedSalesReportContent(List<Sale> sales, DateTime fromDate, DateTime toDate)
    {
        printLines.Clear();

        // Header
        AddReportHeader("تقرير المبيعات");
        AddSeparator();

        // Company information
        AddCompanyInfo();
        AddSeparator();

        // Report period
        printLines.Add($"الفترة: من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");
        printLines.Add($"Period: From {fromDate:yyyy-MM-dd} To {toDate:yyyy-MM-dd}");
        AddSeparator();

        // Summary
        AddSectionHeader("ملخص المبيعات");
        var totalAmount = sales.Sum(s => s.ActualSellPrice);
        var totalPaid = sales.Sum(s => s.TotalPaid);
        var cashSales = sales.Count(s => s.PaymentMethod == PaymentMethod.Cash);
        var installmentSales = sales.Count(s => s.PaymentMethod == PaymentMethod.Installment);

        printLines.Add($"عدد المبيعات: {sales.Count} | Total Sales: {sales.Count}");
        printLines.Add($"المبيعات النقدية: {cashSales} | Cash Sales: {cashSales}");
        printLines.Add($"المبيعات بالتقسيط: {installmentSales} | Installment Sales: {installmentSales}");
        printLines.Add($"إجمالي المبيعات: {totalAmount:C} | Total Sales Amount: {totalAmount:C}");
        printLines.Add($"إجمالي المدفوع: {totalPaid:C} | Total Paid: {totalPaid:C}");
        printLines.Add($"المتوسط لكل بيعة: {(sales.Count > 0 ? totalAmount / sales.Count : 0):C} | Average per Sale: {(sales.Count > 0 ? totalAmount / sales.Count : 0):C}");
        AddSeparator();

        // Detailed sales
        AddSectionHeader("تفاصيل المبيعات");
        AddFilteredSalesDetails(sales);

        AddReportFooter();
    }

    private static void GenerateEnhancedInventoryReportContent(List<Car> cars)
    {
        printLines.Clear();

        // Header
        AddReportHeader("تقرير المخزون");
        AddSeparator();

        // Company information - Arabic only
        AddArabicCompanyInfo();
        AddSeparator();

        // Summary
        AddSectionHeader("ملخص المخزون");
        var totalCars = cars.Count;
        var soldCars = cars.Count(c => c.IsSold);
        var availableCars = totalCars - soldCars;
        var totalValue = cars.Where(c => !c.IsSold).Sum(c => c.PurchasePrice);

        printLines.Add($"إجمالي السيارات: {totalCars}");
        printLines.Add($"السيارات المباعة: {soldCars}");
        printLines.Add($"السيارات المتاحة: {availableCars}");
        printLines.Add($"إجمالي قيمة المخزون: {totalValue:C}");
        AddSeparator();

        // Available cars details
        AddSectionHeader("السيارات المتاحة");

        var availableCarsList = cars.Where(c => !c.IsSold).OrderBy(c => c.Brand).ThenBy(c => c.Model).ToList();

        if(availableCarsList.Any())
        {
            foreach(var car in availableCarsList)
            {
                printLines.Add($"الماركة: {car.Brand}");
                printLines.Add($"الموديل: {car.Model}");
                printLines.Add($"السنة: {car.Year}");
                printLines.Add($"اللون: {car.Color}");
                printLines.Add($"رقم الشاسيه: {car.ChassisNumber}");
                printLines.Add($"الحالة: {(car.Condition == CarCondition.New ? "جديدة" : "مستعملة")}");
                printLines.Add($"سعر الشراء: {car.PurchasePrice:C}");
                printLines.Add($"سعر البيع المقترح: {car.SuggestedSellPrice:C}");
                printLines.Add($"سعر البيع بالتقسيط: {car.InstallmentSellPrice:C}");
                printLines.Add("".PadRight(50, '·'));
            }
        }
        else
        {
            printLines.Add("لا توجد سيارات متاحة في المخزون");
        }

        AddSeparator();

        // Sold cars summary
        if(soldCars > 0)
        {
            AddSectionHeader("السيارات المباعة");
            var soldCarsList = cars.Where(c => c.IsSold).OrderBy(c => c.Brand).ThenBy(c => c.Model).ToList();

            foreach(var car in soldCarsList)
            {
                printLines.Add($"{car.Brand} {car.Model} ({car.Year}) - {car.ChassisNumber}");
            }

            AddSeparator();
        }

        AddReportFooter();
    }

    #endregion

    #region Financial Report Methods

    public static void PrintFinancialReport()
    {
        try
        {
            using(var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options))
            {
                var sales = context.Sales.Include(s => s.Car).Include(s => s.Customer).ToList();
                var installmentPayments = context.InstallmentPayments.Include(ip => ip.Sale).ThenInclude(s => s.Customer).ToList();

                // Generate report content
                GenerateFinancialReportContent(sales, installmentPayments);

                // Show print preview
                ShowPrintPreviewDialog();
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"Error generating financial report: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void GenerateFinancialReportContent(List<Sale> sales, List<InstallmentPayment> installmentPayments)
    {
        printLines.Clear();

        AddReportHeader("التقرير المالي");
        AddSeparator();

        // Calculate totals
        decimal totalSales = sales.Sum(s => s.ActualSellPrice);
        decimal totalPayments = installmentPayments.Sum(p => p.AmountPaid);
        decimal totalInstallments = sales.Where(s => s.PaymentMethod == PaymentMethod.Installment).Sum(s => s.ActualSellPrice);
        decimal totalCashSales = sales.Where(s => s.PaymentMethod == PaymentMethod.Cash).Sum(s => s.ActualSellPrice);
        decimal outstandingBalance = totalInstallments - installmentPayments.Where(p => p.Sale.PaymentMethod == PaymentMethod.Installment).Sum(p => p.AmountPaid);

        // Financial summary
        AddSectionHeader("الملخص المالي");
        printLines.Add($"Total Sales / إجمالي المبيعات: {totalSales:C}");
        printLines.Add($"Cash Sales / المبيعات النقدية: {totalCashSales:C}");
        printLines.Add($"Installment Sales / المبيعات بالتقسيط: {totalInstallments:C}");
        printLines.Add($"Total Payments Received / إجمالي المدفوعات: {totalPayments:C}");
        printLines.Add($"Outstanding Balance / الرصيد المستحق: {outstandingBalance:C}");
        printLines.Add("");

        AddSeparator();

        // Monthly breakdown
        AddSectionHeader("التفصيل الشهري");
        var monthlySales = sales.GroupBy(s => new
        {
            s.SaleDate.Year, s.SaleDate.Month
        })
        .OrderByDescending(g => g.Key.Year)
        .ThenByDescending(g => g.Key.Month)
        .Take(12)
        .ToList();

        foreach(var monthGroup in monthlySales)
        {
            string monthName = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1).ToString("MMMM yyyy");
            decimal monthTotal = monthGroup.Sum(s => s.ActualSellPrice);
            int salesCount = monthGroup.Count();

            printLines.Add($"{monthName}: {salesCount} sales, {monthTotal:C}");
        }

        AddSeparator();

        // Payment status breakdown
        AddSectionHeader("حالة المدفوعات");
        var installmentSales = sales.Where(s => s.PaymentMethod == PaymentMethod.Installment).ToList();

        foreach(var sale in installmentSales.Take(20))  // Show top 20 installment sales
        {
            var salePayments = installmentPayments.Where(p => p.SaleId == sale.SaleId).Sum(p => p.AmountPaid);
            var remaining = sale.ActualSellPrice - salePayments;
            var status = remaining <= 0 ? "Paid" : "Pending";

            printLines.Add($"{sale.Customer.FullName} - {sale.Car.Brand} {sale.Car.Model}: {remaining:C} remaining ({status})");
        }

        AddReportFooter();
    }

    #endregion
}
}
