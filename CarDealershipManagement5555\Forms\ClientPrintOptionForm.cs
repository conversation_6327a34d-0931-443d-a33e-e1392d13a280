using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
public partial class ClientPrintOptionForm : Form
{
    private RadioButton rbAllClients = null!;
    private RadioButton rbSpecificClient = null!;
    private RadioButton rbMultipleClients = null!;
    private ComboBox cmbClient = null!;
    private CheckedListBox clbClients = null!;
    private Button btnOK = null!;
    private Button btnCancel = null!;
    private Panel pnlClientSelection = null!;
    private Panel pnlMultipleSelection = null!;

    public PrintOption SelectedOption
    {
        get;
        private set;
    }
    public Customer? SelectedClient
    {
        get;
        private set;
    }
    public List<Customer> SelectedClients
    {
        get;
        private set;
    } = new();
    public DataGridView? OriginalDataGridView
    {
        get;
        set;
    }

    public enum PrintOption
    {
        AllClients,
        SpecificClient,
        MultipleClients
    }

    public ClientPrintOptionForm()
    {
        InitializeComponent();
        LoadClients();
    }

    private void InitializeComponent()
    {
        this.Text = "خيارات الطباعة - Print Options";
        this.Size = new Size(500, 450);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(248, 249, 250);
        this.Icon = SystemIcons.Application;
        this.MaximizeBox = false;
        this.MinimizeBox = false;
        this.FormBorderStyle = FormBorderStyle.FixedDialog;

        // Header Panel
        var headerPanel = new Panel
        {
            Size = new Size(500, 60),
            Location = new Point(0, 0),
            BackColor = Color.FromArgb(0, 123, 255),
            Dock = DockStyle.Top
        };

        var headerLabel = new Label
        {
            Text = "اختيار خيارات الطباعة",
            Font = new Font("Segoe UI", 14, FontStyle.Bold),
            ForeColor = Color.White,
            Location = new Point(20, 10),
            AutoSize = true
        };

        var headerSubLabel = new Label
        {
            Text = "اختر العملاء المراد طباعة تقاريرهم",
            Font = new Font("Segoe UI", 10),
            ForeColor = Color.FromArgb(220, 240, 255),
            Location = new Point(20, 35),
            AutoSize = true
        };

        headerPanel.Controls.AddRange(new Control[] { headerLabel, headerSubLabel });

        // Main content panel
        var mainPanel = new Panel
        {
            Location = new Point(20, 80),
            Size = new Size(460, 280),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        // Radio buttons
        rbAllClients = new RadioButton
        {
            Text = "طباعة تقارير جميع العملاء",
            Location = new Point(20, 20),
            Size = new Size(200, 25),
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 58, 64),
            Checked = true
        };
        rbAllClients.CheckedChanged += RbAllClients_CheckedChanged;

        rbSpecificClient = new RadioButton
        {
            Text = "طباعة تقرير عميل محدد",
            Location = new Point(20, 55),
            Size = new Size(200, 25),
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 58, 64)
        };
        rbSpecificClient.CheckedChanged += RbSpecificClient_CheckedChanged;

        rbMultipleClients = new RadioButton
        {
            Text = "طباعة تقارير عملاء متعددين",
            Location = new Point(20, 90),
            Size = new Size(200, 25),
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 58, 64)
        };
        rbMultipleClients.CheckedChanged += RbMultipleClients_CheckedChanged;

        // Single client selection panel
        pnlClientSelection = new Panel
        {
            Location = new Point(20, 125),
            Size = new Size(420, 40),
            BackColor = Color.FromArgb(248, 249, 250),
            BorderStyle = BorderStyle.FixedSingle,
            Visible = false
        };

        var lblSelectClient = new Label
        {
            Text = "اختر العميل:",
            Location = new Point(10, 10),
            Size = new Size(80, 20),
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };

        cmbClient = new ComboBox
        {
            Location = new Point(100, 8),
            Size = new Size(300, 25),
            DropDownStyle = ComboBoxStyle.DropDownList,
            Font = new Font("Segoe UI", 9)
        };

        pnlClientSelection.Controls.AddRange(new Control[] { lblSelectClient, cmbClient });

        // Multiple clients selection panel
        pnlMultipleSelection = new Panel
        {
            Location = new Point(20, 125),
            Size = new Size(420, 140),
            BackColor = Color.FromArgb(248, 249, 250),
            BorderStyle = BorderStyle.FixedSingle,
            Visible = false
        };

        var lblSelectMultiple = new Label
        {
            Text = "اختر العملاء (يمكن اختيار أكثر من عميل):",
            Location = new Point(10, 5),
            Size = new Size(400, 20),
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };

        clbClients = new CheckedListBox
        {
            Location = new Point(10, 25),
            Size = new Size(400, 105),
            Font = new Font("Segoe UI", 9),
            CheckOnClick = true
        };

        pnlMultipleSelection.Controls.AddRange(new Control[] { lblSelectMultiple, clbClients });

        mainPanel.Controls.AddRange(new Control[]
        {
            rbAllClients, rbSpecificClient, rbMultipleClients,
            pnlClientSelection, pnlMultipleSelection
        });

        // Button panel
        var buttonPanel = new Panel
        {
            Location = new Point(20, 370),
            Size = new Size(460, 50),
            BackColor = Color.FromArgb(248, 249, 250)
        };

        btnOK = new Button
        {
            Text = "موافق",
            Location = new Point(290, 10),
            Size = new Size(80, 30),
            BackColor = Color.FromArgb(40, 167, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 10, FontStyle.Bold)
        };
        btnOK.Click += BtnOK_Click;

        btnCancel = new Button
        {
            Text = "إلغاء",
            Location = new Point(380, 10),
            Size = new Size(80, 30),
            BackColor = Color.FromArgb(220, 53, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 10, FontStyle.Bold)
        };
        btnCancel.Click += BtnCancel_Click;

        buttonPanel.Controls.AddRange(new Control[] { btnOK, btnCancel });

        // Add controls to form
        this.Controls.AddRange(new Control[] { headerPanel, mainPanel, buttonPanel });
    }

    private async void LoadClients()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var customers = await context.Customers
                            .OrderBy(c => c.FullName)
                            .ToListAsync();

            cmbClient.Items.Clear();
            clbClients.Items.Clear();

            foreach(var customer in customers)
            {
                var displayText = $"{customer.FullName} - {customer.IdNumber}";
                cmbClient.Items.Add(new ComboBoxItem(customer, displayText));
                clbClients.Items.Add(new CheckListItem(customer, displayText));
            }

            if(cmbClient.Items.Count > 0)
            {
                cmbClient.SelectedIndex = 0;
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات العملاء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void RbAllClients_CheckedChanged(object sender, EventArgs e)
    {
        if(rbAllClients.Checked)
        {
            pnlClientSelection.Visible = false;
            pnlMultipleSelection.Visible = false;
        }
    }

    private void RbSpecificClient_CheckedChanged(object sender, EventArgs e)
    {
        if(rbSpecificClient.Checked)
        {
            pnlClientSelection.Visible = true;
            pnlMultipleSelection.Visible = false;
        }
    }

    private void RbMultipleClients_CheckedChanged(object sender, EventArgs e)
    {
        if(rbMultipleClients.Checked)
        {
            pnlClientSelection.Visible = false;
            pnlMultipleSelection.Visible = true;
        }
    }

    private void BtnOK_Click(object sender, EventArgs e)
    {
        try
        {
            if(rbAllClients.Checked)
            {
                SelectedOption = PrintOption.AllClients;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else if(rbSpecificClient.Checked)
            {
                if(cmbClient.SelectedItem is ComboBoxItem selectedItem)
                {
                    SelectedOption = PrintOption.SpecificClient;
                    SelectedClient = selectedItem.Customer;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار عميل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else if(rbMultipleClients.Checked)
            {
                var checkedItems = clbClients.CheckedItems.Cast<CheckListItem>().ToList();
                if(checkedItems.Count > 0)
                {
                    SelectedOption = PrintOption.MultipleClients;
                    SelectedClients = checkedItems.Select(item => item.Customer).ToList();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار عميل واحد على الأقل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحديد الخيار: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void BtnCancel_Click(object sender, EventArgs e)
    {
        this.DialogResult = DialogResult.Cancel;
        this.Close();
    }

    // Helper classes for combo box and checklist items
    public class ComboBoxItem
    {
        public Customer Customer
        {
            get;
        }
        public string DisplayText
        {
            get;
        }

        public ComboBoxItem(Customer customer, string displayText)
        {
            Customer = customer;
            DisplayText = displayText;
        }

        public override string ToString() => DisplayText;
    }

    public class CheckListItem
    {
        public Customer Customer
        {
            get;
        }
        public string DisplayText
        {
            get;
        }

        public CheckListItem(Customer customer, string displayText)
        {
            Customer = customer;
            DisplayText = displayText;
        }

        public override string ToString() => DisplayText;
    }
}
}
