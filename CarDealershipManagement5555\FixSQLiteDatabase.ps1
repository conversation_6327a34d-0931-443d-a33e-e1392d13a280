# Comprehensive SQLite Database Fix Script for Car Dealership Management
# This script will diagnose and fix SQLite data loading errors

Write-Host "=== SQLite Database Repair Tool ===" -ForegroundColor Green
Write-Host "Car Dealership Management System" -ForegroundColor Yellow

# Set paths
$projectPath = "C:\Users\<USER>\Desktop\CarDealershipManagement"
$dbPath = "$projectPath\CarDealership.db"
$binDbPath = "$projectPath\bin\Debug\net8.0-windows\CarDealership.db"

Write-Host "`n1. Checking database files..." -ForegroundColor Cyan

# Check main database
if (Test-Path $dbPath) {
    $dbSize = (Get-Item $dbPath).Length
    Write-Host "✓ Main database found: $dbSize bytes" -ForegroundColor Green
} else {
    Write-Host "✗ Main database not found" -ForegroundColor Red
}

# Check bin database
if (Test-Path $binDbPath) {
    $binDbSize = (Get-Item $binDbPath).Length
    Write-Host "✓ Bin database found: $binDbSize bytes" -ForegroundColor Green
} else {
    Write-Host "✗ Bin database not found" -ForegroundColor Red
}

Write-Host "`n2. Testing SQLite connectivity..." -ForegroundColor Cyan

try {
    # Test main database
    if (Test-Path $dbPath) {
        $userCount = sqlite3 $dbPath "SELECT COUNT(*) FROM Users;" 2>$null
        if ($userCount) {
            Write-Host "✓ Main database operational - Users: $userCount" -ForegroundColor Green
        } else {
            Write-Host "✗ Main database has issues" -ForegroundColor Red
        }
    }
    
    # Test bin database
    if (Test-Path $binDbPath) {
        $binUserCount = sqlite3 $binDbPath "SELECT COUNT(*) FROM Users;" 2>$null
        if ($binUserCount) {
            Write-Host "✓ Bin database operational - Users: $binUserCount" -ForegroundColor Green
        } else {
            Write-Host "✗ Bin database has issues" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "✗ SQLite connectivity test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n3. Database synchronization check..." -ForegroundColor Cyan

# Ensure databases are synchronized
if ((Test-Path $dbPath) -and (Test-Path $binDbPath)) {
    $mainSize = (Get-Item $dbPath).Length
    $binSize = (Get-Item $binDbPath).Length
    
    if ($mainSize -ne $binSize) {
        Write-Host "! Database files are out of sync" -ForegroundColor Yellow
        Write-Host "  Main: $mainSize bytes | Bin: $binSize bytes" -ForegroundColor Yellow
        
        # Use the larger/newer database
        if ($mainSize -gt $binSize) {
            Write-Host "  Copying main database to bin..." -ForegroundColor Cyan
            Copy-Item $dbPath $binDbPath -Force
            Write-Host "✓ Synchronized bin database" -ForegroundColor Green
        } else {
            Write-Host "  Copying bin database to main..." -ForegroundColor Cyan
            Copy-Item $binDbPath $dbPath -Force
            Write-Host "✓ Synchronized main database" -ForegroundColor Green
        }
    } else {
        Write-Host "✓ Database files are synchronized" -ForegroundColor Green
    }
}

Write-Host "`n4. Rebuilding application..." -ForegroundColor Cyan

try {
    Set-Location $projectPath
    $buildResult = dotnet build --configuration Debug 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Application built successfully" -ForegroundColor Green
    } else {
        Write-Host "✗ Build failed:" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Build error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n5. Testing database initialization..." -ForegroundColor Cyan

try {
    # Test database initialization
    $initResult = dotnet run --project . --init-db 2>&1
    Write-Host "Database initialization result:" -ForegroundColor Yellow
    Write-Host $initResult -ForegroundColor Gray
} catch {
    Write-Host "✗ Database initialization failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n6. Final verification..." -ForegroundColor Cyan

if (Test-Path $dbPath) {
    try {
        $finalUserCount = sqlite3 $dbPath "SELECT COUNT(*) FROM Users;"
        $finalCarCount = sqlite3 $dbPath "SELECT COUNT(*) FROM Cars;"
        $finalCustomerCount = sqlite3 $dbPath "SELECT COUNT(*) FROM Customers;"
        
        Write-Host "✓ Final database state:" -ForegroundColor Green
        Write-Host "  Users: $finalUserCount" -ForegroundColor Yellow
        Write-Host "  Cars: $finalCarCount" -ForegroundColor Yellow
        Write-Host "  Customers: $finalCustomerCount" -ForegroundColor Yellow
        
        if ([int]$finalUserCount -gt 0) {
            Write-Host "`n✓ DATABASE REPAIR SUCCESSFUL!" -ForegroundColor Green
            Write-Host "Your application should now work without SQLite errors." -ForegroundColor Green
        } else {
            Write-Host "`n✗ Database still has no users - manual intervention needed" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ Final verification failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "✗ Database file missing after repair" -ForegroundColor Red
}

Write-Host "`n7. Starting application..." -ForegroundColor Cyan
Write-Host "Launching Car Dealership Management..." -ForegroundColor Yellow

try {
    # Start the application
    Start-Process -FilePath "$projectPath\bin\Debug\net8.0-windows\CarDealershipManagement.exe" -WorkingDirectory $projectPath
    Write-Host "✓ Application launched successfully!" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to launch application: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Try running manually: dotnet run" -ForegroundColor Yellow
}

Write-Host "`n=== Repair Complete ===" -ForegroundColor Green
Write-Host "If you still experience issues, check the application window for specific error messages." -ForegroundColor Yellow
