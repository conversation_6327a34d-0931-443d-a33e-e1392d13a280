@echo off
chcp 65001 >nul
title عرض بيانات الشركة

echo.
echo ========================================
echo    📧 بيانات الشركة المحدثة
echo ========================================
echo.

echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo.

echo 📋 بيانات الشركة الحالية:
echo.

sqlite3 "CarDealership_Final\CarDealership.db" "SELECT 'اسم الشركة: ' || CompanyName FROM SystemSettings;"
sqlite3 "CarDealership_Final\CarDealership.db" "SELECT 'عنوان الشركة: ' || CompanyAddress FROM SystemSettings;"
sqlite3 "CarDealership_Final\CarDealership.db" "SELECT 'هاتف الشركة: ' || CompanyPhone FROM SystemSettings;"
sqlite3 "CarDealership_Final\CarDealership.db" "SELECT 'إيميل المعرض: ' || CompanyEmail FROM SystemSettings;"
sqlite3 "CarDealership_Final\CarDealership.db" "SELECT 'الموقع الإلكتروني: ' || CompanyWebsite FROM SystemSettings;"
sqlite3 "CarDealership_Final\CarDealership.db" "SELECT 'العملة: ' || Currency FROM SystemSettings;"

echo.
echo ========================================
echo.

echo 📝 ملاحظة مهمة:
echo.
echo حقول إيميل المعرض والموقع الإلكتروني موجودة في قاعدة البيانات
echo لكن البرنامج المشغل الحالي لا يعرضها في واجهة الإعدادات
echo.
echo هذا يعني أن البرنامج يحتاج إلى إعادة بناء بالكود المحدث
echo.

echo 🔧 الحلول المتاحة:
echo.
echo 1. استخدام البيانات الموجودة في قاعدة البيانات (كما هو موضح أعلاه)
echo 2. تحديث البيانات مباشرة في قاعدة البيانات
echo 3. انتظار إعادة بناء البرنامج بالكود المحدث
echo.

echo 💡 لتحديث البيانات مباشرة:
echo يمكنك استخدام الأوامر التالية:
echo.
echo sqlite3 "CarDealership_Final\CarDealership.db" "UPDATE SystemSettings SET CompanyEmail = '<EMAIL>';"
echo sqlite3 "CarDealership_Final\CarDealership.db" "UPDATE SystemSettings SET CompanyWebsite = 'www.yoursite.com';"
echo.

echo 🚀 تشغيل البرنامج...
start "" "CarDealership_Final\CarDealershipManagement.exe"

echo.
echo للدعم: 01285626623 - <EMAIL>
echo.

pause
