@echo off
chcp 65001 >nul
title إنشاء اختصار على سطح المكتب

echo.
echo ========================================
echo    🖥️ إنشاء اختصار على سطح المكتب
echo ========================================
echo.

set "DESKTOP=%USERPROFILE%\Desktop"
set "PROGRAM_PATH=%~dp0bin\Debug\net8.0-windows\CarDealershipManagement.exe"
set "SHORTCUT_PATH=%DESKTOP%\برنامج إدارة معرض السيارات.lnk"

echo 🔍 فحص البرنامج...
if exist "%PROGRAM_PATH%" (
    echo ✅ تم العثور على البرنامج
    echo 📁 المسار: %PROGRAM_PATH%
    echo.
    
    echo 🖥️ إنشاء اختصار على سطح المكتب...
    
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT_PATH%'); $Shortcut.TargetPath = '%PROGRAM_PATH%'; $Shortcut.WorkingDirectory = '%~dp0bin\Debug\net8.0-windows'; $Shortcut.Description = 'برنامج إدارة معرض السيارات - تبويب الأقساط المحسن'; $Shortcut.Save()"
    
    if exist "%SHORTCUT_PATH%" (
        echo ✅ تم إنشاء الاختصار بنجاح!
        echo 📍 مكان الاختصار: %SHORTCUT_PATH%
        echo.
        echo 🚀 يمكنك الآن:
        echo    • النقر المزدوج على الاختصار في سطح المكتب
        echo    • أو تشغيل البرنامج من هذا المجلد
        echo.
        echo 🔑 بيانات الدخول:
        echo    اسم المستخدم: amrali
        echo    كلمة المرور: braa
        echo.
        echo 📋 للوصول إلى تبويب الأقساط المحسن:
        echo    1. سجل الدخول
        echo    2. اختر "التقارير"
        echo    3. انتقل إلى "📅 تقارير الأقساط"
        echo.
    ) else (
        echo ❌ فشل في إنشاء الاختصار
    )
    
) else (
    echo ❌ لم يتم العثور على البرنامج
    echo 📁 المسار المتوقع: %PROGRAM_PATH%
    echo.
    echo 🔧 تأكد من بناء المشروع أولاً
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
