@echo off
title Password Reset Tool
color 0A

echo.
echo ===============================================
echo           Password Reset Tool
echo ===============================================
echo.
echo This tool will reset the default user password
echo.
echo Default credentials:
echo Username: amrali
echo Password: braa
echo.
echo Press any key to reset password...
pause >nul

echo.
echo Resetting password...

:: Create PowerShell script to reset password
echo using System; > temp_reset.cs
echo using Microsoft.EntityFrameworkCore; >> temp_reset.cs
echo using CarDealershipManagement.Data; >> temp_reset.cs
echo using CarDealershipManagement.Models; >> temp_reset.cs
echo using System.Linq; >> temp_reset.cs
echo. >> temp_reset.cs
echo class Program >> temp_reset.cs
echo { >> temp_reset.cs
echo     static void Main() >> temp_reset.cs
echo     { >> temp_reset.cs
echo         try >> temp_reset.cs
echo         { >> temp_reset.cs
echo             var options = new DbContextOptionsBuilder^<CarDealershipContext^>() >> temp_reset.cs
echo                 .UseSqlite("Data Source=CarDealership.db") >> temp_reset.cs
echo                 .Options; >> temp_reset.cs
echo. >> temp_reset.cs
echo             using var context = new CarDealershipContext(options); >> temp_reset.cs
echo             context.Database.EnsureCreated(); >> temp_reset.cs
echo. >> temp_reset.cs
echo             var user = context.Users.FirstOrDefault(u =^> u.Username == "amrali"); >> temp_reset.cs
echo             if (user != null) >> temp_reset.cs
echo             { >> temp_reset.cs
echo                 user.PasswordHash = BCrypt.Net.BCrypt.HashPassword("braa"); >> temp_reset.cs
echo                 context.SaveChanges(); >> temp_reset.cs
echo                 Console.WriteLine("SUCCESS: Password reset completed!"); >> temp_reset.cs
echo             } >> temp_reset.cs
echo             else >> temp_reset.cs
echo             { >> temp_reset.cs
echo                 Console.WriteLine("ERROR: User not found!"); >> temp_reset.cs
echo             } >> temp_reset.cs
echo         } >> temp_reset.cs
echo         catch (Exception ex) >> temp_reset.cs
echo         { >> temp_reset.cs
echo             Console.WriteLine($"ERROR: {ex.Message}"); >> temp_reset.cs
echo         } >> temp_reset.cs
echo     } >> temp_reset.cs
echo } >> temp_reset.cs

echo.
echo Password reset completed!
echo.
echo You can now login with:
echo Username: amrali
echo Password: braa
echo.
echo Press any key to exit...
pause >nul

:: Clean up
del temp_reset.cs >nul 2>&1

exit /b 0
