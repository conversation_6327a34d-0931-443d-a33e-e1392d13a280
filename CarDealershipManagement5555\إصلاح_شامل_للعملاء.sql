-- إصلاح شامل لجدول العملاء
-- المطور: Amr <PERSON> - 01285626623 - <EMAIL>

-- 1. إنشاء جدول العملاء الجديد المبسط
CREATE TABLE IF NOT EXISTS Customers_New (
    CustomerId INTEGER PRIMARY KEY AUTOINCREMENT,
    FullName TEXT NOT NULL,
    IdNumber TEXT NOT NULL,
    Address TEXT NOT NULL,
    PrimaryPhone TEXT NOT NULL,
    SecondaryPhone TEXT,
    Email TEXT,
    IsActive INTEGER DEFAULT 1,
    IsDeleted INTEGER DEFAULT 0,
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT,
    DeletedDate TEXT
);

-- 2. نقل البيانات الموجودة مع دمج العنوان
INSERT INTO Customers_New (
    CustomerId, FullName, IdNumber, Address, PrimaryPhone,
    SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate
)
SELECT 
    CustomerId,
    FullName,
    IdNumber,
    CASE 
        WHEN Country IS NOT NULL AND Country != '' THEN
            Country || 
            CASE WHEN City IS NOT NULL AND City != '' THEN ', ' || City ELSE '' END ||
            CASE WHEN Area IS NOT NULL AND Area != '' THEN ', ' || Area ELSE '' END ||
            CASE WHEN Street IS NOT NULL AND Street != '' THEN ', ' || Street ELSE '' END
        WHEN City IS NOT NULL AND City != '' THEN
            City ||
            CASE WHEN Area IS NOT NULL AND Area != '' THEN ', ' || Area ELSE '' END ||
            CASE WHEN Street IS NOT NULL AND Street != '' THEN ', ' || Street ELSE '' END
        WHEN Area IS NOT NULL AND Area != '' THEN
            Area ||
            CASE WHEN Street IS NOT NULL AND Street != '' THEN ', ' || Street ELSE '' END
        WHEN Street IS NOT NULL AND Street != '' THEN Street
        ELSE 'غير محدد'
    END AS Address,
    PrimaryPhone,
    SecondaryPhone,
    Email,
    IsActive,
    IsDeleted,
    CreatedDate,
    ModifiedDate,
    DeletedDate
FROM Customers;

-- 3. حذف الجدول القديم وإعادة تسمية الجديد
DROP TABLE Customers;
ALTER TABLE Customers_New RENAME TO Customers;

-- 4. تنظيف العناوين
UPDATE Customers 
SET Address = TRIM(Address)
WHERE Address IS NOT NULL;

UPDATE Customers 
SET Address = 'غير محدد'
WHERE Address IS NULL OR Address = '' OR Address = ', , ,' OR Address = ', ,';

-- 5. إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_customers_fullname ON Customers(FullName);
CREATE INDEX IF NOT EXISTS idx_customers_idnumber ON Customers(IdNumber);
CREATE INDEX IF NOT EXISTS idx_customers_active ON Customers(IsActive);
