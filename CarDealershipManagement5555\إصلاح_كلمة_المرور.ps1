# إصلاح مشكلة كلمة المرور
Add-Type -AssemblyName System.Data.SQLite

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🔧 إصلاح مشكلة كلمة المرور" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$currentDir = $PSScriptRoot
$dbPath = Join-Path $currentDir "CarDealership.db"
$binDbPath = Join-Path $currentDir "bin\Debug\net8.0-windows\CarDealership.db"

# البحث عن قاعدة البيانات في جميع المواقع المحتملة
$dbLocations = @($dbPath, $binDbPath)
$dbToUse = $null

foreach ($location in $dbLocations) {
    if (Test-Path $location) {
        $dbToUse = $location
        Write-Host "✅ تم العثور على قاعدة البيانات: $location" -ForegroundColor Green
        break
    }
}

if (-not $dbToUse) {
    Write-Host "❌ لم يتم العثور على قاعدة البيانات في أي مكان" -ForegroundColor Red
    Write-Host "🔧 إنشاء قاعدة بيانات جديدة..." -ForegroundColor Yellow
    $dbToUse = $dbPath
}

try {
    # الاتصال بقاعدة البيانات
    $connectionString = "Data Source=$dbToUse;Version=3;"
    $connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)
    $connection.Open()
    
    Write-Host "🔗 تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    
    # إنشاء جدول المستخدمين إذا لم يكن موجوداً
    $createTableQuery = @"
CREATE TABLE IF NOT EXISTS Users (
    UserId INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT UNIQUE NOT NULL,
    PasswordHash TEXT NOT NULL,
    FullName TEXT NOT NULL,
    Role TEXT NOT NULL DEFAULT 'Admin',
    IsActive INTEGER NOT NULL DEFAULT 1,
    CreatedDate TEXT NOT NULL,
    LastLoginDate TEXT,
    CanViewSales INTEGER NOT NULL DEFAULT 1,
    CanEditSales INTEGER NOT NULL DEFAULT 1,
    CanDeleteSales INTEGER NOT NULL DEFAULT 1,
    CanViewCustomers INTEGER NOT NULL DEFAULT 1,
    CanEditCustomers INTEGER NOT NULL DEFAULT 1,
    CanDeleteCustomers INTEGER NOT NULL DEFAULT 1,
    CanViewInventory INTEGER NOT NULL DEFAULT 1,
    CanEditInventory INTEGER NOT NULL DEFAULT 1,
    CanDeleteInventory INTEGER NOT NULL DEFAULT 1,
    CanViewReports INTEGER NOT NULL DEFAULT 1,
    CanViewFinancials INTEGER NOT NULL DEFAULT 1,
    CanManageUsers INTEGER NOT NULL DEFAULT 1,
    CanBackupRestore INTEGER NOT NULL DEFAULT 1,
    CanAccessSettings INTEGER NOT NULL DEFAULT 1
);
"@
    
    $command = New-Object System.Data.SQLite.SQLiteCommand($createTableQuery, $connection)
    $command.ExecuteNonQuery() | Out-Null
    
    Write-Host "📋 تم إنشاء/التحقق من جدول المستخدمين" -ForegroundColor Green
    
    # حذف جميع المستخدمين الموجودين
    $deleteQuery = "DELETE FROM Users;"
    $command = New-Object System.Data.SQLite.SQLiteCommand($deleteQuery, $connection)
    $command.ExecuteNonQuery() | Out-Null
    
    Write-Host "🗑️ تم حذف جميع المستخدمين السابقين" -ForegroundColor Yellow
    
    # إنشاء مستخدم جديد بكلمة مرور بسيطة (بدون تشفير معقد)
    $username = "amrali"
    $password = "braa"
    $fullName = "عمرو علي"
    
    # استخدام كلمة المرور كما هي (بدون تشفير معقد)
    $passwordHash = $password
    $createdDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    $insertQuery = @"
INSERT INTO Users (
    Username, PasswordHash, FullName, Role, IsActive, CreatedDate,
    CanViewSales, CanEditSales, CanDeleteSales,
    CanViewCustomers, CanEditCustomers, CanDeleteCustomers,
    CanViewInventory, CanEditInventory, CanDeleteInventory,
    CanViewReports, CanViewFinancials, CanManageUsers,
    CanBackupRestore, CanAccessSettings
) VALUES (
    '$username', '$passwordHash', '$fullName', 'Admin', 1, '$createdDate',
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
);
"@
    
    $command = New-Object System.Data.SQLite.SQLiteCommand($insertQuery, $connection)
    $result = $command.ExecuteNonQuery()
    
    if ($result -gt 0) {
        Write-Host "✅ تم إنشاء المستخدم بنجاح!" -ForegroundColor Green
        
        # التحقق من البيانات المدخلة
        $verifyQuery = "SELECT Username, PasswordHash, FullName FROM Users WHERE Username = '$username';"
        $command = New-Object System.Data.SQLite.SQLiteCommand($verifyQuery, $connection)
        $reader = $command.ExecuteReader()
        
        if ($reader.Read()) {
            Write-Host ""
            Write-Host "🔍 التحقق من البيانات المحفوظة:" -ForegroundColor Cyan
            Write-Host "   اسم المستخدم: $($reader["Username"])" -ForegroundColor White
            Write-Host "   كلمة المرور: $($reader["PasswordHash"])" -ForegroundColor White
            Write-Host "   الاسم الكامل: $($reader["FullName"])" -ForegroundColor White
        }
        $reader.Close()
        
        Write-Host ""
        Write-Host "🔑 بيانات الدخول الصحيحة:" -ForegroundColor Cyan
        Write-Host "   اسم المستخدم: $username" -ForegroundColor White
        Write-Host "   كلمة المرور: $password" -ForegroundColor White
        Write-Host ""
        
        # نسخ قاعدة البيانات إلى جميع المواقع
        foreach ($location in $dbLocations) {
            if ($location -ne $dbToUse) {
                try {
                    $dir = Split-Path $location
                    if (-not (Test-Path $dir)) {
                        New-Item -ItemType Directory -Path $dir -Force | Out-Null
                    }
                    Copy-Item $dbToUse $location -Force
                    Write-Host "📋 تم نسخ قاعدة البيانات إلى: $location" -ForegroundColor Green
                } catch {
                    Write-Host "⚠️ تحذير: لم يتم نسخ قاعدة البيانات إلى: $location" -ForegroundColor Yellow
                }
            }
        }
        
    } else {
        Write-Host "❌ فشل في إنشاء المستخدم" -ForegroundColor Red
    }
    
    $connection.Close()
    
} catch {
    Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
    if ($connection -and $connection.State -eq 'Open') {
        $connection.Close()
    }
}

Write-Host ""
Write-Host "🚀 الآن يمكنك تسجيل الدخول باستخدام:" -ForegroundColor Cyan
Write-Host "   اسم المستخدم: amrali" -ForegroundColor White
Write-Host "   كلمة المرور: braa" -ForegroundColor White
Write-Host ""
Write-Host "💡 نصائح مهمة:" -ForegroundColor Yellow
Write-Host "   • تأكد من كتابة البيانات بالضبط كما هي مكتوبة" -ForegroundColor White
Write-Host "   • لا تضع مسافات إضافية قبل أو بعد النص" -ForegroundColor White
Write-Host "   • تأكد من أن Caps Lock غير مفعل" -ForegroundColor White
Write-Host "   • جرب نسخ ولصق البيانات إذا لم تعمل الكتابة" -ForegroundColor White
Write-Host ""
Write-Host "🔧 إذا استمرت المشكلة:" -ForegroundColor Yellow
Write-Host "   • أعد تشغيل البرنامج" -ForegroundColor White
Write-Host "   • تأكد من أن قاعدة البيانات غير مفتوحة في برنامج آخر" -ForegroundColor White
Write-Host "   • جرب تشغيل البرنامج كمسؤول" -ForegroundColor White
Write-Host ""
Write-Host "📞 للمساعدة أو الدعم الفني، اتصل بالمطور" -ForegroundColor Magenta
Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
