# Gallery Data Validator and Fixer
# This script validates and fixes common gallery data issues

param(
    [string]$DatabasePath = ".\bin\Debug\net8.0-windows\CarDealership.db",
    [switch]$FixIssues = $false
)

Write-Host "Gallery Data Validator Starting..." -ForegroundColor Green

# Check if SQLite is available
try {
    & sqlite3 -version | Out-Null
} catch {
    Write-Host "SQLite3 not found. Please install SQLite." -ForegroundColor Red
    exit 1
}

# Check if database exists
if (-not (Test-Path $DatabasePath)) {
    Write-Host "Database file not found at: $DatabasePath" -ForegroundColor Red
    exit 1
}

Write-Host "Validating database: $DatabasePath" -ForegroundColor Yellow

# 1. Check SystemSettings for gallery configuration
Write-Host "`n=== Gallery Settings Check ===" -ForegroundColor Cyan
$settingsQuery = "SELECT GalleryDataEnabled, ShowCarImages, MaxImagesPerCar FROM SystemSettings LIMIT 1;"
$settings = sqlite3 $DatabasePath $settingsQuery

if ($settings) {
    $settingsParts = $settings -split "\|"
    $galleryEnabled = $settingsParts[0]
    $showImages = $settingsParts[1]
    $maxImages = $settingsParts[2]
    
    Write-Host "Gallery Enabled: $galleryEnabled" -ForegroundColor $(if ($galleryEnabled -eq "1") { "Green" } else { "Red" })
    Write-Host "Show Images: $showImages" -ForegroundColor $(if ($showImages -eq "1") { "Green" } else { "Yellow" })
    Write-Host "Max Images Per Car: $maxImages" -ForegroundColor Green
    
    if ($galleryEnabled -eq "0" -and $FixIssues) {
        Write-Host "Fixing: Enabling gallery..." -ForegroundColor Yellow
        sqlite3 $DatabasePath "UPDATE SystemSettings SET GalleryDataEnabled = 1;"
        Write-Host "Gallery enabled successfully!" -ForegroundColor Green
    }
} else {
    Write-Host "No system settings found!" -ForegroundColor Red
}

# 2. Check Cars table
Write-Host "`n=== Cars Table Check ===" -ForegroundColor Cyan
$carsCount = sqlite3 $DatabasePath "SELECT COUNT(*) FROM Cars;"
Write-Host "Total cars in system: $carsCount" -ForegroundColor Green

if ($carsCount -gt 0) {
    $carsInfo = sqlite3 $DatabasePath "SELECT ChassisNumber, Brand, Model FROM Cars LIMIT 5;"
    Write-Host "Sample cars:" -ForegroundColor Yellow
    $carsInfo -split "`n" | ForEach-Object {
        if ($_ -ne "") {
            $carParts = $_ -split "\|"
            Write-Host "  - $($carParts[1]) $($carParts[2]) (Chassis: $($carParts[0]))" -ForegroundColor White
        }
    }
}

# 3. Check CarImages table
Write-Host "`n=== Car Images Check ===" -ForegroundColor Cyan
$imagesCount = sqlite3 $DatabasePath "SELECT COUNT(*) FROM CarImages;"
Write-Host "Total images in database: $imagesCount" -ForegroundColor Green

if ($imagesCount -gt 0) {
    # Check for broken image paths
    Write-Host "`nChecking image file integrity..." -ForegroundColor Yellow
    $imageQuery = "SELECT CarImageId, CarChassisNumber, ImagePath, OriginalFileName FROM CarImages;"
    $images = sqlite3 $DatabasePath $imageQuery
    
    $brokenImages = @()
    $images -split "`n" | ForEach-Object {
        if ($_ -ne "") {
            $imageParts = $_ -split "\|"
            $imageId = $imageParts[0]
            $chassisNumber = $imageParts[1]
            $imagePath = $imageParts[2]
            $fileName = $imageParts[3]
            
            if (-not (Test-Path $imagePath)) {
                $brokenImages += @{
                    Id = $imageId
                    Chassis = $chassisNumber
                    Path = $imagePath
                    FileName = $fileName
                }
                Write-Host "  BROKEN: $fileName (ID: $imageId) - Path: $imagePath" -ForegroundColor Red
            } else {
                Write-Host "  OK: $fileName (ID: $imageId)" -ForegroundColor Green
            }
        }
    }
    
    if ($brokenImages.Count -gt 0 -and $FixIssues) {
        Write-Host "`nFixing broken image references..." -ForegroundColor Yellow
        foreach ($brokenImage in $brokenImages) {
            $deleteQuery = "DELETE FROM CarImages WHERE CarImageId = $($brokenImage.Id);"
            sqlite3 $DatabasePath $deleteQuery
            Write-Host "  Removed broken image record: $($brokenImage.FileName)" -ForegroundColor Yellow
        }
    }
}

# 4. Check Images directory structure
Write-Host "`n=== Images Directory Check ===" -ForegroundColor Cyan
$imagesDir = ".\bin\Debug\net8.0-windows\Images"

if (Test-Path $imagesDir) {
    Write-Host "Images directory exists: $imagesDir" -ForegroundColor Green
    
    # Check for orphaned image files
    $imageFiles = Get-ChildItem -Path $imagesDir -Recurse -Include "*.jpg", "*.jpeg", "*.png", "*.bmp", "*.gif"
    if ($imageFiles.Count -gt 0) {
        Write-Host "Found $($imageFiles.Count) image files on disk" -ForegroundColor Green
        
        # Check if each file has a database record
        $orphanedFiles = @()
        foreach ($file in $imageFiles) {
            $checkQuery = "SELECT COUNT(*) FROM CarImages WHERE ImagePath = '$($file.FullName)';"
            $dbCount = sqlite3 $DatabasePath $checkQuery
            
            if ($dbCount -eq "0") {
                $orphanedFiles += $file
                Write-Host "  ORPHANED: $($file.Name) - No database record" -ForegroundColor Red
            }
        }
        
        if ($orphanedFiles.Count -gt 0 -and $FixIssues) {
            Write-Host "`nRemoving orphaned image files..." -ForegroundColor Yellow
            foreach ($orphanFile in $orphanedFiles) {
                Remove-Item $orphanFile.FullName -Force
                Write-Host "  Removed: $($orphanFile.Name)" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "No image files found on disk" -ForegroundColor Yellow
    }
} else {
    Write-Host "Images directory does not exist: $imagesDir" -ForegroundColor Red
    if ($FixIssues) {
        Write-Host "Creating images directory..." -ForegroundColor Yellow
        New-Item -Path $imagesDir -ItemType Directory -Force | Out-Null
        Write-Host "Images directory created successfully!" -ForegroundColor Green
    }
}

# 5. Check foreign key constraints
Write-Host "`n=== Foreign Key Integrity Check ===" -ForegroundColor Cyan
$fkQuery = "SELECT ci.CarImageId, ci.CarChassisNumber FROM CarImages ci LEFT JOIN Cars c ON ci.CarChassisNumber = c.ChassisNumber WHERE c.ChassisNumber IS NULL;"
$orphanedImages = sqlite3 $DatabasePath $fkQuery

if ($orphanedImages -ne "") {
    Write-Host "Found orphaned image records (no corresponding car):" -ForegroundColor Red
    $orphanedImages -split "`n" | ForEach-Object {
        if ($_ -ne "") {
            $parts = $_ -split "\|"
            Write-Host "  Image ID: $($parts[0]), Chassis: $($parts[1])" -ForegroundColor Red
        }
    }
    
    if ($FixIssues) {
        Write-Host "Removing orphaned image records..." -ForegroundColor Yellow
        sqlite3 $DatabasePath "DELETE FROM CarImages WHERE CarChassisNumber NOT IN (SELECT ChassisNumber FROM Cars);"
        Write-Host "Orphaned records removed!" -ForegroundColor Green
    }
} else {
    Write-Host "No orphaned image records found" -ForegroundColor Green
}

# 6. Summary Report
Write-Host "`n=== Summary Report ===" -ForegroundColor Cyan
$finalImagesCount = sqlite3 $DatabasePath "SELECT COUNT(*) FROM CarImages;"
$finalCarsCount = sqlite3 $DatabasePath "SELECT COUNT(*) FROM Cars;"
$finalSettings = sqlite3 $DatabasePath "SELECT GalleryDataEnabled FROM SystemSettings LIMIT 1;"

Write-Host "Cars in system: $finalCarsCount" -ForegroundColor Green
Write-Host "Images in database: $finalImagesCount" -ForegroundColor Green
Write-Host "Gallery enabled: $(if ($finalSettings -eq '1') { 'Yes' } else { 'No' })" -ForegroundColor $(if ($finalSettings -eq "1") { "Green" } else { "Red" })

if ($FixIssues) {
    Write-Host "`nAll identified issues have been fixed!" -ForegroundColor Green
} else {
    Write-Host "`nTo fix the identified issues, run this script with the -FixIssues parameter" -ForegroundColor Yellow
}

Write-Host "`nGallery Data Validation Complete!" -ForegroundColor Green
