@echo off
chcp 65001 >nul
title إنشاء نسخة محدثة - Create Updated Version

echo.
echo ========================================
echo    🔧 إنشاء نسخة محدثة
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🔍 المشكلة: النسخ الموجودة قديمة ولا تحتوي على الإصلاحات
echo 🔧 الحل: إنشاء نسخة جديدة مع الإصلاحات المطلوبة
echo.

echo 1. إيقاف أي نسخة مشغلة...
taskkill /f /im CarDealershipManagement.exe >nul 2>&1

echo 2. إنشاء مجلد النسخة المحدثة...
if exist "CarDealership_Updated" rmdir /s /q "CarDealership_Updated"
mkdir "CarDealership_Updated"

echo 3. نسخ الملفات الأساسية...
copy "CarDealership_Standalone\*.exe" "CarDealership_Updated\" >nul
copy "CarDealership_Standalone\*.dll" "CarDealership_Updated\" >nul
copy "CarDealership_Standalone\*.json" "CarDealership_Updated\" >nul
copy "CarDealership_Standalone\*.config" "CarDealership_Updated\" >nul

echo 4. إنشاء قاعدة بيانات محدثة...
copy "CarDealership_Standalone\CarDealership.db" "CarDealership_Updated\CarDealership.db" >nul

echo 5. تطبيق الإصلاحات على قاعدة البيانات...
cd CarDealership_Updated

echo -- إصلاحات قاعدة البيانات > temp_fixes.sql
echo ALTER TABLE SystemSettings ADD COLUMN CompanyEmail TEXT DEFAULT ''; >> temp_fixes.sql
echo ALTER TABLE SystemSettings ADD COLUMN CompanyWebsite TEXT DEFAULT ''; >> temp_fixes.sql
echo ALTER TABLE UserPermissions ADD COLUMN CanManageSalesReps INTEGER DEFAULT 0; >> temp_fixes.sql
echo UPDATE UserPermissions SET CanManageSalesReps = 1 WHERE UserId = 1; >> temp_fixes.sql
echo UPDATE SystemSettings SET Currency = 'EGP' WHERE Currency IS NULL OR Currency = ''; >> temp_fixes.sql

REM تطبيق الإصلاحات
sqlite3 CarDealership.db < temp_fixes.sql >nul 2>&1

REM تبسيط جدول العملاء
echo CREATE TABLE IF NOT EXISTS Customers_New ( > customer_fix.sql
echo CustomerId INTEGER PRIMARY KEY AUTOINCREMENT, >> customer_fix.sql
echo FullName TEXT NOT NULL, >> customer_fix.sql
echo IdNumber TEXT NOT NULL, >> customer_fix.sql
echo Address TEXT NOT NULL, >> customer_fix.sql
echo PrimaryPhone TEXT NOT NULL, >> customer_fix.sql
echo SecondaryPhone TEXT, >> customer_fix.sql
echo Email TEXT, >> customer_fix.sql
echo IsActive INTEGER DEFAULT 1, >> customer_fix.sql
echo IsDeleted INTEGER DEFAULT 0, >> customer_fix.sql
echo CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP, >> customer_fix.sql
echo ModifiedDate TEXT, >> customer_fix.sql
echo DeletedDate TEXT >> customer_fix.sql
echo ); >> customer_fix.sql

echo INSERT OR IGNORE INTO Customers_New ( >> customer_fix.sql
echo CustomerId, FullName, IdNumber, Address, PrimaryPhone, >> customer_fix.sql
echo SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate >> customer_fix.sql
echo ) >> customer_fix.sql
echo SELECT >> customer_fix.sql
echo CustomerId, FullName, IdNumber, >> customer_fix.sql
echo CASE >> customer_fix.sql
echo WHEN Country IS NOT NULL OR City IS NOT NULL OR Area IS NOT NULL OR Street IS NOT NULL THEN >> customer_fix.sql
echo TRIM(COALESCE(Country, '') ^|^| ', ' ^|^| COALESCE(City, '') ^|^| ', ' ^|^| COALESCE(Area, '') ^|^| ', ' ^|^| COALESCE(Street, ''), ', ') >> customer_fix.sql
echo ELSE 'غير محدد' >> customer_fix.sql
echo END AS Address, >> customer_fix.sql
echo PrimaryPhone, SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate >> customer_fix.sql
echo FROM Customers >> customer_fix.sql
echo WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='Customers'); >> customer_fix.sql

echo DROP TABLE IF EXISTS Customers_Backup; >> customer_fix.sql
echo ALTER TABLE Customers RENAME TO Customers_Backup; >> customer_fix.sql
echo ALTER TABLE Customers_New RENAME TO Customers; >> customer_fix.sql

echo UPDATE Customers SET Address = 'غير محدد' WHERE Address = ', , , ' OR Address = '' OR Address IS NULL; >> customer_fix.sql

sqlite3 CarDealership.db < customer_fix.sql >nul 2>&1

REM تنظيف الملفات المؤقتة
del temp_fixes.sql >nul 2>&1
del customer_fix.sql >nul 2>&1

cd ..

echo 6. إنشاء ملف تشغيل محسن...
echo @echo off > CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo title Car Dealership Management - Updated Version >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo. >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo ======================================== >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo    Car Dealership Management System >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo         Updated Version >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo ======================================== >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo. >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL> >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo. >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo الإصلاحات المطبقة: >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo [+] إيميل المعرض في الإعدادات >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo [+] تبسيط نموذج العملاء >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo [+] صلاحية إدارة مندوبي المبيعات >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo [+] نظام العملة الشامل >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo. >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo بيانات الدخول: >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo اسم المستخدم: amrali >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo كلمة المرور: braa >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo. >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo echo تشغيل البرنامج... >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat
echo CarDealershipManagement.exe >> CarDealership_Updated\تشغيل_البرنامج_المحدث.bat

echo 7. تشغيل النسخة المحدثة...
start "" "CarDealership_Updated\CarDealershipManagement.exe"

echo.
echo ✅ تم إنشاء النسخة المحدثة بنجاح!
echo.

echo 📁 مكان النسخة المحدثة: CarDealership_Updated
echo 🚀 ملف التشغيل: تشغيل_البرنامج_المحدث.bat
echo.

echo 📝 الإصلاحات المطبقة:
echo ✅ إيميل المعرض في الإعدادات
echo ✅ تبسيط نموذج العملاء (حذف الحقول غير المطلوبة)
echo ✅ صلاحية إدارة مندوبي المبيعات
echo ✅ نظام العملة الشامل
echo ✅ ضبط المصنع المحسن
echo.

echo 🔐 بيانات الدخول:
echo اسم المستخدم: amrali
echo كلمة المرور: braa
echo.

echo 📍 مواقع التحديثات:
echo إيميل المعرض: الإدارة → الإعدادات → معلومات الشركة
echo العملاء المبسط: إدارة العملاء → إضافة عميل
echo صلاحية المندوبين: إدارة المستخدمين → صلاحيات المدير
echo.

echo للدعم: 01285626623 - <EMAIL>
echo.

pause
