using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class PrintOptionsForm : Form
    {
        private Button btnPrintCustomerStatement;
        private Button btnPrintSalesReport;
        private Button btnPrintInventoryReport;
        private Button btnPrintAccountingReport;
        private Button btnClose;
        private Label lblTitle;
        private Label lblStatus;

        public PrintOptionsForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "خيارات الطباعة - Print Options";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(20, 320),
                Size = new Size(460, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Title
            lblTitle = new Label
            {
                Text = "خيارات الطباعة",
                Location = new Point(20, 20),
                Size = new Size(460, 30),
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.FromArgb(0, 102, 204)
            };

            // Customer Statement Button
            btnPrintCustomerStatement = new Button
            {
                Text = "طباعة كشف حساب العميل",
                Location = new Point(50, 70),
                Size = new Size(400, 40),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold)
            };
            btnPrintCustomerStatement.FlatAppearance.BorderSize = 0;
            btnPrintCustomerStatement.Click += BtnPrintCustomerStatement_Click;

            // Sales Report Button
            btnPrintSalesReport = new Button
            {
                Text = "طباعة تقرير المبيعات",
                Location = new Point(50, 120),
                Size = new Size(400, 40),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold)
            };
            btnPrintSalesReport.FlatAppearance.BorderSize = 0;
            btnPrintSalesReport.Click += BtnPrintSalesReport_Click;

            // Inventory Report Button
            btnPrintInventoryReport = new Button
            {
                Text = "طباعة تقرير المخزون",
                Location = new Point(50, 170),
                Size = new Size(400, 40),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold)
            };
            btnPrintInventoryReport.FlatAppearance.BorderSize = 0;
            btnPrintInventoryReport.Click += BtnPrintInventoryReport_Click;

            // Accounting Report Button
            btnPrintAccountingReport = new Button
            {
                Text = "طباعة تقرير الحسابات",
                Location = new Point(50, 220),
                Size = new Size(400, 40),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold)
            };
            btnPrintAccountingReport.FlatAppearance.BorderSize = 0;
            btnPrintAccountingReport.Click += BtnPrintAccountingReport_Click;

            // Close Button
            btnClose = new Button
            {
                Text = "إغلاق",
                Location = new Point(375, 350),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[]
            {
                lblTitle, btnPrintCustomerStatement, btnPrintSalesReport,
                btnPrintInventoryReport, btnPrintAccountingReport, btnClose, lblStatus
            });
        }

        private void BtnPrintCustomerStatement_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages
            ErrorHandlingService.TryExecute(() =>
            {
                var customerSelectionForm = new CustomerSelectionFormForStatement();
                if (customerSelectionForm.ShowDialog() == DialogResult.OK)
                {
                    var customerId = customerSelectionForm.SelectedCustomerId;
                    var customerStatementForm = new CustomerStatementForm(customerId);
                    customerStatementForm.ShowDialog();
                }
            }, "فتح كشف حساب العميل");
        }

        private void BtnPrintSalesReport_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages
            ErrorHandlingService.TryExecute(() =>
            {
                var salesDataGridView = CreateSalesReportDataGridView();
                var salesReportForm = new PrintReportForm(salesDataGridView, "تقرير المبيعات - Sales Report");
                salesReportForm.ShowDialog();
            }, "فتح تقرير المبيعات");
        }

        private void BtnPrintInventoryReport_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages
            ErrorHandlingService.TryExecute(() =>
            {
                var inventoryDataGridView = CreateInventoryReportDataGridView();
                var inventoryReportForm = new PrintReportForm(inventoryDataGridView, "تقرير المخزون - Inventory Report");
                inventoryReportForm.ShowDialog();
            }, "فتح تقرير المخزون");
        }

        private void BtnPrintAccountingReport_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages
            ErrorHandlingService.TryExecute(() =>
            {
                var accountingDataGridView = CreateAccountingReportDataGridView();
                var accountingReportForm = new PrintReportForm(accountingDataGridView, "تقرير الحسابات - Accounting Report");
                accountingReportForm.ShowDialog();
            }, "فتح تقرير الحسابات");
        }

        private DataGridView CreateSalesReportDataGridView()
        {
            var dataGridView = new DataGridView();
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.ReadOnly = true;
            dataGridView.AllowUserToAddRows = false;

            try
            {
                using (var context = DbContextFactory.CreateContext())
                {
                    var salesData = context.Sales
                        .Include(s => s.Car)
                        .Include(s => s.Customer)
                        .Select(s => new
                        {
                            SaleId = s.SaleId,
CustomerName = s.Customer.FullName,
                            CarMake = s.Car.Brand,
                            CarModel = s.Car.Model,
                            CarYear = s.Car.Year,
                            SalePrice = s.ActualSellPrice,
                            PaymentMethod = s.PaymentMethod,
                            SaleDate = s.SaleDate
                        })
                        .ToList();

                    dataGridView.DataSource = salesData;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading sales data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return dataGridView;
        }

        private DataGridView CreateInventoryReportDataGridView()
        {
            var dataGridView = new DataGridView();
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.ReadOnly = true;
            dataGridView.AllowUserToAddRows = false;

            try
            {
                using (var context = DbContextFactory.CreateContext())
                {
                    var inventoryData = context.Cars
                        .Where(c => c.Status == "Available")
                        .Select(c => new
                        {
                            ChassisNumber = c.ChassisNumber,
                            Make = c.Brand,
                            Model = c.Model,
                            Year = c.Year,
                            Color = c.Color,
                            Condition = c.Condition,
                            CashPrice = c.SuggestedSellPrice,
                            InstallmentPrice = c.InstallmentSellPrice,
                            InventoryStatus = c.Status,
                            CreatedDate = c.CreatedDate
                        })
                        .ToList();

                    dataGridView.DataSource = inventoryData;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading inventory data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return dataGridView;
        }

        private DataGridView CreateAccountingReportDataGridView()
        {
            var dataGridView = new DataGridView();
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.ReadOnly = true;
            dataGridView.AllowUserToAddRows = false;

            try
            {
                using (var context = DbContextFactory.CreateContext())
                {
                    var accountingData = context.SupplierPaymentSchedules
                        .Include(sps => sps.Supplier)
                        .Select(sps => new
                        {
                            ScheduleId = sps.ScheduleId,
SupplierName = sps.Supplier.SupplierName,
                            DueDate = sps.DueDate,
                            Amount = sps.Amount,
                            PaymentStatus = sps.PaymentStatus,
                            Description = sps.Description
                        })
                        .ToList();

                    dataGridView.DataSource = accountingData;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading accounting data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return dataGridView;
        }
    }
}

