using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Helpers;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.IO;
using System.Diagnostics.CodeAnalysis;

namespace CarDealershipManagement.Forms
{
    public partial class AccountingForm : Form
    {
        private TabControl tabControl;
        private TabPage tabSupplierAccounts;
        private TabPage tabCustomerAccounts;
        private TabPage tabSummary;

        // Supplier accounts tab
        private Panel pnlSupplierMain;
        private Panel pnlSupplierButtons;
        private DataGridView dgvSupplierAccounts;
        private Button btnSupplierPayment;
        private Button btnViewSupplierDetails;
        private Button btnAddAmountDue;
        private Button btnCreateReportSupplier;
        private Label lblSupplierStatus = null!;

        // Customer accounts tab
        private Panel pnlCustomerMain;
        private Panel pnlCustomerButtons;
        private DataGridView dgvCustomerAccounts;
        private Button btnCustomerPayment;
        private Button btnViewCustomerDetails;
        private Button btnCreateReportCustomer;
        private Button btnCustomerStatement; // زر كشف حساب العميل الجديد
        private Label lblCustomerStatus = null!;

        // Summary tab
        private Label lblTotalSales;
        private Label lblTotalPurchases;
        private Label lblPendingCustomerPayments;
        private Label lblPendingSupplierPayments;
        private Label lblProfit;
        private Label lblSummaryStatus = null!;

        public AccountingForm()
        {
            InitializeComponent();
            this.Load += AccountingForm_Load;
        }

        private async void AccountingForm_Load(object sender, EventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                // Check if database exists, create if not
                if (!File.Exists("CarDealership.db"))
                {
                    MessageBox.Show("ملف قاعدة البيانات غير موجود. سيتم إنشاؤه تلقائياً.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    await CreateDatabase();
                }

                await LoadSupplierAccounts();
                await LoadCustomerAccounts();
                await LoadSummaryData();
            }, "تحميل بيانات الحسابات");
        }

        private async Task CreateDatabase()
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();
                await context.Database.EnsureCreatedAsync();
            }, "إنشاء قاعدة البيانات");
        }

        private async Task LoadSupplierAccounts()
        {
            lblSupplierStatus.Text = ""; // Clear previous status messages
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var suppliers = await context.Suppliers.ToListAsync();

                var supplierAccounts = suppliers.Select(s => new
                {
                    المورد = s.SupplierName ?? "غير محدد",
                    الشركة = s.SupplierName ?? "غير محدد",
                    الهاتف = s.Phone ?? "غير محدد",
                    المبلغ_المستحق = "0 ريال", // Will be calculated later
                    آخر_دفعة = "لا توجد"
                }).ToList();

                dgvSupplierAccounts.DataSource = supplierAccounts;
            }, "تحميل حسابات الموردين");
        }

        private async Task LoadCustomerAccounts()
        {
            lblCustomerStatus.Text = "جاري تحميل بيانات العملاء..."; // Show loading message
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var customers = await context.Customers.ToListAsync();
                var customerAccountsList = new List<object>();

                foreach (var customer in customers)
                {
                    // حساب المبلغ المستحق للعميل
                    var customerSales = await context.Sales
                        .Where(s => s.CustomerId == customer.CustomerId)
                        .ToListAsync();

                    decimal totalDue = 0;
                    DateTime? lastPaymentDate = null;
                    decimal lastPaymentAmount = 0;

                    foreach (var sale in customerSales)
                    {
                        // حساب المبلغ المستحق من المبيعات
                        if (sale.PaymentMethod == PaymentMethod.Installment)
                        {
                            var installmentPayments = await context.InstallmentPayments
                                .Where(ip => ip.SaleId == sale.SaleId)
                                .ToListAsync();

                            decimal totalPaid = installmentPayments.Sum(ip => ip.AmountPaid);
                            decimal remainingAmount = sale.ActualSellPrice - sale.DownPayment - totalPaid;

                            if (remainingAmount > 0)
                            {
                                totalDue += remainingAmount;
                            }

                            // العثور على آخر دفعة
                            var lastPayment = installmentPayments
                                .Where(ip => ip.PaidDate.HasValue)
                                .OrderByDescending(ip => ip.PaidDate)
                                .FirstOrDefault();

                            if (lastPayment != null && (lastPaymentDate == null || lastPayment.PaidDate > lastPaymentDate))
                            {
                                lastPaymentDate = lastPayment.PaidDate;
                                lastPaymentAmount = lastPayment.AmountPaid;
                            }
                        }
                    }

                    // تنسيق المبلغ المستحق
                    string dueAmountText = totalDue > 0 ? $"{totalDue:N0} ريال" : "لا يوجد مستحقات";

                    // تنسيق آخر دفعة
                    string lastPaymentText = lastPaymentDate.HasValue
                        ? $"{lastPaymentAmount:N0} ريال - {lastPaymentDate.Value:dd/MM/yyyy}"
                        : "لا توجد دفعات";

                    customerAccountsList.Add(new
                    {
                        رقم_العميل = customer.CustomerId,
                        اسم_العميل = customer.FullName ?? "غير محدد",
                        رقم_الهوية = customer.IdNumber ?? "غير محدد",
                        رقم_الهاتف = customer.PrimaryPhone ?? "غير محدد",
                        المبلغ_المستحق = dueAmountText,
                        آخر_دفعة = lastPaymentText,
                        حالة_الحساب = totalDue > 0 ? "يوجد مستحقات" : "مسدد"
                    });
                }

                dgvCustomerAccounts.DataSource = customerAccountsList;

                // تحسين تنسيق الأعمدة
                FormatCustomerAccountsGrid();

                lblCustomerStatus.Text = $"تم تحميل {customerAccountsList.Count} عميل بنجاح";
                lblCustomerStatus.ForeColor = Color.Green;
            }, "تحميل حسابات العملاء");
        }

        private void FormatCustomerAccountsGrid()
        {
            if (dgvCustomerAccounts.Columns.Count > 0)
            {
                // تنسيق عرض الأعمدة للشاشة الكاملة - أعرض وأوضح
                dgvCustomerAccounts.Columns["رقم_العميل"].Width = 120;
                dgvCustomerAccounts.Columns["اسم_العميل"].Width = 250;
                dgvCustomerAccounts.Columns["رقم_الهوية"].Width = 180;
                dgvCustomerAccounts.Columns["رقم_الهاتف"].Width = 180;
                dgvCustomerAccounts.Columns["المبلغ_المستحق"].Width = 200;
                dgvCustomerAccounts.Columns["آخر_دفعة"].Width = 280;
                dgvCustomerAccounts.Columns["حالة_الحساب"].Width = 150;

                // تنسيق رؤوس الأعمدة مع رموز تعبيرية
                dgvCustomerAccounts.Columns["رقم_العميل"].HeaderText = "🆔 رقم العميل";
                dgvCustomerAccounts.Columns["اسم_العميل"].HeaderText = "👤 اسم العميل";
                dgvCustomerAccounts.Columns["رقم_الهوية"].HeaderText = "🆔 رقم الهوية";
                dgvCustomerAccounts.Columns["رقم_الهاتف"].HeaderText = "📞 رقم الهاتف";
                dgvCustomerAccounts.Columns["المبلغ_المستحق"].HeaderText = "💰 المبلغ المستحق";
                dgvCustomerAccounts.Columns["آخر_دفعة"].HeaderText = "📅 آخر دفعة";
                dgvCustomerAccounts.Columns["حالة_الحساب"].HeaderText = "📊 حالة الحساب";

                // تنسيق محاذاة النص
                dgvCustomerAccounts.Columns["رقم_العميل"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgvCustomerAccounts.Columns["المبلغ_المستحق"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgvCustomerAccounts.Columns["آخر_دفعة"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dgvCustomerAccounts.Columns["حالة_الحساب"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

                // تنسيق خاص للأعمدة المهمة - خطوط أكبر للشاشة الكاملة
                dgvCustomerAccounts.Columns["المبلغ_المستحق"].DefaultCellStyle.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
                dgvCustomerAccounts.Columns["حالة_الحساب"].DefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

                // تلوين الصفوف حسب حالة الحساب مع تحسينات بصرية
                for (int i = 0; i < dgvCustomerAccounts.Rows.Count; i++)
                {
                    var row = dgvCustomerAccounts.Rows[i];
                    var accountStatus = row.Cells["حالة_الحساب"].Value?.ToString();

                    if (accountStatus == "يوجد مستحقات")
                    {
                        // تنسيق للعملاء الذين لديهم مستحقات
                        row.DefaultCellStyle.BackColor = i % 2 == 0 ? Color.FromArgb(255, 248, 248) : Color.FromArgb(255, 240, 240);
                        row.Cells["المبلغ_المستحق"].Style.ForeColor = Color.FromArgb(220, 53, 69);
                        row.Cells["المبلغ_المستحق"].Style.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
                        row.Cells["حالة_الحساب"].Style.ForeColor = Color.FromArgb(220, 53, 69);
                        row.Cells["حالة_الحساب"].Style.BackColor = Color.FromArgb(248, 215, 218);
                    }
                    else
                    {
                        // تنسيق للعملاء المسددين
                        row.DefaultCellStyle.BackColor = i % 2 == 0 ? Color.FromArgb(248, 255, 248) : Color.FromArgb(240, 255, 240);
                        row.Cells["المبلغ_المستحق"].Style.ForeColor = Color.FromArgb(40, 167, 69);
                        row.Cells["حالة_الحساب"].Style.ForeColor = Color.FromArgb(40, 167, 69);
                        row.Cells["حالة_الحساب"].Style.BackColor = Color.FromArgb(212, 237, 218);
                    }

                    // تحسين مظهر الخلايا
                    foreach (DataGridViewCell cell in row.Cells)
                    {
                        cell.Style.SelectionBackColor = Color.FromArgb(52, 152, 219);
                        cell.Style.SelectionForeColor = Color.White;
                    }
                }

                // إضافة حدود للأعمدة المهمة
                dgvCustomerAccounts.Columns["المبلغ_المستحق"].DividerWidth = 2;
                dgvCustomerAccounts.Columns["حالة_الحساب"].DividerWidth = 2;
            }
        }

        private async Task LoadSummaryData()
        {
            lblSummaryStatus.Text = ""; // Clear previous status messages
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                // Simplified data for now to avoid SQLite issues
                lblTotalSales.Text = "إجمالي المبيعات: 0 ريال";
                lblTotalPurchases.Text = "إجمالي المشتريات: 0 ريال";
                lblPendingCustomerPayments.Text = "مستحقات العملاء: 0 ريال";
                lblPendingSupplierPayments.Text = "مستحقات الموردين: 0 ريال";
                lblProfit.Text = "الربح الإجمالي: 0 ريال";

                try
                {
                    var salesCount = await context.Sales.CountAsync();
                    var customersCount = await context.Customers.CountAsync();
                    var suppliersCount = await context.Suppliers.CountAsync();
                    var carsCount = await context.Cars.CountAsync();

                    lblTotalSales.Text = $"عدد المبيعات: {salesCount}";
                    lblTotalPurchases.Text = $"عدد السيارات: {carsCount}";
                    lblPendingCustomerPayments.Text = $"عدد العملاء: {customersCount}";
                    lblPendingSupplierPayments.Text = $"عدد الموردين: {suppliersCount}";
                    lblProfit.Text = "النظام يعمل بنجاح";
                }
                catch (Exception ex)
                {
                    lblSummaryStatus.Text = $"خطأ في حساب الملخص: {ex.Message}";
                    // Keep default values if calculation fails
                }
            }, "تحميل الملخص المالي");
        }

        private Button CreateStyledButton(string text, Point location, Color backgroundColor, Color? textColor = null)
        {
            var button = new Button
            {
                Text = text,
                Location = location,
                Size = new Size(120, 35),
                BackColor = backgroundColor,
                ForeColor = textColor ?? Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                UseVisualStyleBackColor = false,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // تحسين مظهر الزر
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = ControlPaint.Dark(backgroundColor, 0.3f);
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(backgroundColor, 0.2f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backgroundColor, 0.1f);

            // إضافة تأثير الظل
            button.Paint += (sender, e) =>
            {
                var btn = sender as Button;
                if (btn != null)
                {
                    // رسم ظل خفيف
                    using (var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                    {
                        e.Graphics.FillRectangle(shadowBrush, 2, 2, btn.Width - 2, btn.Height - 2);
                    }
                }
            };

            return button;
        }

        [MemberNotNull(nameof(tabControl), nameof(tabSupplierAccounts), nameof(tabCustomerAccounts), nameof(tabSummary),
                       nameof(pnlSupplierMain), nameof(pnlSupplierButtons), nameof(dgvSupplierAccounts), nameof(btnSupplierPayment),
                       nameof(btnViewSupplierDetails), nameof(btnAddAmountDue), nameof(btnCreateReportSupplier),
                       nameof(lblSupplierStatus), nameof(pnlCustomerMain), nameof(pnlCustomerButtons), nameof(dgvCustomerAccounts),
                       nameof(btnCustomerPayment), nameof(btnViewCustomerDetails), nameof(btnCreateReportCustomer),
                       nameof(btnCustomerStatement), nameof(lblCustomerStatus), nameof(lblTotalSales), nameof(lblTotalPurchases),
                       nameof(lblPendingCustomerPayments), nameof(lblPendingSupplierPayments), nameof(lblProfit),
                       nameof(lblSummaryStatus))]
        private void InitializeComponent()
        {
            this.Text = "إدارة الحسابات - Accounting Management";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.MinimumSize = new Size(1200, 700);

            // Apply responsive layout for full screen
            ResponsiveLayoutHelper.ApplyResponsiveFormSettings(this, Screen.PrimaryScreen.WorkingArea.Width, Screen.PrimaryScreen.WorkingArea.Height);

            // Create tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // Supplier Accounts Tab
            tabSupplierAccounts = new TabPage("حسابات الموردين");
            SetupSupplierAccountsTab();

            // Customer Accounts Tab
            tabCustomerAccounts = new TabPage("حسابات العملاء");
            SetupCustomerAccountsTab();

            // Summary Tab
            tabSummary = new TabPage("الملخص المالي");
            SetupSummaryTab();

            tabControl.TabPages.AddRange(new TabPage[] { tabSupplierAccounts, tabCustomerAccounts, tabSummary });
            this.Controls.Add(tabControl);
        }

        private void SetupSupplierAccountsTab()
        {
            // Main panel for the supplier tab
            pnlSupplierMain = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                BackColor = Color.White
            };

            // Status Label
            lblSupplierStatus = new Label
            {
                Text = "",
                Location = new Point(10, 500),
                Size = new Size(960, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Supplier accounts DataGridView
            dgvSupplierAccounts = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F),
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(0, 102, 204),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Padding = new Padding(5)
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.Black,
                    SelectionBackColor = Color.FromArgb(173, 216, 230),
                    SelectionForeColor = Color.Black,
                    Font = new Font("Segoe UI", 9F),
                    Padding = new Padding(5)
                }
            };

            // Button panel
            pnlSupplierButtons = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                Padding = new Padding(5),
                BackColor = Color.FromArgb(240, 248, 255)
            };

            // Buttons with consistent styling using CreateStyledButton
            btnSupplierPayment = CreateStyledButton("دفع للمورد", new Point(10, 10), Color.FromArgb(0, 123, 255));
            btnSupplierPayment.Click += BtnSupplierPayment_Click;

            btnViewSupplierDetails = CreateStyledButton("تفاصيل المورد", new Point(140, 10), Color.FromArgb(108, 117, 125));
            btnViewSupplierDetails.Click += BtnViewSupplierDetails_Click;

            btnAddAmountDue = CreateStyledButton("إضافة مبلغ مستحق", new Point(270, 10), Color.FromArgb(40, 167, 69));
            btnAddAmountDue.Size = new Size(140, 35); // Slightly wider for Arabic text
            btnAddAmountDue.Click += BtnAddAmountDue_Click;

            btnCreateReportSupplier = CreateStyledButton("إنشاء تقرير", new Point(420, 10), Color.FromArgb(232, 62, 140));
            btnCreateReportSupplier.Click += BtnCreateReportSupplier_Click;

            // Add buttons to button panel
            pnlSupplierButtons.Controls.AddRange(new Control[] { btnSupplierPayment, btnViewSupplierDetails, btnAddAmountDue, btnCreateReportSupplier });

            // Add controls to main panel
            pnlSupplierMain.Controls.Add(dgvSupplierAccounts);
            pnlSupplierMain.Controls.Add(pnlSupplierButtons);
            pnlSupplierMain.Controls.Add(lblSupplierStatus);

            // Add main panel to tab
            tabSupplierAccounts.Controls.Add(pnlSupplierMain);
        }

        private void SetupCustomerAccountsTab()
        {
            // Main panel for the customer tab
            pnlCustomerMain = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                BackColor = Color.White
            };

            // Status Label
            lblCustomerStatus = new Label
            {
                Text = "",
                Location = new Point(10, 500),
                Size = new Size(960, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Customer accounts DataGridView
            dgvCustomerAccounts = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F),
                RowHeadersVisible = false,
                EnableHeadersVisualStyles = false,
                GridColor = Color.FromArgb(230, 230, 230),
                CellBorderStyle = DataGridViewCellBorderStyle.Single,
                ColumnHeadersHeight = 50,
                RowTemplate = { Height = 45 },
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 73, 94),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Padding = new Padding(10),
                    WrapMode = DataGridViewTriState.True
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.FromArgb(44, 62, 80),
                    SelectionBackColor = Color.FromArgb(52, 152, 219),
                    SelectionForeColor = Color.White,
                    Font = new Font("Segoe UI", 11F),
                    Padding = new Padding(10),
                    Alignment = DataGridViewContentAlignment.MiddleLeft,
                    WrapMode = DataGridViewTriState.False
                },
                AlternatingRowsDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(248, 249, 250),
                    ForeColor = Color.FromArgb(44, 62, 80)
                }
            };

            // Button panel - زيادة الارتفاع لاستيعاب صفين من الأزرار مع تحسين للشاشة الكاملة
            pnlCustomerButtons = new Panel
            {
                Height = 100,
                Dock = DockStyle.Bottom,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(245, 250, 255)
            };

            // الصف الأول من الأزرار - أحجام أكبر للشاشة الكاملة
            btnCustomerPayment = CreateStyledButton("💰 تسجيل دفعة", new Point(20, 15), Color.FromArgb(40, 167, 69));
            btnCustomerPayment.Size = new Size(180, 40);
            btnCustomerPayment.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnCustomerPayment.Click += BtnCustomerPayment_Click;

            btnViewCustomerDetails = CreateStyledButton("👤 تفاصيل العميل", new Point(220, 15), Color.FromArgb(0, 123, 255));
            btnViewCustomerDetails.Size = new Size(180, 40);
            btnViewCustomerDetails.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnViewCustomerDetails.Click += BtnViewCustomerDetails_Click;

            btnCreateReportCustomer = CreateStyledButton("📊 إنشاء تقرير", new Point(420, 15), Color.FromArgb(232, 62, 140));
            btnCreateReportCustomer.Size = new Size(180, 40);
            btnCreateReportCustomer.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnCreateReportCustomer.Click += BtnCreateReportCustomer_Click;

            // زر كشف حساب العميل الجديد
            btnCustomerStatement = CreateStyledButton("📋 كشف الحساب", new Point(620, 15), Color.FromArgb(75, 0, 130));
            btnCustomerStatement.Size = new Size(180, 40);
            btnCustomerStatement.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnCustomerStatement.Click += BtnCustomerStatement_Click;

            // إضافة أزرار جديدة في الصف الثاني
            var btnRefreshCustomers = CreateStyledButton("🔄 تحديث البيانات", new Point(20, 65), Color.FromArgb(108, 117, 125));
            btnRefreshCustomers.Size = new Size(180, 40);
            btnRefreshCustomers.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnRefreshCustomers.Click += async (s, e) => await LoadCustomerAccounts();

            var btnExportCustomers = CreateStyledButton("📤 تصدير البيانات", new Point(820, 15), Color.FromArgb(255, 193, 7));
            btnExportCustomers.Size = new Size(180, 40);
            btnExportCustomers.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnExportCustomers.ForeColor = Color.Black;
            btnExportCustomers.Click += BtnExportCustomers_Click;

            var btnFilterCustomers = CreateStyledButton("🔍 تصفية العملاء", new Point(1020, 15), Color.FromArgb(23, 162, 184));
            btnFilterCustomers.Size = new Size(180, 40);
            btnFilterCustomers.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnFilterCustomers.Click += BtnFilterCustomers_Click;

            // Add buttons to button panel
            pnlCustomerButtons.Controls.AddRange(new Control[] {
                btnCustomerPayment, btnViewCustomerDetails, btnCreateReportCustomer, btnCustomerStatement,
                btnRefreshCustomers, btnExportCustomers, btnFilterCustomers
            });

            // Add controls to main panel
            pnlCustomerMain.Controls.Add(dgvCustomerAccounts);
            pnlCustomerMain.Controls.Add(pnlCustomerButtons);
            pnlCustomerMain.Controls.Add(lblCustomerStatus);

            // Add main panel to tab
            tabCustomerAccounts.Controls.Add(pnlCustomerMain);
        }

        private void SetupSummaryTab()
        {
            int yPos = 30;
            int spacing = 50;

            // Status Label
            lblSummaryStatus = new Label
            {
                Text = "",
                Location = new Point(50, 400),
                Size = new Size(800, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            lblTotalSales = new Label
            {
                Text = "إجمالي المبيعات: تحميل...",
                Location = new Point(50, yPos),
                Size = new Size(400, 30),
                Font = new Font("Arial", 12, FontStyle.Bold)
            };

            lblTotalPurchases = new Label
            {
                Text = "إجمالي المشتريات: تحميل...",
                Location = new Point(50, yPos += spacing),
                Size = new Size(400, 30),
                Font = new Font("Arial", 12, FontStyle.Bold)
            };

            lblPendingCustomerPayments = new Label
            {
                Text = "المدفوعات المعلقة من العملاء: تحميل...",
                Location = new Point(50, yPos += spacing),
                Size = new Size(400, 30),
                Font = new Font("Arial", 12, FontStyle.Bold)
            };

            lblPendingSupplierPayments = new Label
            {
                Text = "المدفوعات المعلقة للموردين: تحميل...",
                Location = new Point(50, yPos += spacing),
                Size = new Size(400, 30),
                Font = new Font("Arial", 12, FontStyle.Bold)
            };

            lblProfit = new Label
            {
                Text = "صافي الربح: تحميل...",
                Location = new Point(50, yPos += spacing),
                Size = new Size(400, 30),
                Font = new Font("Arial", 12, FontStyle.Bold),
                ForeColor = Color.Green
            };

            tabSummary.Controls.AddRange(new Control[]
            {
                lblTotalSales, lblTotalPurchases, lblPendingCustomerPayments,
                lblPendingSupplierPayments, lblProfit, lblSummaryStatus
            });
        }

        private async void BtnSupplierPayment_Click(object? sender, EventArgs e)
        {
            lblSupplierStatus.Text = ""; // Clear previous status messages
            if (dgvSupplierAccounts.SelectedRows.Count > 0)
            {
                var supplierName = dgvSupplierAccounts.SelectedRows[0].Cells["المورد"].Value?.ToString();
                if (!string.IsNullOrEmpty(supplierName))
                {
                    await ErrorHandlingService.TryExecute(async () =>
                    {
                        using var context = DbContextFactory.CreateContext();
                        var supplier = await context.Suppliers.FirstOrDefaultAsync(s => s.SupplierName == supplierName);
                        if (supplier != null)
                        {
                            var paymentForm = new SupplierPaymentForm(supplier.SupplierId);
                            if (paymentForm.ShowDialog() == DialogResult.OK)
                            {
                                await LoadData();
                            }
                        }
                        else
                        {
                            lblSupplierStatus.Text = "لم يتم العثور على المورد.";
                        }
                    }, "فتح نموذج دفع المورد");
                }
            }
            else
            {
                lblSupplierStatus.Text = "يرجى اختيار مورد لتسجيل الدفعة.";
            }
        }

        private async void BtnViewSupplierDetails_Click(object? sender, EventArgs e)
        {
            lblSupplierStatus.Text = ""; // Clear previous status messages
            if (dgvSupplierAccounts.SelectedRows.Count > 0)
            {
                var supplierName = dgvSupplierAccounts.SelectedRows[0].Cells["المورد"].Value?.ToString();
                if (!string.IsNullOrEmpty(supplierName))
                {
                    await ErrorHandlingService.TryExecute(async () =>
                    {
                        using var context = DbContextFactory.CreateContext();
                        var supplier = await context.Suppliers.FirstOrDefaultAsync(s => s.SupplierName == supplierName);
                        if (supplier != null)
                        {
                            var accountForm = new SupplierAccountForm(supplier.SupplierId);
                            accountForm.ShowDialog();
                        }
                        else
                        {
                            lblSupplierStatus.Text = "لم يتم العثور على المورد.";
                        }
                    }, "فتح تفاصيل المورد");
                }
            }
            else
            {
                lblSupplierStatus.Text = "يرجى اختيار مورد لعرض التفاصيل.";
            }
        }

        private async void BtnCustomerPayment_Click(object? sender, EventArgs e)
        {
            lblCustomerStatus.Text = ""; // Clear previous status messages
            if (dgvCustomerAccounts.SelectedRows.Count > 0)
            {
                var customerName = dgvCustomerAccounts.SelectedRows[0].Cells["اسم_العميل"].Value?.ToString();
                if (!string.IsNullOrEmpty(customerName))
                {
                    await ErrorHandlingService.TryExecute(async () =>
                    {
                        using var context = DbContextFactory.CreateContext();
                        var customer = await context.Customers.FirstOrDefaultAsync(c => c.FullName == customerName);
                        if (customer != null)
                        {
                            // Get the sale for this customer to pass to InstallmentPaymentForm
                            var sale = await context.Sales.FirstOrDefaultAsync(s => s.CustomerId == customer.CustomerId);
                            if (sale != null)
                            {
                                var paymentForm = new InstallmentPaymentForm(sale.SaleId);
                                if (paymentForm.ShowDialog() == DialogResult.OK)
                                {
                                    await LoadData();
                                }
                            }
                            else
                            {
                                lblCustomerStatus.Text = "لم يتم العثور على بيع لهذا العميل.";
                            }
                        }
                    }, "فتح نموذج دفع العميل");
                }
            }
            else
            {
                lblCustomerStatus.Text = "يرجى اختيار عميل لتسجيل الدفعة.";
            }
        }

        private void BtnViewCustomerDetails_Click(object? sender, EventArgs e)
        {
            lblCustomerStatus.Text = ""; // Clear previous status messages
            if (dgvCustomerAccounts.SelectedRows.Count > 0)
            {
                var customerName = dgvCustomerAccounts.SelectedRows[0].Cells["اسم_العميل"].Value?.ToString();
                if (!string.IsNullOrEmpty(customerName))
                {
                    // Open customer details form showing sales and payment history
                    MessageBox.Show($"سيتم فتح تفاصيل العميل: {customerName}", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                lblCustomerStatus.Text = "يرجى اختيار عميل لعرض التفاصيل.";
            }
        }

        private async void BtnAddAmountDue_Click(object? sender, EventArgs e)
        {
            lblSupplierStatus.Text = ""; // Clear previous status messages
            if (dgvSupplierAccounts.SelectedRows.Count > 0)
            {
                var supplierName = dgvSupplierAccounts.SelectedRows[0].Cells["المورد"].Value?.ToString();
                if (!string.IsNullOrEmpty(supplierName))
                {
                    await ErrorHandlingService.TryExecute(async () =>
                    {
                        using var context = DbContextFactory.CreateContext();
                        var supplier = await context.Suppliers.FirstOrDefaultAsync(s => s.SupplierName == supplierName);
                        if (supplier != null)
                        {
                            var addAmountForm = new AddAmountDueForm(supplier.SupplierId);
                            if (addAmountForm.ShowDialog() == DialogResult.OK)
                            {
                                await LoadData();
                            }
                        }
                        else
                        {
                            lblSupplierStatus.Text = "لم يتم العثور على المورد.";
                        }
                    }, "فتح نموذج إضافة مبلغ مستحق");
                }
            }
            else
            {
                lblSupplierStatus.Text = "يرجى اختيار مورد لإضافة مبلغ مستحق.";
            }
        }

        private void BtnCreateReportSupplier_Click(object? sender, EventArgs e)
        {
            lblSupplierStatus.Text = ""; // Clear previous status messages
            if (dgvSupplierAccounts.Rows.Count == 0)
            {
                lblSupplierStatus.Text = "لا توجد بيانات لإنشاء تقرير.";
                return;
            }

            // Logic for creating supplier report should go here
            MessageBox.Show("تقرير الموردين تم إنشاؤه بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnCreateReportCustomer_Click(object? sender, EventArgs e)
        {
            lblCustomerStatus.Text = ""; // Clear previous status messages
            if (dgvCustomerAccounts.Rows.Count == 0)
            {
                lblCustomerStatus.Text = "لا توجد بيانات لإنشاء تقرير.";
                return;
            }

            // Logic for creating customer report should go here
            MessageBox.Show("تقرير العملاء تم إنشاؤه بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnExportCustomers_Click(object? sender, EventArgs e)
        {
            lblCustomerStatus.Text = ""; // Clear previous status messages
            if (dgvCustomerAccounts.Rows.Count == 0)
            {
                lblCustomerStatus.Text = "لا توجد بيانات للتصدير.";
                return;
            }

            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv|Excel files (*.xlsx)|*.xlsx",
                    DefaultExt = "csv",
                    FileName = $"حسابات_العملاء_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // تصدير البيانات إلى ملف CSV
                    var csv = new System.Text.StringBuilder();

                    // إضافة رؤوس الأعمدة
                    var headers = new List<string>();
                    foreach (DataGridViewColumn column in dgvCustomerAccounts.Columns)
                    {
                        headers.Add(column.HeaderText);
                    }
                    csv.AppendLine(string.Join(",", headers));

                    // إضافة البيانات
                    foreach (DataGridViewRow row in dgvCustomerAccounts.Rows)
                    {
                        if (!row.IsNewRow)
                        {
                            var values = new List<string>();
                            foreach (DataGridViewCell cell in row.Cells)
                            {
                                values.Add($"\"{cell.Value?.ToString() ?? ""}\"");
                            }
                            csv.AppendLine(string.Join(",", values));
                        }
                    }

                    File.WriteAllText(saveFileDialog.FileName, csv.ToString(), System.Text.Encoding.UTF8);
                    lblCustomerStatus.Text = "تم تصدير البيانات بنجاح.";
                    lblCustomerStatus.ForeColor = Color.Green;

                    MessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{saveFileDialog.FileName}",
                                  "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                lblCustomerStatus.Text = $"خطأ في تصدير البيانات: {ex.Message}";
                lblCustomerStatus.ForeColor = Color.Red;
            }
        }

        private void BtnFilterCustomers_Click(object? sender, EventArgs e)
        {
            var filterForm = new Form
            {
                Text = "تصفية العملاء",
                Size = new Size(400, 300),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                BackColor = Color.White,
                Font = new Font("Segoe UI", 9F)
            };

            var lblFilter = new Label
            {
                Text = "اختر نوع التصفية:",
                Location = new Point(20, 20),
                Size = new Size(150, 25),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            var rbAllCustomers = new RadioButton
            {
                Text = "جميع العملاء",
                Location = new Point(20, 50),
                Size = new Size(150, 25),
                Checked = true
            };

            var rbWithDues = new RadioButton
            {
                Text = "العملاء الذين لديهم مستحقات",
                Location = new Point(20, 80),
                Size = new Size(200, 25)
            };

            var rbPaidUp = new RadioButton
            {
                Text = "العملاء المسددين",
                Location = new Point(20, 110),
                Size = new Size(150, 25)
            };

            var btnApplyFilter = CreateStyledButton("تطبيق التصفية", new Point(20, 150), Color.FromArgb(0, 123, 255));
            btnApplyFilter.Size = new Size(120, 35);

            var btnClearFilter = CreateStyledButton("إلغاء التصفية", new Point(150, 150), Color.FromArgb(108, 117, 125));
            btnClearFilter.Size = new Size(120, 35);

            btnApplyFilter.Click += async (s, e) =>
            {
                await ApplyCustomerFilter(rbWithDues.Checked, rbPaidUp.Checked);
                filterForm.Close();
            };

            btnClearFilter.Click += async (s, e) =>
            {
                await LoadCustomerAccounts();
                filterForm.Close();
            };

            filterForm.Controls.AddRange(new Control[] {
                lblFilter, rbAllCustomers, rbWithDues, rbPaidUp, btnApplyFilter, btnClearFilter
            });

            filterForm.ShowDialog(this);
        }

        private async Task ApplyCustomerFilter(bool showWithDues, bool showPaidUp)
        {
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();
                var customers = await context.Customers.ToListAsync();
                var filteredCustomers = new List<object>();

                foreach (var customer in customers)
                {
                    var customerSales = await context.Sales
                        .Where(s => s.CustomerId == customer.CustomerId)
                        .ToListAsync();

                    decimal totalDue = 0;
                    DateTime? lastPaymentDate = null;
                    decimal lastPaymentAmount = 0;

                    foreach (var sale in customerSales)
                    {
                        if (sale.PaymentMethod == PaymentMethod.Installment)
                        {
                            var installmentPayments = await context.InstallmentPayments
                                .Where(ip => ip.SaleId == sale.SaleId)
                                .ToListAsync();

                            decimal totalPaid = installmentPayments.Sum(ip => ip.AmountPaid);
                            decimal remainingAmount = sale.ActualSellPrice - sale.DownPayment - totalPaid;

                            if (remainingAmount > 0)
                            {
                                totalDue += remainingAmount;
                            }

                            var lastPayment = installmentPayments
                                .Where(ip => ip.PaidDate.HasValue)
                                .OrderByDescending(ip => ip.PaidDate)
                                .FirstOrDefault();

                            if (lastPayment != null && (lastPaymentDate == null || lastPayment.PaidDate > lastPaymentDate))
                            {
                                lastPaymentDate = lastPayment.PaidDate;
                                lastPaymentAmount = lastPayment.AmountPaid;
                            }
                        }
                    }

                    bool hasDues = totalDue > 0;
                    bool shouldInclude = (!showWithDues && !showPaidUp) ||
                                       (showWithDues && hasDues) ||
                                       (showPaidUp && !hasDues);

                    if (shouldInclude)
                    {
                        string dueAmountText = totalDue > 0 ? $"{totalDue:N0} ريال" : "لا يوجد مستحقات";
                        string lastPaymentText = lastPaymentDate.HasValue
                            ? $"{lastPaymentAmount:N0} ريال - {lastPaymentDate.Value:dd/MM/yyyy}"
                            : "لا توجد دفعات";

                        filteredCustomers.Add(new
                        {
                            رقم_العميل = customer.CustomerId,
                            اسم_العميل = customer.FullName ?? "غير محدد",
                            رقم_الهوية = customer.IdNumber ?? "غير محدد",
                            رقم_الهاتف = customer.PrimaryPhone ?? "غير محدد",
                            المبلغ_المستحق = dueAmountText,
                            آخر_دفعة = lastPaymentText,
                            حالة_الحساب = totalDue > 0 ? "يوجد مستحقات" : "مسدد"
                        });
                    }
                }

                dgvCustomerAccounts.DataSource = filteredCustomers;
                FormatCustomerAccountsGrid();

                string filterType = showWithDues ? "العملاء الذين لديهم مستحقات" :
                                  showPaidUp ? "العملاء المسددين" : "جميع العملاء";
                lblCustomerStatus.Text = $"تم تطبيق التصفية: {filterType} - عدد النتائج: {filteredCustomers.Count}";
                lblCustomerStatus.ForeColor = Color.Blue;
            }, "تطبيق تصفية العملاء");
        }

        // معالج حدث زر كشف حساب العميل
        private void BtnCustomerStatement_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvCustomerAccounts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار عميل من القائمة أولاً.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dgvCustomerAccounts.SelectedRows[0];
                var customerId = Convert.ToInt32(selectedRow.Cells["CustomerId"].Value);
                var customerName = selectedRow.Cells["CustomerName"].Value?.ToString() ?? "";

                // إنشاء وعرض نموذج كشف حساب العميل
                var customerStatementForm = new CustomerStatementForm(customerId);
                customerStatementForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح كشف حساب العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}


