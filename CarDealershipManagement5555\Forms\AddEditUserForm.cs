using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System.Text.RegularExpressions;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CarDealershipManagement.Forms
{
    public partial class AddEditUserForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtFullName;
        private TextBox txtPassword;
        private TextBox txtConfirmPassword;
        private ComboBox cmbRole;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;
        private Panel pnlUserInfo;
        private Panel pnlPermissions;
        private TabControl tabControl;
        private TabPage tabUserInfo;
        private TabPage tabPermissions;
        private Label lblStatus = null!;

        // Permission checkboxes - سيتم إنشاؤها ديناميكياً
        private Dictionary<string, CheckBox> permissionCheckboxes = new Dictionary<string, CheckBox>();

        private int? userId;
        private bool isEditMode;
        private User? currentUser;

        public AddEditUserForm(int? userId = null, User? currentUser = null)
        {
            this.userId = userId;
            this.isEditMode = userId.HasValue;
            this.currentUser = currentUser;
            InitializeComponent();
            LoadUserData();
        }

        private void InitializeComponent()
        {
            this.Text = isEditMode ? "تعديل مستخدم - Edit User" : "إضافة مستخدم جديد - Add New User";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(20, 520),
                Size = new Size(640, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Tab Control
            tabControl = new TabControl
            {
                Location = new Point(20, 20),
                Size = new Size(640, 500),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            // User Information Tab
            tabUserInfo = new TabPage("معلومات المستخدم")
            {
                BackColor = Color.White
            };

            // User info panel
            pnlUserInfo = new Panel
            {
                Location = new Point(10, 10),
                Size = new Size(610, 450),
                BackColor = Color.White
            };

            // Common styling for labels
            Action<Label> styleLabel = (lbl) =>
            {
                lbl.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                lbl.ForeColor = Color.FromArgb(52, 58, 64);
                lbl.AutoSize = true;
            };

            // Common styling for textboxes and numeric up-downs
            Action<Control> styleInput = (ctrl) =>
            {
                ctrl.Font = new Font("Segoe UI", 9F);
                ctrl.BackColor = Color.White;
                ctrl.ForeColor = Color.Black;
            };

            // Username
            var lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Location = new Point(20, 30)
            };
            styleLabel(lblUsername);

            txtUsername = new TextBox
            {
                Location = new Point(130, 27),
                Size = new Size(200, 25)
            };
            styleInput(txtUsername);

            // Full Name
            var lblFullName = new Label
            {
                Text = "الاسم الكامل:",
                Location = new Point(20, 70)
            };
            styleLabel(lblFullName);

            txtFullName = new TextBox
            {
                Location = new Point(130, 67),
                Size = new Size(300, 25)
            };
            styleInput(txtFullName);

            // Password
            var lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Location = new Point(20, 110)
            };
            styleLabel(lblPassword);

            txtPassword = new TextBox
            {
                Location = new Point(130, 107),
                Size = new Size(200, 25),
                UseSystemPasswordChar = true
            };
            styleInput(txtPassword);

            // Confirm Password
            var lblConfirmPassword = new Label
            {
                Text = "تأكيد كلمة المرور:",
                Location = new Point(20, 150)
            };
            styleLabel(lblConfirmPassword);

            txtConfirmPassword = new TextBox
            {
                Location = new Point(130, 147),
                Size = new Size(200, 25),
                UseSystemPasswordChar = true
            };
            styleInput(txtConfirmPassword);

            // Role
            var lblRole = new Label
            {
                Text = "الدور:",
                Location = new Point(20, 190)
            };
            styleLabel(lblRole);

            cmbRole = new ComboBox
            {
                Location = new Point(130, 187),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            styleInput(cmbRole);
            cmbRole.Items.AddRange(new[] { "مطور النظام", "مدير النشاط", "مندوب مبيعات" });
            cmbRole.SelectedIndexChanged += CmbRole_SelectedIndexChanged;

            // Is Active
            chkIsActive = new CheckBox
            {
                Text = "حساب نشط",
                Location = new Point(130, 230),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                Checked = true
            };

            // Password note for edit mode
            if (isEditMode)
            {
                var lblPasswordNote = new Label
                {
                    Text = "ملاحظة: اتركي حقول كلمة المرور فارغة إذا لم ترد تغييرها",
                    Location = new Point(130, 270),
                    Size = new Size(400, 40),
                    Font = new Font("Segoe UI", 8F, FontStyle.Italic),
                    ForeColor = Color.Gray
                };
                pnlUserInfo.Controls.Add(lblPasswordNote);
            }

            pnlUserInfo.Controls.AddRange(new Control[]
            {
                lblUsername, txtUsername, lblFullName, txtFullName,
                lblPassword, txtPassword, lblConfirmPassword, txtConfirmPassword,
                lblRole, cmbRole, chkIsActive
            });

            tabUserInfo.Controls.Add(pnlUserInfo);

            // Permissions Tab
            tabPermissions = new TabPage("الصلاحيات")
            {
                BackColor = Color.White
            };

            pnlPermissions = new Panel
            {
                Location = new Point(10, 10),
                Size = new Size(610, 450),
                BackColor = Color.White,
                AutoScroll = true
            };

            CreatePermissionControls();
            tabPermissions.Controls.Add(pnlPermissions);

            tabControl.TabPages.AddRange(new TabPage[] { tabUserInfo, tabPermissions });

            // Action buttons
            btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(450, 540),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(560, 540),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[] { tabControl, btnSave, btnCancel, lblStatus });
        }

        private void CreatePermissionControls()
        {
            int y = 20;
            int spacing = 35;
            int checkBoxSpacing = 25;
            int columnWidth = 300;
            int column1X = 20;
            int column2X = column1X + columnWidth + 20;

            // Clear existing controls
            pnlPermissions.Controls.Clear();
            permissionCheckboxes.Clear();

            // Get permissions grouped by category
            var permissionsByCategory = PermissionService.GetPermissionsByCategory();

            // Define category colors and icons
            var categoryStyles = new Dictionary<PermissionService.PermissionCategory, (Color color, string icon)>
            {
                [PermissionService.PermissionCategory.Inventory] = (Color.FromArgb(40, 167, 69), "📦"),
                [PermissionService.PermissionCategory.Sales] = (Color.FromArgb(0, 123, 255), "💰"),
                [PermissionService.PermissionCategory.Customers] = (Color.FromArgb(108, 117, 125), "👥"),
                [PermissionService.PermissionCategory.Suppliers] = (Color.FromArgb(255, 193, 7), "🏭"),
                [PermissionService.PermissionCategory.Reports] = (Color.FromArgb(23, 162, 184), "📊"),
                [PermissionService.PermissionCategory.Management] = (Color.FromArgb(220, 53, 69), "⚙️"),
                [PermissionService.PermissionCategory.System] = (Color.FromArgb(108, 117, 125), "🔧"),
                [PermissionService.PermissionCategory.Printing] = (Color.FromArgb(111, 66, 193), "🖨️")
            };

            // Split categories into two columns
            var categories = permissionsByCategory.Keys.OrderBy(c => c).ToList();
            var leftColumnCategories = categories.Take((categories.Count + 1) / 2).ToList();
            var rightColumnCategories = categories.Skip(leftColumnCategories.Count).ToList();

            // Create left column
            int leftY = y;
            foreach (var category in leftColumnCategories)
            {
                leftY = CreateCategorySection(category, permissionsByCategory[category].ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                    categoryStyles.GetValueOrDefault(category, (Color.Gray, "📋")),
                    column1X, leftY, columnWidth, spacing, checkBoxSpacing);
            }

            // Create right column
            int rightY = y;
            foreach (var category in rightColumnCategories)
            {
                rightY = CreateCategorySection(category, permissionsByCategory[category].ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                    categoryStyles.GetValueOrDefault(category, (Color.Gray, "📋")),
                    column2X, rightY, columnWidth, spacing, checkBoxSpacing);
            }

            // Update panel height to accommodate all controls
            int maxY = Math.Max(leftY, rightY);
            pnlPermissions.Height = Math.Max(maxY + 20, 500);
            pnlPermissions.Width = Math.Max(column2X + columnWidth + 20, 650);
        }

        private int CreateCategorySection(PermissionService.PermissionCategory category,
            Dictionary<string, PermissionService.PermissionInfo> permissions,
            (Color color, string icon) style, int x, int y, int width, int spacing, int checkBoxSpacing)
        {
            if (!permissions.Any()) return y;

            var categoryName = PermissionService.GetCategoryDisplayName(category);

            // Create category header
            var lblCategory = new Label
            {
                Text = $"{style.icon} {categoryName}:",
                Location = new Point(x, y),
                Size = new Size(width, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = style.color
            };
            pnlPermissions.Controls.Add(lblCategory);
            y += spacing;

            // Create checkboxes for permissions in this category
            foreach (var permission in permissions)
            {
                var permissionName = permission.Key;
                var permissionInfo = permission.Value;

                var checkBox = CreatePermissionCheckBox(permissionInfo.Name, x + 20, y, permissionName, width - 20);
                permissionCheckboxes[permissionName] = checkBox;

                // Add tooltip with description and role information
                var tooltip = new ToolTip();
                var roleInfo = $"المطور: {(permissionInfo.DeveloperDefault ? "✅" : "❌")} | " +
                              $"المدير: {(permissionInfo.ManagerDefault ? "✅" : "❌")} | " +
                              $"المندوب: {(permissionInfo.SalesRepDefault ? "✅" : "❌")}";
                tooltip.SetToolTip(checkBox, $"{permissionInfo.Description}\n\n{roleInfo}");

                y += checkBoxSpacing;
            }

            y += 10; // Extra spacing between categories
            return y;
        }

        private CheckBox CreatePermissionCheckBox(string text, int x, int y, string? permissionName = null, int width = 250)
        {
            var checkBox = new CheckBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, 23),
                Font = new Font("Segoe UI", 9F),
                Name = permissionName != null ? $"chk{permissionName}" : text.Replace(" ", ""),
                ForeColor = Color.FromArgb(33, 37, 41),
                UseVisualStyleBackColor = true
            };
            pnlPermissions.Controls.Add(checkBox);
            return checkBox;
        }

        private void CmbRole_SelectedIndexChanged(object? sender, EventArgs e)
        {
            // Auto-set permissions based on role
            if (cmbRole.SelectedIndex >= 0)
            {
                SetDefaultPermissionsForRole((UserRole)(cmbRole.SelectedIndex + 1));
            }
        }

        private void SetDefaultPermissionsForRole(UserRole role)
        {
            // Reset all permissions first
            ResetAllPermissions();

            // Get default permissions from PermissionService
            var defaultPermissions = PermissionService.GetDefaultPermissionsForRole(role);

            // Apply permissions to checkboxes using the dictionary
            foreach (var permission in PermissionService.AllPermissions)
            {
                var permissionName = permission.Key;

                if (permissionCheckboxes.TryGetValue(permissionName, out var checkBox))
                {
                    // Get the permission value from the default permissions object
                    var property = typeof(UserPermissions).GetProperty(permissionName);
                    if (property != null)
                    {
                        var value = property.GetValue(defaultPermissions) as bool?;
                        checkBox.Checked = value ?? false;
                    }
                }
            }

            // Show role-specific information
            ShowRolePermissionsSummary(role);
        }

        /// <summary>
        /// عرض ملخص الصلاحيات للدور المحدد
        /// </summary>
        private void ShowRolePermissionsSummary(UserRole role)
        {
            string roleName = role switch
            {
                UserRole.Developer => "مطور",
                UserRole.Manager => "مدير",
                UserRole.SalesRepresentative => "مندوب مبيعات",
                _ => "غير محدد"
            };

            string summary = role switch
            {
                UserRole.Developer => "🔧 المطور: جميع الصلاحيات متاحة",
                UserRole.Manager => "👔 المدير: صلاحيات إدارية واسعة (عدا إدارة المستخدمين)",
                UserRole.SalesRepresentative => "🤝 المندوب: صلاحيات أساسية للمبيعات والعملاء",
                _ => "غير محدد"
            };

            // يمكن إضافة label لعرض الملخص إذا لزم الأمر
            this.Text = $"{(isEditMode ? "تعديل" : "إضافة")} مستخدم - {roleName}";
        }

        private void ResetAllPermissions()
        {
            foreach (var checkBox in permissionCheckboxes.Values)
            {
                checkBox.Checked = false;
            }
        }

        private void SetAllPermissions(bool value)
        {
            foreach (var checkBox in permissionCheckboxes.Values)
            {
                checkBox.Checked = value;
            }
        }

        private async void LoadUserData()
        {
            if (!isEditMode)
            {
                return;
            }

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var user = await context.Users.Include(u => u.Permissions).FirstOrDefaultAsync(u => u.UserId == userId);
                if (user != null)
                {
                    txtUsername.Text = user.Username;
                    txtFullName.Text = user.FullName;
                    cmbRole.SelectedIndex = (int)user.Role - 1;
                    chkIsActive.Checked = user.IsActive;

                    // Load permissions
                    if (user.Permissions != null)
                    {
                        LoadPermissions(user.Permissions);
                    }
                }
            }, "تحميل بيانات المستخدم");
        }

        private void LoadPermissions(UserPermissions permissions)
        {
            // Use reflection to load all permissions dynamically
            var permissionProperties = typeof(UserPermissions).GetProperties()
                .Where(p => p.PropertyType == typeof(bool) && p.Name.StartsWith("Can"))
                .ToList();

            foreach (var property in permissionProperties)
            {
                var permissionName = property.Name;
                if (permissionCheckboxes.TryGetValue(permissionName, out var checkBox))
                {
                    var value = property.GetValue(permissions) as bool?;
                    checkBox.Checked = value ?? false;
                }
            }
        }

        private async void BtnSave_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages

            if (!ValidateInput())
            {
                return;
            }

            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                User user;
                UserPermissions permissions;

                if (isEditMode)
                {
                    user = await context.Users.Include(u => u.Permissions).FirstOrDefaultAsync(u => u.UserId == userId);
                    if (user == null)
                    {
                        MessageBox.Show("المستخدم غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // Check if current user is Manager trying to edit Developer
                    if (currentUser?.Role == UserRole.Manager && user.Role == UserRole.Developer)
                    {
                        MessageBox.Show("❌ ليس لديك صلاحية لتعديل المطورين!\n\n" +
                                       "🔒 مدير النشاط لا يمكنه تعديل المطورين.\n" +
                                       "المطورون لديهم صلاحيات أعلى من مدير النشاط.",
                                       "صلاحيات غير كافية", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Check if trying to edit the default developer user
                    if (user.Role == UserRole.Developer && user.Username == "amrali")
                    {
                        // Only allow password change for default developer
                        if (txtUsername.Text.Trim() != "amrali" ||
                            txtFullName.Text.Trim() != user.FullName ||
                            (UserRole)(cmbRole.SelectedIndex + 1) != UserRole.Developer ||
                            !chkIsActive.Checked)
                        {
                            MessageBox.Show("❌ لا يمكن تعديل بيانات المطور الافتراضي!\n\n" +
                                           "🔒 يمكن فقط تغيير كلمة المرور للمطور الافتراضي (amrali).\n" +
                                           "باقي البيانات محمية لضمان استمرارية عمل النظام.",
                                           "حماية المطور", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }
                    }

                    permissions = user.Permissions ?? new UserPermissions { UserId = user.UserId };
                }
                else
                {
                    user = new User();
                    permissions = new UserPermissions();
                    context.Users.Add(user);
                }

                // Update user data
                user.Username = txtUsername.Text.Trim();
                user.FullName = txtFullName.Text.Trim();
                user.Role = (UserRole)(cmbRole.SelectedIndex + 1);
                user.IsActive = chkIsActive.Checked;

                // Update password only if provided
                if (!string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(txtPassword.Text);
                }

                if (!isEditMode)
                {
                    user.CreatedDate = DateTime.Now;
                }
                else
                {
                    user.ModifiedDate = DateTime.Now;
                }

                // Save user first to get UserId
                await context.SaveChangesAsync();

                // Update permissions using reflection
                permissions.UserId = user.UserId;

                // Get all permission properties
                var permissionProperties = typeof(UserPermissions).GetProperties()
                    .Where(p => p.PropertyType == typeof(bool) && p.Name.StartsWith("Can"))
                    .ToList();

                // Update permissions from checkboxes
                foreach (var property in permissionProperties)
                {
                    var permissionName = property.Name;
                    if (permissionCheckboxes.TryGetValue(permissionName, out var checkBox))
                    {
                        property.SetValue(permissions, checkBox.Checked);
                    }
                }

                // Set developer permissions for developer role
                if (user.Role == UserRole.Developer)
                {
                    permissions.CanAddManager = true;
                    permissions.CanManageManagerPassword = true;
                    permissions.CanActivateSubscription = true;
                    permissions.CanActivateInstallation = true;
                    permissions.CanResetSystem = true;
                    permissions.CanRestoreDefaults = true;
                }

                if (!isEditMode)
                {
                    permissions.CreatedDate = DateTime.Now;
                    context.UserPermissions.Add(permissions);
                }
                else
                {
                    permissions.ModifiedDate = DateTime.Now;
                }

                await context.SaveChangesAsync();

                var message = isEditMode ? "تم تحديث المستخدم بنجاح" : "تم إضافة المستخدم بنجاح";
                MessageBox.Show(message, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }, "حفظ المستخدم");
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                lblStatus.Text = "يرجى إدخال اسم المستخدم.";
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                lblStatus.Text = "يرجى إدخال الاسم الكامل.";
                txtFullName.Focus();
                return false;
            }

            if (!isEditMode || !string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    lblStatus.Text = "يرجى إدخال كلمة المرور.";
                    txtPassword.Focus();
                    return false;
                }

                if (txtPassword.Text.Length < 6)
                {
                    lblStatus.Text = "كلمة المرور يجب أن تكون 6 أحرف على الأقل.";
                    txtPassword.Focus();
                    return false;
                }

                if (txtPassword.Text != txtConfirmPassword.Text)
                {
                    lblStatus.Text = "كلمة المرور وتأكيدها غير متطابقان.";
                    txtConfirmPassword.Focus();
                    return false;
                }
            }

            if (cmbRole.SelectedIndex < 0)
            {
                lblStatus.Text = "يرجى اختيار دور المستخدم.";
                cmbRole.Focus();
                return false;
            }

            // Validate username format
            if (!Regex.IsMatch(txtUsername.Text, @"^[a-zA-Z0-9_]{3,20}$"))
            {
                lblStatus.Text = "اسم المستخدم يجب أن يحتوي على أحرف إنجليزية وأرقام فقط (3-20 حرف).";
                txtUsername.Focus();
                return false;
            }

            return true;
        }
    }
}


