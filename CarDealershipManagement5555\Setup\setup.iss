[Setup]
; معلومات التطبيق
AppName=برنامج إدارة معرض السيارات
AppVersion=1.0.0
AppPublisher=Car Dealership Management Solutions
AppPublisherURL=https://www.cardealership.com
AppSupportURL=https://www.cardealership.com/support
AppUpdatesURL=https://www.cardealership.com/updates
AppCopyright=Copyright © 2024 Car Dealership Management Solutions

; معلومات التثبيت
DefaultDirName={autopf}\Car Dealership Management
DefaultGroupName=برنامج إدارة معرض السيارات
AllowNoIcons=yes
LicenseFile=license.txt
InfoBeforeFile=readme.txt
InfoAfterFile=after_install.txt
OutputDir=Output
OutputBaseFilename=CarDealershipManagement_Setup_v1.0.0
SetupIconFile=app-icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; متطلبات النظام
MinVersion=10.0.17763
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; إعدادات التثبيت
PrivilegesRequired=admin
DisableProgramGroupPage=yes
DisableReadyPage=no
DisableFinishedPage=no
DisableWelcomePage=no

; إعدادات إلغاء التثبيت
UninstallDisplayIcon={app}\CarDealershipManagement.exe
UninstallDisplayName=برنامج إدارة معرض السيارات
CreateUninstallRegKey=yes

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "startmenu"; Description: "إضافة إلى قائمة ابدأ"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkedonce

[Files]
; الملف التنفيذي الرئيسي
Source: "..\bin\Release\net8.0-windows\publish\CarDealershipManagement.exe"; DestDir: "{app}"; Flags: ignoreversion

; ملفات قاعدة البيانات والإعدادات
Source: "..\bin\Release\net8.0-windows\publish\*.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\bin\Release\net8.0-windows\publish\*.json"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\bin\Release\net8.0-windows\publish\*.config"; DestDir: "{app}"; Flags: ignoreversion

; ملفات الموارد
Source: "..\Resources\*"; DestDir: "{app}\Resources"; Flags: ignoreversion recursesubdirs createallsubdirs

; ملفات التوثيق
Source: "license.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "readme.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "user_manual.pdf"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
; أيقونة سطح المكتب
Name: "{autoprograms}\برنامج إدارة معرض السيارات"; Filename: "{app}\CarDealershipManagement.exe"; WorkingDir: "{app}"; IconFilename: "{app}\CarDealershipManagement.exe"

; أيقونة سطح المكتب
Name: "{autodesktop}\برنامج إدارة معرض السيارات"; Filename: "{app}\CarDealershipManagement.exe"; WorkingDir: "{app}"; Tasks: desktopicon

; أيقونة شريط المهام السريع
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\برنامج إدارة معرض السيارات"; Filename: "{app}\CarDealershipManagement.exe"; WorkingDir: "{app}"; Tasks: quicklaunchicon

; أيقونة إلغاء التثبيت
Name: "{autoprograms}\إلغاء تثبيت برنامج إدارة معرض السيارات"; Filename: "{uninstallexe}"

[Registry]
; تسجيل البرنامج في النظام
Root: HKLM; Subkey: "SOFTWARE\Car Dealership Management"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\Car Dealership Management"; ValueType: string; ValueName: "Version"; ValueData: "1.0.0"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\Car Dealership Management"; ValueType: string; ValueName: "InstallDate"; ValueData: "{code:GetCurrentDateTime}"; Flags: uninsdeletekey

; ربط ملفات قاعدة البيانات
Root: HKCR; Subkey: ".cdm"; ValueType: string; ValueName: ""; ValueData: "CarDealershipFile"; Flags: uninsdeletevalue
Root: HKCR; Subkey: "CarDealershipFile"; ValueType: string; ValueName: ""; ValueData: "Car Dealership Management File"; Flags: uninsdeletekey
Root: HKCR; Subkey: "CarDealershipFile\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\CarDealershipManagement.exe,0"
Root: HKCR; Subkey: "CarDealershipFile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\CarDealershipManagement.exe"" ""%1"""

[Run]
; تشغيل البرنامج بعد التثبيت
Filename: "{app}\CarDealershipManagement.exe"; Description: "{cm:LaunchProgram,برنامج إدارة معرض السيارات}"; Flags: nowait postinstall skipifsilent

; تثبيت .NET Runtime إذا لم يكن موجوداً
Filename: "https://download.microsoft.com/download/dotnet/8.0/dotnet-runtime-8.0-win-x64.exe"; Description: "تثبيت .NET Runtime 8.0"; Flags: shellexec skipifdoesntexist

[UninstallRun]
; تنظيف الملفات المؤقتة عند إلغاء التثبيت
Filename: "{cmd}"; Parameters: "/c rmdir /s /q ""{userappdata}\Car Dealership Management"""; Flags: runhidden

[Code]
// دوال مساعدة للتثبيت

function GetCurrentDateTime(Param: String): String;
begin
  Result := GetDateTimeString('yyyy/mm/dd hh:nn:ss', #0, #0);
end;

function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // التحقق من إصدار Windows
  if Version.Major < 10 then
  begin
    MsgBox('هذا البرنامج يتطلب Windows 10 أو أحدث.', mbError, MB_OK);
    Result := False;
  end
  else
    Result := True;
end;

procedure InitializeWizard();
begin
  // تخصيص واجهة التثبيت
  WizardForm.WelcomeLabel1.Caption := 'مرحباً بك في معالج تثبيت برنامج إدارة معرض السيارات';
  WizardForm.WelcomeLabel2.Caption := 'سيقوم هذا المعالج بتثبيت برنامج إدارة معرض السيارات على جهازك.' + #13#10 + 
                                      'يُنصح بإغلاق جميع التطبيقات الأخرى قبل المتابعة.';
end;

function PrepareToInstall(var NeedsRestart: Boolean): String;
begin
  // التحقق من المتطلبات قبل التثبيت
  Result := '';
  
  // التحقق من وجود .NET Runtime
  if not RegKeyExists(HKLM, 'SOFTWARE\dotnet\Setup\InstalledVersions\x64\sharedhost') then
  begin
    Result := 'يتطلب هذا البرنامج .NET Runtime 8.0.' + #13#10 + 
              'سيتم تنزيله وتثبيته تلقائياً.';
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  case CurStep of
    ssInstall:
      begin
        // إنشاء مجلدات البيانات
        CreateDir(ExpandConstant('{userappdata}\Car Dealership Management'));
        CreateDir(ExpandConstant('{userappdata}\Car Dealership Management\Backups'));
        CreateDir(ExpandConstant('{userappdata}\Car Dealership Management\Reports'));
        CreateDir(ExpandConstant('{userappdata}\Car Dealership Management\Logs'));
      end;
      
    ssPostInstall:
      begin
        // تسجيل التثبيت في النظام
        RegWriteStringValue(HKLM, 'SOFTWARE\Car Dealership Management', 
                           'DataPath', ExpandConstant('{userappdata}\Car Dealership Management'));
      end;
  end;
end;

procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
var
  DataPath: String;
  ResultCode: Integer;
begin
  case CurUninstallStep of
    usUninstall:
      begin
        // سؤال المستخدم عن حذف البيانات
        if MsgBox('هل تريد حذف جميع بيانات البرنامج (قواعد البيانات والتقارير)؟' + #13#10 + 
                  'تحذير: لن يمكن استرداد هذه البيانات بعد الحذف!', 
                  mbConfirmation, MB_YESNO) = IDYES then
        begin
          if RegQueryStringValue(HKLM, 'SOFTWARE\Car Dealership Management', 'DataPath', DataPath) then
          begin
            Exec(ExpandConstant('{cmd}'), '/c rmdir /s /q "' + DataPath + '"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
          end;
        end;
      end;
  end;
end;
