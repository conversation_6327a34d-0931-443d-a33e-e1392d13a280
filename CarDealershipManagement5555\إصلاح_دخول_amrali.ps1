# إصلاح مشكلة دخول المستخدم amrali
# المطور: Amr <PERSON> - 01285626623 - <EMAIL>

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🔐 إصلاح مشكلة دخول amrali" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "👨‍💻 المطور: Amr Ali Elawamy" -ForegroundColor Green
Write-Host "📞 الهاتف: 01285626623" -ForegroundColor Green
Write-Host "📧 البريد: <EMAIL>" -ForegroundColor Green
Write-Host ""

# التحقق من وجود قاعدة البيانات
$dbPath = "CarDealership.db"
if (-not (Test-Path $dbPath)) {
    Write-Host "❌ قاعدة البيانات غير موجودة: $dbPath" -ForegroundColor Red
    Write-Host "🔄 إنشاء قاعدة بيانات جديدة..." -ForegroundColor Yellow
    
    # تشغيل البرنامج لإنشاء قاعدة البيانات
    try {
        $process = Start-Process -FilePath "bin\Debug\net8.0-windows\CarDealershipManagement.exe" -PassThru -WindowStyle Hidden
        Start-Sleep -Seconds 3
        $process.Kill()
        Write-Host "✅ تم إنشاء قاعدة البيانات" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ تحذير: مشكلة في إنشاء قاعدة البيانات" -ForegroundColor Yellow
    }
}

Write-Host "🔍 فحص بيانات المستخدم amrali..." -ForegroundColor Yellow

# إنشاء اتصال بقاعدة البيانات
Add-Type -Path "bin\Debug\net8.0-windows\Microsoft.Data.Sqlite.dll"

try {
    $connectionString = "Data Source=$dbPath"
    $connection = New-Object Microsoft.Data.Sqlite.SqliteConnection($connectionString)
    $connection.Open()
    
    Write-Host "✅ تم الاتصال بقاعدة البيانات" -ForegroundColor Green
    
    # فحص وجود جدول المستخدمين
    $checkTableQuery = "SELECT name FROM sqlite_master WHERE type='table' AND name='Users';"
    $command = $connection.CreateCommand()
    $command.CommandText = $checkTableQuery
    $result = $command.ExecuteScalar()
    
    if ($null -eq $result) {
        Write-Host "❌ جدول المستخدمين غير موجود" -ForegroundColor Red
        Write-Host "🔄 إنشاء جدول المستخدمين..." -ForegroundColor Yellow
        
        $createTableQuery = @"
CREATE TABLE Users (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL UNIQUE,
    PasswordHash TEXT NOT NULL,
    FullName TEXT NOT NULL,
    Role TEXT NOT NULL,
    IsActive INTEGER NOT NULL DEFAULT 1,
    CreatedAt TEXT NOT NULL,
    LastLogin TEXT
);
"@
        $command.CommandText = $createTableQuery
        $command.ExecuteNonQuery()
        Write-Host "✅ تم إنشاء جدول المستخدمين" -ForegroundColor Green
    }
    
    # فحص وجود المستخدم amrali
    $checkUserQuery = "SELECT COUNT(*) FROM Users WHERE Username = 'amrali';"
    $command.CommandText = $checkUserQuery
    $userExists = $command.ExecuteScalar()
    
    if ($userExists -eq 0) {
        Write-Host "❌ المستخدم amrali غير موجود" -ForegroundColor Red
        Write-Host "🔄 إنشاء المستخدم amrali..." -ForegroundColor Yellow
        
        # إنشاء hash لكلمة المرور "braa"
        Add-Type -Path "bin\Debug\net8.0-windows\BCrypt.Net-Next.dll"
        $passwordHash = [BCrypt.Net.BCrypt]::HashPassword("braa")
        
        $insertUserQuery = @"
INSERT INTO Users (Username, PasswordHash, FullName, Role, IsActive, CreatedAt)
VALUES ('amrali', '$passwordHash', 'Amr Ali Elawamy', 'Admin', 1, datetime('now'));
"@
        $command.CommandText = $insertUserQuery
        $command.ExecuteNonQuery()
        Write-Host "✅ تم إنشاء المستخدم amrali" -ForegroundColor Green
    }
    else {
        Write-Host "✅ المستخدم amrali موجود" -ForegroundColor Green
        Write-Host "🔄 تحديث كلمة مرور amrali..." -ForegroundColor Yellow
        
        # تحديث كلمة المرور
        Add-Type -Path "bin\Debug\net8.0-windows\BCrypt.Net-Next.dll"
        $passwordHash = [BCrypt.Net.BCrypt]::HashPassword("braa")
        
        $updatePasswordQuery = "UPDATE Users SET PasswordHash = '$passwordHash' WHERE Username = 'amrali';"
        $command.CommandText = $updatePasswordQuery
        $command.ExecuteNonQuery()
        Write-Host "✅ تم تحديث كلمة مرور amrali" -ForegroundColor Green
    }
    
    # التحقق من صحة البيانات
    $verifyQuery = "SELECT Username, FullName, Role, IsActive FROM Users WHERE Username = 'amrali';"
    $command.CommandText = $verifyQuery
    $reader = $command.ExecuteReader()
    
    if ($reader.Read()) {
        Write-Host ""
        Write-Host "📋 بيانات المستخدم amrali:" -ForegroundColor Cyan
        Write-Host "   👤 اسم المستخدم: $($reader['Username'])" -ForegroundColor White
        Write-Host "   👨‍💼 الاسم الكامل: $($reader['FullName'])" -ForegroundColor White
        Write-Host "   🔑 الدور: $($reader['Role'])" -ForegroundColor White
        Write-Host "   ✅ نشط: $($reader['IsActive'])" -ForegroundColor White
        Write-Host "   🔐 كلمة المرور: braa" -ForegroundColor Yellow
    }
    
    $reader.Close()
    $connection.Close()
    
    Write-Host ""
    Write-Host "🎉 تم إصلاح مشكلة تسجيل الدخول بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "💡 بيانات تسجيل الدخول:" -ForegroundColor Cyan
    Write-Host "   👤 اسم المستخدم: amrali" -ForegroundColor White
    Write-Host "   🔑 كلمة المرور: braa" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 تشغيل البرنامج للاختبار..." -ForegroundColor Yellow
    
    # تشغيل البرنامج
    Start-Process -FilePath "bin\Debug\net8.0-windows\CarDealershipManagement.exe"
    
    Write-Host "✅ تم تشغيل البرنامج" -ForegroundColor Green
    Write-Host ""
    Write-Host "📝 تعليمات:" -ForegroundColor Cyan
    Write-Host "   1. في شاشة تسجيل الدخول، أدخل:" -ForegroundColor White
    Write-Host "      👤 اسم المستخدم: amrali" -ForegroundColor Yellow
    Write-Host "      🔑 كلمة المرور: braa" -ForegroundColor Yellow
    Write-Host "   2. اضغط على زر تسجيل الدخول" -ForegroundColor White
    Write-Host ""
}
catch {
    Write-Host "❌ خطأ في الاتصال بقاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 حلول بديلة:" -ForegroundColor Yellow
    Write-Host "   1. تشغيل factory_reset.bat" -ForegroundColor White
    Write-Host "   2. تشغيل create_default_user.bat" -ForegroundColor White
    Write-Host "   3. حذف ملف CarDealership.db وإعادة تشغيل البرنامج" -ForegroundColor White
}

Write-Host ""
Write-Host "👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy" -ForegroundColor Green
Write-Host "📞 للتواصل: 01285626623" -ForegroundColor Green
Write-Host "📧 البريد: <EMAIL>" -ForegroundColor Green
Write-Host ""
Read-Host "اضغط Enter للمتابعة"
