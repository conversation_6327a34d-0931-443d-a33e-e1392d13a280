@echo off
chcp 65001 >nul
title إنشاء مجلد مستقل بسيط - برنامج إدارة معرض السيارات

echo.
echo ========================================
echo   📦 إنشاء مجلد مستقل بسيط
echo ========================================
echo.

set "SOURCE_DIR=%~dp0"
set "DIST_DIR=%SOURCE_DIR%CarDealership_Standalone"

echo 🧹 تنظيف المجلد السابق...
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"

echo 📁 إنشاء المجلد الجديد...
mkdir "%DIST_DIR%"
mkdir "%DIST_DIR%\Data"
mkdir "%DIST_DIR%\Backups"
mkdir "%DIST_DIR%\Reports"
mkdir "%DIST_DIR%\Documentation"
mkdir "%DIST_DIR%\CarFiles"
mkdir "%DIST_DIR%\SupplierFiles"

echo.
echo 📋 نسخ الملفات من مجلد Release...

set "RELEASE_DIR=%SOURCE_DIR%bin\Release\net8.0-windows"

if exist "%RELEASE_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج في مجلد Release
    
    REM نسخ جميع ملفات Release
    xcopy "%RELEASE_DIR%\*.*" "%DIST_DIR%\" /E /I /Q >nul
    
    echo ✅ تم نسخ ملفات البرنامج
    
) else (
    set "DEBUG_DIR=%SOURCE_DIR%bin\Debug\net8.0-windows"
    
    if exist "%DEBUG_DIR%\CarDealershipManagement.exe" (
        echo ✅ تم العثور على البرنامج في مجلد Debug
        
        REM نسخ جميع ملفات Debug
        xcopy "%DEBUG_DIR%\*.*" "%DIST_DIR%\" /E /I /Q >nul
        
        echo ✅ تم نسخ ملفات البرنامج
        
    ) else (
        echo ❌ لم يتم العثور على البرنامج
        echo يرجى بناء البرنامج أولاً
        pause
        exit /b 1
    )
)

REM نسخ قاعدة البيانات إذا كانت موجودة
if exist "%SOURCE_DIR%CarDealership.db" (
    copy "%SOURCE_DIR%CarDealership.db" "%DIST_DIR%\Data\" >nul
    echo ✅ تم نسخ قاعدة البيانات
)

echo.
echo 📋 إنشاء ملفات إضافية...

REM إنشاء ملف README
(
echo برنامج إدارة معرض السيارات - الإصدار 1.0.0
echo ===============================================
echo.
echo 🚗 نظام شامل لإدارة معارض السيارات
echo.
echo 🚀 طريقة التشغيل:
echo    1. اضغط مرتين على CarDealershipManagement.exe
echo    2. اختر "نسخة تجريبية" للبدء فوراً ^(30 يوم^)
echo    3. أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo.
echo 🔑 بيانات الدخول الافتراضية:
echo    المطور: developer / dev123 ^(جميع الصلاحيات^)
echo    المدير: admin / 123 ^(صلاحيات إدارية^)
echo    المندوب: user / pass ^(صلاحيات أساسية^)
echo.
echo 🎯 الميزات الرئيسية:
echo    • إدارة شاملة للمخزون والسيارات
echo    • نظام مبيعات متقدم مع الأقساط
echo    • إدارة العملاء والموردين
echo    • تقارير وإحصائيات تفصيلية
echo    • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo    • نظام ضمان سلامة البيانات المالية
echo    • نسخ احتياطي وأرشفة
echo.
echo 💡 نصائح مهمة:
echo    • قم بإنشاء نسخة احتياطية من مجلد Data بانتظام
echo    • لا تحذف أي ملفات من المجلد
echo    • استخدم كلمات مرور قوية
echo.
echo 📞 للدعم الفني: <EMAIL>
echo 🌐 الموقع: www.cardealership.com
echo.
echo تاريخ الإنشاء: %date% %time%
) > "%DIST_DIR%\README.txt"

REM إنشاء ملف تشغيل سريع
(
echo @echo off
echo chcp 65001 ^>nul
echo title برنامج إدارة معرض السيارات
echo.
echo echo ========================================
echo echo    🚗 برنامج إدارة معرض السيارات
echo echo ========================================
echo echo.
echo echo 🚀 جاري تشغيل البرنامج...
echo echo.
echo echo 🔑 بيانات الدخول:
echo echo    المطور: developer / dev123
echo echo    المدير: admin / 123  
echo echo    المندوب: user / pass
echo echo.
echo echo 💡 يمكنك أيضاً اختيار "نسخة تجريبية" للبدء فوراً
echo echo.
echo start "" "CarDealershipManagement.exe"
echo echo ✅ تم تشغيل البرنامج!
echo echo.
echo echo اضغط أي مفتاح للخروج...
echo pause ^>nul
) > "%DIST_DIR%\تشغيل_البرنامج.bat"

REM إنشاء ملف النسخ الاحتياطي
(
echo @echo off
echo chcp 65001 ^>nul
echo title إنشاء نسخة احتياطية
echo.
echo echo ========================================
echo echo    💾 إنشاء نسخة احتياطية
echo echo ========================================
echo echo.
echo.
echo set "BACKUP_DIR=Backups\Backup_%%date:~-4,4%%%%date:~-10,2%%%%date:~-7,2%%_%%time:~0,2%%%%time:~3,2%%%%time:~6,2%%"
echo set "BACKUP_DIR=%%BACKUP_DIR: =0%%"
echo.
echo echo 📁 إنشاء مجلد النسخة الاحتياطية...
echo mkdir "%%BACKUP_DIR%%"
echo.
echo echo 💾 نسخ قاعدة البيانات...
echo if exist "Data\CarDealership.db" copy "Data\CarDealership.db" "%%BACKUP_DIR%%\" ^>nul
echo if exist "CarDealership.db" copy "CarDealership.db" "%%BACKUP_DIR%%\" ^>nul
echo.
echo echo 📁 نسخ ملفات السيارات...
echo if exist "CarFiles" xcopy "CarFiles" "%%BACKUP_DIR%%\CarFiles\" /E /I /Q ^>nul
echo.
echo echo 📁 نسخ ملفات الموردين...
echo if exist "SupplierFiles" xcopy "SupplierFiles" "%%BACKUP_DIR%%\SupplierFiles\" /E /I /Q ^>nul
echo.
echo echo ✅ تم إنشاء النسخة الاحتياطية بنجاح!
echo echo 📂 المكان: %%BACKUP_DIR%%
echo echo.
echo echo اضغط أي مفتاح للخروج...
echo pause ^>nul
) > "%DIST_DIR%\إنشاء_نسخة_احتياطية.bat"

REM إنشاء دليل المستخدم
(
echo دليل المستخدم السريع - برنامج إدارة معرض السيارات
echo ========================================================
echo.
echo 🎯 البدء السريع:
echo ================
echo.
echo 1. التشغيل لأول مرة:
echo    • اضغط مرتين على CarDealershipManagement.exe
echo    • اختر "نسخة تجريبية ^(30 يوم^)" للبدء فوراً
echo    • أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo.
echo 2. تسجيل الدخول:
echo    • المطور ^(جميع الصلاحيات^): developer / dev123
echo    • المدير ^(صلاحيات إدارية^): admin / 123
echo    • المندوب ^(صلاحيات أساسية^): user / pass
echo.
echo 🔧 الميزات الرئيسية:
echo ====================
echo.
echo 📦 إدارة المخزون:
echo    • إضافة وتعديل وحذف السيارات
echo    • نظام ضمان سلامة البيانات المالية
echo    • إدارة ملفات ومرفقات السيارات
echo    • تصنيف وفلترة السيارات
echo.
echo 💰 نظام المبيعات:
echo    • بيع نقدي وبالتقسيط
echo    • إدارة الأقساط والمدفوعات
echo    • طباعة الفواتير والعقود
echo    • تتبع حالة المدفوعات
echo.
echo 👥 إدارة العملاء:
echo    • قاعدة بيانات شاملة للعملاء
echo    • تاريخ المشتريات والمدفوعات
echo    • كشوف حساب مفصلة
echo    • إدارة ملفات العملاء
echo.
echo 📊 التقارير والإحصائيات:
echo    • تقارير المبيعات والأرباح
echo    • تقارير الأقساط المحسنة
echo    • إحصائيات الأداء
echo    • طباعة وتصدير بـ 5 صيغ
echo.
echo 👤 إدارة المستخدمين:
echo    • نظام صلاحيات متكامل ^(64+ صلاحية^)
echo    • أدوار متعددة ^(مطور، مدير، مندوب^)
echo    • إدارة كلمات المرور
echo    • تتبع نشاط المستخدمين
echo.
echo 🔒 الأمان والحماية:
echo    • نسخ احتياطي تلقائي
echo    • تشفير البيانات الحساسة
echo    • نظام تفعيل متقدم
echo    • حماية من فقدان البيانات
echo.
echo 💡 نصائح مهمة:
echo ================
echo.
echo • قم بإنشاء نسخة احتياطية من مجلد Data بانتظام
echo • استخدم كلمات مرور قوية للمستخدمين
echo • راجع التقارير دورياً لمتابعة الأداء
echo • احتفظ بنسخة من ملفات السيارات والعملاء
echo • تأكد من تحديث البرنامج عند توفر إصدارات جديدة
echo.
echo 📞 الدعم الفني:
echo ================
echo.
echo 📧 البريد الإلكتروني: <EMAIL>
echo 📞 الهاتف: +20-XXX-XXX-XXXX
echo 🌐 الموقع: www.cardealership.com
echo ⏰ ساعات العمل: الأحد - الخميس، 9 ص - 5 م
echo.
echo شكراً لاختيارك برنامج إدارة معرض السيارات! 🚗
) > "%DIST_DIR%\Documentation\دليل_المستخدم_السريع.txt"

echo ✅ تم إنشاء الملفات الإضافية

echo.
echo 📊 عرض معلومات المجلد المنشأ...
echo.

if exist "%DIST_DIR%\CarDealershipManagement.exe" (
    echo ✅ تم إنشاء المجلد المستقل بنجاح!
    echo.
    echo 📁 مكان المجلد: %DIST_DIR%
    echo.
    
    REM عرض حجم الملف التنفيذي
    for %%A in ("%DIST_DIR%\CarDealershipManagement.exe") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📏 حجم الملف التنفيذي: !FILE_SIZE_MB! ميجابايت
    )
    
    echo.
    echo 📋 محتويات المجلد:
    echo    ✅ CarDealershipManagement.exe - الملف التنفيذي الرئيسي
    echo    ✅ تشغيل_البرنامج.bat - تشغيل سريع
    echo    ✅ إنشاء_نسخة_احتياطية.bat - أداة النسخ الاحتياطي
    echo    ✅ README.txt - دليل سريع
    echo    ✅ Data\ - مجلد قواعد البيانات
    echo    ✅ Documentation\ - أدلة المستخدم
    echo    ✅ Backups\ - مجلد النسخ الاحتياطية
    echo    ✅ Reports\ - مجلد التقارير
    echo    ✅ CarFiles\ - ملفات السيارات
    echo    ✅ SupplierFiles\ - ملفات الموردين
    echo.
    
    echo 🎯 المجلد جاهز للتوزيع!
    echo.
    echo 💡 يمكنك الآن:
    echo    1. نسخ المجلد إلى أي مكان
    echo    2. ضغطه في ملف ZIP للتوزيع
    echo    3. نسخه إلى فلاشة USB
    echo    4. رفعه على الإنترنت للتحميل
    echo.
    
    echo 🚀 لتشغيل البرنامج من المجلد الجديد:
    echo    1. انتقل إلى: %DIST_DIR%
    echo    2. اضغط مرتين على "تشغيل_البرنامج.bat"
    echo    3. أو اضغط مرتين على "CarDealershipManagement.exe"
    echo.
    
) else (
    echo ❌ فشل في إنشاء المجلد المستقل
    echo يرجى التأكد من وجود البرنامج في مجلد bin\Release أو bin\Debug
)

echo.
echo 🎉 انتهت العملية!
echo.
echo 📂 المجلد الجديد: %DIST_DIR%
echo.

echo هل تريد فتح المجلد الجديد؟ (Y/N)
set /p "OPEN_FOLDER="
if /i "%OPEN_FOLDER%"=="Y" (
    start "" "%DIST_DIR%"
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
