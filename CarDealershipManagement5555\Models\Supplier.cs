using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CarDealershipManagement.Models
{
    public class Supplier
    {
        [Key]
        public int SupplierId
        {
            get;
            set;
        }

        [Required(ErrorMessage = "اسم المورد/الشركة مطلوب.")]
        [StringLength(100, ErrorMessage = "اسم المورد/الشركة لا يمكن أن يتجاوز 100 حرف.")]
        public string SupplierName
        {
            get;
            set;
        } = string.Empty; // اسم المورد/الشركة

        [StringLength(50, ErrorMessage = "رقم السجل التجاري لا يمكن أن يتجاوز 50 حرفًا.")]
        public string? CommercialRegistrationNumber
        {
            get;    // رقم السجل التجاري (اختياري)
            set;
        }

        [Required(ErrorMessage = "الشخص المسؤول مطلوب.")]
        [StringLength(100, ErrorMessage = "اسم الشخص المسؤول لا يمكن أن يتجاوز 100 حرفًا.")]
        public string ResponsiblePerson
        {
            get;
            set;
        } = string.Empty; // الشخص المسؤول

        [Required(ErrorMessage = "رقم الهاتف مطلوب.")]
        [StringLength(20, ErrorMessage = "رقم الهاتف لا يمكن أن يتجاوز 20 حرفًا.")]
        [Phone(ErrorMessage = "صيغة رقم الهاتف غير صحيحة.")]
        public string Phone
        {
            get;
            set;
        } = string.Empty; // رقم الهاتف

        [StringLength(100, ErrorMessage = "البريد الإلكتروني لا يمكن أن يتجاوز 100 حرفًا.")]
        [EmailAddress(ErrorMessage = "صيغة البريد الإلكتروني غير صحيحة.")]
        public string? Email
        {
            get;    // البريد الإلكتروني (اختياري)
            set;
        }

        [Required(ErrorMessage = "العنوان مطلوب.")]
        [StringLength(200, ErrorMessage = "العنوان لا يمكن أن يتجاوز 200 حرفًا.")]
        public string Address
        {
            get;
            set;
        } = string.Empty; // العنوان

        // Financial tracking
        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue, ErrorMessage = "إجمالي المبلغ المستحق يجب أن يكون قيمة موجبة.")]
        public decimal TotalOwed
        {
            get;
            set;
        } = 0; // إجمالي المبلغ المستحق للمورد

        [Column(TypeName = "decimal(18,2)")]
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ المدفوع يجب أن يكون قيمة موجبة.")]
        public decimal TotalPaid
        {
            get;
            set;
        } = 0; // المبلغ المدفوع

        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance
        {
            get;
            set;
        } = 0; // المتبقي (TotalOwed - TotalPaid)

        public SupplierAccountStatus AccountStatus
        {
            get;
            set;
        } = SupplierAccountStatus.Neutral; // حالة الحساب

        public DateTime CreatedDate
        {
            get;
            set;
        } = DateTime.Now;
        public DateTime? ModifiedDate
        {
            get;
            set;
        }

        // Navigation properties
        public virtual ICollection<SupplierDocument> Documents
        {
            get;
            set;
        } = new List<SupplierDocument>();
        public virtual ICollection<SupplierPayment> Payments
        {
            get;
            set;
        } = new List<SupplierPayment>();
        public virtual ICollection<SupplierPaymentSchedule> PaymentSchedules
        {
            get;
            set;
        } = new List<SupplierPaymentSchedule>();
    }

    public enum SupplierAccountStatus
    {
        Creditor = 1,     // دائن - نحن مدينون للمورد
        Debtor = 2,       // مدين - المورد مدين لنا
        Neutral = 3       // متوازن
    }
}


