@echo off
title Run Application - Car Dealership Management
color 0B

echo.
echo ===============================================
echo        RUN APPLICATION TO CREATE USER
echo        Car Dealership Management System
echo ===============================================
echo.
echo This tool will run the application to automatically
echo create the default user with correct password.
echo.

echo Step 1: Running the application...
echo The application will create the database and default user automatically.
echo.
echo Please wait while the application starts...
echo.

start CarDealershipManagement.exe

echo.
echo ✅ Application started successfully!
echo.
echo The application should now have:
echo 1. Created the database automatically
echo 2. Created the default user with correct password
echo.
echo ===============================================
echo           LOGIN CREDENTIALS
echo ===============================================
echo.
echo Username: amrali
echo Password: braa
echo.
echo Try logging in with these credentials.
echo.
echo If login still fails:
echo 1. Close the application completely
echo 2. Run 'fix_login.bat' to reset everything
echo 3. Try again
echo.
echo ===============================================
echo.
pause

exit /b 0
