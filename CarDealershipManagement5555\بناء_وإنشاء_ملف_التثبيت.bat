@echo off
chcp 65001 >nul
title بناء وإنشاء ملف التثبيت - برنامج إدارة معرض السيارات

echo.
echo ========================================
echo   🔨 بناء وإنشاء ملف التثبيت
echo ========================================
echo.

echo 🎯 المرحلة 1: التحقق من المتطلبات...
echo.

REM التحقق من وجود .NET SDK
dotnet --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: .NET SDK غير مثبت
    echo يرجى تثبيت .NET 8.0 SDK من: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo ✅ تم العثور على .NET SDK
dotnet --version

echo.
echo 🧹 المرحلة 2: تنظيف المشروع...
echo.

REM حذف مجلدات البناء السابقة
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "Setup\Output" rmdir /s /q "Setup\Output"

echo ✅ تم تنظيف المشروع

echo.
echo 🔧 المرحلة 3: استعادة الحزم...
echo.

dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في استعادة الحزم
    pause
    exit /b 1
)

echo ✅ تم استعادة الحزم بنجاح

echo.
echo 🏗️ المرحلة 4: بناء المشروع (Release)...
echo.

dotnet build --configuration Release --no-restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح

echo.
echo 📦 المرحلة 5: نشر التطبيق (Single File)...
echo.

dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "bin\Release\net8.0-windows\publish" /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true /p:PublishReadyToRun=true
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في نشر التطبيق
    pause
    exit /b 1
)

echo ✅ تم نشر التطبيق بنجاح

echo.
echo 📋 المرحلة 6: إنشاء ملفات التوثيق...
echo.

REM إنشاء ملف README
echo إنشاء ملف README...
(
echo برنامج إدارة معرض السيارات - الإصدار 1.0.0
echo ===============================================
echo.
echo نظام شامل لإدارة معارض السيارات يتضمن:
echo.
echo ✅ إدارة المخزون والسيارات
echo ✅ نظام المبيعات والفواتير  
echo ✅ إدارة العملاء والموردين
echo ✅ نظام الأقساط المتقدم
echo ✅ التقارير والإحصائيات
echo ✅ نظام الصلاحيات المتكامل
echo ✅ النسخ الاحتياطي والأرشفة
echo.
echo متطلبات النظام:
echo - Windows 10 أو أحدث
echo - 4 جيجابايت رام على الأقل
echo - 500 ميجابايت مساحة فارغة
echo.
echo للدعم الفني: <EMAIL>
) > "Setup\readme.txt"

REM إنشاء ملف معلومات ما بعد التثبيت
(
echo تم تثبيت برنامج إدارة معرض السيارات بنجاح!
echo ===============================================
echo.
echo 🎉 مبروك! تم تثبيت البرنامج بنجاح على جهازك.
echo.
echo 📋 الخطوات التالية:
echo.
echo 1. شغل البرنامج من سطح المكتب أو قائمة ابدأ
echo 2. اختر "نسخة تجريبية" للبدء فوراً لمدة 30 يوم
echo 3. أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo 4. أنشئ أول مستخدم مدير للنظام
echo 5. ابدأ بإدخال بيانات السيارات والعملاء
echo.
echo 💡 نصائح مهمة:
echo.
echo • قم بإنشاء نسخة احتياطية من البيانات بانتظام
echo • استخدم كلمات مرور قوية للمستخدمين
echo • راجع دليل المستخدم للاستفادة من جميع الميزات
echo.
echo 📞 للدعم الفني:
echo   البريد الإلكتروني: <EMAIL>
echo   الهاتف: +20-XXX-XXX-XXXX
echo.
echo شكراً لاختيارك برنامج إدارة معرض السيارات!
) > "Setup\after_install.txt"

echo ✅ تم إنشاء ملفات التوثيق

echo.
echo 📊 المرحلة 7: عرض معلومات الملف المنشور...
echo.

set "PUBLISH_DIR=bin\Release\net8.0-windows\publish"
set "EXE_FILE=%PUBLISH_DIR%\CarDealershipManagement.exe"

if exist "%EXE_FILE%" (
    echo ✅ الملف التنفيذي: %EXE_FILE%
    
    REM عرض حجم الملف
    for %%A in ("%EXE_FILE%") do (
        set "FILE_SIZE=%%~zA"
        set /a "FILE_SIZE_MB=!FILE_SIZE! / 1024 / 1024"
        echo 📏 حجم الملف: !FILE_SIZE_MB! ميجابايت
    )
    
    REM عرض تاريخ الإنشاء
    for %%A in ("%EXE_FILE%") do (
        echo 📅 تاريخ الإنشاء: %%~tA
    )
    
    echo.
    echo 🎯 الملف جاهز للتوزيع كملف تنفيذي مستقل!
    echo.
    
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
)

echo.
echo 🔧 المرحلة 8: التحقق من وجود Inno Setup (اختياري)...
echo.

REM التحقق من وجود Inno Setup لإنشاء ملف التثبيت
where iscc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم العثور على Inno Setup
    echo.
    echo 📦 إنشاء ملف التثبيت...
    
    REM إنشاء ملف التثبيت باستخدام Inno Setup
    iscc "Setup\setup.iss"
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم إنشاء ملف التثبيت بنجاح!
        echo 📁 الملف موجود في: Setup\Output\
        
        if exist "Setup\Output\CarDealershipManagement_Setup_v1.0.0.exe" (
            for %%A in ("Setup\Output\CarDealershipManagement_Setup_v1.0.0.exe") do (
                set "SETUP_SIZE=%%~zA"
                set /a "SETUP_SIZE_MB=!SETUP_SIZE! / 1024 / 1024"
                echo 📏 حجم ملف التثبيت: !SETUP_SIZE_MB! ميجابايت
            )
        )
    ) else (
        echo ❌ فشل في إنشاء ملف التثبيت
    )
) else (
    echo ⚠️ Inno Setup غير مثبت
    echo 💡 لإنشاء ملف تثبيت احترافي، قم بتثبيت Inno Setup من:
    echo    https://jrsoftware.org/isinfo.php
    echo.
    echo 📦 يمكنك استخدام الملف التنفيذي المستقل الموجود في:
    echo    %PUBLISH_DIR%\CarDealershipManagement.exe
)

echo.
echo 🎉 المرحلة 9: ملخص النتائج...
echo.

echo ========================================
echo           📋 ملخص العملية
echo ========================================
echo.

if exist "%EXE_FILE%" (
    echo ✅ تم بناء البرنامج بنجاح
    echo 📁 المجلد: %PUBLISH_DIR%
    echo 📄 الملف: CarDealershipManagement.exe
    echo.
    
    echo 🚀 طرق التوزيع المتاحة:
    echo.
    echo 1️⃣ ملف تنفيذي مستقل:
    echo    📁 %EXE_FILE%
    echo    💡 يمكن تشغيله مباشرة على أي جهاز Windows 10+
    echo.
    
    if exist "Setup\Output\CarDealershipManagement_Setup_v1.0.0.exe" (
        echo 2️⃣ ملف التثبيت الاحترافي:
        echo    📁 Setup\Output\CarDealershipManagement_Setup_v1.0.0.exe
        echo    💡 يتضمن معالج التثبيت وإعداد النظام
        echo.
    )
    
    echo 🔑 ميزات نظام التفعيل:
    echo    • نسخة تجريبية 30 يوم مجانية
    echo    • تراخيص شهرية وسنوية ومدى الحياة
    echo    • حماية بمعرف الجهاز
    echo    • تشفير ملفات الترخيص
    echo.
    
    echo 📋 الميزات المتاحة:
    echo    • إدارة شاملة للمخزون والسيارات
    echo    • نظام مبيعات متقدم مع الأقساط
    echo    • إدارة العملاء والموردين
    echo    • تقارير وإحصائيات تفصيلية
    echo    • نظام صلاحيات متكامل (64+ صلاحية)
    echo    • نظام ضمان سلامة البيانات المالية
    echo    • نسخ احتياطي وأرشفة
    echo.
    
    echo 🎯 البرنامج جاهز للتوزيع والاستخدام!
    
) else (
    echo ❌ فشل في بناء البرنامج
    echo يرجى مراجعة الأخطاء أعلاه
)

echo.
echo 📞 للدعم الفني أو المساعدة:
echo    📧 <EMAIL>
echo    🌐 www.cardealership.com
echo.

echo اضغط أي مفتاح للخروج...
pause >nul
