@echo off
chcp 65001 >nul
title تثبيت برنامج إدارة معرض السيارات - Amr Ali Elawamy

echo.
echo ========================================
echo    📦 تثبيت برنامج إدارة معرض السيارات
echo ========================================
echo.
echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo 🎯 مرحباً بك في برنامج إدارة معرض السيارات
echo.
echo الميزات الرئيسية:
echo    • إدارة شاملة للمخزون والسيارات
echo    • نظام مبيعات متقدم مع الأقساط
echo    • إدارة العملاء والموردين
echo    • تقارير وإحصائيات تفصيلية
echo    • نظام صلاحيات متكامل (64+ صلاحية)
echo    • نظام ضمان سلامة البيانات المالية
echo    • نظام تفعيل متقدم
echo.

set "INSTALL_DIR=%PROGRAMFILES%\Car Dealership Management"
set "SOURCE_DIR=%~dp0CarDealership_Debug_Copy"

echo 📁 مجلد التثبيت: %INSTALL_DIR%
echo.

REM التحقق من وجود ملفات البرنامج
if not exist "%SOURCE_DIR%\CarDealershipManagement.exe" (
    echo ❌ ملفات البرنامج غير موجودة
    echo يرجى التأكد من وجود مجلد CarDealership_Debug_Copy
    pause
    exit /b 1
)

echo ✅ تم العثور على ملفات البرنامج

echo.
echo هل تريد المتابعة مع التثبيت؟ (Y/N)
set /p "CONTINUE="
if /i not "%CONTINUE%"=="Y" (
    echo تم إلغاء التثبيت
    pause
    exit /b 0
)

echo.
echo 📁 إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📋 نسخ ملفات البرنامج...
xcopy "%SOURCE_DIR%\*" "%INSTALL_DIR%\" /E /I /Y >nul

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم نسخ الملفات بنجاح
) else (
    echo ❌ فشل في نسخ الملفات
    pause
    exit /b 1
)

echo 🔗 إنشاء اختصارات...

REM اختصار سطح المكتب
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\برنامج إدارة معرض السيارات.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CarDealershipManagement.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\app-icon.ico'; $Shortcut.Description = 'برنامج إدارة معرض السيارات - Amr Ali Elawamy'; $Shortcut.Save()" >nul 2>&1

REM اختصار قائمة البدء
set "START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%START_MENU%\برنامج إدارة معرض السيارات" mkdir "%START_MENU%\برنامج إدارة معرض السيارات"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\برنامج إدارة معرض السيارات\برنامج إدارة معرض السيارات.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CarDealershipManagement.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\app-icon.ico'; $Shortcut.Description = 'برنامج إدارة معرض السيارات - Amr Ali Elawamy'; $Shortcut.Save()" >nul 2>&1

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\برنامج إدارة معرض السيارات\دليل المستخدم.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\README.txt'; $Shortcut.Save()" >nul 2>&1

echo ✅ تم إنشاء الاختصارات

echo 📝 إنشاء ملف إلغاء التثبيت...
(
echo @echo off
echo chcp 65001 ^>nul
echo title إلغاء تثبيت برنامج إدارة معرض السيارات
echo.
echo echo ========================================
echo echo    🗑️ إلغاء تثبيت برنامج إدارة معرض السيارات
echo echo ========================================
echo echo.
echo echo هل تريد إلغاء تثبيت برنامج إدارة معرض السيارات؟ ^(Y/N^)
echo set /p "UNINSTALL="
echo if /i not "%%UNINSTALL%%"=="Y" exit /b 0
echo.
echo echo جاري إلغاء التثبيت...
echo echo.
echo echo حذف الاختصارات...
echo del "%%USERPROFILE%%\Desktop\برنامج إدارة معرض السيارات.lnk" ^>nul 2^>^&1
echo rmdir /s /q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\برنامج إدارة معرض السيارات" ^>nul 2^>^&1
echo.
echo echo حذف ملفات البرنامج...
echo cd /d "%%PROGRAMFILES%%"
echo rmdir /s /q "Car Dealership Management"
echo.
echo echo ✅ تم إلغاء التثبيت بنجاح!
echo echo.
echo echo 👨‍💻 شكراً لاستخدامك برنامج إدارة معرض السيارات
echo echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo echo.
echo pause
) > "%INSTALL_DIR%\uninstall.bat"

echo ✅ تم إنشاء ملف إلغاء التثبيت

echo.
echo 🎉 تم تثبيت البرنامج بنجاح!
echo.
echo 🔑 بيانات الدخول:
echo    المطور: amrali / braa (جميع الصلاحيات)
echo    المدير: admin / 123 (صلاحيات إدارية)
echo    المندوب: user / pass (صلاحيات أساسية)
echo.
echo 🆕 نظام التفعيل:
echo    • اختر "نسخة تجريبية" للبدء فوراً (30 يوم مجاني)
echo    • أو أدخل مفتاح الترخيص إذا كان لديك واحد
echo.
echo 💡 يمكنك الآن:
echo    1. تشغيل البرنامج من سطح المكتب
echo    2. أو من قائمة البدء
echo    3. اختيار نوع التفعيل المناسب
echo.
echo 📁 مكان التثبيت: %INSTALL_DIR%
echo 🔗 الاختصارات: سطح المكتب + قائمة البدء
echo 🗑️ إلغاء التثبيت: من قائمة البدء أو %INSTALL_DIR%\uninstall.bat
echo.

echo هل تريد تشغيل البرنامج الآن؟ (Y/N)
set /p "RUN_NOW="
if /i "%RUN_NOW%"=="Y" (
    echo 🚀 تشغيل البرنامج...
    start "" "%INSTALL_DIR%\CarDealershipManagement.exe"
    echo ✅ تم تشغيل البرنامج!
    echo 🔑 استخدم: amrali / braa للدخول كمطور
)

echo.
echo 👨‍💻 شكراً لاختيارك برنامج إدارة معرض السيارات!
echo المطور: Amr Ali Elawamy - 01285626623 - <EMAIL>
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

echo اضغط أي مفتاح للخروج...
pause >nul
