using System;
using Microsoft.Data.Sqlite;
using System.IO;

namespace CarDealershipManagement
{
public static class DatabaseSchemaFixer
{
    private static readonly string connectionString = "Data Source=CarDealership.db";

    public static void CheckAndFixSchema()
    {
        try
        {
            using(var connection = new SqliteConnection(connectionString))
            {
                connection.Open();

                // Check if InstallmentSellPrice column exists in Cars table
                if(!ColumnExists(connection, "Cars", "InstallmentSellPrice"))
                {
                    Console.WriteLine("Adding missing InstallmentSellPrice column to Cars table...");
                    AddInstallmentSellPriceColumn(connection);
                }

                // Check if DeletedDate column exists in Customers table
                if(!ColumnExists(connection, "Customers", "DeletedDate"))
                {
                    Console.WriteLine("Adding missing DeletedDate column to Customers table...");
                    AddDeletedDateColumn(connection);
                }

                // Check if IsActive column exists in Customers table
                if(!ColumnExists(connection, "Customers", "IsActive"))
                {
                    Console.WriteLine("Adding missing IsActive column to Customers table...");
                    AddIsActiveColumn(connection, "Customers");
                }

                // Check if IsActive column exists in Users table
                if(!ColumnExists(connection, "Users", "IsActive"))
                {
                    Console.WriteLine("Adding missing IsActive column to Users table...");
                    AddIsActiveColumn(connection, "Users");
                }

                // Check if IsActive column exists in AuthorizedInstallations table
                if(!ColumnExists(connection, "AuthorizedInstallations", "IsActive"))
                {
                    Console.WriteLine("Adding missing IsActive column to AuthorizedInstallations table...");
                    AddIsActiveColumn(connection, "AuthorizedInstallations");
                }

                // Check if IsDeleted column exists in Customers table
                if(!ColumnExists(connection, "Customers", "IsDeleted"))
                {
                    Console.WriteLine("Adding missing IsDeleted column to Customers table...");
                    AddIsDeletedColumn(connection, "Customers");
                }

                // Check if IsDeleted column exists in Users table
                if(!ColumnExists(connection, "Users", "IsDeleted"))
                {
                    Console.WriteLine("Adding missing IsDeleted column to Users table...");
                    AddIsDeletedColumn(connection, "Users");
                }

                // Check if IsDeleted column exists in Cars table
                if(!ColumnExists(connection, "Cars", "IsDeleted"))
                {
                    Console.WriteLine("Adding missing IsDeleted column to Cars table...");
                    AddIsDeletedColumn(connection, "Cars");
                }

                // Check if Status column exists in Cars table
                if(!ColumnExists(connection, "Cars", "Status"))
                {
                    Console.WriteLine("Adding missing Status column to Cars table...");
                    AddStatusColumn(connection);
                }

                // Add other schema checks here as needed

                Console.WriteLine("Database schema check completed successfully.");
            }
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error checking/fixing database schema: {ex.Message}\nStack trace: {ex.StackTrace}");
        }
    }

    private static bool ColumnExists(SqliteConnection connection, string tableName, string columnName)
    {
        try
        {
            using(var command = new SqliteCommand($"PRAGMA table_info({tableName})", connection))
            {
                using(var reader = command.ExecuteReader())
                {
                    while(reader.Read())
                    {
                        var columnNameValue = reader["name"]?.ToString();
                        if(columnNameValue != null && columnNameValue.Equals(columnName, StringComparison.OrdinalIgnoreCase))
                        {
                            return true;
                        }
                    }
                }
            }
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error checking column existence: {ex.Message}\nStack trace: {ex.StackTrace}");
        }
        return false;
    }

    private static void AddInstallmentSellPriceColumn(SqliteConnection connection)
    {
        try
        {
            // Add the column
            using(var command = new SqliteCommand("ALTER TABLE Cars ADD COLUMN InstallmentSellPrice decimal(18,2) DEFAULT 0.00", connection))
            {
                command.ExecuteNonQuery();
            }

            // Update existing cars to have reasonable installment prices (10% higher than suggested price)
            using(var command = new SqliteCommand("UPDATE Cars SET InstallmentSellPrice = SuggestedSellPrice * 1.1 WHERE InstallmentSellPrice = 0.00", connection))
            {
                command.ExecuteNonQuery();
            }

            Console.WriteLine("Successfully added InstallmentSellPrice column and updated existing data.");
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error adding InstallmentSellPrice column: {ex.Message}\nStack trace: {ex.StackTrace}");
        }
    }

    private static void AddDeletedDateColumn(SqliteConnection connection)
    {
        try
        {
            // Add the column
            using(var command = new SqliteCommand("ALTER TABLE Customers ADD COLUMN DeletedDate DATETIME", connection))
            {
                command.ExecuteNonQuery();
            }

            Console.WriteLine("Successfully added DeletedDate column to Customers table.");
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error adding DeletedDate column: {ex.Message}\nStack trace: {ex.StackTrace}");
        }
    }

    private static void AddIsDeletedColumn(SqliteConnection connection, string tableName)
    {
        try
        {
            // Add the column with default value 0 (false)
            using(var command = new SqliteCommand($"ALTER TABLE {tableName} ADD COLUMN IsDeleted INTEGER DEFAULT 0", connection))
            {
                command.ExecuteNonQuery();
            }

            // Update existing records to have IsDeleted = 0 (false) by default
            using(var command = new SqliteCommand($"UPDATE {tableName} SET IsDeleted = 0 WHERE IsDeleted IS NULL", connection))
            {
                command.ExecuteNonQuery();
            }

            Console.WriteLine($"Successfully added IsDeleted column to {tableName} table and updated existing data.");
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error adding IsDeleted column to {tableName} table: {ex.Message}\nStack trace: {ex.StackTrace}");
        }
    }

    private static void AddIsActiveColumn(SqliteConnection connection, string tableName)
    {
        try
        {
            // Add the column with default value 1 (true)
            using(var command = new SqliteCommand($"ALTER TABLE {tableName} ADD COLUMN IsActive INTEGER DEFAULT 1", connection))
            {
                command.ExecuteNonQuery();
            }

            // Update existing records to have IsActive = 1 (true) by default
            using(var command = new SqliteCommand($"UPDATE {tableName} SET IsActive = 1 WHERE IsActive IS NULL", connection))
            {
                command.ExecuteNonQuery();
            }

            Console.WriteLine($"Successfully added IsActive column to {tableName} table and updated existing data.");
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error adding IsActive column to {tableName} table: {ex.Message}\nStack trace: {ex.StackTrace}");
        }
    }

    private static void AddStatusColumn(SqliteConnection connection)
    {
        try
        {
            // Add the column with default value 'Available'
            using(var command = new SqliteCommand("ALTER TABLE Cars ADD COLUMN Status TEXT DEFAULT 'Available'", connection))
            {
                command.ExecuteNonQuery();
            }

            // Update existing records to have appropriate status based on IsSold
            using(var command = new SqliteCommand("UPDATE Cars SET Status = CASE WHEN IsSold = 1 THEN 'Sold' ELSE 'Available' END WHERE Status IS NULL OR Status = ''", connection))
            {
                command.ExecuteNonQuery();
            }

            Console.WriteLine("Successfully added Status column to Cars table and updated existing data.");
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error adding Status column to Cars table: {ex.Message}\nStack trace: {ex.StackTrace}");
        }
    }

    public static void BackupDatabase()
    {
        try
        {
            string backupPath = $"CarDealership_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db";
            File.Copy("CarDealership.db", backupPath);
            Console.WriteLine($"Database backed up to: {backupPath}");
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error backing up database: {ex.Message}");
        }
    }
}
}
