@echo off
chcp 65001 >nul
title اختبار وإصلاح النظام المالي

echo.
echo ========================================
echo    💰 اختبار وإصلاح النظام المالي
echo ========================================
echo.

echo المطور: Amr Ali Elawamy
echo الهاتف: 01285626623
echo البريد: <EMAIL>
echo.

echo 🔍 بدء فحص النظام المالي...
echo.

echo 📋 الفحوصات التي سيتم إجراؤها:
echo    1. فحص صحة حسابات الموردين
echo    2. فحص صحة حسابات العملاء  
echo    3. فحص صحة المبيعات والأقساط
echo    4. فحص تطابق الأرصدة العامة
echo    5. إصلاح المشاكل المكتشفة تلقائياً
echo.

echo ⚠️ تحذير: سيتم إنشاء نسخة احتياطية قبل الإصلاح
echo.

REM إنشاء نسخة احتياطية
echo 💾 إنشاء نسخة احتياطية...
set "BACKUP_NAME=CarDealership_BeforeFinancialRepair_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.db"
set "BACKUP_NAME=%BACKUP_NAME: =0%"

if exist "CarDealership.db" (
    copy "CarDealership.db" "%BACKUP_NAME%" >nul
    echo ✅ تم إنشاء نسخة احتياطية: %BACKUP_NAME%
) else (
    echo ❌ ملف قاعدة البيانات غير موجود
    echo.
    echo 🔄 تشغيل البرنامج لإنشاء قاعدة البيانات...
    start /wait /min "CarDealership" "bin\Debug\net8.0-windows\CarDealershipManagement.exe"
)

echo.
echo 🚀 تشغيل البرنامج لإجراء الفحص والإصلاح...

REM تشغيل البرنامج مع التركيز على قسم الحسابات
start "" "bin\Debug\net8.0-windows\CarDealershipManagement.exe"

echo.
echo ✅ تم تشغيل البرنامج
echo.

echo 📝 تعليمات الاستخدام:
echo.
echo 1. سجل الدخول باستخدام:
echo    👤 اسم المستخدم: amrali
echo    🔑 كلمة المرور: braa
echo.
echo 2. انتقل إلى قسم "إدارة الحسابات"
echo.
echo 3. راجع البيانات التالية:
echo    • حسابات الموردين
echo    • حسابات العملاء
echo    • الملخص المالي
echo.
echo 4. تحقق من وجود أي تحذيرات أو أخطاء
echo.
echo 5. استخدم أزرار الإصلاح إذا ظهرت مشاكل
echo.

echo 🔧 إذا واجهت مشاكل:
echo    • تحقق من ملف النسخة الاحتياطية
echo    • راجع سجل الأخطاء في مجلد Logs
echo    • اتصل بالمطور للمساعدة
echo.

echo 📊 المؤشرات المالية المهمة:
echo    • إجمالي المبيعات
echo    • إجمالي المشتريات  
echo    • مستحقات العملاء
echo    • مستحقات الموردين
echo    • الربح الإجمالي
echo.

echo 💡 نصائح للحفاظ على سلامة النظام المالي:
echo    1. قم بعمل نسخة احتياطية يومية
echo    2. راجع الحسابات أسبوعياً
echo    3. تحقق من تطابق الأرصدة شهرياً
echo    4. لا تحذف البيانات المالية يدوياً
echo    5. استخدم النظام لجميع العمليات المالية
echo.

echo 🎯 الملفات المهمة:
echo    • CarDealership.db - قاعدة البيانات الرئيسية
echo    • %BACKUP_NAME% - النسخة الاحتياطية
echo    • Logs\error_log.txt - سجل الأخطاء
echo.

echo 👨‍💻 للمساعدة والدعم:
echo    المطور: Amr Ali Elawamy
echo    الهاتف: 01285626623
echo    البريد: <EMAIL>
echo.

echo 📅 تاريخ الفحص: %date% %time%
echo.

pause
