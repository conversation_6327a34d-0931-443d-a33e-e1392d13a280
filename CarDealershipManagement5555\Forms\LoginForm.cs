using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class LoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnExit;
        private CheckBox chkShowPassword;
        private Label lblStatus;

        public User? AuthenticatedUser
        {
            get;
            private set;
        }

        public LoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - نظام إدارة معرض السيارات";
            this.Size = new Size(450, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // عنوان النظام
            var titleLabel = new Label
            {
                Text = "🚗 نظام إدارة معرض السيارات",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 102, 204),
                Location = new Point(30, 20),
                Size = new Size(390, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var subtitleLabel = new Label
            {
                Text = "Car Dealership Management System",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(108, 117, 125),
                Location = new Point(30, 60),
                Size = new Size(390, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // حقل اسم المستخدم
            var lblUsername = new Label
            {
                Text = "👤 اسم المستخدم:",
                Location = new Point(50, 120),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64)
            };

            txtUsername = new TextBox
            {
                Location = new Point(50, 150),
                Size = new Size(350, 30),
                Font = new Font("Segoe UI", 11F),
                TabIndex = 0,
                BorderStyle = BorderStyle.FixedSingle
            };

            // حقل كلمة المرور
            var lblPassword = new Label
            {
                Text = "🔒 كلمة المرور:",
                Location = new Point(50, 190),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64)
            };

            txtPassword = new TextBox
            {
                Location = new Point(50, 220),
                Size = new Size(350, 30),
                Font = new Font("Segoe UI", 11F),
                UseSystemPasswordChar = true,
                TabIndex = 1,
                BorderStyle = BorderStyle.FixedSingle
            };

            // خانة إظهار كلمة المرور
            chkShowPassword = new CheckBox
            {
                Text = "إظهار كلمة المرور",
                Location = new Point(50, 260),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 9F),
                TabIndex = 2,
                ForeColor = Color.FromArgb(108, 117, 125)
            };
            chkShowPassword.CheckedChanged += ChkShowPassword_CheckedChanged;

            // رسالة الحالة
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(50, 290),
                Size = new Size(350, 40),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // زر تسجيل الدخول
            btnLogin = new Button
            {
                Text = "🔑 تسجيل الدخول",
                Location = new Point(50, 340),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                TabIndex = 3,
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;

            // زر الخروج
            btnExit = new Button
            {
                Text = "❌ خروج",
                Location = new Point(220, 340),
                Size = new Size(100, 40),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                TabIndex = 4,
                Cursor = Cursors.Hand
            };
            btnExit.FlatAppearance.BorderSize = 0;
            btnExit.Click += (s, e) => Application.Exit();



            // إضافة جميع العناصر إلى النموذج
            this.Controls.AddRange(new Control[]
            {
                titleLabel, subtitleLabel, lblUsername, txtUsername,
                lblPassword, txtPassword, chkShowPassword, lblStatus,
                btnLogin, btnExit
            });

            // إعدادات النموذج
            this.AcceptButton = btnLogin;
            this.CancelButton = btnExit;

            // معالجة مفتاح Enter في حقل كلمة المرور
            txtPassword.KeyPress += (s, e) =>
            {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    BtnLogin_Click(s, e);
                }
            };
        }

        private void ChkShowPassword_CheckedChanged(object? sender, EventArgs e)
        {
            txtPassword.UseSystemPasswordChar = !chkShowPassword.Checked;
        }

        private async void BtnLogin_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                lblStatus.Text = "يرجى إدخال اسم المستخدم وكلمة المرور";
                lblStatus.ForeColor = Color.Red;
                return;
            }

            lblStatus.Text = "جاري التحقق...";
            lblStatus.ForeColor = Color.Blue;
            btnLogin.Enabled = false;

            try
            {
                using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                        .UseSqlite("Data Source=CarDealership.db").Options);

                // تأكد من وجود قاعدة البيانات والبيانات الأولية
                await EnsureDatabaseAndDefaultUser(context);

                var user = await context.Users
                           .Include(u => u.Permissions)
                           .FirstOrDefaultAsync(u => u.Username == txtUsername.Text.Trim() && u.IsActive);

                if (user != null && BCrypt.Net.BCrypt.Verify(txtPassword.Text, user.PasswordHash))
                {
                    // Update last login time
                    user.LastLoginDate = DateTime.Now;
                    await context.SaveChangesAsync();

                    AuthenticatedUser = user;
                    lblStatus.Text = "تم تسجيل الدخول بنجاح!";
                    lblStatus.ForeColor = Color.Green;

                    await Task.Delay(500); // إظهار رسالة النجاح لفترة قصيرة

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    lblStatus.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
                    lblStatus.ForeColor = Color.Red;
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"خطأ في الاتصال بقاعدة البيانات: {ex.Message}";
                lblStatus.ForeColor = Color.Red;
                ErrorHandlingService.HandleException(ex, false, "خطأ في تسجيل الدخول");
            }

            btnLogin.Enabled = true;
        }

        private async Task EnsureDatabaseAndDefaultUser(CarDealershipContext context)
        {
            try
            {
                // تأكد من إنشاء قاعدة البيانات
                await context.Database.EnsureCreatedAsync();

                // تحقق من وجود المستخدم الافتراضي
                var userCount = await context.Users.CountAsync();
                if (userCount == 0)
                {
                    // إنشاء المستخدم الافتراضي
                    var defaultUser = new Models.User
                    {
                        Username = "amrali",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("braa"),
                        FullName = "عمرو علي",
                        Role = Models.UserRole.Developer,
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    };

                    context.Users.Add(defaultUser);
                    await context.SaveChangesAsync();

                    // إنشاء صلاحيات المستخدم الافتراضي
                    var defaultPermissions = new Models.UserPermissions
                    {
                        UserId = defaultUser.UserId,
                        CanAddCar = true,
                        CanEditCar = true,
                        CanDeleteCar = true,
                        CanViewInventory = true,
                        CanSell = true,
                        CanEditSale = true,
                        CanDeleteSale = true,
                        CanViewSales = true,
                        CanAddCustomer = true,
                        CanEditCustomer = true,
                        CanDeleteCustomer = true,
                        CanViewCustomers = true,
                        CanViewCustomerReport = true,
                        CanManageSuppliers = true,
                        CanViewAccounts = true,
                        CanViewGeneralReports = true,
                        CanManageUsers = true,
                        CanManageSettings = true,
                        CanPrintReports = true,
                        CanPrintStatements = true,
                        CreatedDate = DateTime.Now
                    };

                    context.UserPermissions.Add(defaultPermissions);
                    await context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, false, "خطأ في إنشاء البيانات الافتراضية");
                throw;
            }
        }


    }
}


