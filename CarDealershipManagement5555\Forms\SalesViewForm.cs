using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
public partial class SalesViewForm : Form
{
    private DataGridView dgvSales;
    private TextBox txtSearch;
    private Button btnSearch;
    private Button btnPayInstallment;
    private Button btnViewDetails;
    private Button btnRefresh;
    private Button btnPrintSales;
    private ComboBox cmbPaymentStatus;
    private DateTimePicker dtpFromDate;
    private DateTimePicker dtpToDate;
    private CheckBox chkDateFilter;

    public SalesViewForm()
    {
        InitializeComponent();
        LoadData();
    }

    private void InitializeComponent()
    {
        this.Text = "عرض المبيعات - Sales View";
        this.Size = new Size(1200, 700);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(248, 249, 250);

        // Main container panel
        var mainPanel = new Panel
        {
            Dock = DockStyle.Fill,
            BackColor = Color.FromArgb(248, 249, 250),
            Padding = new Padding(10)
        };

        // Header panel
        var headerPanel = new Panel
        {
            Height = 60,
            Dock = DockStyle.Top,
            BackColor = Color.FromArgb(0, 102, 204),
            Padding = new Padding(15)
        };

        var titleLabel = new Label
        {
            Text = "عرض المبيعات - Sales View",
            Font = new Font("Segoe UI", 16, FontStyle.Bold),
            ForeColor = Color.White,
            AutoSize = true,
            Location = new Point(15, 15)
        };
        headerPanel.Controls.Add(titleLabel);

        // Search and filter panel
        var searchPanel = new Panel
        {
            Height = 100,
            Dock = DockStyle.Top,
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle,
            Padding = new Padding(10)
        };

        // Search controls
        var lblSearch = new Label
        {
            Text = "البحث:",
            Location = new Point(20, 20),
            Size = new Size(60, 23),
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };
        txtSearch = new TextBox
        {
            Location = new Point(90, 18),
            Size = new Size(200, 23),
            Font = new Font("Segoe UI", 9)
        };
        btnSearch = CreateStyledButton("بحث", new Point(300, 17), new Size(75, 25));
        btnSearch.Click += BtnSearch_Click;

        // Payment status filter
        var lblPaymentStatus = new Label
        {
            Text = "حالة الدفع:",
            Location = new Point(400, 20),
            Size = new Size(80, 23),
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };
        cmbPaymentStatus = new ComboBox
        {
            Location = new Point(490, 18),
            Size = new Size(120, 23),
            DropDownStyle = ComboBoxStyle.DropDownList,
            Font = new Font("Segoe UI", 9)
        };
        cmbPaymentStatus.Items.AddRange(new[] { "الكل", "مستحقة", "مدفوعة جزئياً", "مدفوعة بالكامل" });
        cmbPaymentStatus.SelectedIndex = 0;
        cmbPaymentStatus.SelectedIndexChanged += (s, e) => LoadData();

        // Date filter controls
        chkDateFilter = new CheckBox
        {
            Text = "فلترة بالتاريخ",
            Location = new Point(630, 20),
            Size = new Size(100, 23),
            Font = new Font("Segoe UI", 9)
        };
        chkDateFilter.CheckedChanged += (s, e) =>
        {
            dtpFromDate.Enabled = dtpToDate.Enabled = chkDateFilter.Checked;
            LoadData();
        };

        var lblFromDate = new Label
        {
            Text = "من:",
            Location = new Point(750, 20),
            Size = new Size(30, 23),
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };
        dtpFromDate = new DateTimePicker
        {
            Location = new Point(790, 18),
            Format = DateTimePickerFormat.Short,
            Enabled = false,
            Font = new Font("Segoe UI", 9)
        };
        dtpFromDate.ValueChanged += (s, e) =>
        {
            if(chkDateFilter.Checked)
            {
                LoadData();
            }
        };

        var lblToDate = new Label
        {
            Text = "إلى:",
            Location = new Point(920, 20),
            Size = new Size(30, 23),
            Font = new Font("Segoe UI", 9, FontStyle.Bold)
        };
        dtpToDate = new DateTimePicker
        {
            Location = new Point(960, 18),
            Format = DateTimePickerFormat.Short,
            Enabled = false,
            Font = new Font("Segoe UI", 9)
        };
        dtpToDate.ValueChanged += (s, e) =>
        {
            if(chkDateFilter.Checked)
            {
                LoadData();
            }
        };

        searchPanel.Controls.AddRange(new Control[]
        {
            lblSearch, txtSearch, btnSearch, lblPaymentStatus, cmbPaymentStatus,
            chkDateFilter, lblFromDate, dtpFromDate, lblToDate, dtpToDate
        });

        // Buttons panel
        var buttonsPanel = new Panel
        {
            Height = 60,
            Dock = DockStyle.Top,
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle,
            Padding = new Padding(10)
        };

        // Action buttons
        btnPayInstallment = CreateStyledButton("دفع قسط", new Point(20, 15), new Size(100, 30));
        btnViewDetails = CreateStyledButton("عرض التفاصيل", new Point(130, 15), new Size(100, 30));
        btnRefresh = CreateStyledButton("تحديث", new Point(240, 15), new Size(100, 30));
        btnPrintSales = CreateStyledButton("🖨️ طباعة", new Point(350, 15), new Size(100, 30));
        btnPrintSales.BackColor = Color.FromArgb(75, 0, 130);

        btnPayInstallment.Click += BtnPayInstallment_Click;
        btnViewDetails.Click += BtnViewDetails_Click;
        btnRefresh.Click += (s, e) => LoadData();
        btnPrintSales.Click += BtnPrintSales_Click;

        buttonsPanel.Controls.AddRange(new Control[]
        {
            btnPayInstallment, btnViewDetails, btnRefresh, btnPrintSales
        });

        // DataGridView
        dgvSales = new DataGridView
        {
            Dock = DockStyle.Fill,
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            MultiSelect = false,
            ReadOnly = true,
            AllowUserToAddRows = false,
            BackgroundColor = Color.White,
            BorderStyle = BorderStyle.Fixed3D,
            Font = new Font("Segoe UI", 9),
            Margin = new Padding(10)
        };

        // Style the DataGridView
        dgvSales.ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
        {
            BackColor = Color.FromArgb(0, 102, 204),
            ForeColor = Color.White,
            Font = new Font("Segoe UI", 9, FontStyle.Bold),
            Alignment = DataGridViewContentAlignment.MiddleCenter
        };

        dgvSales.DefaultCellStyle = new DataGridViewCellStyle
        {
            BackColor = Color.White,
            ForeColor = Color.Black,
            SelectionBackColor = Color.FromArgb(173, 216, 230),
            SelectionForeColor = Color.Black,
            Font = new Font("Segoe UI", 9)
        };

        dgvSales.AlternatingRowsDefaultCellStyle = new DataGridViewCellStyle
        {
            BackColor = Color.FromArgb(248, 249, 250)
        };

        dgvSales.ColumnHeadersHeight = 35;
        dgvSales.RowTemplate.Height = 30;

        // Assemble the layout
        mainPanel.Controls.Add(dgvSales);
        mainPanel.Controls.Add(buttonsPanel);
        mainPanel.Controls.Add(searchPanel);
        mainPanel.Controls.Add(headerPanel);

        this.Controls.Add(mainPanel);
    }

    private Button CreateStyledButton(string text, Point location, Size size)
    {
        return new Button
        {
            Text = text,
            Location = location,
            Size = size,
            BackColor = Color.FromArgb(0, 102, 204),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9, FontStyle.Bold),
            Cursor = Cursors.Hand
        };
    }

    private async void LoadData()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var query = context.Sales
                        .Include(s => s.Car)
                        .Include(s => s.Customer)
                        .AsQueryable();

            // Apply search filter
            if(!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                var searchTerm = txtSearch.Text.ToLower();
                query = query.Where(s => s.Car.ChassisNumber.ToLower().Contains(searchTerm) ||
                                    s.Car.Brand.ToLower().Contains(searchTerm) ||
                                    s.Car.Model.ToLower().Contains(searchTerm) ||
                                    s.Customer.FullName.ToLower().Contains(searchTerm));
            }

            // Apply payment status filter
            if(cmbPaymentStatus.SelectedIndex > 0)
            {
                var status = (PaymentStatus)cmbPaymentStatus.SelectedIndex;
                query = query.Where(s => s.PaymentStatus == status);
            }

            // Apply date filter
            if(chkDateFilter.Checked)
            {
                query = query.Where(s => s.SaleDate >= dtpFromDate.Value.Date &&
                                    s.SaleDate <= dtpToDate.Value.Date.AddDays(1));
            }

            var sales = await query.ToListAsync();

            dgvSales.DataSource = sales.Select(s => new
            {
                رقم_البيع = s.SaleId,
                رقم_الشاسيه = s.CarChassisNumber,
                السيارة = $"{s.Car.Brand} {s.Car.Model} ({s.Car.Year})",
                العميل = s.Customer.FullName,
                سعر_البيع = s.ActualSellPrice.ToString("C"),
                طريقة_الدفع = s.PaymentMethod == PaymentMethod.Cash ? "نقدي" : "تقسيط",
                المدفوع = s.TotalPaid.ToString("C"),
                المتبقي = s.RemainingAmount.ToString("C"),
                حالة_الدفع = s.PaymentStatus switch
            {
                PaymentStatus.Pending => "مستحقة",
                PaymentStatus.PartiallyPaid => "مدفوعة جزئياً",
                PaymentStatus.FullyPaid => "مدفوعة بالكامل",
                _ => "غير محدد"
            },
            تاريخ_البيع = s.SaleDate.ToString("yyyy-MM-dd")
            }).ToList();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void BtnSearch_Click(object? sender, EventArgs e)
    {
        LoadData();
    }

    private void BtnPayInstallment_Click(object? sender, EventArgs e)
    {
        if(dgvSales.SelectedRows.Count > 0)
        {
            var saleId = Convert.ToInt32(dgvSales.SelectedRows[0].Cells["رقم_البيع"].Value);
            var paymentForm = new InstallmentPaymentForm(saleId);
            if(paymentForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار عملية بيع", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnViewDetails_Click(object? sender, EventArgs e)
    {
        if(dgvSales.SelectedRows.Count > 0)
        {
            var saleId = Convert.ToInt32(dgvSales.SelectedRows[0].Cells["رقم_البيع"].Value);
            var detailsForm = new SaleDetailsForm(saleId);
            detailsForm.ShowDialog();
        }
        else
        {
            MessageBox.Show("يرجى اختيار عملية بيع لعرض التفاصيل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnPrintSales_Click(object? sender, EventArgs e)
    {
        if(dgvSales.Rows.Count == 0)
        {
            MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        using var printForm = new PrintReportForm(dgvSales);
        printForm.Text = "طباعة تقرير المبيعات";
        printForm.ShowDialog();
    }
}
}
