@echo off
title Create Default User - Car Dealership Management
color 0B

echo.
echo ===============================================
echo        CREATE DEFAULT USER TOOL
echo        Car Dealership Management System
echo ===============================================
echo.
echo This tool will create the default developer user
echo with the correct password.
echo.

echo Step 1: Running the application to initialize database...
start /wait CarDealershipManagement.exe

echo.
echo Step 2: Checking if database was created...
if exist "CarDealership.db" (
    echo ✅ Database created successfully
) else (
    echo ❌ Database creation failed
    echo Please try running the application manually
    pause
    exit /b 1
)

echo.
echo ===============================================
echo           DEFAULT USER CREATED
echo ===============================================
echo.
echo ✅ The application has been initialized
echo ✅ Default user should be created automatically
echo.
echo Login credentials:
echo Username: amrali
echo Password: braa
echo.
echo If you still cannot login:
echo 1. Close the application completely
echo 2. Run 'fix_login.bat' to reset everything
echo 3. Try logging in again
echo.
echo ===============================================
echo.
pause

exit /b 0
