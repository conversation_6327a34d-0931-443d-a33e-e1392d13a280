using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Forms
{
    public partial class SupplierAccountForm : Form
    {
        private int supplierId;
        private DataGridView dgvPayments;
        private Button btnPrint;
        private Label lblStatus = null!;
        private Label lblSupplierName = null!;
        private Label lblTotalOwed = null!;
        private Label lblTotalPaid = null!;
        private Label lblBalance = null!;
        private Label lblAccountStatus = null!;

        public SupplierAccountForm(int supplierId)
        {
            this.supplierId = supplierId;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "حساب المورد - Supplier Account";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(20, 530),
                Size = new Size(750, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Title
            var lblTitle = new Label
            {
                Text = "كشف حساب المورد",
                Location = new Point(20, 20),
                Size = new Size(250, 30),
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64)
            };

            // Supplier Info Labels
            int yPos = 60;
            lblSupplierName = AddInfoLabel("المورد:", yPos);
            lblTotalOwed = AddInfoLabel("إجمالي المستحق:", yPos += 30);
            lblTotalPaid = AddInfoLabel("المدفوع:", yPos += 30);
            lblBalance = AddInfoLabel("المتبقي:", yPos += 30);
            lblAccountStatus = AddInfoLabel("حالة الحساب:", yPos += 30);

            // Payments DataGridView
            dgvPayments = new DataGridView
            {
                Location = new Point(20, yPos + 40),
                Size = new Size(750, 250),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F),
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(0, 102, 204),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Padding = new Padding(5)
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.Black,
                    SelectionBackColor = Color.FromArgb(173, 216, 230),
                    SelectionForeColor = Color.Black,
                    Font = new Font("Segoe UI", 9F),
                    Padding = new Padding(5)
                }
            };

            // Buttons
            btnPrint = new Button
            {
                Text = "طباعة",
                Location = new Point(560, 550),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(232, 62, 140),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.Click += BtnPrint_Click;

            var btnClose = new Button
            {
                Text = "إغلاق",
                Location = new Point(670, 550),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.Click += (s, e) => this.Close();

            this.Controls.AddRange(new Control[]
            {
                lblTitle, lblSupplierName, lblTotalOwed, lblTotalPaid, lblBalance, lblAccountStatus,
                dgvPayments, btnPrint, btnClose, lblStatus
            });
        }

        private Label AddInfoLabel(string prefix, int yPos)
        {
            var label = new Label
            {
                Text = prefix,
                Location = new Point(20, yPos),
                Size = new Size(400, 23),
                Font = new Font("Segoe UI", 10, FontStyle.Regular),
                ForeColor = Color.FromArgb(52, 58, 64)
            };
            this.Controls.Add(label);
            return label;
        }

        private async void LoadData()
        {
            lblStatus.Text = ""; // Clear previous status messages
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                var supplier = await context.Suppliers
                               .Include(s => s.Payments)
                               .FirstOrDefaultAsync(s => s.SupplierId == supplierId);

                if (supplier != null)
                {
                    lblSupplierName.Text = $"المورد: {supplier.SupplierName}";
                    lblTotalOwed.Text = $"إجمالي المستحق: {supplier.TotalOwed:C}";
                    lblTotalPaid.Text = $"المدفوع: {supplier.TotalPaid:C}";
                    lblBalance.Text = $"المتبقي: {supplier.Balance:C}";
                    lblAccountStatus.Text = $"حالة الحساب: {GetAccountStatusText(supplier.AccountStatus)}";

                    // Add payments grid
                    if (supplier.Payments.Any())
                    {
                        dgvPayments.DataSource = supplier.Payments.OrderByDescending(p => p.PaymentDate).Select(p => new
                        {
                            المبلغ = p.Amount.ToString("C"),
                            تاريخ_الدفع = p.PaymentDate.ToString("yyyy-MM-dd"),
                            ملاحظات = p.Notes ?? ""
                        }).ToList();
                    }
                    else
                    {
                        lblStatus.Text = "لا توجد دفعات مسجلة لهذا المورد.";
                        dgvPayments.DataSource = null;
                    }
                }
                else
                {
                    lblStatus.Text = "لم يتم العثور على بيانات المورد.";
                }
            }, "تحميل بيانات حساب المورد");
        }

        private string GetAccountStatusText(SupplierAccountStatus status)
        {
            return status switch
            {
                SupplierAccountStatus.Creditor => "دائن",
                SupplierAccountStatus.Debtor => "مدين",
                SupplierAccountStatus.Neutral => "متوازن",
                _ => "غير محدد"
            };
        }

        private void BtnPrint_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages
            if (dgvPayments != null && dgvPayments.Rows.Count > 0)
            {
                using var printForm = new PrintReportForm(dgvPayments);
                printForm.Text = "طباعة كشف حساب المورد";
                printForm.ShowDialog();
            }
            else
            {
                lblStatus.Text = "لا توجد بيانات دفعات للطباعة.";
            }
        }
    }
}


