using System;
using System.Linq;
using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement
{
public class DbUserInspector
{
    public static void InspectAllUsers()
    {
        try
        {
            var options = new DbContextOptionsBuilder<CarDealershipContext>()
            .UseSqlite("Data Source=CarDealership.db")
            .Options;
            using var context = new CarDealershipContext(options);

            Console.WriteLine("=== Database User Inspection ===");
            Console.WriteLine();

            var users = context.Users.Include(u => u.Permissions).ToList();

            if(!users.Any())
            {
                Console.WriteLine("No users found in the database!");
                return;
            }

            Console.WriteLine($"Found {users.Count} users:");
            Console.WriteLine();

            foreach(var user in users)
            {
                Console.WriteLine($"User ID: {user.UserId}");
                Console.WriteLine($"Username: '{user.Username}'");
                Console.WriteLine($"Full Name: '{user.FullName}'");
                Console.WriteLine($"Password Hash: '{user.PasswordHash}'");
                Console.WriteLine($"Role: {user.Role}");
                Console.WriteLine($"Is Active: {user.IsActive}");
                Console.WriteLine($"Created: {user.CreatedDate}");

                if(user.Permissions != null)
                {
                    Console.WriteLine("Permissions:");
                    Console.WriteLine($"  - Can Manage Users: {user.Permissions.CanManageUsers}");
                    Console.WriteLine($"  - Can Add Car: {user.Permissions.CanAddCar}");
                    Console.WriteLine($"  - Can Sell: {user.Permissions.CanSell}");
                    Console.WriteLine($"  - Can Add Customer: {user.Permissions.CanAddCustomer}");
                    Console.WriteLine($"  - Can Manage Suppliers: {user.Permissions.CanManageSuppliers}");
                    Console.WriteLine($"  - Can View General Reports: {user.Permissions.CanViewGeneralReports}");
                    Console.WriteLine($"  - Can Activate Installation: {user.Permissions.CanActivateInstallation}");
                }
                else
                {
                    Console.WriteLine("Permissions: NULL");
                }

                Console.WriteLine(new string('-', 50));
            }

            // Test password verification for common passwords
            Console.WriteLine("\n=== Testing Common Passwords ===");
            var testPasswords = new[] { "admin", "password", "123456", "admin123", "developer" };

            foreach(var user in users)
            {
                Console.WriteLine($"\nTesting passwords for user '{user.Username}':");
                foreach(var testPassword in testPasswords)
                {
                    bool isValid = BCrypt.Net.BCrypt.Verify(testPassword, user.PasswordHash);
                    Console.WriteLine($"  Password '{testPassword}': {(isValid ? "VALID" : "Invalid")}");
                }
            }
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error inspecting users: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    public static void CreateTestUser(string username, string password, string email, string role = "Admin")
    {
        try
        {
            var options = new DbContextOptionsBuilder<CarDealershipContext>()
            .UseSqlite("Data Source=CarDealership.db")
            .Options;
            using var context = new CarDealershipContext(options);

            // Check if user already exists
            if(context.Users.Any(u => u.Username == username))
            {
                Console.WriteLine($"User '{username}' already exists!");
                return;
            }

            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(password);

            var user = new User
            {
                Username = username,
                FullName = email,
                PasswordHash = hashedPassword,
                Role = UserRole.Developer,
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            var permissions = new UserPermissions
            {
                CanManageUsers = true,
                CanAddCar = true,
                CanSell = true,
                CanAddCustomer = true,
                CanManageSuppliers = true,
                CanViewGeneralReports = true,
                CanActivateInstallation = true
            };

            user.Permissions = permissions;

            context.Users.Add(user);
            context.SaveChanges();

            Console.WriteLine($"Test user '{username}' created successfully!");
            Console.WriteLine($"Password: {password}");
            Console.WriteLine($"Password Hash: {hashedPassword}");
        }
        catch(Exception ex)
        {
            Console.WriteLine($"Error creating test user: {ex.Message}");
        }
    }
}
}
