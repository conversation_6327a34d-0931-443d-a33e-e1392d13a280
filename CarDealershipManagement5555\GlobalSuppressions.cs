using System.Diagnostics.CodeAnalysis;

// Global suppressions for common warnings that are safe to ignore in Windows Forms applications

// CS8618 - Non-nullable field warnings for Windows Forms designer fields
[assembly: SuppressMessage("Compiler", "CS8618:Non-nullable field must contain a non-null value when exiting constructor", Justification = "Windows Forms designer fields are initialized by InitializeComponent()")]

// CS8600 - Converting null literal warnings for selected cases
[assembly: SuppressMessage("Compiler", "CS8600:Converting null literal or possible null value to non-nullable type", Justification = "Controlled null assignments in form contexts")]

// CS8602 - Possible null reference warnings for designer components
[assembly: SuppressMessage("Compiler", "CS8602:Dereference of a possibly null reference", Justification = "Designer components are guaranteed to be non-null after InitializeComponent()")]

// CS1998 - Async method without await
[assembly: SuppressMessage("Compiler", "CS1998:This async method lacks 'await' operators and will run synchronously", Justification = "Async methods prepared for future async operations")]

// CS0169 - Unused field warnings
[assembly: SuppressMessage("Compiler", "CS0169:The field is never used", Justification = "Fields may be used by designer or for future functionality")]

// CS0414 - Unused assigned field warnings
[assembly: SuppressMessage("Compiler", "CS0414:The field is assigned but its value is never used", Justification = "Fields may be assigned by designer or for future functionality")]
