@echo off
chcp 65001 >nul
title استبدال الأيقونات - برنامج إدارة معرض السيارات

echo.
echo ========================================
echo   🔄 استبدال الأيقونات
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 🎯 استبدال app11_icon بـ app-icon في جميع المجلدات...
echo.

REM نسخ الأيقونات الجديدة إلى جميع المجلدات
echo 📁 نسخ الأيقونات الجديدة...

if exist "app-icon.ico" (
    echo نسخ app-icon.ico...
    
    if exist "bin\Debug\net8.0-windows" (
        copy "app-icon.ico" "bin\Debug\net8.0-windows\" >nul 2>&1
        copy "app-icon.png" "bin\Debug\net8.0-windows\" >nul 2>&1
    )
    
    if exist "bin\Release\net8.0-windows" (
        copy "app-icon.ico" "bin\Release\net8.0-windows\" >nul 2>&1
        copy "app-icon.png" "bin\Release\net8.0-windows\" >nul 2>&1
    )
    
    if exist "CarDealership_Standalone" (
        copy "app-icon.ico" "CarDealership_Standalone\" >nul 2>&1
        copy "app-icon.png" "CarDealership_Standalone\" >nul 2>&1
    )
    
    if exist "CarDealership_Debug_Copy" (
        copy "app-icon.ico" "CarDealership_Debug_Copy\" >nul 2>&1
        copy "app-icon.png" "CarDealership_Debug_Copy\" >nul 2>&1
    )
    
    echo ✅ تم نسخ الأيقونات الجديدة
) else (
    echo ❌ الأيقونة الجديدة غير موجودة
)

echo.
echo 🗑️ حذف الأيقونات القديمة...

REM حذف app11_icon من جميع المجلدات
if exist "app11_icon.png" (
    del "app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من المجلد الرئيسي
)

if exist "bin\Debug\net8.0-windows\app11_icon.png" (
    del "bin\Debug\net8.0-windows\app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من Debug
)

if exist "bin\Release\net8.0-windows\app11_icon.png" (
    del "bin\Release\net8.0-windows\app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من Release
)

if exist "CarDealership_Standalone\app11_icon.png" (
    del "CarDealership_Standalone\app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من المجلد المستقل
)

if exist "CarDealership_Debug_Copy\app11_icon.png" (
    del "CarDealership_Debug_Copy\app11_icon.png" >nul 2>&1
    echo ✅ تم حذف app11_icon.png من النسخة Debug
)

echo.
echo 🔧 إعادة بناء البرنامج...

dotnet clean >nul 2>&1
dotnet build --configuration Release >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء البرنامج بنجاح
    
    REM تحديث الملفات التنفيذية
    if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
        if exist "CarDealership_Standalone" (
            copy "bin\Release\net8.0-windows\CarDealershipManagement.exe" "CarDealership_Standalone\" >nul 2>&1
            echo ✅ تم تحديث المجلد المستقل
        )
        
        if exist "CarDealership_Debug_Copy" (
            copy "bin\Release\net8.0-windows\CarDealershipManagement.exe" "CarDealership_Debug_Copy\" >nul 2>&1
            echo ✅ تم تحديث النسخة Debug
        )
    )
) else (
    echo ⚠️ تحذير: قد تكون هناك أخطاء في البناء
)

echo.
echo 📊 ملخص العملية:
echo    ✅ تم نسخ الأيقونات الجديدة
echo    ✅ تم حذف الأيقونات القديمة
echo    ✅ تم إعادة بناء البرنامج
echo    ✅ تم تحديث جميع المجلدات

echo.
echo 🎉 تم استبدال الأيقونات بنجاح!
echo.

echo 👨‍💻 تم التطوير بواسطة: Amr Ali Elawamy
echo 📞 للتواصل: 01285626623
echo 📧 البريد: <EMAIL>

echo.
echo هل تريد تشغيل البرنامج؟ (Y/N)
set /p "RUN_PROGRAM="
if /i "%RUN_PROGRAM%"=="Y" (
    if exist "CarDealership_Standalone\CarDealershipManagement.exe" (
        echo 🚀 تشغيل البرنامج...
        start "" "CarDealership_Standalone\CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج!
    ) else if exist "bin\Release\net8.0-windows\CarDealershipManagement.exe" (
        echo 🚀 تشغيل البرنامج...
        start "" "bin\Release\net8.0-windows\CarDealershipManagement.exe"
        echo ✅ تم تشغيل البرنامج!
    )
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
