using System.Drawing.Printing;
using System.Text;
using CarDealershipManagement.Models;
using CarDealershipManagement.Data;
using Microsoft.EntityFrameworkCore;

namespace CarDealershipManagement.Services
{
public static class PrintingService
{
    private static List<string> printLines = new List<string>();
    private static int currentLineIndex = 0;
    private static Font printFont = new Font("Arial", 10);
    private static Font printFontBold = new Font("Arial", 10, FontStyle.Bold);
    private static Font printFontTitle = new Font("Arial", 14, FontStyle.Bold);

    public static void PrintCustomerStatement(int customerId)
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var customer = context.Customers
                           .Include(c => c.Sales)
                           .ThenInclude(s => s.Car)
                           .Include(c => c.Sales)
                           .ThenInclude(s => s.InstallmentPayments)
                           .FirstOrDefault(c => c.CustomerId == customerId);

            if(customer == null)
            {
                MessageBox.Show("العميل غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            GenerateCustomerStatementContent(customer);
            PrintDocument();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة كشف حساب العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public static void PrintSupplierStatement(int supplierId)
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var supplier = context.Suppliers
                           .Include(s => s.Payments)
                           .FirstOrDefault(s => s.SupplierId == supplierId);

            if(supplier == null)
            {
                MessageBox.Show("المورد غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            GenerateSupplierStatementContent(supplier);
            PrintDocument();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة كشف حساب المورد: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public static void PrintSalesReport(DateTime fromDate, DateTime toDate)
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var sales = context.Sales
                        .Include(s => s.Car)
                        .Include(s => s.Customer)
                        .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate)
                        .OrderBy(s => s.SaleDate)
                        .ToList();

            GenerateSalesReportContent(sales, fromDate, toDate);
            PrintDocument();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة تقرير المبيعات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public static void PrintFinancialReport(DateTime fromDate, DateTime toDate)
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var sales = context.Sales
                        .Include(s => s.Car)
                        .Where(s => s.SaleDate >= fromDate && s.SaleDate <= toDate)
                        .ToList();

            var supplierPayments = context.SupplierPayments
                                   .Include(sp => sp.Supplier)
                                   .Where(sp => sp.PaymentDate >= fromDate && sp.PaymentDate <= toDate)
                                   .ToList();

            GenerateFinancialReportContent(sales, supplierPayments, fromDate, toDate);
            PrintDocument();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة التقرير المالي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    public static void PrintInventoryReport()
    {
        try
        {
            using var context = new CarDealershipContext(new DbContextOptionsBuilder<CarDealershipContext>()
                    .UseSqlite("Data Source=CarDealership.db").Options);

            var cars = context.Cars.ToList();
            GenerateInventoryReportContent(cars);
            PrintDocument();
        }
        catch(Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة تقرير المخزون: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void GenerateCustomerStatementContent(Customer customer)
    {
        printLines.Clear();

        // Header
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("كشف حساب العميل".PadLeft(50));
        printLines.Add("Customer Statement".PadLeft(45));
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("");

        // Company info (load from settings)
        printLines.Add($"شركة: معرض السيارات");
        printLines.Add($"التاريخ: {DateTime.Now:yyyy-MM-dd HH:mm}");
        printLines.Add("");

        // Customer info
        printLines.Add("بيانات العميل:");
        printLines.Add("-".PadLeft(30, '-'));
        printLines.Add($"الاسم: {customer.FullName}");
        printLines.Add($"الهاتف: {customer.PrimaryPhone}");
        printLines.Add($"الهوية: {customer.IdNumber}");
        printLines.Add($"العنوان: {customer.Address}");
        printLines.Add("");

        // Sales details
        printLines.Add("تفاصيل المشتريات:");
        printLines.Add("-".PadLeft(30, '-'));

        decimal totalAmount = 0;
        decimal totalPaid = 0;

        foreach(var sale in customer.Sales)
        {
            printLines.Add($"تاريخ البيع: {sale.SaleDate:yyyy-MM-dd}");

            // Handle potential null car reference
            if(sale.Car != null)
            {
                printLines.Add($"السيارة: {sale.Car.Brand} {sale.Car.Model} ({sale.Car.Year})");
                printLines.Add($"رقم الشاسيه: {sale.Car.ChassisNumber}");
            }
            else
            {
                printLines.Add("السيارة: غير محدد");
                printLines.Add("رقم الشاسيه: غير محدد");
            }

            printLines.Add($"المبلغ الإجمالي: {sale.ActualSellPrice:C}");

            var paidAmount = sale.DownPayment + (sale.InstallmentPayments?.Sum(p => p.AmountPaid) ?? 0);
            var remainingAmount = sale.ActualSellPrice - paidAmount;

            printLines.Add($"المدفوع: {paidAmount:C}");
            printLines.Add($"المتبقي: {remainingAmount:C}");
            printLines.Add($"نوع الشراء: {(sale.PaymentMethod == PaymentMethod.Cash ? "نقدي" : "أقساط")}");

            if(sale.PaymentMethod == PaymentMethod.Installment && sale.InstallmentPayments != null && sale.InstallmentPayments.Any())
            {
                printLines.Add("  الدفعات:");
                foreach(var payment in sale.InstallmentPayments.OrderBy(p => p.DueDate))
                {
                    var status = payment.Status == InstallmentStatus.Paid ? "مدفوع" :
                                 payment.Status == InstallmentStatus.Overdue ? "متأخر" : "معلق";
                    printLines.Add($"    قسط {payment.InstallmentNumber}: {payment.InstallmentAmount:C} - {status}");
                    if(payment.PaidDate.HasValue)
                    {
                        printLines.Add($"      تاريخ الدفع: {payment.PaidDate:yyyy-MM-dd}");
                    }
                }
            }

            totalAmount += sale.ActualSellPrice;
            totalPaid += paidAmount;

            printLines.Add("".PadLeft(40, '-'));
        }

        // Summary
        printLines.Add("");
        printLines.Add("الملخص:");
        printLines.Add("=".PadLeft(20, '='));
        printLines.Add($"إجمالي المشتريات: {totalAmount:C}");
        printLines.Add($"إجمالي المدفوع: {totalPaid:C}");
        printLines.Add($"إجمالي المتبقي: {(totalAmount - totalPaid):C}");

        printLines.Add("");
        printLines.Add("شكراً لتعاملكم معنا");
        printLines.Add("=".PadLeft(80, '='));
    }

    private static void GenerateSupplierStatementContent(Supplier supplier)
    {
        printLines.Clear();

        // Header
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("كشف حساب المورد".PadLeft(50));
        printLines.Add("Supplier Statement".PadLeft(45));
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("");

        // Company info
        printLines.Add($"شركة: معرض السيارات");
        printLines.Add($"التاريخ: {DateTime.Now:yyyy-MM-dd HH:mm}");
        printLines.Add("");

        // Supplier info
        printLines.Add("بيانات المورد:");
        printLines.Add("-".PadLeft(30, '-'));
        printLines.Add($"الاسم: {supplier.SupplierName}");
        printLines.Add($"الهاتف: {supplier.Phone}");
        printLines.Add($"البريد: {supplier.Email}");
        printLines.Add($"العنوان: {supplier.Address}");
        printLines.Add("");

        // Payment details
        printLines.Add("تفاصيل المدفوعات:");
        printLines.Add("-".PadLeft(30, '-'));

        decimal totalPaid = 0;

        foreach(var payment in supplier.Payments.OrderBy(p => p.PaymentDate))
        {
            printLines.Add($"تاريخ الدفع: {payment.PaymentDate:yyyy-MM-dd}");
            printLines.Add($"المبلغ: {payment.Amount:C}");
            printLines.Add($"طريقة الدفع: نقدي");
            printLines.Add($"الوصف: {payment.Notes ?? "لا يوجد"}");
            printLines.Add("".PadLeft(40, '-'));

            totalPaid += payment.Amount;
        }

        // Summary
        printLines.Add("");
        printLines.Add("الملخص:");
        printLines.Add("=".PadLeft(20, '='));
        printLines.Add($"إجمالي المدفوعات: {totalPaid:C}");

        printLines.Add("");
        printLines.Add("=".PadLeft(80, '='));
    }

    private static void GenerateSalesReportContent(List<Sale> sales, DateTime fromDate, DateTime toDate)
    {
        printLines.Clear();

        // Header
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("تقرير المبيعات".PadLeft(50));
        printLines.Add("Sales Report".PadLeft(45));
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("");

        printLines.Add($"الفترة: من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");
        printLines.Add($"التاريخ: {DateTime.Now:yyyy-MM-dd HH:mm}");
        printLines.Add("");

        // Sales details
        printLines.Add("تفاصيل المبيعات:");
        printLines.Add("-".PadLeft(50, '-'));

        decimal totalSales = 0;

        foreach(var sale in sales)
        {
            printLines.Add($"التاريخ: {sale.SaleDate:yyyy-MM-dd}");
            printLines.Add($"العميل: {sale.Customer?.FullName ?? "غير محدد"}");

            if(sale.Car != null)
            {
                printLines.Add($"السيارة: {sale.Car.Brand} {sale.Car.Model} ({sale.Car.Year})");
            }
            else
            {
                printLines.Add("السيارة: غير محدد");
            }

            printLines.Add($"المبلغ: {sale.ActualSellPrice:C}");
            printLines.Add($"طريقة الدفع: {(sale.PaymentMethod == PaymentMethod.Cash ? "نقدي" : "أقساط")}");
            printLines.Add("".PadLeft(40, '-'));

            totalSales += sale.ActualSellPrice;
        }

        // Summary
        printLines.Add("");
        printLines.Add("الملخص:");
        printLines.Add("=".PadLeft(20, '='));
        printLines.Add($"عدد المبيعات: {sales.Count}");
        printLines.Add($"إجمالي المبيعات: {totalSales:C}");
        printLines.Add($"متوسط البيع: {(sales.Count > 0 ? totalSales / sales.Count : 0):C}");

        printLines.Add("");
        printLines.Add("=".PadLeft(80, '='));
    }

    private static void GenerateFinancialReportContent(List<Sale> sales, List<SupplierPayment> supplierPayments, DateTime fromDate, DateTime toDate)
    {
        printLines.Clear();

        // Header
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("التقرير المالي".PadLeft(50));
        printLines.Add("Financial Report".PadLeft(45));
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("");

        printLines.Add($"الفترة: من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");
        printLines.Add($"التاريخ: {DateTime.Now:yyyy-MM-dd HH:mm}");
        printLines.Add("");

        // Calculate totals
        var totalRevenue = sales.Sum(s => s.ActualSellPrice);
        var totalPurchaseCost = sales.Sum(s => s.Car?.PurchasePrice ?? 0);
        var totalExpenses = supplierPayments.Sum(sp => sp.Amount);
        var grossProfit = totalRevenue - totalPurchaseCost;
        var netProfit = totalRevenue - totalPurchaseCost - totalExpenses;

        // Summary
        printLines.Add("الملخص المالي:");
        printLines.Add("=".PadLeft(30, '='));
        printLines.Add($"إجمالي الإيرادات: {totalRevenue:C}");
        printLines.Add($"تكلفة السيارات المباعة: {totalPurchaseCost:C}");
        printLines.Add($"الربح الإجمالي: {grossProfit:C}");
        printLines.Add($"المصروفات الأخرى: {totalExpenses:C}");
        printLines.Add($"صافي الربح: {netProfit:C}");
        printLines.Add("");

        // Revenue details
        printLines.Add("تفاصيل الإيرادات:");
        printLines.Add("-".PadLeft(30, '-'));
        foreach(var sale in sales)
        {
            var carInfo = sale.Car != null ? $"{sale.Car.Brand} {sale.Car.Model}" : "غير محدد";
            printLines.Add($"{sale.SaleDate:yyyy-MM-dd} - {carInfo} - {sale.ActualSellPrice:C}");
        }

        printLines.Add("");

        // Expense details
        printLines.Add("تفاصيل المصروفات:");
        printLines.Add("-".PadLeft(30, '-'));
        foreach(var payment in supplierPayments)
        {
            var supplierName = payment.Supplier?.SupplierName ?? "غير محدد";
            printLines.Add($"{payment.PaymentDate:yyyy-MM-dd} - {supplierName} - {payment.Amount:C}");
        }

        printLines.Add("");
        printLines.Add("=".PadLeft(80, '='));
    }

    private static void GenerateInventoryReportContent(List<Car> cars)
    {
        printLines.Clear();

        // Header
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("تقرير المخزون".PadLeft(50));
        printLines.Add("Inventory Report".PadLeft(45));
        printLines.Add("=".PadLeft(80, '='));
        printLines.Add("");

        printLines.Add($"التاريخ: {DateTime.Now:yyyy-MM-dd HH:mm}");
        printLines.Add("");

        // Summary
        var totalCars = cars.Count;
        var soldCars = cars.Count(c => c.IsSold);
        var availableCars = totalCars - soldCars;

        printLines.Add("ملخص المخزون:");
        printLines.Add("=".PadLeft(20, '='));
        printLines.Add($"إجمالي السيارات: {totalCars}");
        printLines.Add($"السيارات المباعة: {soldCars}");
        printLines.Add($"السيارات المتاحة: {availableCars}");
        printLines.Add("");

        // Available cars details
        printLines.Add("السيارات المتاحة:");
        printLines.Add("-".PadLeft(50, '-'));

        foreach(var car in cars.Where(c => !c.IsSold))
        {
            printLines.Add($"الماركة: {car.Brand}");
            printLines.Add($"الموديل: {car.Model}");
            printLines.Add($"السنة: {car.Year}");
            printLines.Add($"رقم الشاسيه: {car.ChassisNumber}");
            printLines.Add($"سعر الشراء: {car.PurchasePrice:C}");
            printLines.Add($"سعر البيع المقترح: {car.SuggestedSellPrice:C}");
            printLines.Add($"الحالة: {(car.Condition == CarCondition.New ? "جديدة" : "مستعملة")}");
            printLines.Add("".PadLeft(40, '-'));
        }

        printLines.Add("");
        printLines.Add("=".PadLeft(80, '='));
    }

    private static void PrintDocument()
    {
        currentLineIndex = 0;

        PrintDocument printDoc = new PrintDocument();
        printDoc.PrintPage += PrintDoc_PrintPage;

        PrintDialog printDialog = new PrintDialog();
        printDialog.Document = printDoc;

        if(printDialog.ShowDialog() == DialogResult.OK)
        {
            printDoc.Print();
        }
    }

    private static void PrintDoc_PrintPage(object sender, PrintPageEventArgs e)
    {
        float yPos = 0;
        int count = 0;
        float leftMargin = e.MarginBounds.Left;
        float topMargin = e.MarginBounds.Top;
        string line = "";

        // Calculate the number of lines per page
        float linesPerPage = e.MarginBounds.Height / printFont.GetHeight(e.Graphics!);

        // Print each line of the document
        while(count < linesPerPage && currentLineIndex < printLines.Count)
        {
            line = printLines[currentLineIndex];
            yPos = topMargin + (count * printFont.GetHeight(e.Graphics!));

            // Use different fonts for titles and regular text
            Font currentFont = printFont;
            if(line.Contains("=") && line.Length > 20)
            {
                currentFont = printFontBold;
            }
            else if(line.Contains("تقرير") || line.Contains("كشف حساب"))
            {
                currentFont = printFontTitle;
            }

            e.Graphics.DrawString(line, currentFont, Brushes.Black, leftMargin, yPos, new StringFormat());
            count++;
            currentLineIndex++;
        }

        // If more lines exist, print another page
        if(currentLineIndex < printLines.Count)
        {
            e.HasMorePages = true;
        }
        else
        {
            e.HasMorePages = false;
        }
    }
}
}
