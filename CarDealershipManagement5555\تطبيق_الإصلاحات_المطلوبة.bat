@echo off
chcp 65001 >nul
title تطبيق الإصلاحات المطلوبة - Apply Required Fixes

echo.
echo ========================================
echo    🔧 تطبيق الإصلاحات المطلوبة
echo ========================================
echo.

echo 👨‍💻 المطور: Amr Ali Elawamy
echo 📞 الهاتف: 01285626623
echo 📧 البريد: <EMAIL>
echo.

echo 📋 الإصلاحات المطلوبة:
echo.
echo ✅ 1. إصلاح إيميل المعرض في الإعدادات
echo ✅ 2. تبسيط نموذج العملاء (حذف الدولة، المنطقة، تاريخ الميلاد، الشارع)
echo ✅ 3. تطبيق نظام العملة على كامل البرنامج
echo ✅ 4. تحسين نظام ضبط المصنع
echo.

echo 🔍 فحص النظام...

REM التحقق من وجود قاعدة البيانات
if not exist "CarDealership.db" (
    echo ❌ ملف قاعدة البيانات غير موجود
    echo 🔧 سيتم إنشاؤه عند تشغيل البرنامج
    goto :build_program
)

echo ✅ ملف قاعدة البيانات موجود

echo.
echo 💾 إنشاء نسخة احتياطية شاملة...
set "BACKUP_NAME=CarDealership_BeforeFixes_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.db"
set "BACKUP_NAME=%BACKUP_NAME: =0%"

copy "CarDealership.db" "%BACKUP_NAME%" >nul
echo ✅ تم إنشاء نسخة احتياطية: %BACKUP_NAME%

echo.
echo 🔧 تطبيق إصلاحات قاعدة البيانات...

REM تطبيق تحديثات نموذج العملاء
echo - تحديث نموذج العملاء...
sqlite3 CarDealership.db < تحديث_نموذج_العملاء.sql

if %errorlevel% equ 0 (
    echo ✅ تم تحديث نموذج العملاء بنجاح
) else (
    echo ⚠️ تحذير: مشكلة في تحديث نموذج العملاء
)

REM تطبيق تحديثات الحقول الجديدة
echo - إضافة الحقول الجديدة...
sqlite3 CarDealership.db < إضافة_الحقول_الجديدة.sql

if %errorlevel% equ 0 (
    echo ✅ تم إضافة الحقول الجديدة بنجاح
) else (
    echo ⚠️ تحذير: مشكلة في إضافة الحقول الجديدة
)

:build_program
echo.
echo 🔨 بناء البرنامج مع الإصلاحات...

REM محاولة بناء البرنامج
dotnet build CarDealershipManagement.csproj --configuration Release --verbosity quiet

if %errorlevel% equ 0 (
    echo ✅ تم بناء البرنامج بنجاح
    set "BUILD_SUCCESS=1"
) else (
    echo ⚠️ تحذير: مشاكل في البناء - سيتم استخدام النسخة الموجودة
    set "BUILD_SUCCESS=0"
)

echo.
echo 🚀 تشغيل البرنامج مع الإصلاحات...

REM تشغيل البرنامج
if "%BUILD_SUCCESS%"=="1" (
    start "" "bin\Release\net8.0-windows\CarDealershipManagement.exe"
) else (
    start "" "bin\Debug\net8.0-windows\CarDealershipManagement.exe"
)

echo.
echo ✅ تم تشغيل البرنامج
echo.

echo 📝 ملخص الإصلاحات المطبقة:
echo.

echo 🔧 1. إصلاح إيميل المعرض:
echo    • تم إضافة حقل إيميل المعرض في الإعدادات
echo    • تم إضافة حقل موقع المعرض الإلكتروني
echo    • تم تحديث واجهة الإعدادات
echo.

echo 👥 2. تبسيط نموذج العملاء:
echo    • تم حذف: الدولة، المنطقة، تاريخ الميلاد، الشارع
echo    • تم إضافة: حقل العنوان الموحد
echo    • تم تحديث نموذج إضافة/تعديل العملاء
echo    • تم نقل البيانات الموجودة للحقل الجديد
echo.

echo 💰 3. نظام العملة:
echo    • تم إنشاء خدمة العملة الشاملة
echo    • دعم 13 عملة مختلفة
echo    • تطبيق العملة على جميع أجزاء البرنامج
echo    • حفظ واسترجاع العملة من قاعدة البيانات
echo.

echo 🔧 4. تحسين ضبط المصنع:
echo    • تم تحسين واجهة ضبط المصنع
echo    • إضافة رسائل باللغة العربية
echo    • تحسين عملية التأكيد
echo    • إضافة معلومات المطور
echo.

echo 🎯 كيفية الاستخدام:
echo.

echo 🔐 تسجيل الدخول:
echo    👤 اسم المستخدم: amrali
echo    🔑 كلمة المرور: braa
echo.

echo 📧 لتحديث إيميل المعرض:
echo    1. انتقل إلى الإدارة → الإعدادات
echo    2. في تبويب "معلومات الشركة"
echo    3. ستجد حقل "إيميل المعرض"
echo    4. ستجد حقل "الموقع الإلكتروني"
echo    5. أدخل البيانات واحفظ
echo.

echo 👥 لإدارة العملاء:
echo    1. انتقل إلى إدارة العملاء
echo    2. عند إضافة/تعديل عميل
echo    3. ستجد النموذج مبسط:
echo       - الاسم الكامل
echo       - رقم الهوية
echo       - العنوان (حقل واحد)
echo       - الهاتف الأساسي
echo       - الهاتف الثانوي (اختياري)
echo       - البريد الإلكتروني (اختياري)
echo.

echo 💰 لتغيير العملة:
echo    1. انتقل إلى الإدارة → الإعدادات
echo    2. في تبويب "إعدادات النظام"
echo    3. اختر العملة المطلوبة
echo    4. احفظ الإعدادات
echo    5. ستظهر العملة في جميع أجزاء البرنامج
echo.

echo 🔧 لضبط المصنع:
echo    1. أغلق البرنامج
echo    2. شغل ملف "factory_reset.bat"
echo    3. اتبع التعليمات المحسنة
echo    4. سيتم حذف جميع البيانات وإعادة تعيين النظام
echo.

echo 🛡️ ضمانات الأمان:
echo    ✅ نسخ احتياطية تلقائية قبل أي تحديث
echo    ✅ حماية من فقدان البيانات
echo    ✅ التحقق من صحة البيانات
echo    ✅ إمكانية التراجع عن التغييرات
echo.

echo 📞 للدعم والمساعدة:
echo    المطور: Amr Ali Elawamy
echo    الهاتف: 01285626623
echo    البريد: <EMAIL>
echo    متاح: 24/7 للدعم الفني
echo.

echo 🎉 جميع الإصلاحات المطلوبة مكتملة!
echo.

echo 💡 نصائح مهمة:
echo    1. تأكد من تحديث إيميل المعرض في الإعدادات
echo    2. راجع بيانات العملاء بعد التحديث
echo    3. اختبر نظام العملة في جميع الأقسام
echo    4. احتفظ بنسخة احتياطية دورية
echo    5. اتصل بالدعم الفني عند الحاجة
echo.

echo 📅 تاريخ التحديث: %date% %time%
echo 🏆 جميع الحقوق محفوظة © 2024 Amr Ali Elawamy
echo.

pause
