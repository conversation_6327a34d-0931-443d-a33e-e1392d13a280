-- إصلا<PERSON> قاعدة البيانات الرئيسية
-- المطور: <PERSON><PERSON> <PERSON> - 01285626623 - <EMAIL>

-- 1. إضافة حقول إيميل وموقع المعرض
ALTER TABLE SystemSettings ADD COLUMN CompanyEmail TEXT DEFAULT '';
ALTER TABLE SystemSettings ADD COLUMN CompanyWebsite TEXT DEFAULT '';

-- 2. إض<PERSON><PERSON>ة صلاحية إدارة مندوبي المبيعات
ALTER TABLE UserPermissions ADD COLUMN CanManageSalesReps INTEGER DEFAULT 0;

-- 3. تحديث صلاحيات المطور
UPDATE UserPermissions SET CanManageSalesReps = 1 WHERE UserId = 1;

-- 4. تب<PERSON>يط جدول العملاء - إن<PERSON>اء جدول جديد
CREATE TABLE IF NOT EXISTS Customers_New (
    CustomerId INTEGER PRIMARY KEY AUTOINCREMENT,
    FullName TEXT NOT NULL,
    IdNumber TEXT NOT NULL,
    Address TEXT NOT NULL,
    PrimaryPhone TEXT NOT NULL,
    SecondaryPhone TEXT,
    Email TEXT,
    IsActive INTEGER DEFAULT 1,
    IsDeleted INTEGER DEFAULT 0,
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT,
    DeletedDate TEXT
);

-- 5. نقل البيانات الموجودة مع دمج العنوان
INSERT OR IGNORE INTO Customers_New (
    CustomerId, FullName, IdNumber, Address, PrimaryPhone,
    SecondaryPhone, Email, IsActive, IsDeleted, CreatedDate, ModifiedDate, DeletedDate
)
SELECT 
    CustomerId,
    FullName,
    IdNumber,
    CASE 
        WHEN Country IS NOT NULL OR City IS NOT NULL OR Area IS NOT NULL OR Street IS NOT NULL THEN
            TRIM(COALESCE(Country, '') || 
                 CASE WHEN Country IS NOT NULL AND (City IS NOT NULL OR Area IS NOT NULL OR Street IS NOT NULL) THEN ', ' ELSE '' END ||
                 COALESCE(City, '') || 
                 CASE WHEN City IS NOT NULL AND (Area IS NOT NULL OR Street IS NOT NULL) THEN ', ' ELSE '' END ||
                 COALESCE(Area, '') || 
                 CASE WHEN Area IS NOT NULL AND Street IS NOT NULL THEN ', ' ELSE '' END ||
                 COALESCE(Street, ''), ', ')
        ELSE 'غير محدد'
    END AS Address,
    PrimaryPhone,
    SecondaryPhone,
    Email,
    IsActive,
    IsDeleted,
    CreatedDate,
    ModifiedDate,
    DeletedDate
FROM Customers 
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='Customers');

-- 6. استبدال الجدول القديم بالجديد
DROP TABLE IF EXISTS Customers_Backup;
ALTER TABLE Customers RENAME TO Customers_Backup;
ALTER TABLE Customers_New RENAME TO Customers;

-- 7. تنظيف العناوين
UPDATE Customers 
SET Address = CASE 
    WHEN Address = ', , , ' OR Address = '' OR Address IS NULL THEN 'غير محدد'
    ELSE TRIM(REPLACE(REPLACE(REPLACE(Address, ', , ', ', '), '  ', ' '), ', ,', ','), ', ')
END;

-- 8. إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_customers_fullname ON Customers(FullName);
CREATE INDEX IF NOT EXISTS idx_customers_idnumber ON Customers(IdNumber);
CREATE INDEX IF NOT EXISTS idx_customers_active ON Customers(IsActive);

-- 9. تحديث إعدادات العملة الافتراضية
UPDATE SystemSettings SET Currency = 'EGP' WHERE Currency IS NULL OR Currency = '';

-- 10. التأكد من وجود المطور
UPDATE Users SET IsActive = 1 WHERE Username = 'amrali';
UPDATE UserPermissions SET CanManageSalesReps = 1 WHERE UserId = (SELECT UserId FROM Users WHERE Username = 'amrali');
