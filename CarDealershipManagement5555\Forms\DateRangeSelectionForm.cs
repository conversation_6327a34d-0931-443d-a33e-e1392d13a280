using System;
using System.Drawing;
using System.Windows.Forms;

namespace CarDealershipManagement.Forms
{
public partial class DateRangeSelectionForm : Form
{
    private DateTimePicker dtpFromDate = null!;
    private DateTimePicker dtpToDate = null!;
    private Button btnOK = null!;
    private Button btnCancel = null!;
    private Button btnToday = null!;
    private Button btnThisWeek = null!;
    private Button btnThisMonth = null!;
    private Button btnLastMonth = null!;
    private Button btnThisYear = null!;
    private Label lblInfo = null!;

    public DateTime FromDate
    {
        get;
        private set;
    }
    public DateTime ToDate
    {
        get;
        private set;
    }

    public DateRangeSelectionForm()
    {
        InitializeComponent();
        SetDefaultDates();
    }

    private void InitializeComponent()
    {
        this.Text = "اختيار الفترة الزمنية - Select Date Range";
        this.Size = new Size(500, 400);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.BackColor = Color.FromArgb(248, 249, 250);
        this.Icon = SystemIcons.Application;
        this.MaximizeBox = false;
        this.MinimizeBox = false;
        this.FormBorderStyle = FormBorderStyle.FixedDialog;

        // Header Panel
        var headerPanel = new Panel
        {
            Size = new Size(500, 60),
            Location = new Point(0, 0),
            BackColor = Color.FromArgb(52, 58, 64),
            Dock = DockStyle.Top
        };

        var headerLabel = new Label
        {
            Text = "اختيار الفترة الزمنية",
            Font = new Font("Segoe UI", 16, FontStyle.Bold),
            ForeColor = Color.White,
            Location = new Point(20, 10),
            AutoSize = true
        };

        var headerSubLabel = new Label
        {
            Text = "حدد الفترة الزمنية للتقرير",
            Font = new Font("Segoe UI", 10),
            ForeColor = Color.FromArgb(206, 212, 218),
            Location = new Point(20, 35),
            AutoSize = true
        };

        headerPanel.Controls.AddRange(new Control[] { headerLabel, headerSubLabel });

        // Date Selection Panel
        var datePanel = new Panel
        {
            Size = new Size(460, 120),
            Location = new Point(20, 80),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblFromDate = new Label
        {
            Text = "من تاريخ:",
            Location = new Point(20, 20),
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            AutoSize = true
        };

        dtpFromDate = new DateTimePicker
        {
            Location = new Point(100, 18),
            Size = new Size(150, 23),
            Format = DateTimePickerFormat.Short,
            Font = new Font("Segoe UI", 10)
        };
        dtpFromDate.ValueChanged += DateTimePicker_ValueChanged;

        var lblToDate = new Label
        {
            Text = "إلى تاريخ:",
            Location = new Point(270, 20),
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            AutoSize = true
        };

        dtpToDate = new DateTimePicker
        {
            Location = new Point(340, 18),
            Size = new Size(150, 23),
            Format = DateTimePickerFormat.Short,
            Font = new Font("Segoe UI", 10)
        };
        dtpToDate.ValueChanged += DateTimePicker_ValueChanged;

        lblInfo = new Label
        {
            Text = "عدد الأيام: 0",
            Location = new Point(20, 60),
            Font = new Font("Segoe UI", 9),
            ForeColor = Color.FromArgb(40, 167, 69),
            AutoSize = true
        };

        var lblNote = new Label
        {
            Text = "ملاحظة: يجب أن يكون تاريخ البداية أقل من أو يساوي تاريخ النهاية",
            Location = new Point(20, 85),
            Size = new Size(420, 20),
            Font = new Font("Segoe UI", 8),
            ForeColor = Color.FromArgb(108, 117, 125)
        };

        datePanel.Controls.AddRange(new Control[] { lblFromDate, dtpFromDate, lblToDate, dtpToDate, lblInfo, lblNote });

        // Quick Selection Panel
        var quickPanel = new Panel
        {
            Size = new Size(460, 100),
            Location = new Point(20, 210),
            BackColor = Color.White,
            BorderStyle = BorderStyle.FixedSingle
        };

        var lblQuick = new Label
        {
            Text = "الاختيار السريع:",
            Location = new Point(20, 10),
            Font = new Font("Segoe UI", 10, FontStyle.Bold),
            AutoSize = true
        };

        btnToday = new Button
        {
            Text = "اليوم",
            Location = new Point(20, 35),
            Size = new Size(70, 25),
            BackColor = Color.FromArgb(0, 123, 255),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9)
        };
        btnToday.Click += BtnToday_Click;

        btnThisWeek = new Button
        {
            Text = "هذا الأسبوع",
            Location = new Point(100, 35),
            Size = new Size(80, 25),
            BackColor = Color.FromArgb(0, 123, 255),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9)
        };
        btnThisWeek.Click += BtnThisWeek_Click;

        btnThisMonth = new Button
        {
            Text = "هذا الشهر",
            Location = new Point(190, 35),
            Size = new Size(80, 25),
            BackColor = Color.FromArgb(0, 123, 255),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9)
        };
        btnThisMonth.Click += BtnThisMonth_Click;

        btnLastMonth = new Button
        {
            Text = "الشهر الماضي",
            Location = new Point(280, 35),
            Size = new Size(80, 25),
            BackColor = Color.FromArgb(0, 123, 255),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9)
        };
        btnLastMonth.Click += BtnLastMonth_Click;

        btnThisYear = new Button
        {
            Text = "هذا العام",
            Location = new Point(370, 35),
            Size = new Size(70, 25),
            BackColor = Color.FromArgb(0, 123, 255),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 9)
        };
        btnThisYear.Click += BtnThisYear_Click;

        quickPanel.Controls.AddRange(new Control[] { lblQuick, btnToday, btnThisWeek, btnThisMonth, btnLastMonth, btnThisYear });

        // Button Panel
        var buttonPanel = new Panel
        {
            Size = new Size(460, 50),
            Location = new Point(20, 320),
            BackColor = Color.FromArgb(248, 249, 250)
        };

        btnOK = new Button
        {
            Text = "موافق",
            Location = new Point(290, 10),
            Size = new Size(80, 30),
            BackColor = Color.FromArgb(40, 167, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 10, FontStyle.Bold)
        };
        btnOK.Click += BtnOK_Click;

        btnCancel = new Button
        {
            Text = "إلغاء",
            Location = new Point(380, 10),
            Size = new Size(80, 30),
            BackColor = Color.FromArgb(220, 53, 69),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            Font = new Font("Segoe UI", 10, FontStyle.Bold)
        };
        btnCancel.Click += BtnCancel_Click;

        buttonPanel.Controls.AddRange(new Control[] { btnOK, btnCancel });

        // Add controls to form
        this.Controls.AddRange(new Control[] { headerPanel, datePanel, quickPanel, buttonPanel });
    }

    private void SetDefaultDates()
    {
        // Set default to last month
        var today = DateTime.Today;
        dtpFromDate.Value = today.AddMonths(-1);
        dtpToDate.Value = today;
        UpdateDateInfo();
    }

    private void DateTimePicker_ValueChanged(object sender, EventArgs e)
    {
        UpdateDateInfo();
    }

    private void UpdateDateInfo()
    {
        var fromDate = dtpFromDate.Value.Date;
        var toDate = dtpToDate.Value.Date;

        if(fromDate <= toDate)
        {
            var daysDiff = (toDate - fromDate).Days + 1;
            lblInfo.Text = $"عدد الأيام: {daysDiff}";
            lblInfo.ForeColor = Color.FromArgb(40, 167, 69);
            btnOK.Enabled = true;
        }
        else
        {
            lblInfo.Text = "تاريخ البداية يجب أن يكون أقل من أو يساوي تاريخ النهاية";
            lblInfo.ForeColor = Color.FromArgb(220, 53, 69);
            btnOK.Enabled = false;
        }
    }

    private void BtnToday_Click(object sender, EventArgs e)
    {
        var today = DateTime.Today;
        dtpFromDate.Value = today;
        dtpToDate.Value = today;
    }

    private void BtnThisWeek_Click(object sender, EventArgs e)
    {
        var today = DateTime.Today;
        var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
        var endOfWeek = startOfWeek.AddDays(6);

        dtpFromDate.Value = startOfWeek;
        dtpToDate.Value = endOfWeek;
    }

    private void BtnThisMonth_Click(object sender, EventArgs e)
    {
        var today = DateTime.Today;
        var startOfMonth = new DateTime(today.Year, today.Month, 1);
        var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

        dtpFromDate.Value = startOfMonth;
        dtpToDate.Value = endOfMonth;
    }

    private void BtnLastMonth_Click(object sender, EventArgs e)
    {
        var today = DateTime.Today;
        var startOfLastMonth = new DateTime(today.Year, today.Month, 1).AddMonths(-1);
        var endOfLastMonth = startOfLastMonth.AddMonths(1).AddDays(-1);

        dtpFromDate.Value = startOfLastMonth;
        dtpToDate.Value = endOfLastMonth;
    }

    private void BtnThisYear_Click(object sender, EventArgs e)
    {
        var today = DateTime.Today;
        var startOfYear = new DateTime(today.Year, 1, 1);
        var endOfYear = new DateTime(today.Year, 12, 31);

        dtpFromDate.Value = startOfYear;
        dtpToDate.Value = endOfYear;
    }

    private void BtnOK_Click(object sender, EventArgs e)
    {
        if(dtpFromDate.Value.Date <= dtpToDate.Value.Date)
        {
            FromDate = dtpFromDate.Value.Date;
            ToDate = dtpToDate.Value.Date;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
        else
        {
            MessageBox.Show("يرجى التأكد من صحة التواريخ المدخلة", "خطأ في التواريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void BtnCancel_Click(object sender, EventArgs e)
    {
        this.DialogResult = DialogResult.Cancel;
        this.Close();
    }
}
}
