@echo off
chcp 65001 >nul
title تشغيل برنامج إدارة معرض السيارات

echo.
echo ========================================
echo    🚗 برنامج إدارة معرض السيارات 🚗
echo ========================================
echo.
echo 🔄 جاري تشغيل البرنامج...
echo.

cd /d "%~dp0\bin\Debug\net8.0-windows"

if exist "CarDealershipManagement.exe" (
    echo ✅ تم العثور على البرنامج
    echo.
    echo 🔑 بيانات الدخول:
    echo    اسم المستخدم: amrali
    echo    كلمة المرور: braa
    echo.
    echo 🚀 جاري فتح البرنامج...
    echo.
    
    REM تشغيل البرنامج في نافذة منفصلة
    start "" "CarDealershipManagement.exe"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    echo.
    echo 📋 للوصول إلى تبويب الأقساط المحسن:
    echo    1. سجل الدخول باستخدام البيانات أعلاه
    echo    2. اختر "التقارير" من القائمة الرئيسية
    echo    3. انتقل إلى تبويب "📅 تقارير الأقساط"
    echo.
    echo 🎯 الميزات الجديدة:
    echo    • 📊 إنشاء التقرير - تقرير شامل مع فلترة
    echo    • 🖨️ طباعة محسنة - طباعة احترافية
    echo    • 📤 تصدير - 5 صيغ مختلفة
    echo    • 📈 ملخص الأقساط - إحصائيات سريعة
    echo.
    
) else (
    echo ❌ خطأ: لم يتم العثور على ملف البرنامج
    echo 📁 المسار المتوقع: %~dp0\bin\Debug\net8.0-windows\CarDealershipManagement.exe
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. تأكد من بناء المشروع أولاً
    echo    2. تحقق من وجود الملف في المسار الصحيح
    echo    3. أعد تشغيل Visual Studio كمسؤول
    echo.
)

echo.
echo 📞 للمساعدة أو الدعم الفني، اتصل بالمطور
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
