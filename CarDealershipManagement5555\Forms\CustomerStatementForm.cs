using CarDealershipManagement.Data;
using CarDealershipManagement.Models;
using CarDealershipManagement.Services;
using Microsoft.EntityFrameworkCore;
using System.Drawing.Printing;
using System.IO;

namespace CarDealershipManagement.Forms
{
    public partial class CustomerStatementForm : Form
    {
        private int customerId;
        private Customer? customer;
        private Sale? sale;
        private Car? car;
        private List<InstallmentPayment>? installments;

        private Label lblHeader;
        private Label lblCustomerDetails;
        private Label lblCarDetails;
        private Label lblSaleDetails;
        private Label lblInstallmentHeader;
        private DataGridView dgvInstallments;
        private Label lblSummary;
        private Button btnPrint;
        private Label lblStatus;

        public CustomerStatementForm(int customerId)
        {
            this.customerId = customerId;
            InitializeComponent();
            LoadCustomerStatementData();
        }

        private void InitializeComponent()
        {
            // Form settings - نفس تنسيق InventoryForm
            this.Text = "📋 كشف حساب العميل - Customer Statement";
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Font = new Font("Segoe UI", 10F);
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1200, 700);
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;

            // Status Label
            lblStatus = new Label
            {
                Text = "",
                Location = new Point(20, 850),
                Size = new Size(760, 20),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.Red,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Header (Dealership Name and Logo Placeholder)
            lblHeader = new Label
            {
                Text = "[اسم المعرض هنا]\nكشف حساب العميل",
                Location = new Point(20, 20),
                Size = new Size(760, 60),
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.FromArgb(0, 102, 204)
            };

            // Customer Details
            lblCustomerDetails = new Label
            {
                Text = "بيانات العميل: تحميل...",
                Location = new Point(20, 100),
                Size = new Size(760, 100),
                Font = new Font("Segoe UI", 10F),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(5),
                BackColor = Color.FromArgb(240, 248, 255)
            };

            // Car Details
            lblCarDetails = new Label
            {
                Text = "بيانات السيارة: تحميل...",
                Location = new Point(20, 210),
                Size = new Size(760, 100),
                Font = new Font("Segoe UI", 10F),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(5),
                BackColor = Color.FromArgb(240, 248, 255)
            };

            // Sale Details
            lblSaleDetails = new Label
            {
                Text = "تفاصيل البيع: تحميل...",
                Location = new Point(20, 320),
                Size = new Size(760, 80),
                Font = new Font("Segoe UI", 10F),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(5),
                BackColor = Color.FromArgb(240, 248, 255)
            };

            // Installment Header
            lblInstallmentHeader = new Label
            {
                Text = "جدول الأقساط:",
                Location = new Point(20, 410),
                Size = new Size(760, 25),
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 58, 64)
            };

            // Installments DataGridView
            dgvInstallments = new DataGridView
            {
                Location = new Point(20, 440),
                Size = new Size(760, 250),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Segoe UI", 9F),
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(0, 102, 204),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Padding = new Padding(5)
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.Black,
                    SelectionBackColor = Color.FromArgb(173, 216, 230),
                    SelectionForeColor = Color.Black,
                    Font = new Font("Segoe UI", 9F),
                    Padding = new Padding(5)
                }
            };

            // Summary
            lblSummary = new Label
            {
                Text = "الملخص: تحميل...",
                Location = new Point(20, 700),
                Size = new Size(760, 100),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                BorderStyle = BorderStyle.FixedSingle,
                Padding = new Padding(5),
                BackColor = Color.FromArgb(240, 248, 255)
            };

            // Print Button
            btnPrint = new Button
            {
                Text = "طباعة الكشف",
                Location = new Point(650, 810),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.Click += BtnPrint_Click;

            this.Controls.AddRange(new Control[]
            {
                lblHeader, lblCustomerDetails, lblCarDetails, lblSaleDetails,
                lblInstallmentHeader, dgvInstallments, lblSummary, btnPrint, lblStatus
            });
        }

        private async void LoadCustomerStatementData()
        {
            lblStatus.Text = ""; // Clear previous status messages
            await ErrorHandlingService.TryExecute(async () =>
            {
                using var context = DbContextFactory.CreateContext();

                customer = await context.Customers
                                .Include(c => c.Sales)
                                    .ThenInclude(s => s.Car)
                                .Include(c => c.Sales)
                                    .ThenInclude(s => s.InstallmentPayments)
                                .FirstOrDefaultAsync(c => c.CustomerId == customerId);

                if (customer == null)
                {
                    lblStatus.Text = "لم يتم العثور على بيانات العميل.";
                    return;
                }

                sale = customer.Sales.FirstOrDefault(); // Assuming one sale per customer for simplicity
                car = sale?.Car;
                installments = sale?.InstallmentPayments.OrderBy(i => i.DueDate).ToList();

                DisplayData();
            }, "تحميل بيانات كشف حساب العميل");
        }

        private void DisplayData()
        {
            if (customer == null) return;

            // Customer Details
            lblCustomerDetails.Text = $"الاسم: {customer.FullName}\n" +
                                      $"رقم الهوية: {customer.IdNumber}\n" +
                                      $"الهاتف الأساسي: {customer.PrimaryPhone}\n" +
                                      $"العنوان: {customer.Address}";

            // Car Details
            if (car != null)
            {
                lblCarDetails.Text = $"الماركة: {car.Brand}\n" +
                                     $"الموديل: {car.Model}\n" +
                                     $"سنة الصنع: {car.Year}\n" +
                                     $"رقم الشاسيه: {car.ChassisNumber}";
            }
            else
            {
                lblCarDetails.Text = "لا توجد بيانات سيارة مرتبطة بهذا العميل.";
            }

            // Sale Details
            if (sale != null)
            {
                lblSaleDetails.Text = $"تاريخ البيع: {sale.SaleDate.ToShortDateString()}\n" +
                                      $"السعر الإجمالي: {sale.ActualSellPrice:C}\n" +
                                      $"الدفعة الأولى: {sale.DownPayment:C}\n" +
                                      $"المبلغ المتبقي: {sale.RemainingAmount:C}";
            }
            else
            {
                lblSaleDetails.Text = "لا توجد تفاصيل بيع مرتبطة بهذا العميل.";
            }

            // Installments DataGridView
            if (installments != null && installments.Any())
            {
                dgvInstallments.DataSource = installments.Select(i => new
                {
                    رقم_القسط = i.InstallmentNumber,
                    المبلغ_المستحق = i.InstallmentAmount.ToString("C"),
                    تاريخ_الاستحقاق = i.DueDate.ToShortDateString(),
                    تاريخ_الدفع = i.PaidDate?.ToShortDateString() ?? "لم يدفع",
                    الحالة = GetInstallmentStatusText(i.Status)
                }).ToList();
            }
            else
            {
                lblInstallmentHeader.Text = "لا توجد أقساط مسجلة لهذا العميل.";
                dgvInstallments.DataSource = null;
            }

            // Summary - حساب صحيح للرصيد
            decimal totalPaidInstallments = installments?.Where(i => i.Status == InstallmentStatus.Paid).Sum(i => i.InstallmentAmount) ?? 0;
            decimal totalRemainingInstallments = installments?.Where(i => i.Status != InstallmentStatus.Paid).Sum(i => i.InstallmentAmount) ?? 0;

            // الرصيد النهائي = المبلغ المتبقي الأصلي - إجمالي الأقساط المدفوعة
            decimal originalRemainingAmount = sale?.RemainingAmount ?? 0;
            decimal finalBalance = originalRemainingAmount - totalPaidInstallments;

            lblSummary.Text = $"إجمالي الأقساط المدفوعة: {totalPaidInstallments:C}\n" +
                              $"إجمالي الأقساط المتبقية: {totalRemainingInstallments:C}\n" +
                              $"الرصيد النهائي: {finalBalance:C}";
        }

        private string GetInstallmentStatusText(InstallmentStatus status)
        {
            return status switch
            {
                InstallmentStatus.Pending => "مستحق",
                InstallmentStatus.Paid => "مدفوع",
                InstallmentStatus.Overdue => "متأخر",
                InstallmentStatus.PartiallyPaid => "مدفوع جزئياً",
                _ => "غير محدد"
            };
        }

        private void BtnPrint_Click(object? sender, EventArgs e)
        {
            lblStatus.Text = ""; // Clear previous status messages
            if (customer == null)
            {
                lblStatus.Text = "لا توجد بيانات للطباعة.";
                return;
            }

            PrintDocument printDoc = new PrintDocument();
            printDoc.PrintPage += new PrintPageEventHandler(PrintDocument_PrintPage);

            PrintPreviewDialog printPreviewDialog = new PrintPreviewDialog();
            printPreviewDialog.Document = printDoc;
            printPreviewDialog.ShowDialog();
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics graphics = e.Graphics;
            Font font = new Font("Segoe UI", 10);
            Font boldFont = new Font("Segoe UI", 12, FontStyle.Bold);
            Font headerFont = new Font("Segoe UI", 18, FontStyle.Bold);
            SolidBrush brush = new SolidBrush(Color.Black);
            int startX = 50;
            int startY = 50;
            int offset = 40;

            // تحميل بيانات الشركة الفعلية
            SystemSettings companySettings = null;
            Image companyLogo = null;

            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<CarDealershipContext>();
                optionsBuilder.UseSqlite("Data Source=CarDealership.db");

                using var context = new CarDealershipContext(optionsBuilder.Options);
                companySettings = context.SystemSettings.FirstOrDefault();

                if (companySettings != null && !string.IsNullOrEmpty(companySettings.CompanyLogo))
                {
                    string logoPath = Path.Combine(Application.StartupPath, companySettings.CompanyLogo);
                    if (File.Exists(logoPath))
                    {
                        companyLogo = Image.FromFile(logoPath);
                    }
                }
            }
            catch { /* استخدم القيم الافتراضية في حالة الخطأ */ }

            // رأسية الشركة مع تنسيق احترافي
            var headerRect = new RectangleF(startX, startY, e.PageBounds.Width - 100, 140);
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), headerRect);
            graphics.DrawRectangle(new Pen(Color.FromArgb(0, 102, 204), 3), Rectangle.Round(headerRect));

            // اسم الشركة في الأعلى وسط الصفحة
            string dealershipName = companySettings?.CompanyName ?? "معرض السيارات المتميز";
            var companyNameFont = new Font("Segoe UI", 22F, FontStyle.Bold);
            SizeF nameSize = graphics.MeasureString(dealershipName, companyNameFont);
            float centerX = startX + (headerRect.Width - nameSize.Width) / 2;
            graphics.DrawString(dealershipName, companyNameFont, new SolidBrush(Color.FromArgb(0, 102, 204)),
                centerX, startY + 15);

            // رسم اللوجو على اليسار أولاً
            var logoRect = new RectangleF(startX + 20, startY + 60, 70, 70);
            if (companyLogo != null)
            {
                graphics.DrawImage(companyLogo, logoRect);
                graphics.DrawRectangle(new Pen(Color.FromArgb(0, 102, 204), 2), Rectangle.Round(logoRect));
                companyLogo.Dispose();
            }
            else
            {
                graphics.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), logoRect);
                graphics.DrawString("LOGO", new Font("Segoe UI", 10F, FontStyle.Bold),
                    Brushes.White, logoRect.X + 15, logoRect.Y + 25);
            }

            // خط فاصل تحت اسم الشركة (بعد رسم اللوجو)
            graphics.DrawLine(new Pen(Color.FromArgb(0, 102, 204), 2),
                startX + 100, startY + 50, startX + headerRect.Width - 20, startY + 50);

            // بيانات الشركة في الوسط
            string companyAddress = companySettings?.CompanyAddress ?? "شارع الملك فهد، الرياض، المملكة العربية السعودية";
            string companyPhone = companySettings?.CompanyPhone ?? "966-11-1234567+";
            string commercialRegister = companySettings?.CommercialRegister ?? "";
            string taxCard = companySettings?.TaxCard ?? "";

            var companyInfoFont = new Font("Segoe UI", 11F);
            float infoStartX = startX + 110;

            graphics.DrawString($"العنوان: {companyAddress}", companyInfoFont,
                Brushes.Black, infoStartX, startY + 65);
            graphics.DrawString($"الهاتف: {companyPhone}", companyInfoFont,
                Brushes.Black, infoStartX, startY + 85);

            if (!string.IsNullOrEmpty(commercialRegister))
            {
                graphics.DrawString($"السجل التجاري: {commercialRegister}", companyInfoFont,
                    Brushes.Black, infoStartX, startY + 105);
            }

            if (!string.IsNullOrEmpty(taxCard))
            {
                graphics.DrawString($"البطاقة الضريبية: {taxCard}", companyInfoFont,
                    Brushes.Black, infoStartX, startY + 125);
            }
            startY += 140;

            // عنوان كشف الحساب مع تنسيق محسن
            var titleRect = new RectangleF(startX, startY, e.PageBounds.Width - 100, 50);
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), titleRect);

            string title = "كشف حساب العميل";
            var titleFont = new Font("Segoe UI", 20F, FontStyle.Bold);
            SizeF titleSize = graphics.MeasureString(title, titleFont);
            float titleCenterX = startX + (titleRect.Width - titleSize.Width) / 2;
            graphics.DrawString(title, titleFont, Brushes.White, titleCenterX, startY + 10);

            startY += 70;

            // بيانات العميل والسيارة والبيع في جدول منظم
            var infoTableWidth = e.PageBounds.Width - 100;
            var infoTableHeight = 120;
            var infoRect = new RectangleF(startX, startY, infoTableWidth, infoTableHeight);

            // رسم إطار الجدول الرئيسي
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), infoRect);
            graphics.DrawRectangle(new Pen(Color.FromArgb(0, 102, 204), 2), Rectangle.Round(infoRect));

            // تقسيم الجدول إلى ثلاثة أعمدة
            var colWidth = infoTableWidth / 3;

            // رسم خطوط الفصل العمودية
            graphics.DrawLine(new Pen(Color.FromArgb(0, 102, 204), 1),
                startX + colWidth, startY, startX + colWidth, startY + infoTableHeight);
            graphics.DrawLine(new Pen(Color.FromArgb(0, 102, 204), 1),
                startX + (colWidth * 2), startY, startX + (colWidth * 2), startY + infoTableHeight);

            // العمود الأول: بيانات العميل
            var customerHeaderRect = new RectangleF(startX, startY, colWidth, 30);
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), customerHeaderRect);
            graphics.DrawString("بيانات العميل", new Font("Segoe UI", 12F, FontStyle.Bold),
                Brushes.White, startX + 10, startY + 8);

            if (customer != null)
            {
                var customerDataFont = new Font("Segoe UI", 10F);
                graphics.DrawString($"الاسم: {customer.FullName}", customerDataFont,
                    Brushes.Black, startX + 10, startY + 40);
                graphics.DrawString($"الهاتف: {customer.PrimaryPhone}", customerDataFont,
                    Brushes.Black, startX + 10, startY + 60);
                graphics.DrawString($"العنوان: {customer.Address}", customerDataFont,
                    Brushes.Black, startX + 10, startY + 80);
            }

            // العمود الثاني: بيانات السيارة
            var carHeaderRect = new RectangleF(startX + colWidth, startY, colWidth, 30);
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), carHeaderRect);
            graphics.DrawString("بيانات السيارة", new Font("Segoe UI", 12F, FontStyle.Bold),
                Brushes.White, startX + colWidth + 10, startY + 8);

            if (car != null)
            {
                var carDataFont = new Font("Segoe UI", 10F);
                graphics.DrawString($"الماركة: {car.Brand}", carDataFont,
                    Brushes.Black, startX + colWidth + 10, startY + 40);
                graphics.DrawString($"الموديل: {car.Model}", carDataFont,
                    Brushes.Black, startX + colWidth + 10, startY + 60);
                graphics.DrawString($"السنة: {car.Year}", carDataFont,
                    Brushes.Black, startX + colWidth + 10, startY + 80);
                graphics.DrawString($"السعر: {car.SuggestedSellPrice:C}", carDataFont,
                    Brushes.Black, startX + colWidth + 10, startY + 100);
            }

            // العمود الثالث: تفاصيل البيع
            var saleHeaderRect = new RectangleF(startX + (colWidth * 2), startY, colWidth, 30);
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), saleHeaderRect);
            graphics.DrawString("تفاصيل البيع", new Font("Segoe UI", 12F, FontStyle.Bold),
                Brushes.White, startX + (colWidth * 2) + 10, startY + 8);

            if (sale != null)
            {
                var saleDataFont = new Font("Segoe UI", 10F);
                graphics.DrawString($"تاريخ البيع: {sale.SaleDate:yyyy-MM-dd}", saleDataFont,
                    Brushes.Black, startX + (colWidth * 2) + 10, startY + 40);
                graphics.DrawString($"السعر النهائي: {sale.ActualSellPrice:C}", saleDataFont,
                    Brushes.Black, startX + (colWidth * 2) + 10, startY + 60);
                graphics.DrawString($"المدفوع: {sale.DownPayment:C}", saleDataFont,
                    Brushes.Black, startX + (colWidth * 2) + 10, startY + 80);
                graphics.DrawString($"المتبقي: {(sale.ActualSellPrice - sale.DownPayment):C}", saleDataFont,
                    Brushes.Black, startX + (colWidth * 2) + 10, startY + 100);
            }

            startY += infoTableHeight + 20;

            // Installments Header
            graphics.DrawString("جدول الأقساط:", boldFont, brush, startX, startY);
            startY += 20;

            // رسم جدول الأقساط المحسن
            if (dgvInstallments.Rows.Count > 0)
            {
                int installmentTableWidth = 500;
                int installmentColWidth = installmentTableWidth / dgvInstallments.Columns.Count;

                // رسم خلفية رؤوس الأعمدة
                var installmentHeaderRect = new Rectangle(startX, (int)startY, installmentTableWidth, 25);
                graphics.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), installmentHeaderRect);
                graphics.DrawRectangle(Pens.Black, installmentHeaderRect);

                // رسم رؤوس الأعمدة
                int colX = startX;
                foreach (DataGridViewColumn column in dgvInstallments.Columns)
                {
                    graphics.DrawString(column.HeaderText, new Font("Segoe UI", 9F, FontStyle.Bold),
                        Brushes.White, colX + 5, startY + 5);
                    graphics.DrawLine(Pens.White, colX + installmentColWidth, (int)startY, colX + installmentColWidth, (int)startY + 25);
                    colX += installmentColWidth;
                }
                startY += 30;

                // رسم صفوف البيانات
                int rowIndex = 0;
                foreach (DataGridViewRow row in dgvInstallments.Rows)
                {
                    if (row.IsNewRow) continue;

                    // خلفية الصف المتناوبة
                    var rowRect = new Rectangle(startX, (int)startY, installmentTableWidth, 22);
                    if (rowIndex % 2 == 0)
                        graphics.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), rowRect);

                    graphics.DrawRectangle(Pens.LightGray, rowRect);

                    colX = startX;
                    foreach (DataGridViewCell cell in row.Cells)
                    {
                        string cellValue = cell.Value?.ToString() ?? "";
                        graphics.DrawString(cellValue, font, brush, colX + 5, startY + 3);
                        graphics.DrawLine(Pens.LightGray, colX + installmentColWidth, (int)startY, colX + installmentColWidth, (int)startY + 22);
                        colX += installmentColWidth;
                    }
                    startY += 24;
                    rowIndex++;
                }
            }
            startY += 20;

            // مربع الملخص المالي المحسن
            var summaryRect = new Rectangle(startX, (int)startY, e.PageBounds.Width - 100, 120);
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(248, 249, 250)), summaryRect);
            graphics.DrawRectangle(new Pen(Color.FromArgb(0, 102, 204), 3), summaryRect);

            // عنوان الملخص
            var summaryHeaderRect = new Rectangle(startX, (int)startY, summaryRect.Width, 35);
            graphics.FillRectangle(new SolidBrush(Color.FromArgb(0, 102, 204)), summaryHeaderRect);
            graphics.DrawString("الملخص المالي", new Font("Segoe UI", 14F, FontStyle.Bold),
                Brushes.White, startX + 10, startY + 8);

            // حساب البيانات المالية من الأقساط
            decimal totalAmount = 0;
            decimal paidAmount = 0;

            foreach (DataGridViewRow row in dgvInstallments.Rows)
            {
                if (row.IsNewRow) continue;

                if (decimal.TryParse(row.Cells["المبلغ_المستحق"]?.Value?.ToString()?.Replace("$", "").Replace(",", ""), out decimal installmentAmount))
                    totalAmount += installmentAmount;

                // Since we don't have a "المدفوع" column, we need to calculate paid amount based on status
                string status = row.Cells["الحالة"]?.Value?.ToString() ?? "";
                if (status == "مدفوع" && decimal.TryParse(row.Cells["المبلغ_المستحق"]?.Value?.ToString()?.Replace("$", "").Replace(",", ""), out decimal paidInstallment))
                    paidAmount += paidInstallment;
            }

            decimal remainingAmount = totalAmount - paidAmount;

            // تقسيم الملخص إلى ثلاثة أعمدة
            var summaryColWidth = summaryRect.Width / 3;

            // رسم خطوط الفصل العمودية
            graphics.DrawLine(new Pen(Color.FromArgb(0, 102, 204), 1),
                startX + summaryColWidth, (int)startY + 35, startX + summaryColWidth, (int)startY + summaryRect.Height);
            graphics.DrawLine(new Pen(Color.FromArgb(0, 102, 204), 1),
                startX + (summaryColWidth * 2), (int)startY + 35, startX + (summaryColWidth * 2), (int)startY + summaryRect.Height);

            var summaryFont = new Font("Segoe UI", 12F, FontStyle.Bold);
            var summaryValueFont = new Font("Segoe UI", 14F, FontStyle.Bold);

            // العمود الأول: إجمالي المبلغ
            graphics.DrawString("إجمالي المبلغ", summaryFont, Brushes.Black,
                startX + 10, startY + 50);
            graphics.DrawString($"{totalAmount:C}", summaryValueFont,
                new SolidBrush(Color.FromArgb(0, 102, 204)), startX + 10, startY + 75);

            // العمود الثاني: المبلغ المدفوع
            graphics.DrawString("المبلغ المدفوع", summaryFont, Brushes.Black,
                startX + summaryColWidth + 10, startY + 50);
            graphics.DrawString($"{paidAmount:C}", summaryValueFont,
                new SolidBrush(Color.FromArgb(40, 167, 69)), startX + summaryColWidth + 10, startY + 75);

            // العمود الثالث: المبلغ المتبقي
            graphics.DrawString("المبلغ المتبقي", summaryFont, Brushes.Black,
                startX + (summaryColWidth * 2) + 10, startY + 50);

            var remainingColor = remainingAmount > 0 ? Color.FromArgb(220, 53, 69) : Color.FromArgb(40, 167, 69);
            graphics.DrawString($"{remainingAmount:C}", summaryValueFont,
                new SolidBrush(remainingColor), startX + (summaryColWidth * 2) + 10, startY + 75);
        }
    }
}


